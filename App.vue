<script>
import { mapState, mapMutations } from 'vuex';

// #ifdef APP-PLUS
import appService from '@/api/app-service.js';
// #endif

import formExtend from '@/api/form-extend.js';

export default {
    onLaunch: function () {
        let uniIdToken = uni.getStorageSync('uniIdToken');
        if (uniIdToken) {
            this.login(uni.getStorageSync('username'));
        }
        // #ifdef APP-PLUS
        appService.initAppVersion();
        // #endif
        formExtend.extend();
    },

    onShow: function () {
        uni.getLocation({
            type: 'gcj02',
            success: function (res) {
                uni.setStorageSync('APP_location', res);
            },
            fail(err) {}
        });
    },

    onHide: function () {
        console.log('App Hide');
    },
    methods: {
        ...mapMutations(['login'])
    }
};
</script>

<style>
/* 头条小程序需要把 iconfont 样式放到组件外 */
@import 'components/m-icon/m-icon.css';
@import 'static/css/power.css';
/* @import 'static/css/form.css'; */
@import 'static/css/list.css';
@import 'static/online/css/style.css';
@import 'static/online/css/swiper.min.css';
@import 'static/login/css/style.css';
@import 'static/app/css/style.css';
@import 'static/freedom/css/style.css';
@import 'static/study/css/build.css';
@import 'static/study/css/style.css';
/* @import 'static/app/css/style.css'; */

/*每个页面公共css */
page {
    height: 100%;
    min-height: 100%;
    display: flex;
    font-size: 16px;
}

.video-container {
    position: relative;
    width: 100%;
    /* height: calc((100Vw - 80upx) / 3); */
    top: 208rpx;
}

.custom-play-icon {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 60px;
    height: 60px;
    background-color: rgba(0, 0, 0, 0.7);
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    z-index: 10;
}

.play-button {
    width: 0;
    height: 0;
    border-left: 15px solid white;
    border-top: 10px solid transparent;
    border-bottom: 10px solid transparent;
}


/* #ifdef MP-WEIXIN */
page {
    width: 100%;
}
/* #endif */

/* #ifdef MP-BAIDU */
page {
    width: 100%;
    height: 100%;
    display: block;
}

swan-template {
    width: 100%;
    min-height: 100%;
    display: flex;
}

/* 原生组件模式下需要注意组件外部样式 */
custom-component {
    width: 100%;
    min-height: 100%;
    display: flex;
}

/* #endif */

/* #ifdef MP-ALIPAY */
page {
    min-height: 100vh;
}

/* #endif */

/* 原生组件模式下需要注意组件外部样式 */
m-input {
    width: 100%;
    /* min-height: 100%; */
    display: flex;
    flex: 1;
}

.search-bar {
    background: #fff;
}

.datetime-board {
    color: red !important;
    font-size: 38rpx !important;
}

.datetime-board-active {
    color: #0faeff !important;
    font-size: 38rpx !important;
}

.content {
    display: flex;
    flex: 1;
    flex-direction: column;
    background-color: #efeff4;
    padding: 10px;
}

.input-group {
    background-color: #ffffff;
    margin-top: 20px;
    position: relative;
}

.input-group::before {
    position: absolute;
    right: 0;
    top: 0;
    left: 0;
    height: 1px;
    content: '';
    -webkit-transform: scaleY(0.5);
    transform: scaleY(0.5);
    background-color: #c8c7cc;
}

.input-group::after {
    position: absolute;
    right: 0;
    bottom: 0;
    left: 0;
    height: 1px;
    content: '';
    -webkit-transform: scaleY(0.5);
    transform: scaleY(0.5);
    background-color: #c8c7cc;
}

.input-row {
    display: flex;
    flex-direction: row;
    position: relative;
    font-size: 18px;
    line-height: 40px;
}

.input-row .title {
    width: 100px;
    padding-left: 15px;
}

.input-row.border::after {
    position: absolute;
    right: 0;
    bottom: 0;
    left: 8px;
    height: 1px;
    content: '';
    -webkit-transform: scaleY(0.5);
    transform: scaleY(0.5);
    background-color: #c8c7cc;
}

.btn-row {
    margin-top: 25px;
    padding: 10px;
}

button.primary {
    background-color: #0faeff;
}
.flex-row-layout {
    position: relative;
}
.form-verify {
    position: absolute;
    top: 30rpx;
}

.form-checkbox-element {
    position: relative;
}

.form-radio-element {
    position: relative;
}

.form-textarea-element {
    position: relative;
}

.uni-toast {
    z-index: 9999999 !important;
}
</style>

<style lang="scss">
@import './node_modules/bowo-form/static/css/form.scss';
</style>
