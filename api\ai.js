import axios from '@/common/ajaxRequest.js';

import {
	ULR_BASE
} from '@/common/config.js';


// 移动执法AI-污染源基本信息
export const QUERY_YDZFAI_WRYJBXX = data => {
	data.service = 'QUERY_YDZFAI_WRYJBXX';
	return axios.request({
		url: ULR_BASE,
		method: 'POST',
		data: data
	});
};


// 移动执法AI-检查主体
export const QUERY_YDZFAI_JCZT = data => {
	data.service = 'QUERY_YDZFAI_JCZT';
	return axios.request({
		url: ULR_BASE,
		method: 'POST',
		data: data
	});
};


// 移动执法AI-任务来源
export const QUERY_YDZFAI_RWLY = data => {
	data.service = 'QUERY_YDZFAI_RWLY';
	return axios.request({
		url: ULR_BASE,
		method: 'POST',
		data: data
	});
};

// 移动执法AI-任务类型
export const QUERY_YDZFAI_WTLB = data => {
	data.service = 'QUERY_YDZFAI_WTLB';
	return axios.request({
		url: ULR_BASE,
		method: 'POST',
		data: data
	});
};


// 移动执法AI-问题记录
export const QUERY_YDZFAI_WTJL = data => {
	data.service = 'QUERY_YDZFAI_WTJL';
	return axios.request({
		url: ULR_BASE,
		method: 'POST',
		data: data
	});
};


// 执法归档-执法证据
export const QUERY_DYNAMICFORM_DATA = data => {
	data.service = 'QUERY_DYNAMICFORM_DATA';
	return axios.request({
		url: ULR_BASE,
		method: 'POST',
		data: data
	});
};

// 执法归档-执法笔录
export const QUERY_BL_LIST = data => {
	data.service = 'QUERY_BL_LIST';
	return axios.request({
		url: ULR_BASE,
		method: 'POST',
		data: data
	});
};

// 移动执法AI-智能提醒
export const QUERY_YDZFAI_ZNTX = data => {
	data.service = 'QUERY_YDZFAI_ZNTX';
	return axios.request({
		url: ULR_BASE,
		method: 'POST',
		data: data
	});
};

// 移动执法AI-执法检查记录
export const QUERY_YDZFAI_ZFJCJL = data => {
	data.service = 'QUERY_YDZFAI_ZFJCJL';
	return axios.request({
		url: ULR_BASE,
		method: 'POST',
		data: data
	});
};

// 移动执法AI-执法主体-问题列表
export const QUERY_ZFZT_WTJL = data => {
	data.service = 'QUERY_ZFZT_WTJL';
	return axios.request({
		url: ULR_BASE,
		method: 'POST',
		data: data
	});
};