import http from '@/common/net/http.js';
import { LOGIN_ULR_BASE } from '@/common/config.js'
import { getAuthToken } from '@/api/record.js';

const APP_VERSION_PREFIX = 'APP_VERSION@'
	
let progressUpdater = null

const getAppVersionKey = () => {
	return `${APP_VERSION_PREFIX}${plus.runtime.appid}`
}

export default {
	initAppVersion() {
		plus.runtime.getProperty(plus.runtime.appid, (wgtVersion) => {
			uni.setStorageSync(getAppVersionKey(), wgtVersion)
		})
	},
	
	checkMPAppUpgrade() {
		http.post(`${http.loginUrl}/mobile/base/searchBbxxDetail`, {
			os: 'WECHAT',
			BBH: '0.0.1' // 此字段是为了区分新老版本的app，后台需要这个字段来判断是否要查新表
		},).then(latestVersion => {
			console.log(`MP最新版本：${JSON.stringify(latestVersion, null, 4)}`)
			if(latestVersion) {
				this.shouldUpgrade(latestVersion)
			}
		}).catch(e => {
			console.log(`获取最新版本出错：${JSON.stringify(e, null, 4)}`)
		})
	},
	
	shouldUpgrade(latestVersion) {
		plus.runtime.getProperty(plus.runtime.appid, (wgtInfo) =>{
			let current = wgtInfo.versionCode || 0
			current = parseFloat(current)
			let latest = latestVersion['BBH'] || 0
			if(current < latest) {
				this.downloadUpgradeFile(latestVersion.WJID)
			}
		})
	},
	
	async downloadUpgradeFile(fileId) {
		let downloadUrl = `${http.loginUrl}/server/webapp/downloadFile?wdbh=${fileId}`
		
		let options = {
			filename: `_downloads/mp_upgrade/${fileId}.wgt`
		}
		let task = plus.downloader.createDownload(downloadUrl, options, (download, status) => {
			if(status === 200) {
				this.installUpgradePack(download.filename)
			} else {
				uni.showModal({
					title: '下载文件',
					content: '下载升级包出错'
				})
			}
		})
		let stateChange = (download, status) => {
			if(status === 200) {
				let percent = parseInt((download.downloadedSize / download.totalSize) * 100)
				if(progressUpdater) {
					progressUpdater(percent)
				}
			}
		}
		task.addEventListener('statechanged', stateChange, false)
		task.setRequestHeader('Token', uni.getStorageSync('authToken') || await getAuthToken());  // 或动态值如从全局变量获取
		task.start()
	},
	
	installUpgradePack(path) {
		uni.showLoading({
			title: '安装升级包',
		})
		let successCallback = () => {
			uni.hideLoading()
			uni.showToast({
				title: '安装更新成功',
				duration: 2000
			})
			setTimeout(() => {
				uni.hideToast()
				plus.runtime.restart();
			}, 2000)
		}
		
		let failCallback = (e) => {
			uni.hideLoading()
			uni.showToast({
				title: `安装更新失败`
			})
			uni.showModal({
				title: '升级失败',
				content: JSON.stringify(e, null, 4)
			})
		}
		plus.runtime.install(path, {force: true}, successCallback, failCallback);
	},
	
	setProgressUpdater(updater) {
		progressUpdater = updater
	},
	
	/**
	 * 跳转应用市场
	 * @param {Object} keyword 应用名称关键字
	 */
	goToMarket(keyword) {
		let Uri = plus.android.importClass('android.net.Uri')
		let Intent = plus.android.importClass('android.content.Intent')
		let marketUri = Uri.parse(`market://search?q=${keyword}`)
		let marketIntent = new Intent(Intent.ACTION_VIEW, marketUri)
		plus.android.runtimeMainActivity().startActivity(marketIntent)
	},
	
	checkAppUpgrade() {
		// #ifdef APP-PLUS
		let upgradeManagerClassName = 'com.bovosz.webapp.app.AppUpgradeManager'
		let AppUpgradeManager = plus.android.importClass(upgradeManagerClassName)
		if(AppUpgradeManager) {
			let upgradeManager = new AppUpgradeManager(plus.android.runtimeMainActivity())
			upgradeManager.checkUpgrade(`${LOGIN_ULR_BASE}/`)
		}
		// #endif
	},
	
	/**
	 * 获取App版本
	 */
	getMPVersion() {
		return uni.getStorageSync(getAppVersionKey())
	}
}