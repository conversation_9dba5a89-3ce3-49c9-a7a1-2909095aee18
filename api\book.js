/*
 * @Author: your name
 * @Date: 2021-04-07 16:01:20
 * @LastEditTime: 2021-05-17 17:57:15
 * @LastEditors: your name
 * @Description: In User Settings Edit
 * @FilePath: /UNI_APP_ShanDong/api/book.js
 */
import axios from '@/common/ajaxRequest.js'
import {ULR_BASE,LOGIN_ULR_BASE} from '@/common/config.js'

//发起post请求
const request = (params) => {
	return axios.request({
		method: 'post',
		url: ULR_BASE,
		data: params,
		showLoading: false
	})
}

export const postQueryWryjcTz = data => {
    return axios.request({
      method: 'post',
      url: ULR_BASE,
      data: data
    });
};

//获取台账的的详情
export const postQueryWryjcTzInfo = data => {
  return request(data)
};

//获取台账的的详情--获取对应的每一个动态表单
export const postQueryWryjcTzForm = data => {
  return request(data)
};

// 修改密码
export const postPassword = data => {
  return request(data)
};


export const postDynamicQuery = data => {
  return request(data)
};

/**
 * 查询行业代码类目
 */
export const queryIndustryCategories = (code) => {
	let params = {
		service: 'SEARCH_HYLX'
	}
	if(code) {
		params.HYLXDM = code
	}
	return request(params)
}


