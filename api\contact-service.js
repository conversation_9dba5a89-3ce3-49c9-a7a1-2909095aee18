/*
 * @Author: 姚进玺 ya<PERSON>jin<PERSON>@szboanda.net
 * @Date: 2021-12-29 10:04:46
 * @LastEditors: 姚进玺 <EMAIL>
 * @LastEditTime: 2022-05-25 14:27:08
 * @FilePath: /UNI_APP_ShanDong/api/contact-service.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import http from '@/common/net/http.js';
import { deepCopyObject } from '@/common/merge.js';
import loginService from '@/api/login-service.js';
import storage from '@/common/storage.js';

const USERS_CACHE_KEY = 'organization_users';

const getCacheKey = () => {
    return `${USERS_CACHE_KEY}@${loginService.getUserOrgid()}`;
};

/**
 * 同步用户信息
 */
export const syncUsers = () => {
    return new Promise((resolve, reject) => {
        http.post(`${http.url}`, {
            service: 'QUERY_ZFYH_INFO',
            isFilter: '0',
            version: '1',
            isDelDept: true
        })
            .then(resp => {
                if (resp.length > 0) {
                    saveUsersToLocal(resp);
                }
                resolve(resp);
            })
            .catch(error => {
                console.log(`同步人员数据出错：${error}`);
            });
    });
};

const saveUsersToLocal = users => {
    storage.setStorageWithExpired(getCacheKey(), users);
};

const getUsersFromLocal = () => {
    return storage.getEffectiveStorage(getCacheKey());
};

export const getDepartmentMenbers = (menbers, departmentId) => {
    if (menbers) {
        return menbers.filter(m => {
            return (m.BMBH = departmentId);
        });
    }
    return [];
};

/**
 * 加载所有用户
 */
export const loadUsers = (scope, refresh = false) => {
    // console.log(data, '123');
    return new Promise((resolve, reject) => {
        let localSource = null;
        if (!refresh) {
            localSource = getUsersFromLocal();
        }
        // console.log(localSource, 'localSource');
        if (localSource) {
            // let data = filterUserPurview(deepCopyObject(localSource), scope);
            resolve(deepCopyObject(localSource));
        } else {
            resolve(syncUsers());
        }
    });
};

const filterUserPurview = (localSource, scope) => {
    console.log(scope);
    console.log(localSource);
    localSource.forEach(element => {
        scope.forEach(e => {
            if (element.YHID === e) {
                console.log(element, '123');
            }
        });
    });
};

/**
 * 根据用户ID数组过滤用户
 */
const filterUsersByIds = (users, userIds) => {
    return users.filter(user => {
        return userIds.indexOf(user.YHID) !== -1;
    });
};

export default {
    syncUsers,
    getDepartmentMenbers,
    loadUsers,
    filterUsersByIds
};
