import http from 'bowo-sdk/net/http.js'
import contactService from 'bowo-form/components/service/contact-service.js'
import departmentService from 'bowo-form/components/service/department-service.js'
import action from 'bowo-form/components/action/action.js'

const delegateOrgServices =() => {
	contactService.injectSyncRequest(() => {
		return http.post('/invoke', {
			service: 'QUERY_ZFYH_INFO',
			version: '1',
			isDelDept: true
		})
	})
	
	departmentService.injectSyncRequest(() => {
		return http.post('/invoke', {
			service: 'QUERY_ZFYH_BMXX',
			version: '1'
		})
	})
}

//注册表单内动作处理器		
const registerActions = () => {
	action.registerExecutor('pattern-inquiry', openConvenientInquirySentence)
	action.registerExecutor('pattern-conclusion', openConvenientConclusionSentence)
	action.registerExecutor('pollution-selector', selectPollution)
}

/**
 * 打开询问常用语句页面
 */
const openConvenientInquirySentence = (urlParams) => {
	uni.navigateTo({
	    url: '/pages/form/textarea-template-language?selectType=answer'
	});
}

const openConvenientConclusionSentence = (urlParams) => {
	uni.navigateTo({
	    url: `/pages/form/textarea-template-language?${urlParams}`
	})
}

const selectPollution = () => {
	uni.navigateTo({
	    url: `/pages/wry/wry-list-offline`
	})
}

const extend = () => {
	delegateOrgServices()
	registerActions()
}

export default {
	extend
}