/*
 * @Author: 姚进玺 <EMAIL>
 * @Date: 2021-07-15 14:55:24
 * @LastEditors: 姚进玺 <EMAIL>
 * @LastEditTime: 2022-10-27 18:05:16
 * @FilePath: /UNI_APP_ShanDong/api/freedom.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import axios from '@/common/ajaxRequest.js'
import {ULR_BASE} from '@/common/config.js'


export const getData = data => {
	// data.service = 'DISCRETION_SERVICE';
  data.service = 'DISCRETION_NEW_SERVICE';
    return axios.request({
      method: 'post',
      url: ULR_BASE,
      data: data
    });
};

export const getElementData = data => {
	data.service = 'DISCRETION_NEW_SERVICE';
    return axios.request({
      method: 'post',
      url: ULR_BASE,
      data: data
    });
};
