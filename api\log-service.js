import http from '@/common/net/http.js';

export default {
	
	submitExceptionLog(service, serviceParams, errorMsg) {
		let userInfo = uni.getStorageSync('userInfo') || {}
		let serviceParamsStr = serviceParams
		if(typeof serviceParams === 'object') {
			serviceParamsStr = JSON.stringify(serviceParams)
		}
		
		let errorMsgStr = errorMsg
		if(typeof errorMsg === 'object') {
			errorMsgStr = JSON.stringify(errorMsg)
		}
		let params = {
			service: 'MOBILE_ERROR_RECORD',
			SERVICENAME: service,
			PARAM: serviceParamsStr,
			ERRORMSG: errorMsgStr,
			ERRORUSER: userInfo.id,
			version: 1
		}
		return http.post(`${http.url}`, params)
	}
	
}