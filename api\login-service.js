import http from '@/common/net/http.js';
import formContext from 'bowo-form/components/context.js'
import Vue from 'vue';

const CACHE_KEY_USER_INFO = 'user_info';

const CACHE_KEY_TOKEN = 'token';

const CACHE_KEY_USER_ID = 'user_id';

const CACHE_KEY_PASSWORD = 'password';

export const LOGIN_CACHE_KEYS = {
  userInfo: CACHE_KEY_USER_INFO,
  token: CACHE_KEY_TOKEN,
  userId: CACHE_KEY_USER_ID,
  password: CACHE_KEY_PASSWORD
}

export const loginByPassword = (userId, password) => {
  clearAuthInfoBeforeLogin(userId)
  http.post(`${http.loginUrl}/mobileLogin`, {
    userid: userId,
    password,
    BBH: '0.0.1' // 此字段是为了区分新老版本的app，老版本app没有这个字段则弹窗提示更新
  }).then(resp => {
    uni.setStorageSync('userInfo', resp.userInfo);
    onLoginSuccess(userId, resp);
    uni.$emit('onLoginSuccess', resp.userInfo);
  }).catch(error => {
    uni.$emit('onLoginFail', error || error.errMsg);
    console.log(`登录出错：${JSON.stringify(error, null, 4)}`)
  });
}

const onLoginSuccess = (userId, loginResp) => {
  //缓存用户ID
  uni.setStorage({
    key: CACHE_KEY_USER_ID,
    data: userId,
    fail(error) {
      console.log(`缓存用户ID出错: ${JSON.stringify(error, null, 4)}`)
    }
  })

  //缓存token
  uni.setStorage({
    key: CACHE_KEY_TOKEN,
    data: loginResp.jwtToken,
    fail(error) {
      console.log(`缓存token出错: ${JSON.stringify(error, null, 4)}`)
    }
  })
  formContext.setAccessToken(loginResp.jwtToken)

  //缓存用户基本信息
  let userProfile = loginResp.userInfo
  userProfile.departmentId = userProfile.bmbh
  uni.setStorage({
    key: CACHE_KEY_USER_INFO,
    data: userProfile,
    fail(error) {
      console.log(`缓存用户信息出错：${JSON.stringify(error, null, 4)}`)
    }
  })

  formContext.setUserProfile(userProfile)

  // #ifdef APP-PLUS
  setAuthorityUserOnAndroid(userId, loginResp)
  // #endif
}

// #ifdef APP-PLUS
const setAuthorityUserOnAndroid = (userId, loginResp) => {
  if (plus.android) {
    let AuthorityUser = plus.android.importClass('com.bovosz.webapp.auth.AuthorityUser')
    if (AuthorityUser) {
      let authorityUser = new AuthorityUser()
      authorityUser.setUserId(userId)
      authorityUser.setToken(loginResp.jwtToken)
      let AuthAgent = plus.android.importClass('com.bovosz.webapp.auth.AuthAgent')
      let authAgent = AuthAgent.getInstance()
      authAgent.setAuthorityUser(authorityUser)
    }
  }
}
// #endif

/**
 * 登录前清除用户缓存
 */
const clearAuthInfoBeforeLogin = (userId) => {
  let cachedUserId = uni.getStorageSync(CACHE_KEY_USER_ID)
  if (cachedUserId && cachedUserId !== userId) {
    for (let p in LOGIN_CACHE_KEYS) {
      try {
        uni.removeStorageSync(LOGIN_CACHE_KEYS[p])
      } catch (error) {
        console.log(`删除用户信息缓存[${key}]出错`)
      }
    }
    uni.removeStorageSync('userInfo')
  }
}

export default {
  loginByPassword,
  getUserOrgid() {
    let userInfo = uni.getStorageSync('userInfo')
    return userInfo.orgid
  },

  getAuthUser() {
    return uni.getStorageSync(CACHE_KEY_USER_INFO) || {}
  },

  getAuthUserId() {
    return this.getAuthUser().id || ''
  },

  //获取用户部门ID
  getUserDepartmentId() {
    return this.getAuthUser().department || this.getAuthUser().bmbh || ''
  },

  isAdministrator() {
    let authedUser = this.getAuthUser()
    if (authedUser) {
      return (authedUser.name || '').indexOf('管理员') !== -1
    }
    return false
  }

}
