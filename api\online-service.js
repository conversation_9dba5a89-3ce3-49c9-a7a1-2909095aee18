import axios from '@/common/ajaxRequest.js'
import http from '@/common/net/http.js'
import { ULR_BASE, LOGIN_ULR_BASE } from '@/common/config.js'

//发起post请求
const request = (params) => {
	return axios.request({
		method: 'post',
		url: ULR_BASE,
		data: params,
		showLoading: false
	})
}

/**
 * 查询污染源排口信息
 * @param {pollutionId} 污染源编号   
 */
export const queryDischargePorts = (pollutionId) => {
	return new Promise((resolve, reject) => {
		http.get(`${http.loginUrl}/index/indexcontroller/pfk?WRYBH=${pollutionId}`)
			.then(resp => {
				let originalPorts = resp.data || []
				let wrapPorts = originalPorts.map(p => {
					let factors = p.children.map(f => {
						return {
							id: f.value,
							name: f.label
						}
					})
					return {
						id: p.value,
						name: p.label,
						factors
					}
				})
				resolve(wrapPorts)
			})
	})
}

/**
 * 查询污染源指定排口最近监测数据
 * @param {pollutionId} 污染源编号
 * @param {portId} 排口ID
 */
export const queryLatestOnlineData = (pollutionId, portId) => {
	return new Promise((resolve, reject) => {
		http.get(`${http.loginUrl}/index/indexcontroller/getPkzxjcsj?WRYBH=${pollutionId}&PK=${portId}`)
			.then(resp => {
				let originalData = resp.data || []
				let wrapKeyData = originalData.map(item => {
					let discharge = item.XYZ
					if(typeof discharge === 'string') {
						discharge = parseFloat(discharge)
					}
					return {
						factor: item.WRWMC,
						discharge,
						standard: item.CBSX || '--',
						status: item.ZT || '正常'
					}
				})
				resolve(wrapKeyData)
			})
	})
}

/**
 * 查询污染源指定排口指定因子最近监测数据
 * @param {pollutionId} 污染源编号
 * @param {portId} 排口ID
 * @param {factorId} 因子ID  
 */
export const queryFactorHistoryData = (pollutionId, portId, factorId, timeType) => {
	return new Promise((resolve, reject) => {
		http.get(`${http.loginUrl}/index/indexcontroller/getChangingTrend?WRYBH=${pollutionId}&PK=${portId}&WRW=${factorId}&TIME=${timeType}`)
			.then(resp => {
				resolve(resp)
			})
	})
}

