import http from '@/common/net/http.js';
import loginService from '@/api/login-service.js'
import storage from '@/common/storage.js'

//组织机构的根节点
const ORG_ROOT_ID = loginService.getUserOrgid() == '370000' ? 'ROOT' : loginService.getUserOrgid();

//最顶级orgid
const TOP_LEVEL_ORGID = loginService.getUserOrgid()

const ORG_CACHE_KEY = 'organization_department';

/**
 * 部门信息缓存键
 */
const getCacheKey = () => {
	return `${ORG_CACHE_KEY}@${loginService.getUserOrgid()}`
}

const getRootOrgid = () => {
	let rootOrigid = loginService.getUserOrgid()
	if(TOP_LEVEL_ORGID === rootOrigid) {
		rootOrigid = ORG_ROOT_ID
	}
	return rootOrigid
}

/**
 * 检测组织机构数据源为无效
 */
const sourceInvalid = (source) => {
	return typeof source === 'undefined' 
		|| Array.isArray(source) === false 
		|| source.length === 0;
}

/**
 * 获取组织机构根节点
 * @param {Object} allDepartment
 * @param {Object} rootId
 */
const getRootDepartment = (departments) => {
	if(sourceInvalid(departments)){
		console.log(`无效的源：${JSON.stringify(departments, null, 4)}`)
		return null
	}
	let matched = departments.filter(item => {
		return item.ZZBH === getRootOrgid();
	}) 
	if(matched.length > 0){
		return matched[0];
	} else { 
		//当部门编号不是按钮行政区划定义，会出现找不到根节点的情况，此时需要根据数据权限查找根节点
		return resolveRootDepartment(departments)
	}
	return null;
}

const resolveRootDepartment = (departments) => {
	let userDepartment = getUserDepartment(departments)
	if(userDepartment) {
		let userPermissionCode = loginService.getUserOrgid()
		return searchMatchDeparment(departments, userDepartment, userPermissionCode)
	} else {
		return null
	}
}

/**
 * 递归查找符合用户权限码的最高级别部门
 */
const searchMatchDeparment = (departments, child, userPermissionCode) => {
	let parent = getParentDepartment(departments, child)
	if(parent) {
		let parentPermissionCode = parent.SJQX
		if(parentPermissionCode !== userPermissionCode) { //父节点权限码跟子节点的不相同，直接返回子节点
			return child
		} else { //父节点与子节点权限码一致，继续向上寻找，直接不一致
			return searchMatchDeparment(departments, parent, userPermissionCode)
		}
	} else { //不存在父节点，直接返回子节点
		return child
	}
}

/**
 * 查看当前用户所有父链节点
 */
const resolveParentChain = (departments) => {
	let chain = []
	let directParent = getRootDepartment(departments)
	if(directParent) {
		chain.push(directParent)
		recurveParentDepartment(departments, directParent, chain)
	}
	return chain
}

/**
 * 递归查看父节点
 */
const recurveParentDepartment = (departments, child, chain) => {
	let parent = getParentDepartment(departments, child)
	if(parent) {
		chain.unshift(parent)
		recurveParentDepartment(departments, parent, chain)
	}
}

/**
 * 获取部门的父部门
 */
const getParentDepartment = (departments, child) => {
	let parentId = child.SJZZXH
	return getDepartmentById(departments, parentId)
}

/**
 * 获取用户部门
 */
const getUserDepartment = (departments) => {
	let departmentId = loginService.getUserDepartmentId()
	return getDepartmentById(departments, departmentId)
}

/**
 * 根据部门编号获取部门信息
 */
const getDepartmentById = (departments, departmentId) => {
	let matched = departments.filter(d => {
		return d.ZZBH === departmentId
	})
	return matched.length > 0 ? matched[0] : null
}

/**
 * 获取指定节点子节点
 */
const getSubDepartments = (departments, parentOrId) => {
	if(sourceInvalid(departments)){
		return [];
	}
	let parentId = parentOrId.ZZBH || parentOrId;
	return departments.filter(item => {
		return item.SJZZXH === parentId && item.ZZBH !== getRootOrgid();
	}) 
}

/**
 * 判断一个部门下是否有子部门
 */
const hasChildren = (departments, departmentOrId) => {
	let departmentId = departmentOrId.ZZBH || departmentOrId;
	let subDepartments = getSubDepartments(departments, departmentId);
	return subDepartments.length > 0;
}

/**
 * 根据机构获取下辖成员
 */
const getOrgMembers = (menbers, orgId) => {
	if(sourceInvalid(menbers)){
		return [];
	}
	
	return menbers.filter(item => {
		return item.BMBH == orgId;
	});
}

/**
 * 是否包含下辖机构
 */
const hasOrgChildren = (menbers, orgId) => {
	let children = getOrgMembers(menbers, orgId);
	return children.length > 0;
}

/**
 * 缓存部门信息到本地
 */
const saveDepartmentsToLocal = (departments) => {
	storage.setStorageWithExpired(getCacheKey(), departments);
}

/**
 * 从本地缓存读取部门信息
 */
const getDepartmentsFromLocal = () => {
	return storage.getEffectiveStorage(getCacheKey());
}

/**
 * 同服务端同步全部缓存信息
 */
const syncDepartments = () => {
	return new Promise((resolve, reject) => {
		http.post(`${http.url}`, {
			service:"SEARCH_BMXX",
			isFilter: '0',
			version: "1"
		})
			.then(resp => {
				if(Array.isArray(resp) && resp.length > 0){
					saveDepartmentsToLocal(resp);
				}
				resolve(resp);
			})
			.catch(error => {
				console.log(`同步部门信息出错：${JSON.stringify(error, null, 4)}`)
			})
	});
};

/**
 * 加载全部部门信息
 * @param {Boolean} refresh 是否刷新缓存  
 */
export const loadDepartments = (refresh = false) => {
	return new Promise((resolve, reject) => {
		let localSource = null;
		if(!refresh){
			localSource = getDepartmentsFromLocal();
		}
		if(localSource){
			resolve(localSource);
		} else {
			resolve(syncDepartments());
		}
	});
}

/**
 * 根据部门编号过滤部门信息
 */
const filterDepartmentByIds = (departments, ids) => {
	return departments.filter(d => {
		return ids.indexOf(d.ZZBH) !== -1;
	});
}

export default {
	syncDepartments,
	loadDepartments,
	getRootDepartment,
	getSubDepartments,
	hasChildren,
	getOrgMembers,
	hasOrgChildren,
	filterDepartmentByIds,
	resolveParentChain
}