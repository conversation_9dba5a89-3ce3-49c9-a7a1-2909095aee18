// 污染源接口
import axios from '@/common/ajaxRequest.js'
import {ULR_BASE,LOGIN_ULR_BASE} from '@/common/config.js'


// 查询污染源关联的菜单 QUERY_WRY_MENU
export const getPollutionMenu = data => {
    return axios.request({
      method: 'post',
      url: ULR_BASE,
      data: {
		  service : 'QUERY_WRY_MENU'
	  }
    });
};


// 查询污染源列表  
// 查询污染源关联环境信访 QUERY_WRY_HJXF
// 查询污染源关联环境应急 QUERY_WRY_HJYJL
export const getPollutionList = data => {
    return axios.request({
      method: 'post',
      url: ULR_BASE,
      data: data
    });
};