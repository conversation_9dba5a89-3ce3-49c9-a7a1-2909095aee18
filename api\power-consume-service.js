import axios from '@/common/ajaxRequest.js'
import { ULR_BASE } from '@/common/config.js'
import http from '@/common/net/http.js'

//发起post请求
const request = (params) => {
	return axios.request({
		method: 'post',
		url: ULR_BASE,
		data: params,
		showLoading: false
	})
}

/**
 * 查询企业设备最近用电情况
 */
export const queryLatestPowerConsume = (pollutionId) => {
	let wrybh = '659f8fcf-1bdc-4238-989d-94e36af7ab28'
	return new Promise((resolve, reject) => {
		http.get(`${http.loginUrl}/index/indexcontroller/getYdqyYdsssj?enterpriseId=${wrybh}`)
			.then(resp => {
				let originalData = resp.data || []
				let wrapData = originalData.map(item => {
					return {
						device: item.equipmentName,
						phaseA: item.ua,
						phaseB: item.ub,
						phaseC: item.uc
					}
				})
				resolve(wrapData)
			})
			.catch(error => {
				resolve([])
			})
	})
}

/**
 * 查询所有企业生产线
 */
export const queryPollutionProduceLines = (pollutionId) => {
	let wrybh = '659f8fcf-1bdc-4238-989d-94e36af7ab28'
	return new Promise((resolve, reject) => {
		http.get(`${http.loginUrl}/index/indexcontroller/getEquipmentMsg?enterpriseId=${wrybh}`)
			.then(resp => {
				let lines = []
				let crudeData = resp.data.sysProductionLineList
				if(crudeData) {
					lines = crudeData.map(item => {
						return {
							id: item.id,
							name: item.name
						}
					})
				}
				resolve(lines)
			})
			.catch(error => {
				resolve([])
			})
	})
}

export const queryProduceLineData = (pollutionId, produceLineId, dataType = 1, timeType) => {
	let wrybh = '659f8fcf-1bdc-4238-989d-94e36af7ab28'
	return new Promise((resolve, reject) => {
		http.get(`${http.loginUrl}/index/indexcontroller/getTrendOfElectricityConsumption?enterpriseId=${wrybh}&dataType=${dataType}&sysProductionLineId=${produceLineId}&TIME=${timeType}`)
			.then(resp => {
				// console.log(`用电数据：${JSON.stringify(resp, null, 4)}`)
				let crudeData = resp.data
				let trendData = {}
				if(crudeData.timeData) {
					trendData.time = crudeData.timeData
					trendData.produceFacilities = crudeData.produceNameData
					trendData.cleanupFacilities = crudeData.pollutionNameData
					trendData.produceFacilityData = crudeData.produceSeriesData.map(item => {
						return {
							name: item.name,
							data: item.data
						}
					})
					
					trendData.cleanupFacilityData = crudeData.pollutionSeriesData.map(item => {
						return {
							name: item.name,
							data: item.data
						}
					})
				}
				resolve(trendData)
			})
			.catch(error => {
				resolve({})
			})
	})
}