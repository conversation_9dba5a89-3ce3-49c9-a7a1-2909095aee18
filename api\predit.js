/*
 * @Author: your name
 * @Date: 2021-03-23 12:22:37
 * @LastEditTime: 2022-11-16 20:18:18
 * @LastEditors: 姚进玺 <EMAIL>
 * @Description: In User Settings Edit
 * @FilePath: /UNI_APP_ShanDong/api/predit.js
 */
import axios from '@/common/ajaxRequest.js'
import {ULR_BASE} from '@/common/config.js'

export const queryXcjcRwsl = data => {
    return axios.request({
      method: 'post',
      url: ULR_BASE,
      data: {
		  service : 'QUERY_XCJCRWSL_SERVICE',
      userId: uni.getStorageSync('userInfo').id
	  }
    });
};

export const getUserInfoPermissions = data => {
  data.service = 'MOBILE_MENU';
  return axios.request({
    method: 'post',
    url: ULR_BASE,
    data: data
  });
};

//查询预警信息
export const queryYjxx = data => {
	data.service = 'QUERY_YWXJ_YJXX_SERVICE';
    return axios.request({
      method: 'post',
      url: ULR_BASE,
      data: data
    });
};

//查询预警信息的汇总数据
export const queryFltj = data => {
	data.service = 'QUERY_YWXJ_FLTJ_SERVICE';
    return axios.request({
      method: 'post',
      url: ULR_BASE,
      data: data
    });
};

// 预警信息确认
export const queryYjxxQr = data => {
	data.service = 'QUERY_YWXJ_YJQR_SERVICE';
    return axios.request({
      method: 'post',
      url: ULR_BASE,
      data: data
    });
};

// 水源地预警
export const querySyd = data => {
	data.service = 'SYD_ZF_YJ';
    return axios.request({
      method: 'post',
      url: ULR_BASE,
      data: data
    });
};
