import axios from '@/common/ajaxRequest.js';
import formUtil from '@/pages/form/Form.js';

import loginService from '@/api/login-service.js';
import http from '@/common/net/http.js';

import {
	deepCopyObject
} from '@/common/merge.js';
import {
	ULR_BASE,
	LOGIN_ULR_BASE,
	API_TT_SERVICE_URL,
	SERVER_FULL_URL
} from '@/common/config.js';
import {
	ATTACH_DATA_KEY
} from '@/api/form-service.js';

//表单编辑页退出事件
export const EVENT_EDITOR_EXIT = 'onFormEditorExit';

//进入预览笔录页面事件
export const EVENT_PREVIEW_RECORD = 'previewRecord';

//表单预览页删除退出事件
export const EVENT_RECORD_DELETE_EXIT = 'onRecordDeleteExit';

//笔录的新增模式
export const MODE_ADD = '1';
//笔录的修改模式
export const MODE_MODIFY = '2';

//综合执法、信访舆情及其他流程ID定义
export const WORKFLOW_TYPE_ZHZF = '1604994498221025833472';
export const WORKFLOW_TYPE_XFYQ = '1615712304090027631616';

//发起post请求
const request = params => {
	return axios.request({
		method: 'post',
		url: ULR_BASE,
		data: params,
		showLoading: false
	});
};

// 获取当前执法的步骤
export const getStepIndex = data => {
	data.service = 'INSPECT_STEP_OPERATE';
	return axios.request({
		method: 'post',
		url: ULR_BASE,
		data: data
	});
};

// 获取已办任务列表
export const getDoneTaskList = data => {
	data.service = 'QUERY_WRYJC_YBRW';
	return axios.request({
		method: 'post',
		url: ULR_BASE,
		data: data
	});
};

// 上传--证据的上传
export const getAttachList = data => {
	return axios.request({
		method: 'post',
		url: LOGIN_ULR_BASE + '/webapp/copyenclosure',
		data: data
	});
};

/**
 * 查询任务办理步骤列表
 * @param { String } 任务ID
 */
export const queryTaskProcessSteps = taskId => {
	let params = {
		service: 'QUERY_HJXF_BLGC_SERVICE',
		params: {
			LCSLBH: taskId
		}
	};
	return request(params);
};

//获取公共代码集--任务来源
export const postQueryGgdmj = data => {
	data.service = 'SEARCH_GGDMZ';
	return axios.request({
		method: 'post',
		url: ULR_BASE,
		data: data
	});
};

//获取台账列表数据（待办）
export const postQueryWryjcTz = data => {
	return axios.request({
		method: 'post',
		url: ULR_BASE,
		data: data
	});
};

//转办执法人员
export const postForwardPerson = data => {
	return axios.request({
		method: 'post',
		url: LOGIN_ULR_BASE + '/mobile/flow/fix/workflowturndealaction',
		data: data
	});
};

//现场执法人员签到
export const postQueryXcjcQd = data => {
	return axios.request({
		method: 'post',
		url: ULR_BASE,
		data: data
	});
};

//现场执法人员签到
export const postUpDataPerson = data => {
	data.service = 'UPDATE_YWZB_ZFZHSD';
	return axios.request({
		method: 'post',
		url: ULR_BASE,
		data: data
	});
};

//获取现场执法人员的数据
export const postQueryZfryxx = data => {
	return axios.request({
		method: 'post',
		url: ULR_BASE,
		data: data
	});
};

//现场执法人员签到的数据校验核对
export const postPersonEnter = data => {
	return axios.request({
		method: 'post',
		url: ULR_BASE,
		data: data
	});
};

//附件的列表
export const postOtherFile = data => {
	return axios.request({
		method: 'post',
		url: ULR_BASE,
		data: data
	});
};

//获取现场勘查最后一次的笔录信息
export const postQueryJcbl = data => {
	return axios.request({
		method: 'post',
		url: ULR_BASE,
		data: data
	});
};

// 获取执法两年内的次数统计
export const postTwoYearInfo = data => {
	data.service = 'QUERY_WRY_TWO_YEARS_QK';
	return axios.request({
		method: 'post',
		url: ULR_BASE,
		data: data
	});
};

//获取拥有的所有表单模型
export const postQueryBdmblx = data => {
	return axios.request({
		method: 'post',
		url: ULR_BASE,
		data: data
	});
};

//查询任务指定所有表单模板
export const queryTaskTemplates = data => {
	return request(data);
};

//获取拥有的所有表单模型
export const postAnnexTemplate = data => {
	return axios.request({
		method: 'post',
		url: ULR_BASE,
		data: data
	});
};

//查询指定污染源指定表单的历史笔录
export const queryHistoryRecord = (templateId, wrybh) => {
	let params = {
		service: 'QUERY_HISTORY_LIST',
		MBBH: templateId,
		WRYBH: wrybh
	};
	return request(params);
};

//查询表单元素关联关系
export const queryElementConstraints = templateId => {
	let params = {
		service: 'DYNAMIC_FORM_INTERACTION',
		template_id: templateId
	};
	return request(params);
};

//保存表单
export const dynamicformsave = data => {
	return axios.request({
		method: 'post',
		url: ULR_BASE,
		data: data,
		noShowError: true
	});
};

//获取待结束的笔录列表（用于删除编辑排序列表使用）
export const postBLFormList = data => {
	return axios.request({
		method: 'post',
		url: ULR_BASE,
		data: data
	});
};

//删除笔录
export const postDeleteFormList = data => {
	return axios.request({
		method: 'post',
		url: ULR_BASE,
		data: data
	});
};

//预览表单
export const postPrintForm = data => {
	data.service = 'PRINT_FORM';
	return axios.request({
		method: 'post',
		url: ULR_BASE,
		data: data
	});
};

//下载表单
export const postUpdataForm = data => {
	return axios.request({
		method: 'get',
		url: LOGIN_ULR_BASE + '/server/webapp/downloadFile',
		params: data,
		header: {
			token: uni.getStorageSync('authToken'), // 这里是要添加的请求头
		},
	});
};

//结束流程
export const postOverForm = data => {
	return axios.request({
		method: 'post',
		url: ULR_BASE,
		data: data
	});
};

//删除下载的数据
export const deleteUpData = data => {
	return axios.request({
		method: 'post',
		url: ULR_BASE,
		data: data
	});
};

//获取动态表单详情
export const getRecordData = data => {
	return axios.request({
		method: 'post',
		url: ULR_BASE,
		data: data
	});
};

//获取动态表单中的基本模板语句
export const getRecordLanguage = data => {
	return axios.request({
		method: 'post',
		url: ULR_BASE,
		data: data
	});
};

// 业务类型
export const getRecordType = data => {
	return axios.request({
		method: 'post',
		url: ULR_BASE,
		data: {
			service: 'SEARCH_GGDMZ',
			DMJBH: 'MBYJ_YWLX'
		}
	});
};

export const getCompanyList = (longitude, latitude) => {
	let params = {
		service: 'QUERY_WRY_FJQX_SERVICE',
		JD: longitude,
		WD: latitude
	};
	return request(params);
};

//修改日期更新
export const getTemplateUpdataTime = data => {
	data.service = 'QUERY_DATA_LAST_UPDATE_SERVICE';
	return axios.request({
		method: 'post',
		url: ULR_BASE,
		data: data
	});
};

// 修改污染物定位
export const changePollutionLocation = data => {
	data.service = 'BATCH_SAVE_BEAN';
	return axios.request({
		method: 'post',
		url: ULR_BASE,
		data: data
	});
};

// 电子归档附件列表
export const postElectronicList = data => {
	data.service = 'QUERY_FJWJ_LIST';
	return axios.request({
		method: 'post',
		url: ULR_BASE,
		data: data
	});
};

// 修改附件名称
export const editAttachNameFile = (data, type) => {
	let url = ULR_BASE;
	//烟台的地区特殊判断，如果是烟台地区就专门换地址
	if (type === 'yantai') {
		url = API_TT_SERVICE_URL;
	}
	data.service = 'FILE_INFORMATION';
	return axios.request({
		method: 'post',
		url: url,
		data: data
	});
};

// 删除电子归档附件
export const deleteElectronicFile = (data, type) => {
	let url = ULR_BASE;
	//烟台的地区特殊判断，如果是烟台地区就专门换地址
	if (type === 'yantai') {
		url = API_TT_SERVICE_URL;
	}
	data.service = 'DELETE_FILE';
	return axios.request({
		method: 'post',
		url: url,
		data: data
	});
};

/**
 * 删除已上传服务器的附件
 */
export const deleteRemoteFile = fileId => {
	let params = {
		service: 'DELETE_FILE',
		WDBH: fileId
	};
	return request(params);
};

// 获取电子归档列表
export const queryArchiveTypes = taskId => {
	let params = {
		service: 'QUERY_FJLX_LIST',
		YWSJID: taskId
	};
	return request(params);
};

//获取附件的基本详情
// 获取执法两年内的次数统计
export const postGetBasicData = data => {
	data.service = 'QUERY_DYNAMICFORM_DATA';
	return axios.request({
		method: 'post',
		url: ULR_BASE,
		data: data
	});
};

// 获取水环境列表
export const getWaterTaskList = data => {
	data.service = 'WATER_SOURCE_CHOOSING';
	data.CHECKTYPE = 'SYDJC';
	data.orgid = loginService.getUserOrgid();
	return axios.request({
		method: 'post',
		url: ULR_BASE,
		data: data
	});
};

// 获取地块检查列表
export const getLandTaskList = data => {
	data.service = 'LAND_MASS_CHOOSING';
	data.CHECKTYPE = 'DKJC';
	data.orgid = loginService.getUserOrgid();
	return axios.request({
		method: 'post',
		url: ULR_BASE,
		data: data
	});
};

// 获取自然保护地检查列表
export const getAreaTaskList = data => {
	data.service = 'NATURAL_AREA_CHOOSING';
	data.CHECKTYPE = 'ZRBHQ';
	data.orgid = loginService.getUserOrgid();
	return axios.request({
		method: 'post',
		url: ULR_BASE,
		data: data
	});
};

// 更新主表数据
export const updataForm = data => {
	data.service = 'UPDATE_COPY_FJXX';
	return axios.request({
		method: 'post',
		url: ULR_BASE,
		data: data
	});
};

// 查询大气帮扶待办列表
export const getWeatherSupportList = data => {
	data.service = 'QUERY_DQJDBF_DBRW';
	data.orgid = loginService.getUserOrgid();
	return axios.request({
		method: 'post',
		url: ULR_BASE,
		data: data
	});
};

// 查询大气帮扶待办列表
export const getWeatherBookList = data => {
	data.service = 'QUERY_DQJDBF_ZFTZ';
	data.orgid = loginService.getUserOrgid();
	return axios.request({
		method: 'post',
		url: ULR_BASE,
		data: data
	});
};

// 查询大气帮扶转办及办结
export const postTransferOrCheck = data => {
	data.service = 'QUERY_DQJDBF_ZBORBJ';
	data.orgid = loginService.getUserOrgid();
	return axios.request({
		method: 'post',
		url: ULR_BASE,
		data: data
	});
};

export const postCheckQuestionList = data => {
	data.service = 'QUERY_DQBF_XCJC_WTXX';
	return axios.request({
		method: 'post',
		url: ULR_BASE,
		data: data
	});
};

//删除大气督查表单
export const deleteQuestionList = data => {
	return axios.request({
		method: 'post',
		url: LOGIN_ULR_BASE + '/dynamicform/viewresolvercontroller/deletedata',
		data: data
	});
};

//执法app调查询问笔录是否填写
export const checkTableStatus = data => {
	data.service = 'CHECK_TABLE_STATUS';
	return axios.request({
		method: 'post',
		url: ULR_BASE,
		data: data
	});
};


/**
 * 查询任务指定表单模板
 * @param {String} 任务ID
 * @param {String} 表单模板ID
 * @return {Promise} 从resolve回调返回模板JSON对象
 */
export const queryTaskFormTemplate = (taskId, templateId) => {
	let userInfo = uni.getStorageSync('userInfo');
	let params = {
		service: 'GET_DYNAMICFORM_MODEL',
		businessId: taskId,
		bbxh: templateId,
		userId: userInfo.id
	};

	return new Promise((resolve, reject) => {
		axios
			.request({
				method: 'post',
				url: ULR_BASE,
				data: params
			})
			.then(resp => {
				let originalTemplate = resp.datas_json;
				let reorganizeTemplate =
					formUtil.packPeerElementIntoGroup(originalTemplate);
				resolve(reorganizeTemplate);
			})
			.catch(error => {
				reject(error);
				uni.showModal({
					title: '网络异常',
					content: '获取表单出错，请稍后再试'
				});
			});
	});
};

/**
 * 查询任务下所有已提交的笔录
 * @param {String}
 */
export const queryTaskRecords = taskId => {
	return new Promise((resolve, reject) => {
		let params = {
			service: 'QUERY_BL_LIST',
			xh: taskId,
			bllx: 'ZJSJ'
		};
		request(params)
			.then(resp => {
				let records = resp.data_json || [];
				resolve(records);
			})
			.catch(error => {
				console.log(`获取表单数据出错：${error}`);
				reject(error);
			});
	});
};

/**
 * 将表单的附件信息归类，以附件大类_小类为键进行分组
 * @param {Array} 表单未分组的附件信息
 */
const sortFormAattachs = attachs => {
	let groupedAttachs = {};
	if (Array.isArray(attachs)) {
		attachs.forEach(attach => {
			let majorCate = attach.LXDM || '';
			let minorCate = attach.ZLXDM || '';
			//预防返回数据没有分类，需要一个默认的分类
			let key = `${majorCate}_${minorCate}` || ATTACH_DATA_KEY;
			let group = [];
			if (groupedAttachs.hasOwnProperty(key)) {
				group = groupedAttachs[key];
			} else {
				groupedAttachs[key] = group;
			}
			uni.setStorageSync('form_data_key', key);
			group.push(attach);
		});
	}

	return groupedAttachs;
};

/**
 * 查询表单默认值
 */
export const queryFormDefaultValues = (taskId, templateId) => {
	let params = {
		service: 'GET_DYNAMICFORM_DEFAULTVALUE',
		businessId: taskId,
		bbxh: templateId
	};
	return request(params);
};

/**
 * 根据表单模板ID和笔录ID获取笔录详情数据
 */
export const queryRecordData = (templateId, recordId) => {
	let params = {
		service: 'QUERY_DYNAMICFORM_DATA',
		mbbh: templateId,
		recordId: recordId
	};
	return new Promise((resolve, reject) => {
		axios
			.request({
				method: 'post',
				url: ULR_BASE,
				data: params,
				showLoading: false
			})
			.then(resp => {
				let record = resp.data;
				if (record && Object.keys(record).length === 0) {
					record = null;
				}
				if (record !== null) {
					record = deepCopyObject(record);
				}
				let attachs = resp.data_fjxx;
				if (Array.isArray(attachs) && attachs.length > 0) {
					let groupedAttachs = sortFormAattachs(
						deepCopyObject(attachs)
					);
					if (record === null) {
						record = {};
					}
					Object.assign(record, groupedAttachs);
				}
				resolve(record);
			})
			.catch(error => {
				// console.log(`获取表单数据出错：${error}`)
			});
	});
};

/**
 * 提交现场执法任务笔录信息
 * @param {Object} 任务信息
 * @param {Object} 表单模板配置JSON
 * @param {String} 笔录ID
 * @param {Object} 表单数据
 */
export const submitRecord = (taskInfo, template, recordId, formData) => {
	let params = {
		service: 'DYNAMICFORM_SAVE',
		client_type: 'mobile_web',
		user_id: uni.getStorageSync('user_info').yhid || 'SDSZFJ',
		batch_id: taskInfo.YWXTBH,
		ywlcdybh: taskInfo.LCBH,
		template_id: template.templateId,
		record_id: recordId
	};
	let id = uni.getStorageSync('form_data_key');
	let copyData = deepCopyObject(formData);
	// if(Array.isArray(copyData[id]) && copyData[id].length > 0) {
	// if(copyData.WRYBH){
	copyData.WRYBH = taskInfo.WRYBH;
	copyData.WRYMC = taskInfo.RWBT;
	copyData.YWXTBH = taskInfo.YWXTBH;
	copyData.ORGID = uni.getStorageSync('user_info').orgid;
	let table = template.templateTable;
	params[table] = copyData;
	return axios.request({
		method: 'post',
		url: ULR_BASE,
		data: params
	});
	// }
};

/**
 * 保存笔录数据到服务器
 */
export const saveRecordToService = (taskInfo, template, recordId, formData) => {
	let taskId = taskInfo.YWXTBH;
	let templateId = template['templateId'];
	let table = template['templateTable'];

	let cloneFormData = deepCopyObject(formData);
	cloneFormData.YWXTBH = taskId;
	cloneFormData.WRYBH = taskInfo.WRYBH;
	cloneFormData.ORGID = loginService.getUserOrgid();

	let wrymc = cloneFormData.WRYMC || '';
	if (taskInfo.RWBT && wrymc === '') {
		cloneFormData.WRYMC = taskInfo.RWBT;
	}

	let params = {
		service: 'DYNAMICFORM_SAVE',
		client_type: 'mobile_web',
		user_id: loginService.getAuthUserId() || 'SDSZFJ',
		version: 1,
		batch_id: taskId,
		ywlcdybh: taskInfo.LCBH,
		template_id: templateId,
		record_id: recordId
	};
	params[table] = cloneFormData;
	return http.post(ULR_BASE, params);
};

/**
 * 查询证据收集表单模板
 */
export const queryEvidenceTemplate = taskId => {
	let params = {
		service: 'GET_MODELJSON_BY_FLAG',
		modelFlag: 'YDZF_ZJSJ'
	};
	if (taskId) {
		params.businessId = taskId;
	}
	return new Promise((resolve, reject) => {
		request(params)
			.then(resp => {
				resolve(resp.data_json);
			})
			.catch(error => {
				console.log(`查询证据收集表单出错：${error}`);
			});
	});
};

/**
 * 查询任务已分配表单模板
 */
export const queryTaskDispatchTemplates = taskId => {
	let params = {
		service: 'QUERY_FORMS',
		xczfbh: taskId,
		zdlx: 'SJD'
	};
	return request(params);
};

/**
 * 链接综合执法任务到信访舆情任务上
 * @param {String} taskId 信访舆情任务ID
 * @param {String} foreginTaskId 综合执法任务ID
 * @param {String} wrybh 污染源编号
 * @param {String} wrymc 污染源名称
 */
export const linkZhzfToXfyqTask = (taskId, foreginTaskId, wrybh, wrymc) => {
	let params = {
		service: 'ADD_HJYQZF_SERVICE',
		rwbh: taskId,
		xfyqbh: foreginTaskId,
		wrybh: wrybh,
		rwmc: wrymc
	};
	return request(params);
};

// 获取智能判案列表数据
// POST /mobile/bussiness/queryWfxw查询智能判案数据获取接口 QUERY_SMART_VERDICT_SERVICE
export const postIntelligentList = data => {
	return axios.request({
		method: 'post',
		url: ULR_BASE,
		data: {
			...data,
			service: 'QUERY_SMART_VERDICT_SERVICE'
		}
	});
};

// 获取便签列表
export const postBookMarkList = data => {
	data.service = 'QUERY_BQ_LIST';
	return axios.request({
		method: 'post',
		url: ULR_BASE,
		data: data
	});
};

// 删除笔录
export const deleteBookMarkList = data => {
	data.service = 'DELETE_BEAN';
	return axios.request({
		method: 'post',
		url: ULR_BASE,
		data: data
	});
};

//获取表单关联关系
export const postRelated = data => {
	return axios.request({
		method: 'post',
		url: ULR_BASE,
		data: data
	});
};

//查询污染源执法、处罚记录
export const queryPunishAndInspectRecord = (inspectionId, pollutionId) => {
	let params = {
		service: 'QUERY_WRY_ZFCFQK',
		xczfbh: inspectionId,
		wrybh: pollutionId
	};
	return request(params);
};

//查询污染源执法处罚情况
export const postPunishiAndEnforceMentInfo = data => {
	data.service = 'QUERY_WRY_BUSINESS';
	return axios.request({
		method: 'post',
		url: ULR_BASE,
		data: data
	});
};

/**
 * 检查企业是否为正面清单企业
 * @param {String} pollutionId 污染源ID
 */
export const checkPollutionPositive = pollutionId => {
	let params = {
		service: 'DYNAMIC_QUERY_PAGEDATA',
		xh: '1622706317644128282624',
		urlParams: {
			wrybh: pollutionId
		}
	};
	return new Promise((resolve, reject) => {
		request(params)
			.then(resp => {
				let positive = resp.data_json.list.length > 0;
				resolve(true);
			})
			.catch(error => {
				console.log(`出错了`);
				resolve(true);
			});
	});
};

// 记录污染源现场检查记录表数据
export const recordTableSave = data => {
	data.service = 'RECORD_TABLE_SAVE';
	return axios.request({
		method: 'post',
		url: ULR_BASE,
		data: data
	});
};

// 问题发现列表
export const XS_QUERY_LIST = data => {
	data.service = 'XS_QUERY_LIST';
	return axios.request({
		method: 'post',
		url: ULR_BASE,
		data: data
	});
};

// 问题发现保存
export const XS_CZJL_SAVE = data => {
	data.service = 'XS_CZJL_SAVE';
	return axios.request({
		method: 'post',
		url: ULR_BASE,
		data: data
	});
};

//问题发现重新检查
export const XS_RECHECK = data => {
	data.service = 'XS_RECHECK';
	return axios.request({
		method: 'post',
		url: ULR_BASE,
		data: data
	});
};





// 01企业历史检查记录
export const QUERY_HISTORY_CHECK_LIST = data => {
	data.service = 'QUERY_HISTORY_CHECK_LIST';
	return axios.request({
		method: 'post',
		// url: 'http://yapi.powerdata.com.cn:31665/mock/2218/QUERY_HISTORY_CHECK_LIST',
		url: ULR_BASE,
		data: data
	});
};

// 01保存继续检查原因
export const SAVE_CHECK_REASON = data => {
	data.service = 'SAVE_CHECK_REASON';
	return axios.request({
		method: 'post',
		// url: 'http://yapi.powerdata.com.cn:31665/mock/2218/SAVE_CHECK_REASON',
		url: ULR_BASE,
		data: data
	});
};

// 02任务待办预警NUM
export const QUERY_RWDB_NUM = data => {
	data.service = 'QUERY_RWDB_NUM';
	return axios.request({
		method: 'post',
		// url: 'http://yapi.powerdata.com.cn:31665/mock/2218/QUERY_RWDB_NUM',
		url: ULR_BASE,
		data: data
	});
};

// 03未上传视频附件
export const NOT_UPLOAD_VIDEO = data => {
	data.service = 'NOT_UPLOAD_VIDEO';
	return axios.request({
		method: 'post',
		url: ULR_BASE,
		// url: 'http://yapi.powerdata.com.cn:31665/mock/2218/NOT_UPLOAD_VIDEO',
		data: data
	});
};

// 04获取行政处罚立案提醒
export const GET_XZCF_LATX = data => {
	data.service = 'GET_XZCF_LATX';
	return axios.request({
		method: 'post',
		url: ULR_BASE,
		// url: 'http://yapi.powerdata.com.cn:31665/mock/2218/NOT_UPLOAD_VIDEO',
		data: data
	});
};

export async function getAuthToken() {
	try {
	  const response = await uni.request({
		url: SERVER_FULL_URL + "/server/auth/token?systemKey=be80999d-7c4b-cdb6-50df-7728d85e4ccd",
		method: 'GET'
	  });
	  uni.setStorageSync('authToken', response[1]?.data.result)
	  return response[1]?.data.result; // 根据实际数据结构调整
	} catch (error) {
	  console.error('获取Token失败:', error);
	  return null; // 或抛出错误 throw error;
	}
}

// 保存前调用进行规范化判断
export const verifyStandards = data => {
	data.service = 'VERIFY_STANDARDS';
	return axios.request({
		method: 'post',
		url: ULR_BASE,
		data: data
	});
};

// 保存规范化提醒结果
export const saveValidationResults = data => {
	data.service = 'SAVE_VALIDATIONRESULTS';
	return axios.request({
		method: 'post',
		url: ULR_BASE,
		data: data
	});
};

// 获取是否是初始密码
export const checkCsmm = data => {
	data.service = 'CHECK_CSMM';
	return axios.request({
		method: 'post',
		url: ULR_BASE,
		data: data
	});
};