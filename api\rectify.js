/*
 * @Author: your name
 * @Date: 2022-01-05 16:56:53
 * @LastEditTime: 2022-10-25 19:34:23
 * @LastEditors: 姚进玺 <EMAIL>
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: /UNI_APP_ShanDong/api/rectify.js
 */
import { deepCopyObject } from '@/common/merge.js';
import formUtil from '@/pages/form/Form.js';
import loginService from '@/api/login-service.js';
import { ATTACH_DATA_KEY } from '@/api/form-service.js';
import {
    ULR_BASE,
    LOGIN_ULR_BASE,
    API_TT_SERVICE_URL
} from '@/common/config.js';
//笔录的新增模式
export const MODE_ADD = '1';
//表单编辑页退出事件
export const EVENT_EDITOR_EXIT = 'onFormEditorExit';
//笔录的修改模式
export const MODE_MODIFY = '2';
import axios from '@/common/ajaxRequest.js';
//发起post请求
const request = params => {
    return axios.request({
        method: 'post',
        url: API_TT_SERVICE_URL,
        data: params,
        showLoading: false
    });
};

// 获取督办任务列表
export const getRectifyList = data => {
    data.service = 'QUERY_SUERVISE_LIST';
    return axios.request({
        method: 'post',
        url: API_TT_SERVICE_URL,
        data: data
    });
};

// 获取督办任务列表
export const getRectifyCount = data => {
    data.service = 'QUERY_SUERVISE_COUNT';
    return axios.request({
        method: 'post',
        url: API_TT_SERVICE_URL,
        data: data
    });
};

//查询任务指定所有表单模板
export const queryTaskTemplates = (taskId, templateId, wrybh) => {
    let params = {
        service: 'GET_DYNAMICFORM_MODEL',
        businessId: taskId,
        bbxh: templateId,
        userId: loginService.getAuthUserId()
    };
    if (wrybh) {
        params.WRYBH = wrybh;
    }
    return request(params);
};

export const dynamicformsave = data => {
    return axios.request({
        method: 'post',
        url: API_TT_SERVICE_URL,
        data: data,
        noShowError: true
    });
};

export const postGetBasicData = data => {
    data.service = 'QUERY_DYNAMICFORM_DATA';
    return axios.request({
        method: 'post',
        url: API_TT_SERVICE_URL,
        data: data
    });
};

/**
 * 查询任务指定表单模板
 * @param {String} 任务ID
 * @param {String} 表单模板ID
 * @return {Promise} 从resolve回调返回模板JSON对象
 */
export const queryTaskFormTemplate = (taskId, templateId) => {
    let userInfo = uni.getStorageSync('userInfo');
    let params = {
        service: 'GET_DYNAMICFORM_MODEL',
        businessId: taskId,
        bbxh: templateId,
        userId: userInfo.id
    };

    return new Promise((resolve, reject) => {
        axios
            .request({
                method: 'post',
                url: API_TT_SERVICE_URL,
                data: params
            })
            .then(resp => {
                let originalTemplate = resp.datas_json;
                let reorganizeTemplate =
                    formUtil.packPeerElementIntoGroup(originalTemplate);
                resolve(reorganizeTemplate);
            })
            .catch(error => {
                reject(error);
                uni.showModal({
                    title: '网络异常',
                    content: '获取表单出错，请稍后再试'
                });
            });
    });
};

/**
 * 根据表单模板ID和笔录ID获取笔录详情数据
 */
export const queryRecordData = (templateId, recordId) => {
    let params = {
        service: 'QUERY_DYNAMICFORM_DATA',
        mbbh: templateId,
        recordId: recordId
    };
    return new Promise((resolve, reject) => {
        axios
            .request({
                method: 'post',
                url: API_TT_SERVICE_URL,
                data: params,
                showLoading: false
            })
            .then(resp => {
                let record = resp.data;
                console.log(record, '123');
                if (record && Object.keys(record).length === 0) {
                    record = null;
                }
                console.log(record, '456');
                if (record !== null) {
                    record = deepCopyObject(record);
                }
                console.log(record, '789');
                let attachs = resp.data_fjxx;
                if (Array.isArray(attachs) && attachs.length > 0) {
                    let groupedAttachs = sortFormAattachs(
                        deepCopyObject(attachs)
                    );
                    if (record === null) {
                        record = {};
                    }
                    Object.assign(record, groupedAttachs);
                }
                console.log(record, '12345');
                resolve(record);
            })
            .catch(error => {
                // console.log(`获取表单数据出错：${error}`)
            });
    });
};

/**
 * 将表单的附件信息归类，以附件大类_小类为键进行分组
 * @param {Array} 表单未分组的附件信息
 */
const sortFormAattachs = attachs => {
    let groupedAttachs = {};
    if (Array.isArray(attachs)) {
        attachs.forEach(attach => {
            let majorCate = attach.LXDM || '';
            let minorCate = attach.ZLXDM || '';
            //预防返回数据没有分类，需要一个默认的分类
            let key = `${majorCate}_${minorCate}` || ATTACH_DATA_KEY;
            let group = [];
            if (groupedAttachs.hasOwnProperty(key)) {
                group = groupedAttachs[key];
            } else {
                groupedAttachs[key] = group;
            }
            uni.setStorageSync('form_data_key', key);
            group.push(attach);
        });
    }

    return groupedAttachs;
};

// ;
