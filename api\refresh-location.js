import http from '@/common/net/http.js';
import { LOGIN_ULR_BASE } from '@/common/config.js'

//刷新位置时间间隔，第10分钟刷新一次
const REFRESH_INTERVAL = 10 * 60 * 1000

//上一次定位任务是否执行完毕
let lastRequestFinish = true

const refreshLocation = () => {
	if(!lastRequestFinish) {
		return
	}
	console.log(`开始刷新位置信息`)
	lastRequestFinish = false
	uni.getLocation({
		type: 'wgs84',
		success(result) {
			submitRealtimeLocation(result.longitude, result.latitude)
		},
		fail(error) {
			console.log(`定位出错：${error}`)
		},
		complete() {
			lastRequestFinish = true
		}
	})
}

const submitRealtimeLocation = (longitude, latitude) => {
	let params = {
		service: 'REAL_TIME_LOCATION',
		version: 1,
		JD: longitude,
		WD: latitude
	}
	http.post(`${LOGIN_ULR_BASE}/invoke`, params)
		.then(() => {
			console.log(`上传位置成功: ${JSON.stringify(params, null, 4)}`)
		})
}

const startFixLocationInterval = () => {
	refreshLocation()
	return setInterval(() => {
		refreshLocation()
	}, REFRESH_INTERVAL)
}

export default {
	startFixLocationInterval
}