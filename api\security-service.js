import http from '@/common/net/http.js';
import CryptoJS from 'crypto-js';

/**
 * 加密因子
 */
const ENCRYPT_SECRET_KEY = 'boandaxxjsgfyxgs';

/**
 * 密码校验正则：请至少包含字母、数字、特殊字符中的至少3种且长度为8-16个字符
 */
const PASSWORD_CHECK_REGEX = /^(?=.{8,16}$)(?=.*[a-zA-Z])(?=.*\d)(?=.*[^a-zA-Z0-9])/;

/**
 * 使用AES算法进行加密
 * @param {String} content
 */
const encryptPasswordByAes = (password, secret) => {
	let key = CryptoJS.enc.Utf8.parse(secret || ENCRYPT_SECRET_KEY);
	let source = CryptoJS.enc.Utf8.parse(password);
	return CryptoJS.AES.encrypt(source, key, {
		mode: CryptoJS.mode.ECB,
		padding: CryptoJS.pad.Pkcs7
	}).toString();
}

const encryptPasswordByDes = (password, secret) => {
	let keyHex = CryptoJS.enc.Utf8.parse(secret);
	let encrypted = CryptoJS.DES.encrypt(password, keyHex, {
		mode: CryptoJS.mode.ECB,
		padding: CryptoJS.pad.Pkcs7
	});
	return encrypted.ciphertext.toString();
}

export default {
	
	encrypt(p){
		let ep = encryptPasswordByAes(p, 'SZBOANDA');
		console.log(`[${p}]加密密码：${ep}`)
		
		let ep2 = encryptPasswordByDes('SYSTEM###1', 'SZBOANDA');
		console.log(`DES加密：${ep2}`)
	},
	
	/**
	 * 校验密码格式
	 * @param {String} password
	 */
	checkPasswordFormat(password){
		return PASSWORD_CHECK_REGEX.test(password);
	},
	
	/**
	 * 检验旧密码
	 * @param {Object} userId
	 * @param {Object} oldPass
	 */
	checkOldPassword(userInfo){
		return new Promise((resolve, reject) => {
			let cipherPassword = encryptPasswordByAes(userInfo.oldPass);
			http.post(`${http.loginUrl}/api/appservercontroller/finduser`, {
				YHID: userInfo.userId,
				YHMM: cipherPassword
			})
			.then(resp => {
				resolve(userInfo);
			})
			.catch(error => {
				reject(error);
			})
		});
	},

	/**
	 * 修改密码
	 * @param {String} userId
	 * @param {String} password
	 */
	changePassword(userInfo){
		let cipherPassword = encryptPasswordByAes(userInfo.newPass);
		return http.post(`${http.loginUrl}/api/appservercontroller/updatecurrentuser`, {
			YHID: userInfo.userId,
			YHMM: cipherPassword
		});
	}
	
}