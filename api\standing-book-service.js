import http from '@/common/net/http.js';

/**
 * 获取系统中已配置的不同类型台账列表
 */
const getBookList = (pageIndex = 1) => {
	return http.post(`${http.loginUrl}/platform/tz/tzconfig/tzconfigcontroller/querytzconfigs`, {
		pageNum: pageIndex,
		pageSize: 10
	});
}

/**
 * 获取指定台账ID的台账目录树
 */
const getBookCatalogueTree = (bookId) => {
	// return http.post(`${http.loginUrl}/platform/component/tree/commontreecontroller/initTree`, {
	return http.post(`${http.loginUrl}/mobile/base/querytzmenutreeconfigs`, {
		SFYX: '1',
		isCount: 'false',
		XTXH: bookId,
		isFilterZero: 'true',
		// classpath: 'com.szboanda.platform.tz.tzconfig.service.impl.TzMenuConfigServiceImpl'
	});
}

/**
 * 获取台账目录节点配置
 */
const getBookCatalogueConfig = (catalogueId) => {
	return http.post(`${http.loginUrl}/platform/tz/tzconfig/tzmenuconfigcontroller/gettzmenuconfig`, {
		XH: catalogueId
	})
}

const getDynamicListConfig = (configId) => {
	return new Promise((resolve, reject) => {
		http.post(`${http.loginUrl}/mobile/base/configquery`, {
			XH: configId
		}).then(config => {
			//因服务平台历史原因，这个服务接口本该只返回一个JSON的，但是使用了分页，实际数据项只有一条
			let actualConfig = JSON.parse(config.list[0].PZLB);
			let detailConfig = config.list[0].KZPZ || {
				FORMID: '',
				CS: 'recordId=rid'
			}
			let resolveConfig = {
				columnsConfig: actualConfig,
				detailConfig
			};
			resolve(resolveConfig);
		}).catch(error => {
			reject(error);
			console.log(`动态列表配置查询出错：${error}`);
		})
	});
}

const getDynamicListData = (configId, pageIndex, params) => {
	let resolveParams = {
		pageNum: pageIndex,
		pageSize: 10,
		xh: configId
	}
	Object.assign(resolveParams, params);
	return http.post(`${http.loginUrl}/mobile/base/query`, resolveParams);
}

/**
 * 查询污染源业务分类
 */
const queryBusinessCategory = () => {
	return http.post(`${http.loginUrl}/mobile/server/controller/pollution`)
}

/**
 * 获取污染源业务数据分类
 */
const queryWryBusinessList = (categoryId, primaryId) => {
	let params = {
		service: 'DYNAMIC_QUERY_PAGEDATA',
		xh: categoryId,
		urlParams: {
			WRYBH: primaryId,
			recordId: primaryId
		},
		pageNum: 1,
		pageSize: 100,
		version: 1
	}
	return http.post(`${http.loginUrl}/invoke`, params)
}

export default {
	getBookList,
	getBookCatalogueTree,
	getBookCatalogueConfig,
	getDynamicListConfig,
	getDynamicListData,
	queryBusinessCategory,
	queryWryBusinessList,
}