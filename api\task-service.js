/*
 * @Author: your name
 * @Date: 2021-04-19 10:30:40
 * @LastEditTime: 2022-05-25 14:42:50
 * @LastEditors: 姚进玺 <EMAIL>
 * @Description: In User Settings Edit
 * @FilePath: /UNI_APP_ShanDong/api/task-service.js
 */
import axios from '@/common/ajaxRequest.js';
import loginService from '@/api/login-service.js';

import { ULR_BASE, LOGIN_ULR_BASE } from '@/common/config.js';

const request = params => {
    return axios.request({
        method: 'post',
        url: ULR_BASE,
        data: params
    });
};

export default {
    /**
     * 结束污染源监察任务
     * @param {Object} businessId
     * @param {Object} stepId
     */
    finishWryjcTask(businessId, stepId) {
        return this.finishTask('WRYJC_FINISH_SERVICE', businessId, stepId);
    },

    /**
     * 结束信访舆情任务
     */
    finishXfyqTask(businessId, stepId, ywxtbh, taskType) {
        return this.finishTask(
            'XFYQ_FINISH_SERVICE',
            businessId,
            stepId,
            ywxtbh,
            taskType
        );
    },

    /**
     * 结束流程任务
     * @param {Object} service
     * @param {Object} businessId
     * @param {Object} stepId
     */
    finishTask(service, businessId, stepId, ywxtbh, taskType) {
        let userId = loginService.getAuthUserId();
        let params = {
            service: service,
            bussinessId: businessId,
            bzbh: stepId,
            userId
        };
        if (service === 'XFYQ_FINISH_SERVICE') {
            params.YWXTBH = ywxtbh;
            params.XFYQRWBJ = taskType;
        }
        return request(params);
    },

    /**
     * 查询待办任务
     * @param {Object} keywords
     * @param {Object} pageIndex
     */
    queryTodoTasks(keywords, pageIndex, type, paramsData) {
        let params = {
            service: 'QUERY_WRYJCWITHXFYQ_DBRW',
            pageSize: 20,
            pageNum: pageIndex,
            JD: uni.getStorageSync('APP_location').longitude,
            WD: uni.getStorageSync('APP_location').latitude,
            params: {
                SFCQ: type
            }
        };
        if (keywords) {
            params.params.searchText = keywords;
        }
        if (paramsData) {
            Object.assign(params.params, paramsData);
        }
        return request(params);
    },

    /**
     * 查询任务业务主表模板ID
     * @param {Object} workflowId
     */
    queryTaskMainTemplate(workflowId) {
        let params = {
            service: 'GET_DynamicformID_BY_LCBH',
            LCBH: workflowId
        };
        return request(params);
    },

    queryTaskInspectObjects(taskId) {
        let params = {
            service: 'QUERY_HJYQZF_SERVICE',
            xfyqbh: taskId
        };
        return request(params);
    },

    //查询行政区划
    queryXzqhList(FDM, XZJB) {
        let params = {
            service: 'QUERY_GYFF_XZQH',
            FDM: FDM,
            XZJB: XZJB || ''
        };
        return request(params);
    }
};
