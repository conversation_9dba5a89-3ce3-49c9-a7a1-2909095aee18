import axios from '@/common/ajaxRequest.js'
import { ULR_BASE } from '@/common/config.js'

//发起post请求
const request = (params) => {
	return axios.request({
		method: 'post',
		url: ULR_BASE,
		data: params,
		showLoading: false
	})
}

export const queryWryJbxx = (creditId, wrymc) => {
	let params = {
		service: 'QUERY_WRY_JBXX',
		TYSHXYDM: creditId,
		WRYMC: wrymc
	}
	return request(params)
}

export const queryPollutionBaseInfo = (pollutionId) => {
	let params = {
		service: 'QUERY_WRY_JBXX',
		WRYBH: pollutionId
	}
	return request(params)
}

/**
 * 查询污染源排污许可证信息
 */
export const queryDischargeLicense = (wrybh) => {
	let params = {
		service: 'QUERY_WRY_PWXK',
		wrybh
	}
	return request(params)
}

export const queryPollutionHealthStatus = (pollutionId) => {
	let params = {
		service: 'QUERY_WRY_JKBDF',
		wrybh: pollutionId
	}
	return request(params)
}

export const queryPollutionTags = (pollutionId) => {
	return new Promise((resolve, reject) => {
		let params = {
			service: 'QUERY_WRY_TAG',
			WRYBH: pollutionId
		}
		request(params)
			.then(resp => {
				resolve(resp.data_json)
			})
			.catch(error => reject(error))
	})
}