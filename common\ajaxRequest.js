import axios from '@/js_sdk/gangdiedao-uni-axios'
import store from '../store';
import logService from '@/api/log-service.js'
// #ifdef APP-PLUS
import appService from '@/api/app-service.js'
// #endif


// 当第一次请求 显示loading  剩下的时候就不调用了
// 当都请求完毕后 隐藏loading
class AjaxRequest {
    // baseURL
    constructor() {
        // 请求的路径
        this.baseURL = '';
        this.timeout = 12 * 60 * 1000; // 超时时间
        this.queue = {}; // 存放每次的请求 TODO 重复一样的请求会有问题 by 黄冠豪
    }
    merge(options) {
        return { ...options, baseURL: this.baseURL, timeout: this.timeout };
    }
    setInterceptor(instance, options) {
        //每次请求时 都会加一个loading效果
        // 更改请求头
        instance.interceptors.request.use(config => {
            if(config.method=='post'){
                config.data.jwtToken = uni.getStorageSync('token')
                config.data.version = '1'

				// #ifdef APP-PLUS
				let wgtVersion = appService.getMPVersion()
				if(wgtVersion) {
					config.data.wgtVersion = {
						code: wgtVersion.versionCode,
						name: wgtVersion.version
					}
				}
				// #endif
            }

            config.headers.token = uni.getStorageSync('token');
            if (Object.keys(this.queue).length === 0 && options.showLoading) {
                store.commit('showLoading');
            }
            this.queue[options.url] = options.url;
            return config;
        });
        // 如果上一个promise 返回了一个常量 会作为下一个promise的输入
        instance.interceptors.response.use(
            res => {
                delete this.queue[options.url]; // 每次请求成功后 都删除队列里的路径
                if (Object.keys(this.queue).length === 0) {
                    store.commit('hideLoading');
                }
                // 状态码拦截器判断，如果是000的话，就返回
                if (res.data.status_code == '0' || res.data.success  || res.data.result === 'success' || res.data.code == '000' || res.data.hasOwnProperty('datas') || res.data.type == 'FeatureCollection') {
                    return res.data;
                } else {
					if(!options.noShowError){
						let errorMsg = res.data.msg || res.data.errMsg || res.data.error_msg || '请求异常'
						try {
							let service = options.data.service
							logService.submitExceptionLog(service, options, res)
								.then(submitResp => {
									console.log(`提交异常成功`)
								})
						} catch(error) {
							console.log(`提交异常信息出错：${JSON.stringify(error, null, 4)}`)
						}

						store.commit('errorMsg', {
						    msg: errorMsg,
						    res
						});
					}
                    return Promise.reject(res.data);
                }
            },
            (error) => {
                delete this.queue[options.url]; // 每次请求成功后 都删除队列里的路径
                if (Object.keys(this.queue).length === 0) {
                    store.commit('hideLoading');
                }

				try {
					let service = options.data.service
					logService.submitExceptionLog(service, options, error)
						.then(submitResp => {
							console.log(`提交异常成功`)
						})
				} catch(error) {
					console.log(`提交异常信息出错：${JSON.stringify(error, null, 4)}`)
				}
				if(!options.noShowError) {
					let errorMsg = error.message
					if(errorMsg.startsWith('网络错误')) {
						errorMsg = '网络错误'
					}
					store.commit('errorMsg', {
						msg: errorMsg || '请求异常'
					});
				}
				return Promise.reject(error)
            }
        );
    }

    request(options) {
        options = Object.assign(
            {
                showLoading: true
            },
            options
        );
        // url,method
        let instance = axios.create(); // 通过axios库创建一个axios实例
        this.setInterceptor(instance, options);
        let config = this.merge(options);
        return instance(config); // axios执行后返回的是一个promise
    }
}
export default new AjaxRequest();
