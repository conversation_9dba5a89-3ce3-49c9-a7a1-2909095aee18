/*
 * @Author: your name
 * @Date: 2021-05-13 09:24:16
 * @LastEditTime: 2022-10-27 18:27:11
 * @LastEditors: 姚进玺 <EMAIL>
 * @Description: In User Settings Edit
 * @FilePath: /UNI_APP_ShanDong/common/config.js
 */

//默认的服务地址、服务上下文路径
const SERVER_BASE_URL = 'http://***************:9001';
// const SERVER_BASE_URL = 'http://*************:8000';
const SERVER_CONTEXT_PATH = '/zhzf';
// const SERVER_CONTEXT_PATH = '/zfpt';
let baseUrl = SERVER_BASE_URL;
let contextPath = SERVER_CONTEXT_PATH;
let apiServerUrl = `${SERVER_BASE_URL}${SERVER_CONTEXT_PATH}`;

//用户通过登录页双击logo自定义的服务地址
let preferApiServerUrl = uni.getStorageSync('PERFER_API_SERVER_URL');
if (preferApiServerUrl) {
	apiServerUrl = preferApiServerUrl;
	let lastSlashIndex = preferApiServerUrl.lastIndexOf('/');
	if (lastSlashIndex !== -1) {
		baseUrl = preferApiServerUrl.substring(0, lastSlashIndex);
		contextPath = preferApiServerUrl.substring(lastSlashIndex + 1);
	}
}
//是否是h5，对接山东通，打包用
export const isH5 = false;

const API_SERVICE_URL = `${apiServerUrl}`;
const API_YANTAI_SERVICE_URL = 'http://*************:9001/zfpt';
//*************:9001/zfpt/index
//*************:10997/zfpt/index

export const DOWNLOAD_URL = `${API_SERVICE_URL}/server/webapp/downloadFile`;
export const YANTAI_DOWNLOAD_URL = `${API_YANTAI_SERVICE_URL}`;

export const WMZF = `http://***************:9001/zhzf/resources/wmzftcwj/index.html`;

// #ifdef APP-PLUS
export const ULR_BASE = `${API_SERVICE_URL}/invoke`;
export const LOGIN_ULR_BASE = API_SERVICE_URL;
export const API_TT_SERVICE_URL = `${API_YANTAI_SERVICE_URL}/invoke`;

export const IP_URL = SERVER_BASE_URL;
// #endif

// #ifdef H5
export const ULR_BASE = `${contextPath}/invoke`;
export const LOGIN_ULR_BASE = contextPath;
export const API_TT_SERVICE_URL = `${API_YANTAI_SERVICE_URL}/invoke`;

export const IP_URL = SERVER_BASE_URL;
// #endif

export const SERVER_FULL_URL = `${SERVER_BASE_URL}${contextPath}`;