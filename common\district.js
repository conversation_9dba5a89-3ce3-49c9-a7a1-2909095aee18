import districtSource from '@/common/city.data.js';

/**
 * 获取全国省份及直辖市
 */
export const getProvinces = () => {
	return districtSource.map(district => {
		return {
			code: district.value,
			name: district.label
		}
	});
}

/**
 * 获取全国省份及直辖市
 */
export const getProvince = (provinceCode) => {
	let matchProvinces = districtSource.filter(province => {
		return province.value === provinceCode;
	});
	
	let province = matchProvinces.length > 0 ? matchProvinces[0] : null;
	if(province){
		return {
			code: province.value,
			name: province.label
		}
	}
	return null;
}

/**
 * 获取省份下所有市
 * @param {String} provinceCode 省份行政区划
 */
export const getProvinceCities = (provinceCode) => {
	let matchProvinces = districtSource.filter(province => {
		return province.value === provinceCode;
	});
	
	let province = matchProvinces.length > 0 ? matchProvinces[0] : null;
	if(province){
		return province.children.map(city => {
			return {
				code: city.value,
				name: city.label
			}
		});
	}
	return [];
}