export const open = (path) => {
	if(typeof path === 'undefined' || path === null || path === '') {
		console.log('文件路径不可用')
		return
	}
	let File = plus.android.importClass('java.io.File')
	let Intent = plus.android.importClass('android.content.Intent')
	
	let wordFile = new File(path)
	if(wordFile.exists()){
		let openIntent = new Intent(Intent.ACTION_VIEW)
		openIntent.setPackage('cn.wps.moffice_eng')
		let Uri = plus.android.importClass("android.net.Uri")
		let FileProvider = plus.android.importClass('io.dcloud.common.util.DCloud_FileProvider')
		
		let mainActivity = plus.android.runtimeMainActivity()
		let packName = mainActivity.getPackageName()
		let fileUri = FileProvider.getUriForFile(mainActivity, `${packName}.dc.fileprovider`, wordFile)
		openIntent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
		openIntent.setDataAndType(fileUri, 'application/msword')
		mainActivity.startActivity(openIntent)
	} else {
		console.log(`${path} ### 文件不存在`)
	}
}