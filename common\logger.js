/**
 * 日志打印工具，输出格式化对象信息方便查看
 */

import Vue from 'vue';

const printLogMessage = (printer, tag, message) => {
	let stringifyTag = tag || ''
	let stringifyMsg = message || ''
	
	if(typeof stringifyTag === 'object') {
		stringifyTag = JSON.stringify(stringifyTag, null, 4)
	}
	
	if(typeof stringifyMsg === 'object') {
		stringifyMsg = JSON.stringify(stringifyMsg, null, 4)
	}
	
	let readableMsg = `${stringifyTag}：${stringifyMsg}`;
	printer(readableMsg);
}

Vue.prototype.log = (tag, message) => {
	printLogMessage(console.log, tag, message);
}

Vue.prototype.error = (tag, message) => {
	printLogMessage(console.error, tag, message);
}
