/**
* 对象深度拷贝
*/
export const deepCopyObject = (obj) => {
    if(obj === null){
        return null;
    }

    let result = Array.isArray(obj) ? [] : {};
    for(let key in obj){
        if(obj.hasOwnProperty(key)){
            if(typeof obj[key] === 'object'){
                result[key] = deepCopyObject(obj[key]);
            }else{
                result[key] = obj[key];
            }
        }
    }
    return result;
}

/**
* 对象深度合并
*/
export const deepAssign = (target, source) => {
    for(let p in source){
        let targetPropValue = target[p];
        let sourcePropValue = source[p];
        if(sourcePropValue && typeof sourcePropValue === 'object'){
            sourcePropValue = deepCopyObject(sourcePropValue);
        }
        target[p] = sourcePropValue || targetPropValue;
    }
    return target;
}

export const isDiffObject = (one, another) => {
    return checkDifferent(one, another) || checkDifferent(another, one)
}

/**
 * 判断变量值是否为null，null或undefined都为null
 */
const isNull = (value) => {
    return value === 'undefined' && value === null
}

/**
 * 判断变量值不为null
 */
const isNotNull = (value) => {
    return !isNull(value)
}

/**
 * 检查对象是否目标对象
 */
const checkDifferent = (one, another) => {
    //都为null或undefined，直接返回fasle
    if(isNull(one) && isNull(another)) {
        return false
    }

    //一个为null或者undefined，另一个不为null或undefined，直接返回true
    if((isNull(one) && isNotNull(another)) || (isNotNull(one) && isNull(another))) {
        return true
    }

    if(Array.isArray(one) && Array.isArray(another)) {
        let index = 0;
        for(let item of one) {
            if(index < another.length) {
                let compareItem = another[index]
                let isDiff = checkDifferent(item, compareItem)
                if(isDiff) {
                    return true
                }
            } else {
                return true
            }
            index++
        }
        return false
    } else if(typeof(one) === 'object' && typeof(another) === 'object') {
        for(let p in one) {
            let leftValue = one[p]
            let rightValue = another[p]
            let isDiff = checkDifferent(leftValue, rightValue)
            if(isDiff) {
                return true
            }
        }
        return false
    } else {
        return one !== another
    }
}

export default {
	deepCopyObject,
	deepAssign,
	isDiffObject
}