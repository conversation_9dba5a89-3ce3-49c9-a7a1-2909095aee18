import http from './http.js';

import {LOGIN_CACHE_KEYS} from '@/api/login-service.js';

http.interceptors.request.push((request, token) => {
	let params = request.params;
	let header = request.header || {};
	
	if(params){
		params.sim = params.sim || '17777392862';
		params.imei = params.imei || '111';
		params.os = params.os || 'Android';
	};
	
	let resolveToken = token || uni.getStorageSync(LOGIN_CACHE_KEYS.token);
	if(resolveToken){
		params.jwtToken = resolveToken;
		
		header.Authorization = `Bearer ${resolveToken}`;
	}
	let userId = uni.getStorageSync(LOGIN_CACHE_KEYS.userId);
	if(userId){
		params.userid = userId;
	}
	let password = uni.getStorageSync(LOGIN_CACHE_KEYS.password);
	if(password){
		params.pwd = password;
	}
});

console.log(`注入完成`)