import http from '@/common/net/http.js'
import loginService from '@/api/login-service.js'

const resolveAnroidId = () => {
	let Settings = plus.android.importClass('android.provider.Settings')
	let contentResolver = plus.android.runtimeMainActivity().getContentResolver()
	return Settings.Secure.getString(contentResolver, Settings.Secure.ANDROID_ID)
}

const resolvePushClinetId = () => {
	return new Promise((resolve, reject) => {
		plus.push.getClientInfoAsync((clientInfo) => {
			resolve(clientInfo.clientid)
		}, (error) => {
			uni.showModal({
				title: '推送',
				content: `${error.code}: ${error.message}`
			})
		})
	})
}

/**
 * 向服务器注册推送客户端ID，以便定向推送消息
 */
const registerPushClinetId = (clientId) => {
	let params = {
		service: 'REGISTER_PUSH_USER',
		cid: clientId,
		imei: resolveAnroidId(),
		version: 1
	}
	return new Promise((resolve, reject) => {
		http.post(http.url, params)
			.then(resp => {
				resolve()
			})
			.catch(error => reject(error))
	})
}

const registerClickMessageListener = () => {
	plus.push.addEventListener('click', (msg) => {
		if(msg.action) {
			
		}
	})
}

const registerReceiveMessageListener = () => {
	plus.push.addEventListener('receive', (msg) => {
		if(msg.action === 'command') {
			
		}
	})
}

resolvePushClinetId()
	.then(registerPushClinetId)
	.then(() => {
		registerClickMessageListener()
		registerReceiveMessageListener()
	})