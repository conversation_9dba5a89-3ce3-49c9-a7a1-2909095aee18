//默认缓存过期时间
const CACHE_EXPIRED_DEFAULT = 60 * 60 * 1000


/**
 * 缓存数据并存储打过期时间标签
 * @param {Object} cacheKey
 * @param {Object} cacheData
 */
export const setStorageWithExpired = (cacheKey, cacheData) => {
	let saveTime = new Date().getTime()
	uni.setStorage({
		key: cacheKey,
		data: {
			time: saveTime,
			cache: cacheData
		}
	})
}

/**
 * 获取在有效期内的缓存数据
 * @param {Object} cacheKey
 */
export const getEffectiveStorage = (cacheKey, expired) => {
	let data = uni.getStorageSync(cacheKey)
	if(data && !isCacheExpired(data, expired)) {
		return data.cache
	}
	return null
}

//判断缓存数据是否已过期
const isCacheExpired = (data, expired) => {
	let now = new Date().getTime()
	let diffTime = now - data.time
	let effectiveExpired = expired || CACHE_EXPIRED_DEFAULT
	return diffTime - effectiveExpired >= 0
}

export default {
	setStorageWithExpired,
	getEffectiveStorage
}

