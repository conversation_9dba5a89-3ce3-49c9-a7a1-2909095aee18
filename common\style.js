/**
 * 获取父节点
 */
export const getParentDom = (dom) => {
	return dom.parentNode || dom.parentElement;
}

const parsePadding = (style, paddingAttr) => {
	let padding = 0;
	if(style[paddingAttr]){
		let paddingStr = style[paddingAttr].replace('px', '');
		padding = parseInt(paddingStr);
	}
	return padding;
}

/**
 * 获取父节点可用高度（不包含内边距）
 */
export const getParentAvailableHeight = (dom) => {
	let availableHeight = dom.clientHeight;
	if(window){
		let style = dom.currentStyle || getComputedStyle(dom, null);
		if(style){
			let paddingTop = parsePadding(style, 'paddingTop');
			let paddingBottom = parsePadding(style, 'paddingBottom');
			availableHeight = availableHeight - paddingTop - paddingBottom;
		}
	}
	return availableHeight;
}

/**
 * 适用于App、小程序的获取屏幕尺寸的方法
 */
export const getScreenLayout = () => {
	let screenSelector = uni.createSelectorQuery();
	let screen = screenSelector.selectViewport();
	return new Promise((resolve, reject) => {
		screen.boundingClientRect(data => {
			resolve(data);
		}).exec();
	});
}

/**
 * 适用于App、小程序的获取组件尺寸的方法
 */
export const getNodeLayout = (instance, selector) => {
	// #ifdef MP-ALIPAY
	let nodeSelector = uni.createSelectorQuery();
	// #endif
	
	// #ifndef MP-ALIPAY
	let nodeSelector = uni.createSelectorQuery().in(instance);
	// #endif
	let selectedNode = nodeSelector.select(selector);
	return new Promise((resolve, reject) => {
		selectedNode.boundingClientRect(data => {
			resolve(data);
		}).exec();
	});
}

/**
 * 兼容微信或钉钉小程序获取Dom节点尺寸的方法，获取指定id的dom节点尺寸时，
 * 微信可以直接获取到自定义组件的尺寸，而钉钉不行，钉钉需要获取自定义组件内部
 * 的uni组件view的，这样微信和钉钉平台的ID就可能不一致，通过以下的Promise包装
 * 兼容以上两种情况，避免代码里老是写两套条件编译代码的方式
 * @param {Vue} Vue实例
 * @param {Array} id集合  
 */
export const getEitherNodeLayout = (instance, selectors) => {
	let eitherLayout = [];
	selectors.forEach(selector => {
		eitherLayout.push(getNodeLayout(instance, selector));
	})
	return new Promise((resolve, reject) => {
		Promise.all(eitherLayout)
			.then(layouts => {
				let sureLayout = layouts[0] || layouts[1];
				if(sureLayout) {
					resolve(sureLayout);
				} else {
					let msg = `未获取到DOM[${JSON.stringify(selectors, null, 4)}]的尺寸信息`;
					reject(msg)
				}
			})
	});
}

/**
 * 使用对象表示的样式转换成字符串，用于兼容小程序样式不能直接绑定对象的兼容性问题
 */
export const styleObjectToString = (style) => {
	let styleInStr = '';
	for(let attr in style){
		styleInStr += `; ${attr}: ${style[attr]}`;
	}
	if(styleInStr.startsWith(';')){
		styleInStr = styleInStr.substring(2);
	}
	return `${styleInStr};`;
}

/**
 * 适配样式对象，使兼容不同平台
 */
export const wrapStyleObject = (style) => {
	return styleObjectToString(style)
}

export default {
	getParentDom,
	getParentAvailableHeight,
	getScreenLayout,
	getNodeLayout,
	getEitherNodeLayout,
	styleObjectToString,
	wrapStyleObject
}