/**
 * /*
 *
 * @format
 * @Author: Caijw
 * @LastEditors: Caijw
 * @Description: 模板工具类
 * @Date: 2021-03-02 20:17:00
 * @LastEditTime: 2021-03-02 20:17:00
 */
import { queryRecordData, submitRecord } from '@/api/record.js';

class TemplateUtils {
	
	constructor() {
	    
	}
	
	/**
	 * // 模板id 记录id formData 数据
	 */
	loadAttachRecord(templateId, recordId, formData, VueObj) {
		this.templateId = templateId;
		this.recordId = recordId;
		this.formData = formData;
		
		let _self = this
		let serverRecord = queryRecordData(this.templateId, recordId)
		Promise.all([serverRecord, _self.loadCacheFromLocal()])
			.then(eitherRecords => {
				
				// debugger
				let submittedRecord = eitherRecords[0]
				//服务端未查询到数据的情况下，总是提交一次
				if(submittedRecord === null && _self.data) {
					submitRecord(_self.data, _self.template, _self.recordId, {})
				}
				let record = submittedRecord || eitherRecords[1]
				if(record) {
					VueObj.$nextTick(function(){
						VueObj.formData = record
					})
				}
			})
	}
	
	/**
	 * 从本地加载附件表单笔录缓存
	 */
	loadCacheFromLocal() {
		return new Promise((resolve) => {
			let keyOfCache = `form-Data:${this.recordId}`
			let cacheRecord = uni.getStorageSync(keyOfCache) || null
			resolve(cacheRecord)
		})
	}
}
export default new TemplateUtils();