import dayjs from 'dayjs';

export default {
    //针对不同时间类型的情况选择
    timePicker(val){
        let timeInfo = {}
        if(val === 'week'){
            timeInfo.kssj = dayjs(new Date()).subtract(6, 'day').format('YYYY-MM-DD');
            timeInfo.jssj = dayjs(new Date()).format('YYYY-MM-DD');
        }
        else if(val === 'month'){
            timeInfo.kssj = dayjs().startOf('month').format('YYYY-MM-DD');
            timeInfo.jssj = dayjs(new Date()).format('YYYY-MM-DD');
        }
        else if(val === 'season'){
            timeInfo.kssj = dayjs(new Date()).subtract(2, 'month').format('YYYY-MM-DD');
            timeInfo.jssj = dayjs(new Date()).format('YYYY-MM-DD');
        }else if(val === 'year'){
            timeInfo.kssj = dayjs().startOf('year').format('YYYY-MM-DD');
            timeInfo.jssj = dayjs(new Date()).format('YYYY-MM-DD');
        }
        else if(val === 'all'){
            timeInfo.kssj = ''
            timeInfo.jssj = ''
        }
        return timeInfo
    }
}