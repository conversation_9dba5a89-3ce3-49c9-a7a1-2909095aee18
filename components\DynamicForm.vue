<!--
 * @Author: 姚进玺 <EMAIL>
 * @Date: 2022-09-24 11:31:31
 * @LastEditors: 姚进玺 <EMAIL>
 * @LastEditTime: 2022-10-11 10:01:07
 * @FilePath: /YDZF_APP/components/DynamicForm.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
    <template-form
        ref="TemplateForm"
        :editable="editable"
        :recordId="recordId"
        :template-id="templateId"
        @templateLoaded="onTemplateLoaded"
        @onFormMounted="onFormMounted"
        @onFormDataUpdate="onFormDataUpdate"
        :form-data="formData"
        :height="formHeight"
    />
</template>

<script>
import TemplateForm from 'bowo-form';
import { guid } from 'bowo-sdk/util/uuid.js';
import formService from '@/node_modules/bowo-form/components/service/form-service.js';
export default {
    components: {
        TemplateForm
    },

    props: {
        exteriorRecordId: {
            type: String,
            default: ''
        },
        editable: {
            type: Boolean,
            default: false
        },
        templateId: {
            type: String,
            default: ''
        },
        formHeight: {
            type: [String, Number],
            default: 600
        },
        attachFormData: {
            type: Object
        },
        changeFormStatus: {
            type: Boolean,
            default: false
        },

        refName: {
            type: String,
            default: 'TemplateForm'
        }
    },
    data() {
        return {
            template: null,
            recordId: '',
            formData: {}
        };
    },
    computed: {},
    watch: {
        changeFormStatus() {
            let form = this.getFormRef().getFormData();
            this.formData = Object.assign(form, this.attachFormData);
        }
    },
    methods: {
        getFormRef() {
            return this.$refs.TemplateForm;
        },

        onTemplateLoaded(template) {
            this.template = template;
            this.$emit('returnTemplate', this.template);
            if (this.exteriorRecordId) {
                this.recordId = this.exteriorRecordId;
                this.getFormServiceData();
            } else {
                this.getRecordId();
            }
        },

        getFormServiceData() {
            let self = this;
            formService
                .getFormRecordDetail(this.template, this.recordId)
                .then(detail => {
                    self.formData = detail;
                    let form = detail;
                    // console.log(this.formData, '123');
                    self.$emit('returnFormDataInfo', form);
                });
        },

        onFormDataUpdate(data) {
            this.$emit('onFormDataUpdate', data);
        },

        getFormData() {
            let formData = this.getFormRef().getFormData();
            this.$emit('getFormDataInfo', formData);
        },

        onFormMounted() {
            // console.log(this.getFormRef().getFormData());
        },

        checkRequireForm() {
            let formData = this.getFormRef().getFormData();
            console.log(formData, 'formData');
            formData.XH = this.recordId;
            //获取表单的格式校验的内容
            let requiredFields = this.getFormRef().checkRequireFields();
            if (requiredFields.length > 0) {
                uni.showToast({
                    title: '请检查必填项或格式信息',
                    icon: 'none',
                    duration: 2000
                });
                this.getFormRef().scrollToElement(requiredFields[0]);
                this.$emit('returnCheckRequireForm', false);
                return;
            }
            this.$emit('returnCheckRequireForm', true);
            return;
        },

        submitFormData() {
            let formData = this.getFormRef().getFormData();
            formData.XH = this.recordId;
            uni.showLoading({
                title: '正在提交数据'
            });
            //获取表单校验的数据

            formService
                .submitFormData(this.template, this.recordId, formData)
                .then(resp => {
                    this.$emit('formSaveSuccuss');
                })
                .catch(error => {
                    this.$emit('formSaveFail');
                })
                .finally(() => {
                    uni.hideLoading();
                });
        },

        getRecordId() {
            this.recordId = guid();
            this.$emit('getCraetRcordId', this.recordId);
        }
    }
};
</script>

<style scoped></style>
