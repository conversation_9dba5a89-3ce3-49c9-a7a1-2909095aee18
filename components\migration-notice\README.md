# 内网迁移通知组件

## 组件说明
这是一个用于显示内网迁移通知的弹窗组件，包含通知内容和附件查看功能。

## 文件结构
```
components/migration-notice/
├── migration-notice.vue          # 完整功能版本
├── simple-migration-notice.vue   # 简化版本（推荐使用）
├── example.vue                   # 使用示例
├── register-component.js         # 全局注册脚本
└── README.md                     # 说明文档
```

## 快速开始

### 方式一：直接使用（推荐）

#### 1. 在页面中引入简化版组件
```javascript
import SimpleMigrationNotice from '@/components/migration-notice/simple-migration-notice.vue'

export default {
  components: {
    SimpleMigrationNotice
  },
  data() {
    return {
      showMigrationNotice: true
    }
  },
  methods: {
    handleNoticeConfirm() {
      // 用户确认通知后的处理
      console.log('用户已确认迁移通知');
      // 可以记录到本地存储或调用API
      uni.setStorageSync('migration_notice_confirmed', true);
    }
  }
}
```

#### 2. 在模板中使用
```html
<simple-migration-notice
  :show.sync="showMigrationNotice"
  @confirm="handleNoticeConfirm"
></simple-migration-notice>
```

### 方式二：全局注册

#### 1. 在 main.js 中注册组件
```javascript
// 在 main.js 中添加以下代码
import SimpleMigrationNotice from '@/components/migration-notice/simple-migration-notice.vue';
Vue.component('SimpleMigrationNotice', SimpleMigrationNotice);
```

#### 2. 在任意页面中直接使用
```html
<simple-migration-notice
  :show.sync="showNotice"
  @confirm="handleConfirm"
></simple-migration-notice>
```

## 组件属性

### simple-migration-notice.vue

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| show | Boolean | false | 是否显示通知弹窗，支持.sync修饰符 |
| image-url | String | '/static/images/migration-guide.png' | 附件图片路径 |

### migration-notice.vue（完整版）

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| show | Boolean | false | 是否显示通知弹窗 |
| attachment-image | String | '/static/images/migration-guide.png' | 附件图片路径 |
| mask-closable | Boolean | false | 是否允许点击遮罩关闭 |

## 组件事件

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| confirm | 用户点击"我知道了"按钮时触发 | - |
| close | 用户关闭通知时触发 | - |

## 完整使用示例

```vue
<template>
  <view class="page">
    <!-- 页面内容 -->
    <view class="page-content">
      <text>这里是页面内容</text>
      <button @click="showNotice">显示迁移通知</button>
    </view>

    <!-- 迁移通知组件 -->
    <simple-migration-notice
      :show.sync="migrationNoticeVisible"
      image-url="/static/images/migration-guide.png"
      @confirm="onNoticeConfirmed"
      @close="onNoticeClosed"
    ></simple-migration-notice>
  </view>
</template>

<script>
import SimpleMigrationNotice from '@/components/migration-notice/simple-migration-notice.vue'

export default {
  components: {
    SimpleMigrationNotice
  },
  data() {
    return {
      migrationNoticeVisible: false
    }
  },
  onLoad() {
    // 检查用户是否已经确认过通知
    const hasConfirmed = uni.getStorageSync('migration_notice_confirmed');
    if (!hasConfirmed) {
      // 延迟显示通知，给页面加载一些时间
      setTimeout(() => {
        this.migrationNoticeVisible = true;
      }, 1000);
    }
  },
  methods: {
    showNotice() {
      this.migrationNoticeVisible = true;
    },

    onNoticeConfirmed() {
      console.log('用户确认了迁移通知');
      // 记录用户已确认状态
      uni.setStorageSync('migration_notice_confirmed', true);
      uni.setStorageSync('migration_notice_confirm_time', Date.now());

      uni.showToast({
        title: '已确认通知',
        icon: 'success'
      });
    },

    onNoticeClosed() {
      console.log('用户关闭了迁移通知');
    }
  }
}
</script>
```

## 附件图片设置

### 1. 准备图片文件
- 将操作说明图片命名为 `migration-guide.png`
- 放置在 `/static/images/` 目录下
- 图片建议尺寸：宽度800-1200px，保证清晰度

### 2. 图片路径说明
- 默认路径：`/static/images/migration-guide.png`
- 可通过 `image-url` 属性自定义路径
- 支持网络图片URL（需要配置域名白名单）

## 样式特点

1. **现代化设计**：圆角卡片、渐变按钮、阴影效果
2. **响应式布局**：适配不同屏幕尺寸
3. **用户友好**：清晰的视觉层次，易于操作
4. **品牌一致性**：使用项目主色调 #009BFF

## 注意事项

1. 组件默认不允许点击遮罩关闭，确保用户看到重要通知
2. 建议在应用启动时检查用户是否已确认通知
3. 可以结合本地存储记录用户确认状态，避免重复显示
4. 图片加载失败时会显示错误提示
5. 组件使用了 `position: fixed`，确保在所有内容之上显示
