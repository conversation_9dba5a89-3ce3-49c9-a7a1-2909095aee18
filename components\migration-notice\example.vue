<template>
	<view class="example-page">
		<view class="header">
			<text class="title">内网迁移通知组件示例</text>
		</view>
		
		<view class="content">
			<view class="demo-section">
				<text class="section-title">基础用法</text>
				<button class="demo-btn" @click="showBasicNotice">显示基础通知</button>
			</view>
			
			<view class="demo-section">
				<text class="section-title">允许遮罩关闭</text>
				<button class="demo-btn" @click="showMaskClosableNotice">显示可遮罩关闭通知</button>
			</view>
			
			<view class="demo-section">
				<text class="section-title">自定义附件图片</text>
				<button class="demo-btn" @click="showCustomImageNotice">显示自定义图片通知</button>
			</view>
		</view>
		
		<!-- 内网迁移通知组件 -->
		<migration-notice 
			:show.sync="noticeVisible" 
			:attachment-image="currentAttachmentImage"
			:mask-closable="currentMaskClosable"
			@confirm="handleNoticeConfirm"
			@close="handleNoticeClose"
		></migration-notice>
	</view>
</template>

<script>
import MigrationNotice from './migration-notice.vue'

export default {
	name: 'migration-notice-example',
	components: {
		MigrationNotice
	},
	data() {
		return {
			noticeVisible: false,
			currentAttachmentImage: '/static/images/migration-guide.png',
			currentMaskClosable: false
		}
	},
	methods: {
		// 显示基础通知
		showBasicNotice() {
			this.currentAttachmentImage = '/static/images/migration-guide.png';
			this.currentMaskClosable = false;
			this.noticeVisible = true;
		},
		
		// 显示可遮罩关闭的通知
		showMaskClosableNotice() {
			this.currentAttachmentImage = '/static/images/migration-guide.png';
			this.currentMaskClosable = true;
			this.noticeVisible = true;
		},
		
		// 显示自定义图片通知
		showCustomImageNotice() {
			this.currentAttachmentImage = '/static/images/wmzf.png'; // 使用现有图片作为示例
			this.currentMaskClosable = false;
			this.noticeVisible = true;
		},
		
		// 处理通知确认
		handleNoticeConfirm() {
			console.log('用户确认了内网迁移通知');
			uni.showToast({
				title: '已确认通知',
				icon: 'success'
			});
			
			// 这里可以调用API记录用户已查看通知的状态
			// this.recordNoticeViewed();
		},
		
		// 处理通知关闭
		handleNoticeClose() {
			console.log('用户关闭了内网迁移通知');
		},
		
		// 记录通知已查看状态（示例方法）
		recordNoticeViewed() {
			// 可以调用后端API记录用户已查看状态
			// 或者存储到本地缓存
			uni.setStorageSync('migration_notice_viewed', true);
		}
	},
	
	onLoad() {
		// 页面加载时可以检查是否需要显示通知
		// const hasViewed = uni.getStorageSync('migration_notice_viewed');
		// if (!hasViewed) {
		//   this.noticeVisible = true;
		// }
	}
}
</script>

<style lang="scss" scoped>
.example-page {
	padding: 40rpx;
	
	.header {
		text-align: center;
		margin-bottom: 60rpx;
		
		.title {
			font-size: 36rpx;
			font-weight: bold;
			color: #333333;
		}
	}
	
	.content {
		.demo-section {
			margin-bottom: 40rpx;
			
			.section-title {
				display: block;
				font-size: 28rpx;
				color: #666666;
				margin-bottom: 20rpx;
			}
			
			.demo-btn {
				width: 100%;
				height: 80rpx;
				background: #009BFF;
				color: #ffffff;
				border: none;
				border-radius: 12rpx;
				font-size: 30rpx;
				display: flex;
				align-items: center;
				justify-content: center;
			}
			
			.demo-btn::after {
				border: none;
			}
		}
	}
}
</style>
