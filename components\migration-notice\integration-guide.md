# 内网迁移通知组件集成指南

## 快速集成步骤

### 第一步：准备附件图片
1. 将内网迁移操作说明图片重命名为 `migration-guide.png`
2. 将图片放置到 `/static/images/` 目录下
3. 删除占位文件 `/static/images/migration-guide-placeholder.txt`

### 第二步：选择集成方式

#### 方式A：页面级使用（推荐）
适合在特定页面显示通知的场景。

```vue
<template>
  <view class="page">
    <!-- 你的页面内容 -->
    <view class="content">
      <!-- 页面内容 -->
    </view>
    
    <!-- 迁移通知 -->
    <simple-migration-notice 
      :show.sync="showMigrationNotice"
      @confirm="handleMigrationConfirm"
    ></simple-migration-notice>
  </view>
</template>

<script>
import SimpleMigrationNotice from '@/components/migration-notice/simple-migration-notice.vue'

export default {
  components: {
    SimpleMigrationNotice
  },
  data() {
    return {
      showMigrationNotice: false
    }
  },
  onLoad() {
    // 检查是否需要显示迁移通知
    this.checkMigrationNotice();
  },
  methods: {
    checkMigrationNotice() {
      const hasConfirmed = uni.getStorageSync('migration_notice_confirmed');
      if (!hasConfirmed) {
        // 延迟显示，给页面加载时间
        setTimeout(() => {
          this.showMigrationNotice = true;
        }, 1500);
      }
    },
    
    handleMigrationConfirm() {
      // 记录用户已确认
      uni.setStorageSync('migration_notice_confirmed', true);
      uni.setStorageSync('migration_notice_time', Date.now());
      
      console.log('用户已确认内网迁移通知');
    }
  }
}
</script>
```

#### 方式B：全局注册使用
适合在多个页面都可能显示通知的场景。

1. 在 `main.js` 中添加全局注册：
```javascript
// 在现有的组件注册代码后添加
import SimpleMigrationNotice from '@/components/migration-notice/simple-migration-notice.vue';
Vue.component('SimpleMigrationNotice', SimpleMigrationNotice);
```

2. 在任意页面中直接使用：
```html
<simple-migration-notice 
  :show.sync="showNotice"
  @confirm="handleConfirm"
></simple-migration-notice>
```

### 第三步：实现业务逻辑

#### 基础实现
```javascript
export default {
  data() {
    return {
      showMigrationNotice: false
    }
  },
  
  onLoad() {
    this.initMigrationNotice();
  },
  
  methods: {
    // 初始化迁移通知
    initMigrationNotice() {
      const storageKey = 'migration_notice_confirmed';
      const hasConfirmed = uni.getStorageSync(storageKey);
      
      if (!hasConfirmed) {
        // 首次访问或未确认，显示通知
        setTimeout(() => {
          this.showMigrationNotice = true;
        }, 1000);
      }
    },
    
    // 处理用户确认
    handleMigrationConfirm() {
      // 记录确认状态
      uni.setStorageSync('migration_notice_confirmed', true);
      uni.setStorageSync('migration_notice_confirm_time', Date.now());
      
      // 可选：调用后端API记录
      // this.recordNoticeStatus();
      
      uni.showToast({
        title: '已确认通知',
        icon: 'success'
      });
    }
  }
}
```

#### 高级实现（带API记录）
```javascript
methods: {
  async handleMigrationConfirm() {
    try {
      // 本地记录
      uni.setStorageSync('migration_notice_confirmed', true);
      
      // 调用后端API记录（根据项目实际API调整）
      await this.$http.post('/api/user/notice-status', {
        noticeType: 'migration',
        status: 'confirmed',
        confirmTime: Date.now()
      });
      
      uni.showToast({
        title: '已确认通知',
        icon: 'success'
      });
    } catch (error) {
      console.error('记录通知状态失败:', error);
      // 即使API失败，也保持本地记录
    }
  }
}
```

## 常见使用场景

### 场景1：应用启动时显示
在App.vue或首页中检查并显示通知。

### 场景2：登录后显示
在登录成功后检查用户是否已确认通知。

### 场景3：定期提醒
可以设置过期时间，定期重新提醒用户。

```javascript
// 检查是否需要重新提醒（例如：7天后重新提醒）
checkMigrationNotice() {
  const confirmTime = uni.getStorageSync('migration_notice_confirm_time');
  const now = Date.now();
  const sevenDays = 7 * 24 * 60 * 60 * 1000;
  
  if (!confirmTime || (now - confirmTime > sevenDays)) {
    this.showMigrationNotice = true;
  }
}
```

## 自定义配置

### 修改通知内容
如需修改通知文本，编辑组件中的 `notice-content` 部分：

```vue
<text class="notice-content">
  您的自定义通知内容
</text>
```

### 修改样式
组件使用了项目主色调 `#009BFF`，如需修改可在组件样式中调整：

```scss
.confirm-button {
  background: linear-gradient(135deg, #your-color 0%, #your-color-dark 100%);
}
```

### 自定义附件图片
```html
<simple-migration-notice 
  :show.sync="showNotice"
  image-url="/static/images/your-custom-image.png"
  @confirm="handleConfirm"
></simple-migration-notice>
```

## 测试建议

1. 测试不同屏幕尺寸的显示效果
2. 测试图片加载失败的情况
3. 测试用户确认后的状态记录
4. 测试组件的显示和隐藏动画

## 故障排除

### 问题1：图片不显示
- 检查图片路径是否正确
- 确认图片文件是否存在
- 检查图片格式是否支持

### 问题2：组件不显示
- 检查 `show` 属性是否为 `true`
- 确认组件是否正确引入
- 检查是否有CSS样式冲突

### 问题3：事件不触发
- 确认事件名称是否正确
- 检查是否使用了 `.sync` 修饰符
- 确认方法是否正确定义
