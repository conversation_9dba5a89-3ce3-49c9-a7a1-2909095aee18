<template>
  <view class="migration-notice" v-if="visible">
    <!-- 遮罩层 -->
    <view class="notice-mask" @click="handleMaskClick"></view>

    <!-- 通知内容 -->
    <view class="notice-content">
      <!-- 关闭按钮 -->
      <view class="close-btn" @click="closeNotice">
        <text class="close-icon">×</text>
      </view>

      <!-- 标题 -->
      <view class="notice-title"> 内网迁移通知 </view>

      <!-- 通知内容 -->
      <view class="notice-body">
        <text class="notice-text">
          为加强安全防护，9.1号起本系统将从互联网迁移至政务外网APP端可在登录山东通后，搜索环境执法APP使用，具体操作说明请查看附件资料
        </text>

        <!-- 查看附件按钮 -->
        <view class="attachment-btn" @click="showAttachment">
          <text class="attachment-text">查看附件资料</text>
        </view>
      </view>

      <!-- 确认按钮 -->
      <!-- <view class="confirm-btn" @click="confirmNotice">
				<text class="confirm-text">我知道了</text>
			</view> -->
    </view>

    <!-- 附件图片弹窗 -->
    <view
      class="attachment-modal"
      v-if="showAttachmentModal"
      @click="hideAttachment"
    >
      <view class="attachment-mask"></view>
      <view class="attachment-content" @click.stop="">
        <view class="attachment-close" @click="hideAttachment">
          <text class="close-icon">×</text>
        </view>
        <image
          class="attachment-image"
          :src="attachmentImageSrc"
          mode="aspectFit"
          @error="onImageError"
        ></image>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: "migration-notice",
  props: {
    // 是否显示通知
    show: {
      type: Boolean,
      default: false,
    },
    // 附件图片路径
    attachmentImage: {
      type: String,
      default: "/static/images/migration-guide.png",
    },
    // 是否允许点击遮罩关闭
    maskClosable: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      visible: false,
      showAttachmentModal: false,
    };
  },
  computed: {
    attachmentImageSrc() {
      return this.attachmentImage;
    },
  },
  watch: {
    show: {
      handler(newVal) {
        this.visible = newVal;
      },
      immediate: true,
    },
  },
  methods: {
    // 显示附件
    showAttachment() {
      this.showAttachmentModal = true;
    },

    // 隐藏附件
    hideAttachment() {
      this.showAttachmentModal = false;
    },

    // 确认通知
    confirmNotice() {
      this.visible = false;
      this.$emit("confirm");
      this.$emit("update:show", false);
    },

    // 关闭通知
    closeNotice() {
      this.visible = false;
      this.$emit("close");
      this.$emit("update:show", false);
    },

    // 点击遮罩
    handleMaskClick() {
      if (this.maskClosable) {
        this.closeNotice();
      }
    },

    // 图片加载错误
    onImageError() {
      uni.showToast({
        title: "图片加载失败",
        icon: "none",
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.migration-notice {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 9999;

  .notice-mask {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 10000;
  }

  .notice-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 600rpx;
    background: #ffffff;
    border-radius: 20rpx;
    z-index: 10001;
    overflow: hidden;

    .close-btn {
      position: absolute;
      top: 20rpx;
      right: 20rpx;
      width: 60rpx;
      height: 60rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 10002;

      .close-icon {
        font-size: 40rpx;
        color: #999999;
        font-weight: bold;
      }
    }

    .notice-title {
      padding: 40rpx 40rpx 20rpx;
      text-align: center;
      font-size: 32rpx;
      font-weight: bold;
      color: #333333;
    }

    .notice-body {
      padding: 0 40rpx 30rpx;

      .notice-text {
        font-size: 28rpx;
        color: #666666;
        line-height: 40rpx;
        text-align: justify;
      }

      .attachment-btn {
        margin-top: 30rpx;
        padding: 20rpx;
        background: #f0f8ff;
        border: 2rpx solid #009bff;
        border-radius: 10rpx;
        text-align: center;

        .attachment-text {
          font-size: 28rpx;
          color: #009bff;
        }
      }
    }

    .confirm-btn {
      margin: 20rpx 40rpx 40rpx;
      padding: 24rpx;
      background: #009bff;
      border-radius: 12rpx;
      text-align: center;

      .confirm-text {
        font-size: 30rpx;
        color: #ffffff;
        font-weight: 500;
      }
    }
  }

  // 附件图片弹窗样式
  .attachment-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: 10010;

    .attachment-mask {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.8);
    }

    .attachment-content {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 90%;
      max-width: 700rpx;
      background: #ffffff;
      border-radius: 20rpx;
      padding: 40rpx;

      .attachment-close {
        position: absolute;
        top: 20rpx;
        right: 20rpx;
        width: 60rpx;
        height: 60rpx;
        display: flex;
        align-items: center;
        justify-content: center;

        .close-icon {
          font-size: 40rpx;
          color: #999999;
          font-weight: bold;
        }
      }

      .attachment-image {
        width: 100%;
        max-height: 80vh;
        border-radius: 10rpx;
      }
    }
  }
}
</style>
