/*
 * 内网迁移通知组件全局注册
 * 
 * 使用方法：
 * 1. 在 main.js 中引入此文件
 * 2. 调用注册函数
 * 
 * 示例代码：
 * import registerMigrationNotice from '@/components/migration-notice/register-component.js';
 * registerMigrationNotice(Vue);
 */

import MigrationNotice from './migration-notice.vue';

export default function registerMigrationNotice(Vue) {
  // 全局注册内网迁移通知组件
  Vue.component('MigrationNotice', MigrationNotice);
  
  // 可选：添加全局方法来显示通知
  Vue.prototype.$showMigrationNotice = function(options = {}) {
    // 这里可以实现全局调用的逻辑
    // 类似于项目中现有的 $showModal 方法
    console.log('显示内网迁移通知', options);
  };
}

/*
 * 在 main.js 中添加以下代码来注册组件：
 * 
 * // 引入内网迁移通知组件
 * import registerMigrationNotice from '@/components/migration-notice/register-component.js';
 * registerMigrationNotice(Vue);
 * 
 * 注册后可以在任何页面中直接使用：
 * <migration-notice :show.sync="showNotice" @confirm="handleConfirm"></migration-notice>
 */
