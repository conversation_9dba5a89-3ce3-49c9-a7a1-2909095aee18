<template>
  <view class="migration-notice-wrapper" v-if="visible">
    <!-- 遮罩 -->
    <view class="notice-mask" @click="handleMaskClick"></view>

    <!-- 通知弹窗 -->
    <view class="notice-dialog">
      <!-- 关闭按钮 -->
      <view class="close-button" @click="closeNotice">
        <text class="close-text">×</text>
      </view>

      <!-- 通知标题 -->
      <view class="notice-header">
        <text class="notice-title">内网迁移通知</text>
      </view>

      <!-- 通知内容 -->
      <view class="notice-main">
        <text class="notice-content">
          为加强安全防护，9.1号起本系统将从互联网迁移至政务外网APP端可在登录山东通后，搜索环境执法APP使用，具体操作说明请查看附件资料
        </text>

        <!-- 查看附件按钮 -->
        <view class="attachment-button" @click="viewAttachment">
          <text class="attachment-text">查看附件资料</text>
        </view>
      </view>

      <!-- 确认按钮 -->
      <!-- <view class="notice-footer">
        <view class="confirm-button" @click="confirmNotice">
          <text class="confirm-text">我知道了</text>
        </view>
      </view> -->
    </view>

    <!-- 附件图片查看弹窗 -->
    <view class="image-viewer" v-if="showImageViewer" @click="hideImageViewer">
      <view class="image-mask"></view>
      <view class="image-container" @click.stop="">
        <view class="image-close" @click="hideImageViewer">
          <text class="close-text">×</text>
        </view>

        <!-- 如果有图片则显示图片，否则显示文字说明 -->
        <view v-if="hasValidImage">
          <image class="attachment-img" :src="imgUrl" mode="aspectFit"></image>
        </view>
        <view v-else class="attachment-text-content">
          <view class="attachment-title">操作说明</view>
          <view class="attachment-steps">
            <text class="step-item">1. 打开山东通APP</text>
            <text class="step-item">2. 在搜索框中输入"环境执法"</text>
            <text class="step-item">3. 点击搜索结果中的"环境执法APP"</text>
            <text class="step-item">4. 使用原账号密码登录即可</text>
          </view>
          <view class="attachment-note">
            <text class="note-text">注意：迁移后请使用政务外网访问</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import imgUrl from "@/static/images/migration-guide.png";
export default {
  name: "simple-migration-notice",
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    // 附件图片路径，默认使用同级目录下的图片
    imageUrl: {
      type: String,
      default: "/static/images/migration-guide.png",
    },
  },
  data() {
    return {
      imgUrl,
      visible: false,
      showImageViewer: false,
      hasValidImage: true, // 标记是否有有效图片
    };
  },
  computed: {
    imageSrc() {
      return this.imageUrl;
    },
  },
  watch: {
    show: {
      handler(val) {
        this.visible = val;
      },
      immediate: true,
    },
  },
  methods: {
    // 查看附件
    viewAttachment() {
      this.showImageViewer = true;
    },

    // 隐藏图片查看器
    hideImageViewer() {
      this.showImageViewer = false;
    },

    // 确认通知
    confirmNotice() {
      this.visible = false;
      this.$emit("update:show", false);
      this.$emit("confirm");
    },

    // 关闭通知
    closeNotice() {
      this.visible = false;
      this.$emit("update:show", false);
      this.$emit("close");
    },

    // 点击遮罩
    handleMaskClick() {
      // 默认不允许点击遮罩关闭，确保用户看到重要通知
      // 如果需要允许，可以调用 this.closeNotice();
    },

    // 图片加载错误处理
    handleImageError() {
      this.hasValidImage = false;
      console.log("附件图片加载失败，显示文字说明");
    },
  },
};
</script>

<style lang="scss" scoped>
.migration-notice-wrapper {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 9999;
}

.notice-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
}

.notice-dialog {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 640rpx;
  background: #ffffff;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 40rpx rgba(0, 0, 0, 0.2);
}

.close-button {
  position: absolute;
  top: 24rpx;
  right: 24rpx;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;

  .close-text {
    font-size: 48rpx;
    color: #cccccc;
    font-weight: bold;
    line-height: 1;
  }
}

.notice-header {
  padding: 50rpx 40rpx 30rpx;
  text-align: center;

  .notice-title {
    font-size: 36rpx;
    font-weight: bold;
    color: #333333;
  }
}

.notice-main {
  padding: 0 40rpx 40rpx;

  .notice-content {
    font-size: 30rpx;
    color: #666666;
    line-height: 44rpx;
    text-align: justify;
    display: block;
    margin-bottom: 40rpx;
  }

  .attachment-button {
    padding: 24rpx;
    background: linear-gradient(135deg, #e6f3ff 0%, #cce7ff 100%);
    border: 2rpx solid #009bff;
    border-radius: 16rpx;
    text-align: center;

    .attachment-text {
      font-size: 30rpx;
      color: #009bff;
      font-weight: 500;
    }
  }
}

.notice-footer {
  padding: 0 40rpx 40rpx;

  .confirm-button {
    width: 100%;
    height: 88rpx;
    background: linear-gradient(135deg, #009bff 0%, #0080e6 100%);
    border-radius: 16rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4rpx 16rpx rgba(0, 155, 255, 0.3);

    .confirm-text {
      font-size: 32rpx;
      color: #ffffff;
      font-weight: 500;
    }
  }
}

// 图片查看器样式
.image-viewer {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 10010;
}

.image-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.9);
}

.image-container {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 90%;
  max-width: 800rpx;
  background: #ffffff;
  border-radius: 20rpx;
  padding: 40rpx;
}

.image-close {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 50%;

  .close-text {
    font-size: 40rpx;
    color: #666666;
    font-weight: bold;
    line-height: 1;
  }
}

.attachment-img {
  width: 100%;
  max-height: 80vh;
  border-radius: 12rpx;
}

.attachment-text-content {
  text-align: left;

  .attachment-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333333;
    margin-bottom: 30rpx;
    text-align: center;
  }

  .attachment-steps {
    margin-bottom: 30rpx;

    .step-item {
      display: block;
      font-size: 28rpx;
      color: #666666;
      line-height: 40rpx;
      margin-bottom: 16rpx;
      padding-left: 20rpx;
    }
  }

  .attachment-note {
    padding: 20rpx;
    background: #fff3cd;
    border: 2rpx solid #ffeaa7;
    border-radius: 8rpx;

    .note-text {
      font-size: 26rpx;
      color: #856404;
      text-align: center;
    }
  }
}
</style>
