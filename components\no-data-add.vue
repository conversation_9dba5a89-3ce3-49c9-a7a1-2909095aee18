<template>
	<view>
		<div class="box-region">
			<image v-if="type == 'message'" src="/static/login/images/none-message.png" style="width:451rpx;height:344rpx; display: inline-block;"></image>
			<image v-if="type == 'data'" src="/static/login/images/no-data.png" style="width:451rpx;height:344rpx; display: inline-block;"></image>	
			<div @click="addPollution">新增企业信息></div>
		</div>
	</view>
</template>

<script>
	export default {
		props: {
			type: {
				type: String,
				default: 'data' // message data
			}
		},
		data(){
			return {
			}
		},
		methods: {
			addPollution(){
				uni.navigateTo({
					url: '/pages/record/record-sign-add-pollution?turnPage=2'
				});
			}
		}
	}
</script>

<style>
	.box-region{
		width: 100%;
		height: 30vh;
		position: absolute;
		left: 0;
		right: 0;
		top: 0;
		bottom: 0;
		margin: auto;
		text-align: center;
		color: orange;
		
	}
</style>
