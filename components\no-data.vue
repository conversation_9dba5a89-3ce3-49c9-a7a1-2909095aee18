<template>
	<div>
		<div class="box-region" :style="{height: boxHeight}">
			<image v-if="type == 'message'" src="/static/login/images/none-message.png"
				style="width:451rpx;height:344rpx; display: inline-block;"></image>
			<image v-if="type == 'data'" src="/static/login/images/no-data.png"
				style="width:451rpx;height:344rpx; display: inline-block;"></image>
		</div>
	</div>

</template>

<script>
	export default {
		props: {
			boxHeight: {
				type: String,
				default: '75vh'
			},
			type: {
				type: String,
				default: 'data' // message data
			}
		},
		data() {
			return {}
		}
	}
</script>

<style>
	.box-region {
		display: flex;
		justify-content: center;
		/* 水平居中 */
		align-items: center;
		/* 垂直居中 */
		width: 100%;
		height: 75vh;
	}
</style>