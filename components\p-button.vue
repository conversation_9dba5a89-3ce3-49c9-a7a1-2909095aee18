<template>
	<button class="primary-btn" :disabled="disabled">{{name}}</button>
</template>

<script>
	export default {
		name: 'p-button',
		props: {
			name: {  // 传入名字
				type: String,
				default: ''
			},

			disabled: {
				type: Boolean,
				default: false
			}
		}
	}
</script>

<style scoped>
	.primary-btn{
		width: 750rpx;
		height: 77rpx;
		background: #009BFF;
		color: white;
		position: fixed;
		bottom: 0;
		left: 0;
		font-size: 29rpx;
		font-family: PingFang SC;
		font-weight: 500;
		color: #FFFFFF;
		
	}
	.primary-btn::after {
		border-radius: 0rpx!important;
		border:0rpx!important
	}
</style>
