<!--
 * @Author: your name
 * @Date: 2021-10-19 14:30:54
 * @LastEditTime: 2021-10-20 15:40:23
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /YDZF_APP_HB_ShiYan/components/uview-ui/components/u-time-line/u-time-line.vue
-->
<template>
    <view class="u-time-axis">
        <slot />
    </view>
</template>

<script>
/**
 * timeLine 时间轴
 * @description 时间轴组件一般用于物流信息展示，各种跟时间相关的记录等场景。
 * @tutorial https://www.uviewui.com/components/timeLine.html
 * @example <u-time-line></u-time-line>
 */
export default {
    name: 'u-time-line',
    data() {
        return {};
    }
};
</script>

<style lang="scss" scoped>
@import '../../libs/css/style.components.scss';

.u-time-axis {
    padding-left: 100rpx;
    position: relative;
}

.u-time-axis::before {
    content: ' ';
    position: absolute;
    left: 60rpx;
    top: 12rpx;
    width: 1px;
    bottom: 0;
    border-left: 1px solid #ddd;
    transform-origin: 0 0;
    transform: scaleX(0.5);
}
</style>
