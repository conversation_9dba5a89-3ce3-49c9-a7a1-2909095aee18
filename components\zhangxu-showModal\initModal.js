/*
 * @Author: your name
 * @Date: 2021-04-21 03:02:59
 * @LastEditTime: 2021-05-06 11:40:35
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /SmartFormWeb/components/zhangxu-showModal/initModal.js
 */
import Vuex from 'vuex'
export default function initModal(v) {
  // 挂在store到全局Vue原型上
  v.prototype.$modalStore = new Vuex.Store({
    state: {
		show:false,
		title:"标题",
		content:'内容',
		showCancel:true,
		cancelText:"取消",
		cancelColor:"#000000",
		confirmText:"确定",
		confirmColor:"rgb(15, 174, 255)",
		success:null,
		textAlign: 'center',
		clickMaskClose:true // 新增clickMaskClose参数，请同步在show-modal.vue中'clickBtn方法' 和 'beforeDestroy生命周期' 添加this.$modalStore.state.clickMaskClose = true;
    },
    mutations: {
		hideModal(state) {
			if(!state.clickMaskClose){
				return
			}
			// 小程序导航条页面控制
			// #ifndef H5
			if(state.hideTabBar){
				wx.showTabBar();
			}
			// #endif
			setTimeout(() => {
				state.show = false
				state.cancelText = '取消'
				state.confirmText = '确定'
				state.textAlign = 'center'
				state.clickMaskClose = true
			}, 100);
		},
		showModal(state,data) {
			state = Object.assign(state,data)
			// console.log(state);
			state.show = true
		},
		success(state,res) {
			let cb = state.success
			let resObj={
				cancel:false,
				confirm:false
			}
			res=="confirm"?resObj.confirm=true:resObj.cancel=true
			cb && cb(resObj)
		}
    }
  })
  // 注册$showModal到Vue原型上，以方便全局调用
  v.prototype.$showModal = function (option) { 
	if (typeof option === 'object') {
		// #ifndef H5
		if(option.hideTabBar){
			wx.hideTabBar();
		}
		// #endif
		
		v.prototype.$modalStore.commit('showModal', option)
	}else{
		throw "配置项必须为对象传入的值为："+typeof option;
	}
  }
}