<template>
	<div class="_showModal" v-show="show">
		<div class="_shade"></div>
		<div class="_modalBox" @click="closeModal" @touchmove.stop.prevent="">
			<div class="_modal">
				<slot name="title">
					<div class="title" v-show="title">{{title}}</div>
				</slot>
				<slot name="content">
					<scroll-view style="max-height: 900rpx" scroll-y="true">
						<div class="content" v-html="content" :style="`text-align: ${$modalStore.state.textAlign};`">
					
						</div>
					</scroll-view>
					
				</slot>
				<slot name="btn">
					<div class="btnbox">
						<div class="cancel btn" v-show="showCancel" :style="cancelColor" @click.stop="clickBtn('cancel')">{{cancelText}}</div>
						<div class="confirm btn" :style="confirmColor" @click.stop="clickBtn('confirm')">{{confirmText}}</div>
					</div>
				</slot>
			</div>
		</div>
	</div>
</template>

<script>
export default {
	computed: {
		show(){
			return this.$modalStore.state.show;
		},
		title(){
			return this.$modalStore.state.title;
		},
		content(){
			return this.$modalStore.state.content;
		},
		showCancel(){
			return this.$modalStore.state.showCancel;
		},
		cancelText(){
			return this.$modalStore.state.cancelText;
		},
		cancelColor(){
			return "color:"+this.$modalStore.state.cancelColor;
		},
		confirmText(){
			return this.$modalStore.state.confirmText;
		},
		confirmColor(){
			return "color:"+this.$modalStore.state.confirmColor;
		}
	},
	methods:{
		closeModal(){
			this.$modalStore.commit('hideModal')
		},
		clickBtn(res){
			this.$modalStore.state.clickMaskClose = true;
			this.$modalStore.commit('hideModal')
			this.$modalStore.commit('success',res)
		}
	},
	beforeDestroy(){
		this.$modalStore.state.clickMaskClose = true;
		this.$modalStore.commit('hideModal',true)
	}
};
</script>

<style lang="scss" scoped>
	._showModal{
		position: fixed;
		top: 0;
		left:0;
		width: 100vw;
		height: 100vh;
		z-index:10000;
		._shade{
			width: 100%;
			height: 100%;
			position: absolute;
			top: 0;
			left: 0;
			background: #555;
			opacity: .4;
			z-index:11000;
		}
		._modalBox{
			width: 100%;
			height: 100%;
			position: absolute;
			top: 0;
			left: 0;
			z-index:12000;
			display: flex;
			justify-content: center;
			align-items: center;
			._modal{
				flex: none;
				width:70%;
				// min-height:200rpx;
				background: #fff;
				border-radius: 10rpx;
				.title{
					height: 40rpx;
					line-height: 40rpx;
					text-align: center;
					font-size: 26rpx;
					font-weight: bold;
					padding: 15rpx 0 0;
					color: #666;
					// border-bottom: 1rpx solid #e1e1e1;
				}
				.content{
					padding:30rpx;
					font-size: 30rpx;
					color: #666;
					line-height: 35rpx;
					text-align: center;
					background: white;
				}
				.btnbox{
					display: flex;
					.btn{
						font-size: 30rpx;
						text-align: center;
						flex: auto;
						line-height: 80rpx;
						border-top: 1rpx solid #e1e1e1;
						border-right: 1rpx solid #e1e1e1;
					}
					.btn:last-child{
						border-right:none;
					}
					.cancel{
						
					}
				}
			}
		}
		
	}
</style>
