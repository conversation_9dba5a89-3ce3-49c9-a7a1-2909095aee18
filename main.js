/*
 * @Author: 姚进玺 <EMAIL>
 * @Date: 2022-06-23 15:56:20
 * @LastEditors: 姚进玺 <EMAIL>
 * @LastEditTime: 2022-10-02 20:38:44
 * @FilePath: /UNI_APP_ShanDong/main.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import Vue from 'vue';
import App from './App';
import store from './store';
// import vueEsign from 'vue-esign'
import { getAuthToken } from '@/api/record.js';
import '@/common/logger.js';
import VueI18n from 'vue-i18n';
import watermark from './uni_modules/bowo-watermark/components/bowo-watermark/watermark.js';
watermark.enableScaleImage();
import uView from '@/components/uview-ui';
Vue.use(uView);
import initModal from '@/components/zhangxu-showModal/initModal.js';
import showModal from '@/components/zhangxu-showModal/show-modal';
initModal(Vue);
Vue.component('show-modal', showModal);
import Page from '@/pages/component/Page.vue';
Vue.component('MainPage', Page);

import DynamicForm from '@/components/DynamicForm.vue';
Vue.component('DynamicForm', DynamicForm);

import IntegrityDeclaration from './pages/form/integrity-declaration.vue';
Vue.component('IntegrityDeclaration', IntegrityDeclaration);


import AiFab from '@/views/ai/components/aiFab/Index.vue';
// import Map from 'umap-gd';
Vue.component('AiFab', AiFab);


import NoData from '@/components/no-data.vue';
Vue.component('NoData', NoData);

//#ifdef H5
import AMap from 'vue-amap';
Vue.use(AMap);
AMap.initAMapApiLoader({
	// 高德key
	key: 'b698e8fc43715a16053e9fa156601706',
	// 插件集合 （插件按需引入）
	plugin: ['AMap.Geolocation']
});
//#endif

import dayjs from './components/dayjs/dayjs.min.js';
import isSameOrAfter from './components/dayjs/plugin/isSameOrAfter/index';
dayjs.extend(isSameOrAfter);
Vue.prototype.$dayjs = dayjs;

import TemplateUtils from '@/common/template.utils.js';
Vue.prototype.$templateUtils = TemplateUtils;

// Vue.use(vueEsign)

Vue.prototype.$store = store;
App.mpType = 'app';

import {
	LOGIN_ULR_BASE,
	SERVER_FULL_URL
} from '@/common/config.js';

import formContext from 'bowo-form/components/context.js';
formContext.enableCompressUploadImage()
formContext.style.primaryColor = '#009bff';
formContext.initServerBaseUrl(LOGIN_ULR_BASE);
formContext.initServerFullUrl(SERVER_FULL_URL);


import TemplateForm from 'bowo-form';
Vue.component('template-form', TemplateForm);

Vue.use(VueI18n);
const i18n = new VueI18n({
	locale: 'zh-CN',
	message: {
		'zh-CN': require('@/language/zh_CN.json'),
		'en-US': require('@/language/en_US.json')
	}
});

// 初始化附件鉴权Token
getAuthToken().then(res=>{
	// console.log(res);
})

const app = new Vue({
	i18n,
	store,
	...App
});
app.$mount();