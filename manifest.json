{
	"name": "山东环境执法",
	"appid": "__UNI__44BBF2D",
	"description": "移动执法基线版本",
	"versionName": "1.5.3",
	"versionCode": 153,
	"transformPx": false,
	/* 5+App特有相关 */
	"app-plus": {
		"statusbar": {
			"immersed": false
		},
		"compatible": {
			"ignoreVersion": true
		},
		"usingComponents": true,
		"compilerVersion": 3,
		/* 模块配置 */
		"modules": {
			"Share": {},
			"Maps": {},
			"Bluetooth": {},
			"Geolocation": {}
		},
		/* 应用发布信息 */
		"distribute": {
			/* android打包配置 */
			"android": {
				"permissions": [
					"<uses-feature android:name=\"android.hardware.camera\"/>",
					"<uses-feature android:name=\"android.hardware.camera.autofocus\"/>",
					"<uses-permission android:name=\"android.permission.ACCESS_COARSE_LOCATION\"/>",
					"<uses-permission android:name=\"android.permission.ACCESS_FINE_LOCATION\"/>",
					"<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>",
					"<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>",
					"<uses-permission android:name=\"android.permission.CALL_PHONE\"/>",
					"<uses-permission android:name=\"android.permission.CAMERA\"/>",
					"<uses-permission android:name=\"android.permission.CAPTURE_AUDIO_OUTPUT\"/>",
					"<uses-permission android:name=\"android.permission.CAPTURE_VIDEO_OUTPUT\"/>",
					"<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>",
					"<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>",
					"<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>",
					"<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>",
					"<uses-permission android:name=\"android.permission.MODIFY_AUDIO_SETTINGS\"/>",
					"<uses-permission android:name=\"android.permission.MODIFY_PHONE_STATE\"/>",
					"<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>",
					"<uses-permission android:name=\"android.permission.READ_CONTACTS\"/>",
					"<uses-permission android:name=\"android.permission.READ_LOGS\"/>",
					"<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>",
					"<uses-permission android:name=\"android.permission.RECORD_AUDIO\"/>",
					"<uses-permission android:name=\"android.permission.VIBRATE\"/>",
					"<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>",
					"<uses-permission android:name=\"android.permission.WRITE_CONTACTS\"/>",
					"<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>"
				],
				"abiFilters": ["armeabi-v7a", "arm64-v8a", "x86"]
			},
			/* ios打包配置 */
			"ios": {},
			/* SDK配置 */
			"sdkConfigs": {
				"share": {},
				"maps": {},
				"geolocation": {}
			},
			// "oauth" :
			"icons": {
				"android": {
					"xhdpi": "static/img/launcher_96.png",
					"xxhdpi": "static/img/launcher_144.png"
				}
			}
		}
	},
	/* 快应用特有相关 */
	"quickapp": {},
	/* 小程序特有相关 */
	"mp-weixin": {
		"appid": "wx0e90ac017e1228a0",
		"setting": {
			"urlCheck": false
		},
		"usingComponents": true,
		"permission": {}
	},
	"mp-alipay": {
		"usingComponents": true,
		"appid": "920793623"
	},
	"mp-baidu": {
		"usingComponents": true
	},
	"mp-toutiao": {
		"usingComponents": true
	},
	"h5": {
		"router": {
			"mode": "hash",
			"base": "./"
		},
		"sdkConfigs": {
			"maps": {
				"qqmap": {
					"key": "MQHBZ-7ZS6S-GNPOX-64KGU-QJ743-EWBCZ"
				}
			}
		},
		"devServer": {
			"port": 8011,
			"proxy": {
				"/zhzf": {
					"target": "http://***************:9001",
					// "target": "http://*************:8000",
					"ws": true,
					"changeOrigin": true
				}
			},
			"disableHostCheck": true
		},
		"template": "index.html"
	},
	"vueVersion": "2",
	"sassImplementationName": "node-sass"
}