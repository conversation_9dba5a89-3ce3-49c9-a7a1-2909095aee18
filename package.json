{"name": "smart_form_app", "version": "1.0.0", "description": "混合App工程模板", "main": "main.js", "scripts": {"test": "test"}, "uni-app": {"scripts": {"mp-dingtalk": {"title": "钉钉小程序", "env": {"UNI_PLATFORM": "mp-alipay"}, "define": {"MP-DINGTALK": true}}}}, "dependencies": {"axios": "^0.19.2", "bowo-form": "2.2.5", "bowo-sdk": "0.7.1", "crypto-js": "^4.0.0", "dayjs": "^1.8.35", "js-base64": "^3.7.7", "json-loader": "^0.5.7", "less": "^4.1.1", "less-loader": "^7.3.0", "sass": "^1.35.1", "sass-loader": "^10.1.1", "stylus": "^0.54.8", "stylus-loader": "3.0.2", "video.js": "^8.6.1", "vconsole": "^3.15.1", "vue-amap": "^0.5.10", "vue-esign": "^1.0.5", "vue-i18n": "^8.24.3", "pdfh5": "1.4.2"}, "repository": {"type": "git", "url": "http://gitlab.powerdata.com.cn/Android/SmartFormWeb.git"}, "author": "", "license": "ISC"}