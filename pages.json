{
    "easycom": {
        "^u-(.*)": "components/uview-ui/components/u-$1/u-$1.vue"
    },
    "pages": [
        {
            "path": "pages/login/login",
            "style": {
                "navigationBarTitleText": ""
            }
        },

        {
            "path": "views/ai/CompanyDetail",
            "style": {
                "navigationBarTitleText": ""
            }
        },

        {
            "path": "views/ai/Finish",
            "style": {
                "navigationBarTitleText": ""
            }
        },

        {
            "path": "views/ai/ProblemList",
            "style": {
                "navigationBarTitleText": ""
            }
        },

        {
            "path": "pages/login/changeUrl",
            "style": {
                "navigationBarTitleText": ""
            }
        },
        {
            "path": "pages/mine/my",
            "style": {
                "navigationBarTitleText": ""
            }
        },
        {
            "path": "pages/main/PollutionDetail",
            "style": {
                "navigationBarTitleText": ""
            }
        },
        {
            "path": "pages/main/PollutionDetailList",
            "style": {
                "navigationBarTitleText": ""
            }
        },
        {
            "path": "pages/main/TemplateDetail",
            "style": {
                "navigationBarTitleText": ""
            }
        },
        {
            "path": "pages/mine/change-password",
            "style": {
                "navigationBarTitleText": ""
            }
        },
        {
            "path": "pages/main/portal",
            "style": {
                "app-plus": {
                    "titleNView": false
                }
            }
        },
        {
            "path": "pages/main/Search",
            "style": {
                "navigationBarTitleText": ""
            }
        },
        {
            "path": "pages/main/SearchDetail",
            "style": {
                "navigationBarTitleText": ""
            }
        },
        {
            "path": "pages/record/record-Intelligent-judgment",
            "style": {
                "navigationBarTitleText": "",
                "enablePullDownRefresh": false
            }
        },
        {
            "path": "pages/record/record-Intelligent-judgment-detail",
            "style": {
                "navigationBarTitleText": "",
                "enablePullDownRefresh": false
            }
        },
        {
            "path": "pages/wry/wry-tags",
            "style": {
                "navigationBarTitleText": "",
                "enablePullDownRefresh": false
            }
        },
        {
            "path": "pages/record/record-company-list",
            "style": {
                "navigationBarTitleText": "",
                "enablePullDownRefresh": false
            }
        },
        {
            "path": "pages/record/record-company-twoYearList",
            "style": {
                "navigationBarTitleText": "",
                "enablePullDownRefresh": false
            }
        },
        {
            "path": "pages/record/record-bookMark-list",
            "style": {
                "navigationBarTitleText": "",
                "enablePullDownRefresh": false
            }
        },
        {
            "path": "pages/record/record-bookMark-add",
            "style": {
                "navigationBarTitleText": "",
                "enablePullDownRefresh": false
            }
        },
        {
            "path": "pages/predit/Layout",
            "style": {
                "navigationBarTitleText": ""
            }
        },
        {
            "path": "pages/online/Layout",
            "style": {
                "navigationBarTitleText": ""
            }
        },
        {
            "path": "pages/eleMap/Layout",
            "style": {
                "navigationBarTitleText": ""
            }
        },
        {
            "path": "pages/eleMap/SearchPollution",
            "style": {
                "navigationBarTitleText": ""
            }
        },
        {
            "path": "pages/online/CompanyList",
            "style": {
                "navigationBarTitleText": ""
            }
        },
        {
            "path": "pages/online/CompanyDetail",
            "style": {
                "navigationBarTitleText": ""
            }
        },
        {
            "path": "pages/record/record",
            "style": {
                "navigationBarTitleText": ""
            }
        },
        {
            "path": "pages/record/record-template-form-tips",
            "style": {
                "navigationBarTitleText": ""
            }
        },
        {
            "path": "pages/record/record-sign-change-location",
            "style": {
                "navigationBarTitleText": ""
            }
        },
        {
            "path": "pages/record/record-sign-change-pollution",
            "style": {
                "navigationBarTitleText": ""
            }
        },
        {
            "path": "pages/record/record-sign-add-pollution",
            "style": {
                "navigationBarTitleText": ""
            }
        },
        {
            "path": "pages/record/template-detail",
            "style": {
                "navigationBarTitleText": ""
            }
        },
        {
            "path": "pages/record/record-printpdf",
            "style": {
                "navigationBarTitleText": ""
            }
        },
        {
            "path": "pages/record/form-photo-list",
            "style": {
                "navigationBarTitleText": ""
            }
        },
        {
            "path": "pages/department/department",
            "style": {
                "enablePullDownRefresh": true,
                "app-plus": {
                    "pullToRefresh": {
                        "style": "circle",
                        "color": "#0077FF"
                    }
                }
            }
        },
        {
            "path": "pages/department/department-company",
            "style": {
                "navigationBarTitleText": ""
            }
        },
        {
            "path": "pages/book/book-list",
            "style": {
                "navigationBarTitleText": ""
            }
        },
        {
            "path": "pages/book/book-info",
            "style": {
                "navigationBarTitleText": ""
            }
        },
        {
            "path": "pages/book/book-detail",
            "style": {
                "app-plus": {
                    "scrollIndicator": "none"
                },
                "navigationBarTitleText": ""
            }
        },
        {
            "path": "pages/book/dyna-list-detail",
            "style": {}
        },
        {
            "path": "pages/form/textarea-template-language",
            "style": {}
        },
        {
            "path": "pages/form/textarea-template-language-item",
            "style": {}
        },
        {
            "path": "pages/book/dyna-list-page",
            "style": {}
        },
        {
            "path": "pages/form/form-previewer",
            "style": {}
        },
        {
            "path": "pages/detail/hjxf",
            "style": {}
        },
        {
            "path": "pages/component/tree/district-tree",
            "style": {}
        },
        {
            "path": "pages/component/tree/tree",
            "style": {}
        },
        {
            "path": "pages/component/tree/industry-tree",
            "style": {}
        },
        {
            "path": "pages/component/signature/signature-index",
            "style": {}
        },
        {
            "path": "pages/component/location/location-index",
            "style": {}
        },
        {
            "path": "pages/map/location",
            "style": {}
        },
        {
            "path": "pages/record/picker-test",
            "style": {
                "navigationBarTitleText": "",
                "enablePullDownRefresh": "false"
            }
        },
        {
            "path": "pages/record/record-sign-change-location",
            "style": {
                "navigationBarTitleText": ""
            }
        },
        {
            "path": "pages/record/record-sign-change-pollution",
            "style": {
                "navigationBarTitleText": ""
            }
        },
        {
            "path": "pages/record/record-sign-add-pollution",
            "style": {
                "navigationBarTitleText": ""
            }
        },
        {
            "path": "pages/record/template-detail",
            "style": {
                "navigationBarTitleText": ""
            }
        },
        {
            "path": "pages/record/record-printpdf",
            "style": {
                "navigationBarTitleText": ""
            }
        },
        {
            "path": "pages/record/form-photo-list",
            "style": {
                "navigationBarTitleText": ""
            }
        },
        {
            "path": "pages/department/department",
            "style": {}
        },
        {
            "path": "pages/book/book-list",
            "style": {}
        },
        {
            "path": "pages/book/book-info",
            "style": {
                "navigationBarTitleText": ""
            }
        },
        {
            "path": "pages/book/book-detail",
            "style": {
                "app-plus": {
                    "scrollIndicator": "none"
                },
                "navigationBarTitleText": ""
            }
        },
        {
            "path": "pages/book/dyna-list-detail",
            "style": {
                "navigationBarTitleText": ""
            }
        },
        {
            "path": "pages/form/textarea-template-language",
            "style": {
                "navigationBarTitleText": ""
            }
        },
        {
            "path": "pages/form/textarea-template-language-item",
            "style": {
                "navigationBarTitleText": ""
            }
        },
        {
            "path": "pages/book/dyna-list-page",
            "style": {
                "navigationBarTitleText": ""
            }
        },
        {
            "path": "pages/form/form-previewer",
            "style": {
                "navigationBarTitleText": ""
            }
        },
        {
            "path": "pages/detail/hjxf",
            "style": {}
        },
        {
            "path": "pages/component/tree/district-tree",
            "style": {}
        },
        {
            "path": "pages/component/tree/tree",
            "style": {}
        },
        {
            "path": "pages/component/tree/industry-tree",
            "style": {}
        },
        {
            "path": "pages/component/signature/signature-index",
            "style": {}
        },
        {
            "path": "pages/component/location/location-index",
            "style": {}
        },
        {
            "path": "pages/map/location",
            "style": {}
        },
        {
            "path": "pages/record/picker-test",
            "style": {
                "navigationBarTitleText": "",
                "enablePullDownRefresh": false
            }
        },
        {
            "path": "pages/component/signature/signature-page",
            "style": {
                "navigationBarTitleText": "",
                "enablePullDownRefresh": false,
                "pageOrientation": "landscape"
            }
        },
        {
            "path": "pages/component/drawBoard/signature-page",
            "style": {
                "navigationBarTitleText": "",
                "enablePullDownRefresh": false,
                "pageOrientation": "landscape"
            }
        },
        {
            "path": "pages/task/task-list",
            "style": {
                "navigationBarTitleText": "",
                "enablePullDownRefresh": false
            }
        },
        {
            "path": "pages/task/Template",
            "style": {
                "navigationBarTitleText": "",
                "enablePullDownRefresh": false
            }
        },
        {
            "path": "pages/record/record-sign-all-pollution",
            "style": {
                "navigationBarTitleText": "",
                "enablePullDownRefresh": false
            }
        },
        {
            "path": "pages/task/task-detail",
            "style": {
                "navigationBarTitleText": "",
                "enablePullDownRefresh": false
            }
        },
        {
            "path": "pages/task/task-info-list",
            "style": {
                "navigationBarTitleText": "",
                "enablePullDownRefresh": false
            }
        },
        {
            "path": "pages/task/task-done-detail",
            "style": {
                "navigationBarTitleText": ""
            }
        },
        {
            "path": "pages/record/record-resolver",
            "style": {
                "navigationBarTitleText": "",
                "enablePullDownRefresh": false
            }
        },
        {
            "path": "pages/record/record-water-map",
            "style": {
                "navigationBarTitleText": "",
                "enablePullDownRefresh": false
            }
        },
        {
            "path": "pages/record/record-form-editor",
            "style": {
                "navigationBarTitleText": "",
                "enablePullDownRefresh": false
            }
        },
        {
            "path": "pages/record/record-share-print",
            "style": {
                "navigationBarTitleText": "",
                "enablePullDownRefresh": false
            }
        },
        {
            "path": "pages/record/record-form-previewer",
            "style": {
                "navigationBarTitleText": "",
                "enablePullDownRefresh": false
            }
        },
        {
            "path": "pages/record/record-history-list",
            "style": {
                "navigationBarTitleText": "",
                "enablePullDownRefresh": false
            }
        },
        {
            "path": "pages/record/record-eleclist",
            "style": {
                "navigationBarTitleText": "",
                "enablePullDownRefresh": false
            }
        },
        {
            "path": "pages/task/task-transfer",
            "style": {
                "navigationBarTitleText": "",
                "enablePullDownRefresh": false
            }
        },
        {
            "path": "pages/task/dispatch-task",
            "style": {
                "navigationBarTitleText": "",
                "enablePullDownRefresh": false
            }
        },
        {
            "path": "pages/task/water/water-list",
            "style": {
                "navigationBarTitleText": "",
                "enablePullDownRefresh": false
            }
        },
        {
            "path": "pages/task/water/water-detail",
            "style": {
                "navigationBarTitleText": "",
                "enablePullDownRefresh": false
            }
        },
        {
            "path": "pages/task/land/land-list",
            "style": {
                "navigationBarTitleText": "",
                "enablePullDownRefresh": false
            }
        },
        {
            "path": "pages/task/land/land-detail",
            "style": {
                "navigationBarTitleText": "",
                "enablePullDownRefresh": false
            }
        },
        {
            "path": "pages/task/area/area-list",
            "style": {
                "navigationBarTitleText": "",
                "enablePullDownRefresh": false
            }
        },
        {
            "path": "pages/task/area/area-detail",
            "style": {
                "navigationBarTitleText": "",
                "enablePullDownRefresh": false
            }
        },
        {
            "path": "pages/wry/wry-list",
            "style": {
                "navigationBarTitleText": "",
                "enablePullDownRefresh": false
            }
        },
        {
            "path": "pages/component/attach-preview",
            "style": {
                "navigationBarTitleText": "",
                "enablePullDownRefresh": false,
                "pageOrientation": "landscape"
            }
        },
        {
            "path": "pages/media/video-player",
            "style": {
                "navigationBarTitleText": "",
                "enablePullDownRefresh": false
            }
        },
        {
            "path": "pages/record/record-attach-choose-list",
            "style": {
                "navigationBarTitleText": "",
                "enablePullDownRefresh": false
            }
        },
        {
            "path": "pages/record/absent/absent-record-resolver",
            "style": {
                "navigationBarTitleText": "",
                "enablePullDownRefresh": false
            }
        },
        {
            "path": "pages/rectify/rectify-list",
            "style": {
                "navigationBarTitleText": "",
                "enablePullDownRefresh": false
            }
        },
        {
            "path": "pages/rectify/rectify-template",
            "style": {
                "navigationBarTitleText": "",
                "enablePullDownRefresh": false
            }
        },
        {
            "path": "pages/web/web-page",
            "style": {
                "navigationBarTitleText": "",
                "enablePullDownRefresh": false
            }
        },
        {
            "path": "pages/support/layout",
            "style": {
                "navigationBarTitleText": "",
                "enablePullDownRefresh": false
            }
        },
        {
            "path": "pages/support/task-list",
            "style": {
                "navigationBarTitleText": "",
                "enablePullDownRefresh": false
            }
        },
        {
            "path": "pages/support/support-detail",
            "style": {
                "navigationBarTitleText": "",
                "enablePullDownRefresh": false
            }
        },
        {
            "path": "pages/support/support-question-detail",
            "style": {
                "navigationBarTitleText": "",
                "enablePullDownRefresh": false
            }
        },
        {
            "path": "pages/support/support-transfer",
            "style": {
                "navigationBarTitleText": "",
                "enablePullDownRefresh": false
            }
        },
        {
            "path": "pages/support/book-list",
            "style": {
                "navigationBarTitleText": "",
                "enablePullDownRefresh": false
            }
        },
        {
            "path": "node_modules/bowo-form/components/signature/signature-index",
            "style": {
                "navigationStyle": "custom",
                "navigationBarTitleText": "",
                "enablePullDownRefresh": false
            }
        },
        {
            "path": "node_modules/bowo-form/components/location/location-index",
            "style": {
                "navigationStyle": "custom",
                "navigationBarTitleText": "",
                "enablePullDownRefresh": false
            }
        },
        {
            "path": "node_modules/bowo-form/components/attachment/attach-preview",
            "style": {
                "navigationStyle": "custom",
                "navigationBarTitleText": "",
                "enablePullDownRefresh": false
            }
        },
        {
            "path": "node_modules/bowo-form/components/attachment/attach-video",
            "style": {
                "navigationStyle": "custom",
                "navigationBarTitleText": "",
                "enablePullDownRefresh": false
            }
        },
        {
            "path": "node_modules/bowo-form/components/contact/contact",
            "style": {
                "navigationStyle": "custom",
                "navigationBarTitleText": "",
                "enablePullDownRefresh": false
            }
        },
        {
            "path": "node_modules/bowo-form/components/template-page",
            "style": {
                "navigationStyle": "custom",
                "enablePullDownRefresh": false
            }
        },
        {
            "path": "node_modules/bowo-form/components/select/codeset-select-page",
            "style": {
                "navigationStyle": "custom",
                "enablePullDownRefresh": false
            }
        },

        {
            "path": "node_modules/bowo-form/components/select/dyna-tree-selector",
            "style": {
                "navigationStyle": "custom",
                "enablePullDownRefresh": false
            }
        },
        {
            "path": "node_modules/bowo-form/components/tree/tree-selector",
            "style": {
                "navigationStyle": "custom",
                "navigationBarTitleText": "",
                "enablePullDownRefresh": false
            }
        },
        {
            "path": "node_modules/bowo-form/components/attachment/document-previewer",
            "style": {
                // #ifdef H5 || APP-PLUS
                "navigationStyle": "custom",
                //#endif
                "navigationBarTitleText": "文档预览",
                "enablePullDownRefresh": false
            }
        },
        {
            "path": "pages/freedom/freedom",
            "style": {
                "navigationBarTitleText": "",
                "enablePullDownRefresh": false
            }
        },
        {
            "path": "pages/freedom/specialProject/specialProject",
            "style": {
                "navigationBarTitleText": "",
                "enablePullDownRefresh": false
            }
        },
        {
            "path": "pages/freedom/unlawfulAct/unlawfulAct",
            "style": {
                "navigationBarTitleText": "",
                "enablePullDownRefresh": false
            }
        },
        {
            "path": "pages/freedom/clause/clause",
            "style": {
                "navigationBarTitleText": "",
                "enablePullDownRefresh": false
            }
        },
        {
            "path": "pages/freedom/discretion/discretion",
            "style": {
                "navigationBarTitleText": "",
                "enablePullDownRefresh": false
            }
        },
        {
            "path": "pages/freedom/discretion/element",
            "style": {
                "navigationBarTitleText": "",
                "enablePullDownRefresh": false
            }
        },
        {
            "path": "pages/freedom/discretion/decide/decide",
            "style": {
                "navigationBarTitleText": "",
                "enablePullDownRefresh": false
            }
        },
        {
            "path": "pages/freedom/plot/plot",
            "style": {
                "navigationBarTitleText": "",
                "enablePullDownRefresh": false
            }
        },
        {
            "path": "pages/freedom/universal/universal",
            "style": {
                "navigationBarTitleText": "",
                "enablePullDownRefresh": false
            }
        },
        {
            "path": "pages/study/study",
            "style": {
                "navigationBarTitleText": "",
                "enablePullDownRefresh": false
            }
        },
        {
            "path": "pages/exercise/Layout",
            "style": {
                "navigationBarTitleText": ""
            }
        },
        {
            "path": "pages/exercise/exercise/Layout",
            "style": {
                "navigationBarTitleText": ""
            }
        },
        {
            "path": "pages/exercise/error/Layout",
            "style": {
                "navigationBarTitleText": ""
            }
        },
        {
            "path": "pages/exercise/exam/Layout",
            "style": {
                "navigationBarTitleText": ""
            }
        },
        {
            "path": "pages/exercise/exam/Answer",
            "style": {
                "navigationBarTitleText": ""
            }
        },
        {
            "path": "pages/exercise/myExam/Layout",
            "style": {
                "navigationBarTitleText": ""
            }
        },
        {
            "path": "pages/media/video-player",
            "style": {
                "navigationBarTitleText": "",
                "enablePullDownRefresh": false
            }
        },
        {
            "path": "pages/study/bt/bt",
            "style": {
                "navigationBarTitleText": "",
                "enablePullDownRefresh": false
            }
        },
        {
            "path": "pages/study/exam/exam",
            "style": {
                "navigationBarTitleText": "",
                "enablePullDownRefresh": false
            }
        },
        {
            "path": "pages/study/integral/integral",
            "style": {
                "navigationBarTitleText": "",
                "enablePullDownRefresh": false
            }
        },
        {
            "path": "pages/study/knowledge/knowledge",
            "style": {
                "navigationBarTitleText": "",
                "enablePullDownRefresh": false
            }
        },
        {
            "path": "pages/study/knowledge/knowledgeDetails",
            "style": {
                "navigationBarTitleText": "",
                "enablePullDownRefresh": false
            }
        },
        {
            "path": "pages/study/knowledge/searchPage",
            "style": {
                "navigationBarTitleText": "",
                "enablePullDownRefresh": false
            }
        },
        {
            "path": "pages/study/wrong/wrongQuestion",
            "style": {
                "navigationBarTitleText": "",
                "enablePullDownRefresh": false
            }
        },
        {
            "path": "pages/study/studyList/studyList",
            "style": {
                "navigationBarTitleText": "",
                "enablePullDownRefresh": false
            }
        },
        {
            "path": "pages/study/myExam/myExam",
            "style": {
                "navigationBarTitleText": "",
                "enablePullDownRefresh": false
            }
        },
        {
            "path": "pages/study/myExam/details/details",
            "style": {
                "navigationBarTitleText": "",
                "enablePullDownRefresh": false
            }
        },
        {
            "path": "pages/study/collect/collect",
            "style": {
                "navigationBarTitleText": "",
                "enablePullDownRefresh": false,
                "onReachBottomDistance": 50
            }
        },
        {
            "path": "pages/task/wtfx/wtfx-list",
            "style": {
                "navigationBarTitleText": "",
                "enablePullDownRefresh": false
            }
        },
        {
            "path": "pages/task/wtfx/wtfx-detail",
            "style": {
                "navigationBarTitleText": "",
                "enablePullDownRefresh": false
            }
        },
        {
            "path": "pages/task/wtfx/wtfx-recheck",
            "style": {
                "navigationBarTitleText": "",
                "enablePullDownRefresh": false
            }
        }
    ],
    "globalStyle": {
        "navigationBarTextStyle": "white",
        "navigationBarBackgroundColor": "#009bff",
        "backgroundColor": "#fbf9fe",
        "app-plus": {
            "navigationStyle": "custom"
        },
        "usingComponents": {
            "ly-tree-node": "/components/ly-tree/ly-tree-node"
        }
    }
}
