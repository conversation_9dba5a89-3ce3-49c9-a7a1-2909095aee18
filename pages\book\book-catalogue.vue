<template>
	<view class="flex-row-layout book-catalogue">
		<scroll-view 
			class="catalogue-list"
			:style="topCatalogueStyle"
			scroll-y="true">
			<template v-for="(catalogue, index) in topLevelCatalogues">
				<view 
					v-if="index > 0"
					class="list-divider"
				/>
				<catalogue-item
					:catalogue="catalogue"
					:hasChild="hasChild(catalogue)"
					:active="topCatalogueActiveStatus[index]"
					@click.native="selectTopLevelCatalogue(index, catalogue)"
				/>
			</template>
		</scroll-view>
		
		<scroll-view 
			class="catalogue-list"
			:style="subCatalogueStyle"
			v-show="showSubCatalogue"
			scroll-y="true">
			<template v-for="(catalogue, index) in subCatalogues">
				<view 
					v-if="index > 0"
					class="list-divider"
				/>
				<catalogue-item
					:hasChild="false"
					:active="subCatalogueActiveStatus[index]"
					:catalogue="catalogue"
					@click.native="selectSubLevelCatalogue(index, catalogue)"
				/>
			</template>
		</scroll-view>
	</view>
</template>

<script>
	import catalogueGroupTickIcon from '@/static/img/book/catalogue_group_tick_icon.png';
	import catalogueItem from  './catalogue-item.vue';
	
	import catalogueUtil from './catalogue.js';
	
	export default {
		components: {
			catalogueItem
		},
		
		props: {
			catalogues: {
				type: Array,
				default: () => {
					return null;
				}
			},
			
			topSelected: {
				type: Number,
				default: -1
			},
			
			subSelected: {
				type: Number,
				default: -1
			}
		},
		
		data() {
			return {
				catalogueGroupTickIcon,
				parentHeight: 600,
				topFocusIndex: -1,
				topCatalogueActiveStatus: null,
				subFocusIndex: -1,
				subCatalogueActiveStatus: null,
				topLevelCatalogues: null,
				subCatalogues: null
			}
		},
		
		computed: {
			topCatalogueStyle: function(){
				return {
					height: `${this.parentHeight}px`,
					'background-color': '#f4f4f4'
				}
			},
			
			subCatalogueStyle: function(){
				return {
					height: `${this.parentHeight}px`
				}
			},
			
			showSubCatalogue: function(){
				return this.subCatalogues && this.subCatalogues.length > 0;
			}
		},
		
		mounted(){
			let parentDom = this.$el.parentNode || this.$el.parentElement;
			this.parentHeight = parentDom.clientHeight;
			if(this.catalogues){
				//使用父组件保存的状态初始化选中状态
				this.topFocusIndex = this.topSelected;
				this.subFocusIndex = this.subSelected;
				
				//drawer关闭后，所有数据丢失，需要重新初始化
				this.initCatalogues(this.catalogues);
			}
		},
		
		watch: {
			catalogues: function(catalogues){
				//drawer第一次打开时数据初始化
				this.initCatalogues(catalogues);
			}
		},
		
		methods: {
			initCatalogues(catalogues){
				let topLevelCatalogues = catalogueUtil.resolveTopCatalogues(catalogues);
				if(topLevelCatalogues.length > 0){
					this.topLevelCatalogues = topLevelCatalogues;
					this.topCatalogueActiveStatus = new Array(topLevelCatalogues.length);
					this.topCatalogueActiveStatus.fill(false);
					
					let topSelected = this.topFocusIndex === -1 ? 0 : this.topFocusIndex;
					this.$set(this.topCatalogueActiveStatus, topSelected, true);
					
					let selectedTopCatalogue = topLevelCatalogues[topSelected];
					this.selectTopLevelCatalogue(topSelected, selectedTopCatalogue, true);
				}
			},
			
			/**
			 * 指定目录是否拥有子目录
			 * @param {Object} catalogue
			 */
			hasChild(catalogue){
				return catalogueUtil.hasChild(this.catalogues, catalogue);
			},
			
			/**
			 * 一级目录点击事件监听
			 * @param {Object} index
			 * @param {Object} catalogue
			 */
			selectTopLevelCatalogue(index, catalogue, init = false){
				if(this.topFocusIndex != -1){
					this.$set(this.topCatalogueActiveStatus, this.topFocusIndex, false);
				}
				this.topFocusIndex = index;
				this.$set(this.topCatalogueActiveStatus, index, true);
					
				this.subCatalogues = catalogueUtil.getSubCatalogues(this.catalogues, catalogue);
				this.subCatalogueActiveStatus = new Array(this.subCatalogues.length);

				let subSelected = -1;
				if(init){//关闭drawer重新打开，需要保持选中状态
					subSelected = this.subFocusIndex === -1 ? -1 : this.subFocusIndex;
				} 
				
				if(this.subCatalogues.length > 0 && subSelected !== -1){
					this.subCatalogueActiveStatus.fill(false);
					this.$set(this.subCatalogueActiveStatus, subSelected, true);
					this.subFocusIndex = subSelected;
					
					//通知父组件，以便父组件保存状态
					this.$emit('selectSub', subSelected, this.subCatalogues[subSelected]);
				}
				this.$emit('selectTop', index, catalogue);
				this.$emit('showSubCatalogue', this.subCatalogues.length > 0);
			},
			
			/**
			 * 二级目录点击事件监听
			 */
			selectSubLevelCatalogue(index, catalogue){
				if(this.subFocusIndex != -1){
					this.$set(this.subCatalogueActiveStatus, this.subFocusIndex, false);
				}
				this.subFocusIndex = index;
				this.$set(this.subCatalogueActiveStatus, index, true);
				
				//通知父组件，以便父组件保存状态
				this.$emit('selectSub', index, catalogue);
			}
		}
	}
</script>

<style scoped>
	.book-catalogue {
		background-color: #fff;
	}
	
	.catalogue-list {
		width: 240rpx;
	}
</style>
