<template>
	<Page title="台账详情">
		<template v-slot:bar>
			<NaviBar
				:title="title"
				style="position: fixed;">
				<template v-slot:option>
					<image
						style="width: 32px; height: 32px;"
						:src="menuIcon"
						mode="aspectFit"
						@click="openCatalogue"
					/>
				</template>
			</NaviBar>
		</template>
		<web-view 
			v-if="isWebPage"
			:src="webPage"
		/>
		<template-form
			v-if="isForm"
			:template="template"
			:form-data="formData"
		/>
		<dyna-list 
			v-if="isList"
			:autoload="false"
			:configId="dynaListConfigId"
			:urlParams="urlParams"
		/>
		
		<uni-drawer
			style="margin-top: 96rpx; transition: width 2s"
			ref="catalogueMenu"
			mode="left"
			:width="menuDrawerWidth"
			@change="onCatalogueDrawerChange">
			<book-catalogue 
				:catalogues="bookCatalogues"
				:topSelected="topCatalogueSelected"
				:subSelected="subCatalogueSelected"
				@selectTop="selectTop"
				@selectSub="selectSubCatalogue"
				@showSubCatalogue="showSubCatalogue"
			/>
		</uni-drawer>
	</Page>
</template>

<script>
	import Page from '@/pages/component/Page.vue';
	import NaviBar from '@/pages/component/NaviBar.vue';
	import templateForm from '@/pages/form/template-form.vue';
	import dynaList from './dyna-list.vue';
	
	import bookCatalogue from '@/pages/book/book-catalogue.vue';
	import menuIcon from '@/static/img/book/catalogue_menu_icon.png';
	
	import bookService from '@/api/standing-book-service.js';
	import formService from '@/api/form-service.js';
	import catalogueUtil from './catalogue.js';
	import uriUtil from '@/common/net/uri.js';
	
	export default {
		components: {
			Page, NaviBar, bookCatalogue, templateForm, dynaList
		},
		
		data() {
			return {
				menuIcon,
				bookId: null,
				bookCatalogues: [],
				//目录抽屉菜单宽度，因为二级目录菜单有显示和不显示情况，需要动态调整
				menuDrawerWidth: 120,
				//一级目录选中项索引，为目录菜单关闭再打开还原使用
				topCatalogueSelected: -1,
				//二级目录 选中项索引，为目录菜单关闭再打开还原使用
				subCatalogueSelected: -1,
				//当前选中目录
				selectedCatalogue: null,
				//目录菜单显示与否
				showCatalogue: false,
				isWebPage: false,
				isList: false,
				isForm: false,
				webPage: 'https://www.baidu.com',
				businessId: '91530100MA6K3EB438001C',
				//目录详情为动态表单时的表单配置
				template: {},
				//目录详情为动态表单时的表单数据
				formData: {},
				dynaListConfigId: '',
				urlParams: null
			}
		},
		
		computed: {
			title: function(){
				if(this.selectedCatalogue){
					return this.selectedCatalogue.CDMC;
				}
				return '台账详情';
			}
		},
		
		onLoad(options) {
			this.bookId = options.bookId;
			this.urlParams = options;
		},
		
		mounted() {
			this.loadBookMenu();
		},
		
		methods: {
			/**
			 * 加载台账目录菜单
			 */
			loadBookMenu(){
				bookService.getBookCatalogueTree(this.bookId)
					.then(resp => {
						// console.log(`获取台账目录：${JSON.stringify(resp, null, 4)}`)
						this.bookCatalogues = resp;
						this.onCatalogueLoaded(resp);
					})
					.catch(error => {
						console.log(`获取台账目录出错：${error}`)
					})
			},
			
			onCatalogueLoaded(catalogues){
				let topCatalogues = catalogueUtil.resolveTopCatalogues(catalogues);
				if(topCatalogues.length > 0){
					let firstTopCatalogue = topCatalogues[0];
					let defaultCatalogue = firstTopCatalogue;
					if(catalogueUtil.hasChild(catalogues, firstTopCatalogue)){
						let subCatalogues = catalogueUtil.getSubCatalogues(catalogues, firstTopCatalogue);
						defaultCatalogue = subCatalogues[0];
					}
					this.selectedCatalogue = defaultCatalogue;
					this.loadDetailConfig(defaultCatalogue);
					
				}
			},
			
			/**
			 * 显示目录菜单侧边栏
			 */
			openCatalogue(){
				this.$refs.catalogueMenu.open();
			},
			
			/**
			 * 目录菜单显示、隐藏回调
			 * @param {Object} visible
			 */
			onCatalogueDrawerChange(visible){
				this.showCatalogue = visible;
			},
			
			/**
			 * 目录菜单显示二级目录回调
			 * @param {Object} visible
			 */
			showSubCatalogue(visible){
				this.menuDrawerWidth = visible ? 240 : 120;
			},
			
			/**
			 * 目录一级菜单点击回调事件
			 * @param {Object} index
			 * @param {Object} catalogue
			 */
			selectTop(index, catalogue){
				if(this.selectedCatalogue.XH === catalogue.XH){
					return;
				}
				this.topCatalogueSelected = index;
				if(!catalogueUtil.hasChild(this.bookCatalogues, catalogue)){
					this.$refs.catalogueMenu.close();
					this.selectedCatalogue = catalogue;
					this.loadDetailConfig(catalogue);
				}
			},
			
			/**
			 * 目录二级菜单点击回调事件
			 * @param {Object} index
			 * @param {Object} catalogue
			 */
			selectSubCatalogue(index, catalogue){
				if(this.selectedCatalogue.XH === catalogue.XH){
					return;
				}
				this.selectedCatalogue = catalogue;
				this.subCatalogueSelected = index;
				this.loadDetailConfig(catalogue);
				this.$refs.catalogueMenu.close();
			},
			
			/**
			 * 加载台账目录详情配置
			 * @param {Object} catalogue
			 */
			loadDetailConfig(catalogue){
				bookService.getBookCatalogueConfig(catalogue.XH)
					.then(config => {
						let link = config.CDLJ;
						if(link.startsWith('/dynamicform')){
							this.isForm = true;
							this.isWebPage = false;
							this.isList = false;
							this.loadTemplateForm(link);
						} else if(link.startsWith('/platform/component/queryservice/analysis/analysiscontroller/showview')) {
							this.isForm = false;
							this.isWebPage = false;
							this.isList = true;
							this.updateDynamicListConfigId(link);
						} else {
							this.isForm = false;
							this.isWebPage = true;
							this.isList = false;
							this.loadWebPage();
						}
					})
					.catch(error => {
						console.log(`目录配置出错：${error}`)
					})
			},
			
			loadWebPage(link){
				let mockLink = '/pages/detail/hjxf';
				let params = '';
				if(this.urlParams){
					for(let p in this.urlParams){
						params += `&${p}=${this.urlParams[p]}`;
					}
					if(params.startsWith('&')){
						params = params.substring(1);
						params = `?${params}`;
					}
				}
				this.webPage = `${uriUtil.getAppHostUrl()}#${mockLink}${params}`;
			},
			
			/**
			 * 加载动态表单模板配置
			 * @param {Object} link
			 */
			loadTemplateForm(link){
				let templateId = formService.parseTemplateIdFromLink(link);
				if(templateId){
					formService.getTemplateById(templateId)
						.then(template => {
							// console.log(`动态表单模板：${JSON.stringify(template, null, 4)}`)
							this.template = template;
							this.onTemplateLoaded(templateId);
						})
						.catch(error => {
							console.log(`获取模板出错：${error}`)
						})
				} else {
					console.log(`未解析到动态表单模板ID: ${link}`)
				}
			},
			
			/**
			 * 动态表单模板加载回调方法，在加载模板后加载表单数据
			 * @param {Object} templateId
			 */
			onTemplateLoaded(templateId){
				formService.getRecordData(templateId, this.businessId)
					.then(recordData => {
						this.formData = recordData;
						// console.log(`表单数据：${JSON.stringify(recordData, null, 4)}`)
					})
					.catch(error => {
						console.log(`获取表单数据出错：${JSON.stringify(error, null, 4)}`);
					})
			},
			
			/**
			 * 加载动态列表配置
			 */
			updateDynamicListConfigId(link){
				let lastSlashIndex = link.lastIndexOf('/');
				let configId = link.substring(lastSlashIndex + 1);
				this.$nextTick(() => {
					//这里必须异步更新配置ID，否则不会触发dyna-list内的监听加载数据
					this.dynaListConfigId = configId;
				})
			}
			
		}
	}
</script>

<style>

</style>
