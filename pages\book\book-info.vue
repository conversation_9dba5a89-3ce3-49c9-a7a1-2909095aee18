<template>
	<Page :padding="false"
		  @layoutAware="onFixedContentHeight">
		<template v-slot:bar>
			<NaviBar title="执法台账">
				<template v-slot:option>
					<image @click.stop="printTemplateForm"
						   v-show=" !( templateId === EVIDENCE_TEMPLATE_ID || templateId === ELECTIC_TEMPLATE_ID )"
						   :src="record_addIcon"
						   style="width: 39rpx; height: 39rpx; margin-left: 2rpx"></image>
				</template>
			</NaviBar>
		</template>
		<ul class="pd-ultbs1">
			<li :class="{ on: infoIndex === index }"
				@click="chooseBookInfo(index)"
				v-for="(item, index) in infoList"
				:key="item.id">
				<i>{{ JSON.parse(item.modeJson).templateName }}</i>
			</li>
		</ul>
		<!-- <ul class="pd-ultbs1"
		    v-if="formTabList"
		    style="margin-top:80rpx;">
			<li :class="{on: TabCur === index}"
			    @click="changeFormData(index)"
			    v-for="(item,index) in changeFormDataList"
			    :key="item.id"><i>{{item.name}}</i></li>
		</ul> -->
		<view class="template-form-layout"
			  id="fragment"
			  style="
					height: calc(100% - 77px);
					background-color: #f4f4f4;
					text-align: center;
					overflow: auto;
					margin-top: 50px;
				">
			<template-form :form-data="formData"
						   v-if=" templateId === EVIDENCE_TEMPLATE_ID || templateId === ELECTIC_TEMPLATE_ID "
						   :record-id="recordId"
						   :constraints="constraints"
						   :template="template"
						   :parentHeight="height"
						   :editable="editable">
			</template-form>
			<view v-else>
				<ImagePrint :imageUrl="printUrlList"
						v-if="printUrlType"
						 />
			</view>
		</view>
		<NoData :type="!formTabList ? 'message' : ''" />
		<navi-bar-option-menu 
			ref="chooseMenuDialog"
			:menus="chooseFileMenus"
			@menuClick="onChooseMenuClick" 
		/>
	</Page>
</template>

<script>
	import record_addIcon from "@/static/img/record/record_add.png";
	import naviBarOptionMenu from "@/pages/component/dialog/right-top-dialog.vue";
	import ImagePrint from "@/pages/component/image-print.vue";
	import printIcon from "@/static/img/record/print2.png";
	import WucTab from "@/components/wuc-tab/wuc-tab.vue";
	import NoData from "@/components/no-data.vue";
	import formUtil from "@/pages/form/Form.js";
	import Page from "@/pages/component/Page.vue";
	import NaviBar from "@/pages/component/NaviBar.vue";
	import templateForm from "@/pages/form/template-form.vue";
	import styleUtil from "@/common/style.js";
	import printTemplate from "@/api/print-template.js";
	import {
		DOWNLOAD_URL
	} from "@/common/config.js";
	import {
		postRelated,
		queryRecordData,
		postPrintForm
	} from "@/api/record.js";

	import {
		postQueryWryjcTzInfo
	} from "@/api/book.js";

	export const EVIDENCE_TEMPLATE_ID =
		"202011161722220c3f488fabc7497d90931b5f904b9109"; //证据收集
	export const ELECTIC_TEMPLATE_ID =
		"20210316152924f0e93ad41e524f7db1b3f6f39ac7d936"; //电子归档
	export const ATTACH_DATA_KEY = "T_ATTACH";
	export default {
		components: {
			naviBarOptionMenu,
			Page,
			NaviBar,
			templateForm,
			NoData,
			WucTab,
			ImagePrint,
		},
		data() {
			return {
				EVIDENCE_TEMPLATE_ID: "202011161722220c3f488fabc7497d90931b5f904b9109",
				ELECTIC_TEMPLATE_ID: "20210316152924f0e93ad41e524f7db1b3f6f39ac7d936",
				printIcon,
				record_addIcon,
				pageHeight: -1,
				TabCur: 0,
				constraints: [],
				printData: [],
				editable: false,
				formData: {}, //表单数据
				template: {}, //动态表单模板
				chooseFormListr: {}, //可选择的基础表单
				formTabList: null, //切换时使用的基础的表单
				infoId: "", //台账表单编号--接收列表传过来的值
				infoList: [], //详情列表
				infoIndex: 0, //台账表单序号
				height: null,
				changeFormDataList: [], //切换表单用的数组
				templateId: "",
				recordId: "",
				printUrlList: [],
				downLoadUrl: "",
				printUrlType:false,//图片预览的状态
				chooseFileMenus: [{
						name: "分享",
						id: "share",
					},
					{
						name: "打印",
						id: "print",
					},
				],
			};
		},

		mounted() {
			// #ifdef APP-PLUS
			this.height = 600;
			styleUtil.getNodeLayout(this, "#fragment").then((layout) => {
				this.height = layout.height - 40;
			});
			// #endif
		},

		onLoad(option) {
			this.infoId = option.xh;

			this.getTZInfoList();
		},

		methods: {
			//切换表单类型方法
			chooseBookInfo(index) {
				this.printUrlList = [];
				this.printUrlType = false
				console.log(this.printUrlType);
				this.constraints = [];
				this.infoIndex = index;
				let reorganizeTemplate = formUtil.packPeerElementIntoGroup(
					JSON.parse(this.infoList[index].modeJson)
				);
				this.template = reorganizeTemplate;
				this.templateType = this.template.templateTable;
				this.templateId = this.template.templateId;
				this.chooseFormListr = this.infoList[this.infoIndex]; //获取的基础表单
				this.getTZFromList();
			},

			//获取台账详情基础模板以及是否有具体详情
			getTZInfoList() {
				postQueryWryjcTzInfo({
					service: "QUERY_BL_COUNT_INFO",
					xh: this.infoId,
				}).then((res) => {
					this.infoList = res.data_json;

					let reorganizeTemplate = formUtil.packPeerElementIntoGroup(
						JSON.parse(this.infoList[this.infoIndex].modeJson)
					); //获取对应渲染的模板基础
					this.template = reorganizeTemplate;
					this.chooseFormListr = this.infoList[this.infoIndex]; //获取的基础表单
					this.templateType = this.template.templateTable; //获取详情的类型
					this.getTZFromList();
				});
			},

			//获取列表具体的表单详情，通过获取基础表单中对应的模板类型里的recordID
			getTZFromList() {
				let type = this.templateType;
				this.printData = []
				this.formTabList = this.chooseFormListr[type].recordId;
				if (this.formTabList) {

					this.formTabList.forEach(element => {
						let list = {}
						list.record_id = element.XH
						list.template_id = this.chooseFormListr[type].mbbh;
						this.printData.push(list)
					});
					// console.log(this.printData,'123');
					this.templateId = this.chooseFormListr[type].mbbh;

					this.recordId = this.chooseFormListr[type].recordId[0].XH;

					this.formDataInfo(this.templateId, this.recordId);
				}
			},

			formDataInfo(mbbh, recordId) {
				uni.showLoading({
					title: "加载表单中",
				});
				this.formData = {};
				this.getFormTabList();
				
				if (mbbh !== EVIDENCE_TEMPLATE_ID && mbbh !== ELECTIC_TEMPLATE_ID) {
					
					// 判断是否是第一次打开，如果是则调接口，如果不是则走缓存
					let obj = uni.getStorageSync(this.infoId + "-" + mbbh);
					if (obj == "") {
						// 将封装图片的数组制空
						let type = false;
							printTemplate.getPrintUrl(this.printData, type)
								.then((result) => {
									for (let j = 0; j < result.length; j++) {
										this.printUrlList.push(result[j]);

										// 把装图片的数组 缓存进本地
										uni.setStorage({
											key: this.infoId + "-" + mbbh, // kay值是  台账的ID 加上  类型ID
											data: this.printUrlList,
										});
									}
									uni.hideLoading();
									this.printUrlType = true
								});
					} else {
						this.printUrlList = obj;
						uni.hideLoading();
						this.printUrlType = true
						console.log(this.printUrlType);
					}
				} else {
					postRelated({
						template_id: mbbh,
						service: "DYNAMIC_FORM_INTERACTION",
					}).then((res) => {
						res.data.forEach((element) => {
							let constraintList = {};
							constraintList.active_id = element.GLID;
							constraintList.follow_id = element.EID;
							constraintList.rule = element.JSGS;
							this.constraints.push(constraintList);
						});
						let serverRecord = queryRecordData(this.templateId, this.recordId);
						serverRecord.then((res) => {
							this.formData = res;
						});
					});
				}
			},

			//重构单独笔录的方式
			getFormTabList() {
				let list = [];
				let type = this.templateType;
				this.formTabList.forEach((element, index) => {
					let obj = {};
					obj.name =
						this.chooseFormListr[type].mbmc + (parseInt(index.toString()) + 1);
					obj.id = element.XH;
					list.push(obj);
				});
				this.changeFormDataList = list;
			},

			changeFormData(id) {
				this.TabCur = id;
				let type = this.templateType;
				this.formTabList = this.chooseFormListr[type].recordId;
				this.recordId = this.changeFormDataList[id].id;
				this.templateId = this.chooseFormListr[type].mbbh;
				this.formDataInfo(this.templateId, this.recordId);
			},

			onFixedContentHeight(layout) {
				this.pageHeight = layout.height;
			},

			onChooseMenuClick(menu) {
				if (menu.id === "share") {
					let self = this;
					postPrintForm({
							service: "PRINT_FORM",
							printlist: [{
									record_id: self.recordId,
									template_id: self.templateId,
								}
							]
						})
						.then((res) => {
							self.downLoadUrl = DOWNLOAD_URL + "?wdbh=" + res.WJID;
							// #ifdef APP-PLUS
							uni.shareWithSystem({
								// imageUrl:testIcon,
								summary: "下载连接：",
								href: self.downLoadUrl,
								header: {
									token: uni.getStorageSync('authToken'), // 这里是要添加的请求头
								},
								success() {
									// 分享完成，请注意此时不一定是成功分享
								},
								fail() {
									// 分享失败
								},
							});
							// #endif
						})
						.catch(() => {});
				}
				if (menu.id === "print") {
					printTemplate.getCallPrinter(this.printData, false);
				}
			},

			printTemplateForm() {

				this.$refs.chooseMenuDialog.show = true
			},
		},
	};
</script>

<style scoped>
	.template-form-layout {
		height: 100%;
		border-radius: 5px;
		padding: 10px;
		width: calc(100% - 20px);
		background-color: #fff;
		margin-top: 140rpx;
	}

	.template-form-tabsList {
		margin-top: 60upx;
	}

	.pd-ultbs1 {
		white-space: nowrap;
		overflow-x: scroll;
		overflow-y: hidden;
		-webkit-overflow-scrolling: touch;
	}

	.pd-ultbs1 li {
		color: white;
		height: 100%;
		font-size: 20px;
		margin: 0 12rpx;
		display: inline-block;
		vertical-align: top;
	}
</style>
