<template>
	<Page :padding="false"
	      @layoutAware="onFixedContentHeight">
		<template v-slot:bar>
			<NaviBar title="执法台账"
			         textRight=""
			         @optionClick="createNewRecordTask">
				<template v-slot:option>
					<image @click.stop="printTemplateForm"
					       :src="printIcon"
					       style="width: 39rpx; height: 39rpx;margin-left:2rpx"></image>
				</template>
			</NaviBar>
		</template>
		<ul class="pd-ultbs1">
			<li :class="{on: infoIndex === index}"
			    @click="chooseBookInfo(index)"
			    v-for="(item,index) in infoList"
			    :key="item.id"><i>{{JSON.parse(item.modeJson).templateName}}</i></li>

		</ul>
		<ul class="pd-ultbs1"
		    v-if="formTabList"
		    style="margin-top:80rpx;">
			<li :class="{on: TabCur === index}"
			    @click="changeFormData(index)"
			    v-for="(item,index) in changeFormDataList"
			    :key="item.id"><i>{{item.name}}</i></li>
		</ul>
		<view class="template-form-layout"
		      id="fragment"
		      style="height: calc(100% - 77px); background-color: #fff;">
			<template-form :form-data="formData"
			               v-if="formTabList"
						   :constraints="constraints"
			               :template="template"
			               :parentHeight="height"
			               :editable="editable">
			</template-form>
		</view>
		<NoData :type="!formTabList ? 'message':''" />
	</Page>
</template>

<script>
	import printIcon from '@/static/img/record/print2.png'
	import WucTab from '@/components/wuc-tab/wuc-tab.vue';
	import NoData from '@/components/no-data.vue'
	import formUtil from '@/pages/form/Form.js';
	import Page from '@/pages/component/Page.vue'
	import NaviBar from '@/pages/component/NaviBar.vue'
	import templateForm from '@/pages/form/template-form.vue'
	import styleUtil from '@/common/style.js'
	import printTemplate from '@/api/print-template.js';
	import { queryElementConstraints } from '@/api/record.js'
	import { postQueryWryjcTzForm, postQueryWryjcTzInfo } from '@/api/book.js'
	
	export const ATTACH_DATA_KEY = 'T_ATTACH';
	
	export default {
		components: {
			Page,
			NaviBar,
			templateForm,
			NoData,
			WucTab
		},
		data() {
			return {
				printIcon,
				pageHeight: -1,
				TabCur: 0,
				constraints:[],
				editable: false,
				formData: {}, //表单数据
				template: {}, //动态表单模板
				chooseFormListr: {}, //可选择的基础表单
				formTabList: null, //切换时使用的基础的表单
				infoId: '', //台账表单编号--接收列表传过来的值
				infoList: [], //详情列表
				infoIndex: 0, //台账表单序号
				height: null,
				changeFormDataList: [], //切换表单用的数组
				templateId: '',
				recordId: ''
			}
		},

		watch: {
			formTabList(val) {
				// console.log(val);
			}
		},

		mounted() {
			// #ifdef APP-PLUS
			this.height = 600
			styleUtil.getNodeLayout(this, '#fragment')
				.then(layout => {
					this.height = layout.height
				})
			// #endif

		},

		onLoad(option) {
			this.infoId = option.xh
			this.getTZInfoList()
		},

		methods: {
			//切换表单类型方法
			chooseBookInfo(index) {
				this.infoIndex = index;
				this.template = formUtil.packPeerElementIntoGroup(JSON.parse(this.infoList[index].modeJson))
				this.templateType = this.template.templateTable
				this.chooseFormListr = this.infoList[this.infoIndex] //获取的基础表单
				this.getTZFromList()
			},

			//获取台账详情基础模板以及是否有具体详情
			getTZInfoList() {
				postQueryWryjcTzInfo({
					service: 'QUERY_BL_COUNT_INFO',
					xh: this.infoId
				}).then((res) => {
					let records = res.data_json
					if(Array.isArray(records) && records.length === 0) {
						return
					}
					this.infoList = res.data_json
					this.template = formUtil.packPeerElementIntoGroup(JSON.parse(this.infoList[this.infoIndex].modeJson)) //获取对应渲染的模板基础
					// console.log(this.template,'template');
					this.chooseFormListr = this.infoList[this.infoIndex] //获取的基础表单
					this.templateType = this.template.templateTable //获取详情的类型
					this.getTZFromList()
				})
			},

			//获取列表具体的表单详情，通过获取基础表单中对应的模板类型里的recordID
			getTZFromList() {
				let type = this.templateType
				this.formTabList = this.chooseFormListr[type].recordId

				// console.log(this.formTabList);
				if (this.formTabList) {
					this.templateId = this.chooseFormListr[type].mbbh
					this.recordId = this.chooseFormListr[type].recordId[0].XH
					this.formDataInfo(this.templateId, this.recordId)
				}
			},

			formDataInfo(mbbh, recordId) {
				this.formData = {}
				this.getFormTabList()
				queryElementConstraints(mbbh)
					.then((res) => {
						res.data.forEach(element => {
							let constraintList = {}
							constraintList.active_id = element.GLID
							constraintList.follow_id = element.EID
							constraintList.rule = element.JSGS
							this.constraints.push(constraintList)
						});
						postQueryWryjcTzForm({
							service: 'QUERY_DYNAMICFORM_DATA',
							mbbh: mbbh,
							recordId: recordId
						}).then((res) => {
							this.formData = res.data
							this.formData[ATTACH_DATA_KEY] = res.data_fjxx
						})
					})
				
				
			},

			//重构单独笔录的方式
			getFormTabList() {
				let list = []
				let type = this.templateType
				this.formTabList.forEach((element, index) => {
					let obj = {}
					obj.name = this.chooseFormListr[type].mbmc + (parseInt(index.toString())+1)
					obj.id = element.XH
					list.push(obj)
				});
				this.changeFormDataList = list
			},

			changeFormData(id) {
				this.TabCur = id;
				let type = this.templateType
				this.formTabList = this.chooseFormListr[type].recordId
				this.recordId = this.changeFormDataList[id].id
				this.templateId = this.chooseFormListr[type].mbbh
				this.formDataInfo(this.templateId, this.recordId)
			},

			onFixedContentHeight(layout) {
				this.pageHeight = layout.height;
			},

			printTemplateForm() {
				printTemplate.getCallPrinter(this.recordId, this.templateId)
			},

			createNewRecordTask() {}
		}
	}
</script>

<style scoped>
	.template-form-layout {
		height: 100%;
		border-radius: 5px;
		padding: 10px;
		width: calc(100% - 20px);
		background-color: #fff;
		margin-top: 140rpx;
	}

	.template-form-tabsList {
		margin-top: 60upx;
	}

	.pd-ultbs1 {
		white-space: nowrap;
		overflow-x: scroll;
		overflow-y: hidden;
		-webkit-overflow-scrolling: touch;
	}

	.pd-ultbs1 li {
		color: white;
		height: 100%;
		font-size: 20px;
		margin: 0 12rpx;
		display: inline-block;
		vertical-align: top;
	}
</style>
