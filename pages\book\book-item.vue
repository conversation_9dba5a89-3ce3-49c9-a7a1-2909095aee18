<template>
	<view 
		class="flex-row-layout book-item"
		@click="showBookDetail">
		<text style="max-width: 90%;">{{order + 1}}.&nbsp;&nbsp;{{book.TZBT}}</text>
		<view
			class="navi-next"
			style="margin-left: auto;"
		/>
	</view>
</template>

<script>
	export default {
		name: 'BookItem',
		props: {
			order: {
				type: Number,
				default: 1
			},
			
			book: {
				type: Object
			}
		},
		
		methods: {
			showBookDetail(){
				uni.navigateTo({
					url: `/pages/book/book-detail?bookId=${this.book.XH}&companyPK=91530181594593236E001P&enterCode=91530181594593236E001P`
				})
			}
		}
	}
</script>

<style scoped>
	.book-item {
		padding-left: 10px;
		width: calc(100% - 10px);
		height: 42px;
		background-color: #fff;
		font-size: 16px;
	}
</style>
