<template>
  <Page :padding="false" :title="title ? title : '执法台账'">
    <section class="pd-main">
      <div>
        <view class="search-bar">
          <uni-search-bar
            v-model="searchValue"
            placeholder="请输入想要查询的污染源企业"
            cancelButton="none"
            cancelText=""
            clearButton="always"
            bgColor="#F7F7F7"
            :radius="50"
            @input="searchByKeyword"
          />
        </view>
        <view style="position: relative; z-index: 100">
          <dropdown-menu v-if="taskTypeOptions.length > 0">
            <dropdown-item :list="taskTypeOptions" v-model="taskTypeIndex" />
            <dropdown-item
              :list="deadlineTypeOptions"
              v-model="deadlineTypeIndex"
            />
          </dropdown-menu>
        </view>
        <div class="gap"></div>
        <dl class="pd-dlbx1">
          <dt>
            <span
              >台账总数：<i>（{{ total }}）</i></span
            >
          </dt>
        </dl>
        <scroll-view
          :scroll-y="true"
          @scrolltolower="loadMore"
          :style="pageListStyle"
        >
          <dl
            class="pd-dlbx1"
            v-for="(book, index) in books"
            @click="doRecordTask(book.XH)"
            :key="book.id"
          >
            <dd>
              <image
                style="z-index: 0"
                src="/static/app/images/lstic1a.png"
                class="imgic"
              ></image>
              <h2>
                {{ book.WRYMC }}
                <span class="mk" v-if="book.RWLXMC">
                  {{ book.RWLXMC }}
                </span>
              </h2>
              <p><em>创建日期：</em>{{ book.CJSJ }}</p>
              <p><em>结束时间：</em>{{ book.JSSJ || "--" }}</p>
              <small></small>
            </dd>
          </dl>
          <NoData v-if="!pageShow" :type="books.length < 1 ? 'message' : ''" />
        </scroll-view>
      </div>
    </section>
  </Page>
</template>

<script>
import NoData from "@/components/no-data.vue";
import dropdownMenu from "@/components/ms-dropdown/dropdown-menu.vue";
import dropdownItem from "@/components/ms-dropdown/dropdown-item.vue";
import Page from "@/pages/component/Page.vue";
import bookItem from "./book-task-item.vue";
import time from "@/common/timePicker.js";
import { postQueryWryjcTz } from "@/api/book.js";
export default {
  components: {
    Page,
    bookItem,
    NoData,
    dropdownMenu,
    dropdownItem,
  },

  data() {
    return {
      title: "执法台账",
      pageHeight: -1,
      timeInfo: {
        kssj: "",
        jssj: "",
      }, //时间的选择
      books: [],
      total: "",
      JCR: "",
      SFFXWT: "",
      JCWRY: "",
      taskTypeOptions: [], //任务类型集合
      deadlineTypeOptions: [
        {
          value: "all",
          text: " 结束日期",
        },
        {
          value: "week",
          text: "本周",
        },
        {
          value: "month",
          text: "本月",
        },
        {
          value: "season",
          text: "本季度",
        },
        {
          value: "year",
          text: "本年",
        },
      ],
      taskTypeIndex: 0, //任务类型选择的参数
      deadlineTypeIndex: 0, //时间选择的参数
      lastPageIndex: null, //最大页面数
      pageNum: 1, //当前的页面数量
      pageLoadType: true, //页面可滚动的状态
      pageShow: true, //页面显示的状态
      searchValue: "", //污染源关键字
    };
  },

  watch: {
    //监控改变的任务类型
    taskTypeIndex(val) {
      this.books = [];
      this.loadBookList();
    },

    //查询时间的变化
    deadlineTypeIndex(val) {
      this.books = [];
      this.changeTime(val);
    },
  },

  computed: {
    //计算页面可滚动距离的高度
    pageListStyle: function () {
      return {
        height: "calc(100vh - 390upx)",
      };
    },
  },

  onLoad(options) {
    let data = uni.getStorageSync("user_info");
    console.log(data, "123");
    this.JCR = options.JCR === "0" ? uni.getStorageSync("user_info").id : "";
    // this.JCR = uni.getStorageSync("user_info").id;
    this.SFFXWT = options.SFFXWT === "1" ? "1" : "";
    this.JCWRY = options.JCWRY === "1" ? "1" : "";
    this.title = options.TITLE;

    this.deadlineTypeIndex = 0;
    this.deadlineTypeIndex = options.timeType || 0;
    


     //获取固定编码-任务类型的值（已缓存过）
    let self = this;
    uni.getStorage({
      key: "YDZF_RWLX_DATA",
      success: function (res) {
        self.taskTypeOptions = res.data;
      },
    });

    if(this.deadlineTypeIndex == 4){
      console.log('ccccccccccc')
      this.searchValue = options.searchValue || '';
      this.timeInfo = time.timePicker('year');
      console.log(this.timeInfo)
    }else{
      this.loadBookList();
    }
   
  },

  mounted() {
    
  },

  methods: {
    //获取台账的所有数据
    loadBookList() {
      this.pageShow = true;
      this.pageLoadType = false;

      if(this.deadlineTypeIndex == 4){
        this.timeInfo = time.timePicker('year');
        console.log(this.timeInfo)
      }

      postQueryWryjcTz({
        params: {
          searchText: this.searchValue,
          RWQX_KSSJ: this.timeInfo.kssj,
          RWQX_JSSJ: this.timeInfo.jssj,
          RWLX: this.taskTypeIndex === 0 ? "" : this.taskTypeIndex,
          JCR: this.JCR,
          SFFXWT: this.SFFXWT,
          JCWRY: this.JCWRY,
          BZMC: "",
          // SFLA: ''
        },
        service: "QUERY_WRYJC_TZXX",
        pageSize: 20,
        pageNum: this.pageNum,
      })
        .then((res) => {
          this.books.push(...res.data_json.list);
          this.lastPageIndex = res.data_json.lastPage;
          this.total = res.data_json.total;
          this.pageLoadType = true;
          if (this.books.length < 1) {
            this.pageShow = false;
          }
        })
        .catch((res) => {
          this.pageLoadType = true;
          this.pageShow = false;
        });

      // bookService.getBookList()
      // 	.then(resp => {
      // 		this.books = resp.list;
      // 	})
      // 	.catch(error => {
      // 		console.log(`获取台账列表出错${JSON.stringify(error, null, 4)}`)
      // 	})
    },

    //时间的变化选择
    changeTime(val) {
      this.timeInfo = time.timePicker(val);
      this.loadBookList();
    },

    //页面跳转-跳转至台账详情页面
    doRecordTask(xh) {
      uni.navigateTo({
        url: `/pages/book/book-info?xh=${xh}`,
      });
    },

    //关键字搜索
    searchByKeyword(parms) {
      this.searchValue = parms;
      this.books = [];
      this.loadBookList();
    },

    //滚动到下一页
    loadMore() {
      if (this.pageLoadType) {
        this.pageNum++;
        if (this.pageNum > this.lastPageIndex) {
          uni.showToast({
            title: "已经没有数据了",
            duration: 2000,
            icon: "none",
          });
        } else {
          this.loadBookList();
        }
      }
    },

    //返回上一级
    backPage() {
      uni.navigateBack({
        delta: 1,
      });
    },
  },
};
</script>

<style scoped>
.book {
  width: 100%;
  background-color: #f1f2f6;
}

.mk {
  background-color: #009bff;
  font-size: 22rpx;
  padding: 2rpx 2rpx;
  color: #fff;
  border-radius: 4rpx;
  margin-left: 2rpx;
}
</style>
