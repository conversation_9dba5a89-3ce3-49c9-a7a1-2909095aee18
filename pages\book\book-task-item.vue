<template>
	<view 
		class="flex-row-layout record-task-item"
		@click="doRecordTask">
		<image 
			class="bovo-list-icon"
			mode="aspectFit"
			:src="iconType"
		/>
		<view class="flex-column-layout task-content-layout">
			<text class="bovo-list-title" style="width: 100%;">{{task.WRYMC}}</text>
			<view class="flex-row-layout" style="margin-top: 5px;">
				<text class="bovo-list-prop">任务期限：{{task.RWQX}}</text>
				<text class="bovo-list-prop task-type">{{task.RWLXMC}}</text>
				<text v-if="task.expired">超期{{task.expired}}</text>
			</view>
		</view>
		
		<image 
			class="navi-next"
		/>
	</view>
</template>

<script>
	import iconType from '@/static/img/portal/menu_task.png'
	
	import {guid} from '@/common/uuid.js' 
	
	export default {
		//获取到的企业信息
		props: {
			task: {
				type: Object,
				// default: () => {
				// 	return {
				// 		WRYMC: '任务名称',
				// 		deadline: '2020.12.31',
				// 		type: '双随机',
				// 		typeId: '0123456',
				// 		expired: ''
				// 	}
				// }
			}
		},
		
		data() {
			return {
				iconType
			}
        },


		
		methods: {
			doRecordTask() {
				uni.navigateTo({
					url: `/pages/book/book-info`
				})
            },
		}
	}
</script>

<style scoped>
	.record-task-item {
		padding: 10px 10px;
		width: calc(100% - 20px);
	}
	
	.record-task-item:active {
		background-color: #007AFF;
	}
	
	.record-task-type-icon {
		width: 36px;
		height: 36px;
	}
	
	.task-content-layout {
		flex: 1;
		margin: 0 10px;
	}
	
	.task-type {
		margin-left: auto;
		border-radius: 50px;
		padding: 2px 10px;
		color: #fff;
		background-color: #0FAEFF;
	}
</style>
