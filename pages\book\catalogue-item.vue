<template>
	<view 
		class="flex-row-layout catalogue-item"
		:class="itemClass">
		<view>
			{{catalogue.CDMC}}
		</view>
		<image 
			v-if="showChildIndicator"
			style="width: 24rpx; height: 24rpx; margin-left: auto;"
			mode="aspectFit"
			:src="naviNextIcon"
		/>
	</view>
</template>

<script>
	import naviNextIcon from '@/static/img/navi_next_icon.png';
	export default {
		name: 'CatalogueItem',
		props: {
			catalogue: {
				type: Object,
				default: () => {
					return {};
				}
			},
			
			active: {
				type: Boolean,
				default: false
			},
			
			hasChild: {
				type: Boolean,
				default: false
			}
		},
		
		data() {
			return {
				naviNextIcon
			}
		},
		
		computed: {
			itemClass: function(){
				return {
					'catalogue-item-active': this.active
				}
			},
			
			showChildIndicator: function(){
				return this.hasChild && !this.active;
			}
		},
		
		methods: {
			
		}
	}
</script>

<style scoped>
	.catalogue-item {
		width: calc(100% - 40rpx);
		line-height: 72rpx;
		padding: 0rpx 20rpx;
	}
	
	.catalogue-item-active {
		background-color: #007AFF;
		color: #fff
	}
</style>
