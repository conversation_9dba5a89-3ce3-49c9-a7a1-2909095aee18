/**
 * 确定一级目录
 */
const resolveTopCatalogues = (catalogues) => {
	let topCatalogues = catalogues.filter(item => {
		return item.SFYX === '1' && !item.hasOwnProperty('FCDXH');
 	})
	sortCatalogs(topCatalogues);
	return topCatalogues;
}

const sortCatalogs = (catalogues) => {
	catalogues.sort((first, sec) => {
		if(first.PXM === sec.PXM){
			return 0;
		} else {
			return first.PXM > sec.PXM ? 1 : -1;
		}
	});
}

/**
 * 获取指定目录的子目录
 */
const getSubCatalogues = (allCatalogues, parent) => {
	let children = allCatalogues.filter(item => {
		return item.FCDXH === parent.XH;
	});
	sortCatalogs(children);
	return children;
	
}

/**
 * 指定目录是否包含子目录
 */
const hasChild = (catalogues, catalogue) => {
	return getSubCatalogues(catalogues, catalogue).length > 0;
}

export default{
	resolveTopCatalogues,
	getSubCatalogues,
	hasChild
}