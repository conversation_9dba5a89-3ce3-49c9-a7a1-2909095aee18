<template>
	<view 
		class="dyna-list-item"
		@click="onClick">
		<view
			class="flex-row-layout dyna-list-item-attr"
			v-for="(column, index) in columnArray"
			:style="{'margin-top': (index > 1 ? '10rpx' : '0')}"
			:key="index">
			<text class="dyna-list-column-name">{{column.name}}</text>
			<text class="dyna-list-column-value">{{column.value}}</text>
		</view>
	</view>
</template>

<script>
	//需要忽略的列，通过是序号
	const IGNORE_COLUMN_NAME = 'ROW_ID';
	
	export default {
		props: {
			itemData: {
				type: Object,
				default: null
			}
		},
		
		data() {
			return {
				
			}
		},
		
		computed: {
			
			title: function(){
				return this.itemData.title || '';
			},
			
			/**
			 * 列表条目配置
			 */
			columnsConfig: function(){
				return this.itemData.config ? this.itemData.config.columnsConfig.columns : [];
			},
			
			detailConfig: function(){
				let tempDetailConfig = this.itemData.config ? this.itemData.config.detailConfig: {};
				if(typeof tempDetailConfig !== 'object'){
					tempDetailConfig = JSON.parse(tempDetailConfig);
				}
				return tempDetailConfig;
			},
			
			detailTemplateId: function(){
				return this.detailConfig.FORMID;
			},
			
			detailBusinessIdKey: function(){
				let key = 'XH';
				if(this.detailConfig){
					let urlParams = this.detailConfig.CS;
					if(urlParams){
						let paramsInArray = urlParams.split('&');
						for(let p of paramsInArray){
							let pn = p.split('=')[0];
							if(pn === 'recordId'){
								let pv = p.split('=')[1];
								key = pv;
							}
						}
					}
				}
				return key;
			},
			
			columnArray: function(){
				//对象形式的列数据
				let itemData = this.itemData.data;
				let columns = [];
				
				for(let column of this.columnsConfig){
					let name = column.name;
					if(name === IGNORE_COLUMN_NAME){
						continue;
					}
					let value = itemData[name];
					let displayName = column.displayName;
					columns.push({
						name: displayName,
						value: value
					})
				}
				return columns;
			}
		},
		
		methods: {
			getColumnStyle(index){
				let style = {
					width: '100%'
				};
				if(index > 1){
					style['margin-top'] = '10rpx';
				}
				return style;
			},
			
			onClick(){
				let templateId = this.detailTemplateId;
				if(templateId){
					let businessIdKey = this.detailBusinessIdKey;
					let businessId = this.itemData.data[businessIdKey] || '2018111514282529017dea81d644cc9eddfa7eb32c48be';
					uni.navigateTo({
						url: `/pages/form/form-previewer?title=${this.title}&templateId=${templateId}&recordId=${businessId}`
					})
				}
			}
		}
	}
</script>

<style scoped>
	.dyna-list-item {
		border-radius: 10rpx;
		padding: 20rpx;
		width: calc(100% - 40rpx);
		background-color: #fff;
	}
	
	.dyna-list-item-attr {
		width: 100%;
	}
	
	.dyna-list-column-name {
		display: inline-block;
		white-space: nowrap;
		margin-right: 20rpx;
		color: #999;
		font-size: 28rpx;
	}
	
	.dyna-list-column-value {
		margin-left: auto;
		color: #999;
		font-size: 28rpx;
	}
</style>
