<template>
	<Page :title="title">
		<dyna-list
			:title="title"
			:autoload="false"
			:configId="configId"
			:urlParams="urlParams"
		/>
	</Page>
</template>

<script>
	import Page from '@/pages/component/Page.vue';
	import dynaList from './dyna-list.vue';
	
	export default {
		components: {
			Page, dynaList
		},
		
		data() {
			return {
				title: '动态列表',
				configId: '',
				urlParams: {}
			}
		},
		
		onLoad(options) {
			this.title = options.title;
			this.configId = options.listId;
			this.urlParams = options;
		},
		
		methods: {
			
		}
	}
</script>

<style scoped> 

</style>
