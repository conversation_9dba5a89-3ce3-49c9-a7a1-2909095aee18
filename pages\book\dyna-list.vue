<!-- #ifdef MP-ALIPAY -->
<template>
	<view class="load-more-list">
		<view 
			:style="totalTipStyle">
			共<text style="color: #FBA72E; margin-right: 8px;">{{total}}条</text>
			已加载<text style="color: #FBA72E;">{{list.length}}条</text>
		</view>
		<scroll-view 
			:style="scrollListStyle"
			:scroll-y="true"
			@scrolltolower="onLoadMore">
			<loadMoreItemWrapper
				:itemVue="itemVue"
				v-for="(item, index) in list"
				:key="index"
				:item-data="item"
				:style="{'margin-top': (index === 0 ? '0' : '20rpx')}"
			/>
		</scroll-view>
		<uni-load-more
			v-show="showStatus"
			:style="loadStatusStyle"
			:class="loadStatusClass"
			:status="status"
		/>
	</view>
</template>
<!-- #endif -->
<script>
	import loadMoreList from '@/pages/component/list/load-more-list.vue';
	import dynaListItem from './dyna-list-item.vue';
	import bookService from '@/api/standing-book-service.js';
	
	export default {
		name: 'DynaList',
		extends: loadMoreList,
		components: {
			dynaListItem
		},
		
		props: {
			title: {
				type: String,
				default: '标题'
			},
			
			configId: {
				type: String
			},
			
			urlParams: {
				type: Object,
				default: () => {
					return {}
				}
			}
		},
		
		data() {
			return {
				itemVue: 'dyna-list-item',
				config: null
			}
		},
		
		mounted(){
			if(this.configId){
				this.loadDynamicListConifg(this.configId);
			}
		},
		
		watch: {
			configId: function(val){
				this.loadDynamicListConifg(val);
			}
		},
		
		methods: {
			loadMore(pageIndex){
				let _self = this;
				return new Promise((resolve, reject) => {
					bookService.getDynamicListData(this.configId, pageIndex, {
						urlParams: this.urlParams
					}).then(data => {
						_self.total = data.total;
						let listData = data.list.map(item => {
							return {
								title: _self.title,
								config: _self.config,
								data: item
							}
						});
						resolve(listData);
					}).catch(error => {
						reject(error);
						_self.log(error, `获取动态列表数据出错`)
					});
				});
			},
			
			/**
			 * 加载动态列表配置
			 */
			loadDynamicListConifg(configId){
				bookService.getDynamicListConfig(configId)
					.then(config => {
						this.config = config;
						this.refresh();
					})
					.catch(error => {
						console.log(`动态列表配置：${JSON.stringify(error, null, 4)}`)
					})
			}
		}
	}
</script>

<style>

</style>
