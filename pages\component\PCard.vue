
<template>
    <view class="power-card" :style="cardStyle">
        <view 
            class="power-card-title-block"
            v-if="showTitle">
            <slot name="mark" v-if="mark">
                <view 
                    class="power-card-title-mark" 
                    :style="markStyle"
                />
            </slot>
            <span 
                class="power-card-title" 
                :style="titleStyle">
                {{title}}        
            </span>
            <view class="power-card-option">
                <!-- 右上角插槽 -->
                <slot name="option"/>
            </view>
        </view>
		
		<view 
			class="power-card-divider"
			v-if="divider && showTitle"
		/>
		
        <!-- Card主体内容 -->
        <view 
			class="power-card-main"
			:style="contentStylePlain">
            <slot />
        </view>
    </view>
</template>

<script>
	import styleUtil from '@/common/style.js';
	
    export default {
        name: 'PCard',
        props: {
            title: {
                type: String,
                default: '标题'
            },

            titleColor: {
                type: String,
                default: '#111'
            },

            //标题字体大小，单位：px
            titleFontSize: {
                type: Number,
                default: 16
            },

            //标题左侧标记的颜色
            markColor: {
                type: String,
                default: '#1E8EEF'
            },

            mark: {
                type: Boolean,
                default: true
            },

            showTitle: {
                type: Boolean,
                default: true
            },
			
			contentStyle: {
				type: Object,
				default: () => {
					return {
						width: 'calc(100% - 20px)',
						padding: '0px 10px'
					};
				}
			},
			
			divider: {
				type: Boolean,
				default: false
			},
			
			round: {
				type: Boolean,
				default: true
			}
        },

        computed: {
			cardStyle: function() {
				let style = {
					'border-radius': this.round ? '5px' : '0'
				}
				return styleUtil.wrapStyleObject(style)
			},
			
            markStyle: function(){
                return `background: ${this.markColor}`;
            },

            titleStyle: function(){
                let styleInObject = {
                    'font-size': `${this.titleFontSize}px`,
                    color: this.titleColor,
                    'margin-left': this.mark ? '8px' : '0px' 
                }
				return styleUtil.styleObjectToString(styleInObject);
            },
			
			/**
			 * 为兼容小程序，转换为字符串
			 */
			contentStylePlain: function() {
				let style = '';
				if(this.contentStyle){
					style = styleUtil.styleObjectToString(this.contentStyle);
				}
				return style;
			}
        }
    };
</script>

<style scoped>

    .power-card {
        display: flex;
        flex-flow: column nowrap;
        justify-content: center;
        align-items: center;
		background-color: #fff;
		border-radius: 10rpx;
    }

    .power-card-title-block {
        display: flex;
        flex-flow: row nowrap;
        justify-content: flex-start;
        align-items: center;
        width: calc(100% - 20px);
        /* height: 40px; */
		padding: 8px 10px;
    }

    .power-card-title-mark {
        width: 4px;
        height: 18px;
        margin-top: 2px;
        border-radius: 5px;
    }

    .power-card-title {
        /* line-height: 40px; */
		font-size: 16px;
    }

    .power-card-option {
        margin-left: auto;
        display: flex;
        flex-flow: row nowrap;
        justify-content: flex-end;
        align-items: center;
        height: 100%;
		color: #666;
    }

    .power-card-main {
        flex-grow: 1;
        width: 100%;
    }
	
	.power-card-divider {
		width: 100%;
		height: 1px;
		background-color: #eee;
	}
</style>