<template>
	<view 
		class="flex-row-layout power-segmented-button"
		:style="segmentedStyle">
		<text 
			class="power-segmented-text"
			v-for="(option, index) in options"
			:key="index"
			:style="resolvedOptionStyle(index)"
			@click="onOptionClick(index)">
			{{option.text || option}}
		</text>
	</view>
</template>

<script>
export default {
	props: {
		borderWidth: {
			type: Number,
			default: 2
		},

		buttonWidth: {
			type: Number,
			default: 180
		},

		//边框圆角弧度
		borderRadius: {
			type: Number,
			default: 5
		},

		//边框颜色
		borderColor: {
			type: String,
			default: '#3399FF'
		},

		//正常状态填充色
		fillColor: {
			type: String,
			default: '#0191FE'
		},

		//激活状态填充色
		activeColor: {
			type: String,
			default: '#3399FF'
		},

		//文本颜色
		textColor: {
			type: String,
			default: '#fff'
		},

		//激活状态填充色
		activeTextColor: {
			type: String,
			default: '#fff'
		},

		textStyle: {
			type: Object,
			default: () => {
				return {};
			}
		},

		//选项值
		options: {
			type: Array,
			default: () => {
				return [
					{ code: 'option1', text: '选项一' },
					{ code: 'option2', text: '选项二' },
					{ code: 'option3', text: '选项三' }
				]
			}
		}
	},

	data () {
		let checkStatus = new Array(this.options.length);
		checkStatus.fill(false);
		checkStatus[0] = true;
		return {
			checkStatus
		}
	},

	computed: {
		innerBorderRadius: function () {
			return this.borderRadius - this.borderWidth;
		},

		segmentedStyle: function () {
			return {
				'border-radius': `${this.borderRadius}px`,
				border: `${this.borderWidth}px solid ${this.borderColor}`
			}
		}
	},

	methods: {
		isOptionActive (index) {
			return this.checkStatus[index];
		},

		resolvedOptionStyle (index) {
			let active = this.isOptionActive(index);
			let style = {
				color: active ? this.activeTextColor : this.textColor,
			}
			Object.assign(style, this.textStyle);
			if (index !== 0) {
				style['border-left'] = `${this.borderWidth}px solid ${this.borderColor}`;
			}

			if (index === 0) {
				style['border-radius'] = `${this.innerBorderRadius}px 0px 0px ${this.innerBorderRadius}px`;
			}

			if (index === (this.options.length - 1)) {
				style['border-radius'] = `0px ${this.innerBorderRadius}px ${this.innerBorderRadius}px 0px`;
			}

			if (active) {
				style.background = this.activeColor;
			} else {
				style.background = this.fillColor;
			}
			return style;
		},

		onOptionClick (index) {
			if (this.isOptionActive(index)) {
				return;
			}

			let preActiveIndex = this.checkStatus.indexOf(true);
			this.$set(this.checkStatus, preActiveIndex, false);
			this.$set(this.checkStatus, index, true);
			this.$emit('checkChange', this.options[index]);
		}
	}
};
</script>

<style scoped>
	.power-segmented-button {
		width: 180px;
	}

	.power-segmented-text {
		flex: 1;
		font-size: 28upx;
		padding: 5px 5px;
		text-align: center;
	}
</style>
