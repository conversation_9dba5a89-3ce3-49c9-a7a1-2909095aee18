<template>
	<view
		class="flex-row-layout power-span-options"
		:style="warpStyle">
		<template
			v-for="(option, index) in options">
			<view 
				class="power-span-option-divider"
				v-if="divider && index !== 0"
			/> 
			<view
				class="flex-column-layout power-span-option-item"
				@click="onOptionClick(option)">
				<slot v-bind:option="option">
					<text class="power-span-option-value">
						{{option.value}}
					</text>
					<text class="power-span-option-label">
						{{option.label}}
					</text>
				</slot>
			</view>
		</template>
	</view>
</template>

<script>
	export default {
		name: 'PSpanOptionsCard',
		props:{
			divider: {
				type: Boolean,
				default: true
			},
			
			height: {
				type: Number,
				default: 48
			},
			
			options: {
				type: Array,
				default: () => {
					return [
						{label: '选项一', value: 0},
						{label: '选项二', value: 0},
						{label: '选项三', value: 0}
					];
				}
			}
		},
		
		computed: {
			warpStyle: function(){
				return {
					height: `${this.height}px`
				};
			}
		},
		
		methods: {
			onOptionClick(option){
				this.$emit('optionClick', option);
			}
		}
	}
</script>

<style scoped>
	.power-span-options {
		height: 48px;
		padding: 8px 0px;
		border-radius: 3px;
		background-color: #fff;
		box-shadow: 0px 0px 5px #999;
	}
	
	.power-span-option-item {
		flex: 1;
	}
	
	.power-span-option-divider {
		width: 1px;
		height: 80%;
		background-color: #ccc;
	}
	
	.power-span-option-value {
		font-size: 16px;
		color: #1E8EEF;
	}
	
	.power-span-option-label {
		font-size: 14px;
		color: #666;
		margin-top: 5px;
	}
	
	

</style>
