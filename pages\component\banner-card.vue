<template>
	<view class="banner-card">
		<view class="flex-column-layout banner-card__title">
			<text>{{ title }}</text>
		</view>
		<view class="flex-column-layout banner-card__content">
			<slot />
		</view>
	</view>
</template>

<script>
	export default {
		name: 'Banner<PERSON><PERSON>',
		props: {
			title: {
				type: String,
				default: ''
			}
		},
		
		data() {
			return {
				
			}
		}
	}
</script>

<style>
	.banner-card {
		width: 100%;
		position: relative;
		border-radius: 10rpx;
		background-color: #fff;
	}
	
	.banner-card__title {
		position: relative;
		height: 52rpx;
		top: -10rpx;
		font-size: 16px;
		color: #fff;
		background: url(../../static/component/img/banner_card_title_bg.png) no-repeat center;
		background-size: 320rpx 52rpx;
	}
	
	.banner-card__content {
		padding: 20rpx;
		width: calc(100% - 40rpx);
		align-items: flex-start;
	}
</style>
