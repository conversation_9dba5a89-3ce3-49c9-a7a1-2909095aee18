<template>
	<view v-if="visible" class="bottom-sheet">
		<view :class="maskStyle" @click.stop="cancel"/>
		<view :class="contentClass" :style="contentStyle">
			<view v-if="operate" class="flex-row-layout sheet-operate-layout">
				<text class="power-button" @click="onCancelClick">取消</text>
				<view 
					style="margin-left: auto; margin-right: 20rpx;"
					class="power-button power-button-primary" 
					@click="onConfirmClick">
					确认
				</view>
			</view>
			<slot />
		</view>
	</view>
</template>

<script>
	import styleUtil from '@/common/style.js';
	
	export default {
		name: 'BottomSheet',
		props: {
			operate: Boolean,
			bgColor: {
				type: String,
				default: '#fff'
			}
		},
		
		data() {
			return {
				visible: false,
				showContent: false,
				timer: null
			}
		},
		
		computed: {
			maskStyle: function(){
				let classNames = 'bottom-sheet-mask';
				if(this.showContent) {
					classNames += ' bottom-sheet-mask-visible';
				}
				return classNames;
			},
			
			contentClass: function(){
				let classNames = 'bottom-sheet-content';
				if(this.showContent) {
					classNames += ' bottom-sheet-visible';
				} else {
					classNames += ' bottom-sheet-invisible';
				}
				return classNames;
			},
			
			contentStyle: function() {
				let style = {
					'background-color': this.bgColor
				}
				return styleUtil.styleObjectToString(style);
			}
		},
		
		methods: {
			show(){
				this.visible = true;
				this.setContentVisibleAsync(true);
			},
			
			dismiss(){
				this.visible = false;
				this.setContentVisibleAsync(false);
			},
			
			/**
			 * 这里必须异步的触发窗体内容的样式变更，否则窗口的过渡动画不会生效，
			 * 因为父节点的显示状态通过v-if控制，如果窗体内容的样式变更时，父容器并没有准备好
			 * @param {Object} visible
			 */
			setContentVisibleAsync(visible){
				if(this.timer !== null){
					clearTimeout(this.timer);
				}
				
				this.timer = setTimeout(() => {
					this.showContent = visible;
				}, visible ? 50 : 300);
			},
			
			cancel(){
				this.dismiss();
				this.$emit('cancel');
			},
			
			onConfirmClick() {
				this.dismiss();
				this.$emit('confirm')
			},
			
			onCancelClick() {
				this.dismiss()
				this.$emit('cancel');
			}
		}
	}
</script>

<style scoped>
	.bottom-sheet {
		position: fixed;
		top: 0;
		right: 0;
		bottom: 0;
		left: 0;
		z-index: 999;
		overflow: hidden;
	}
	
	.bottom-sheet-mask {
		position: absolute;
		width: 100%;
		height: 100%;
		background-color: rgba(0, 0, 0, .3);
		opacity: 0;
		transition: opacity .3s;
	}
	
	.bottom-sheet-content {
		position: absolute;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: #fff;
		transform: translateX(-100%);
		transition: transform .6s ease, height .6s ease;
	}
	
	.bottom-sheet-invisible {
		transform: translateY(100%);
	}
	
	.bottom-sheet-visible {
		transform: translateY(0px);
	}
	
	.bottom-sheet-mask-visible {
		opacity: 1;
	}
	
	.sheet-operate-layout {
		width: 100%;
		height: 96rpx;
		box-shadow: 0 2rpx 2rpx #ccc;
	}
</style>
