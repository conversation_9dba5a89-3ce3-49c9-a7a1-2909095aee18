<template>
	<view class="circle-progress-wrapper" :style="ringStyle">
		<view class="wrapper right" :style="ringWrapperStyle">
			<view class="circle-progress right-circle" :style="rightRotateStyle"/>
		</view>
		<view class="wrapper left" :style="ringWrapperStyle">
			<view class="circle-progress left-circle" :style="leftRotateStyle"/>
		</view>
		
		<text class="circle-progress-text" :style="textStyle">{{ mutableProgress }}%</text>
	</view>
</template>

<script>
	import styleUtil from '@/common/style.js';
	
	export default {
		props: {
			width: {
				type: Number,
				default: 200
			},
			
			ringWidth: {
				type: Number,
				default: 3
			},
			
			ringColor: {
				type: String,
				default: '#fff'
			},
			
			progress: {
				type: Number,
				default: 0
			},
			
			max: {
				type: Number,
				default: 100
			}
		},
		
		data() {
			return {
				rightStart: -135,
				leftStart: -135,
				mutableProgress: 0
			}
		},
		
		computed: {
			ringStyle: function() {
				let style = {
					width: `${this.width}px`,
					height: `${this.width}px`
				}
				return styleUtil.styleObjectToString(style);
			},
			
			ringWrapperStyle: function() {
				let style = {
					width: `${this.width / 2}px`,
					height: `${this.width}px`
				}
				return styleUtil.styleObjectToString(style);
			},
						
			percentFactor: function() {
				return Math.min(this.mutableProgress / this.max, 1);
			},
			
			rightRotateStyle: function() {
				let percentDeg = 0;
				if(this.percentFactor <= 0.5) {
					percentDeg = Math.round(180 * this.percentFactor * 2);
				} else {
					percentDeg = 180;
				}
				let rotatedDeg = this.rightStart + percentDeg;
				let style = {
					width: `${ this.width - this.ringWidth * 2 }px`,
					height: `${ this.width - this.ringWidth * 2 }px`,
					border: `${this.ringWidth}px solid transparent`,
					'border-top': `${this.ringWidth}px solid ${this.ringColor}`,
					'border-right': `${this.ringWidth}px solid ${this.ringColor}`,
					transform: `rotate(${rotatedDeg}deg)`
				}
				return styleUtil.styleObjectToString(style);
			},
			
			leftRotateStyle: function() {
				let percentDeg = 0;
				if(this.percentFactor >= 0.5) {
					percentDeg = Math.round(180 * (this.percentFactor - 0.5) * 2);
				}
				let rotatedDeg = this.leftStart + percentDeg;
				let style = {
					width: `${ this.width - this.ringWidth * 2 }px`,
					height: `${ this.width - this.ringWidth * 2 }px`,
					border: `${this.ringWidth}px solid transparent`,
					'border-left': `${this.ringWidth}px solid ${this.ringColor}`,
					'border-bottom': `${this.ringWidth}px solid ${this.ringColor}`,
					transform: `rotate(${rotatedDeg}deg)`
				}
				return styleUtil.styleObjectToString(style);
			},
			
			textStyle: function() {
				let style = {
					width: `${this.width}px`,
					height: `${this.width}px`,
					'line-height': `${this.width}px`,
					color: this.ringColor
				}
				return styleUtil.styleObjectToString(style);
			}
		},
		
		mounted() {
			this.mutableProgress = this.progress;
		},
		
		methods: {
			updateProgress(progress) {
				this.mutableProgress = progress;
			}
		}
	}
</script>

<style scoped>
	.circle-progress-wrapper {
		width: 200px;
		height: 200px;
		margin: 0 auto;
		position: relative;
	}
	
	.wrapper {
		width: 100px;
		height: 200px;
		position: absolute;
		top: 0;
		overflow: hidden;
	}
	
	.right{
		right: 0;
	}
	
	.left{
		left: 0;
	}
	
	.circle-progress {
		width: 194px;
		height: 194px;
		border: 3px solid transparent;
		border-radius: 50%;
		position: absolute;
		top: 0;
	}
	
	.right-circle {
		border-top: 3px solid green;
		border-right: 3px solid green;
		right: 0;
		transform: rotate(-135deg);
	}
	
	.left-circle {
	    border-bottom: 3px solid green;
	    border-left: 3px solid green;
	    left: 0;
		transform: rotate(-135deg);
	}
	
	.circle-progress-text {
		position: absolute;
		left: 0;
		right: 0;
		text-align: center;
		font-size: 12px;
	}
	
</style>
