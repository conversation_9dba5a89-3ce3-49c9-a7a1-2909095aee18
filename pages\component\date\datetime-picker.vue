<template>
    <bottom-sheet ref="sheet" @cancel="onSelectCancel">
        <view class="date-time-picker">
            <view class="flex-row-layout" style="margin-top: 10rpx">
                <text :class="dateBoardStyle" @click="selectDate">
                    {{ easyReadDate }}
                </text>
                <text :class="timeBoardStyle" @click="selectTime">
                    {{ time }}
                </text>
                <view
                    class="power-button power-button-primary datetime-picker-confirm"
                    @click="onSelectConfirm"
                >
                    确定
                </view>
            </view>
            <view style="height: 800rpx">
                <uni-calendar
                    v-if="showCalendar"
                    :date="date"
                    :start-date="startDate"
                    :end-date="endDate"
                    @change="onDateChange"
                />
                <time-picker
                    v-if="!showCalendar"
                    :time="time"
                    @change="onTimeChange"
                />
            </view>
        </view>
    </bottom-sheet>
</template>

<script>
import BottomSheet from '@/pages/component/bottom-sheet.vue';
import timePicker from './time-picker.vue';
import dayjs from 'dayjs';

export default {
    name: 'DatetimePicker',
    components: {
        BottomSheet,
        timePicker
    },

    props: {
        //指定选中的日期时间
        datetime: {
            type: String
        },
        startDate: {
            type: String
        },
        endDate: {
            type: String
        }
    },

    data() {
        let day = dayjs().format('YYYY-MM-DD HH:mm');
        let defaultDate = day.split(' ')[0];
        let defaultTime = day.split(' ')[1];
        return {
            //显示日期
            showCalendar: true,
            //当前日期
            date: defaultDate,
            //当前时间
            time: defaultTime
        };
    },

    computed: {
        easyReadDate: function () {
            let dateDeparts = this.date.split('-');
            return `${dateDeparts[0]}年${dateDeparts[1]}月${dateDeparts[2]}日`;
        },

        dateBoardStyle: function () {
            return `datetime-board${
                this.showCalendar ? ' datetime-board-active' : ''
            }`;
            // return {
            // 	'datetime-board-active': this.showCalendar ? true : false
            // }
        },

        timeBoardStyle: function () {
            return `datetime-board${
                this.showCalendar ? '' : ' datetime-board-active'
            }`;
            // return {
            // 	'datetime-board-active': this.showCalendar ? false : true
            // }
        },

        selectedDatetime: function () {
            return `${this.date} ${this.time}`;
        }
    },

    mounted() {
        this.resetDateAndTime(this.datetime);
    },

    methods: {
        /**
         * 重置日期和时间的值
         * @param {Object} datetime
         */
        resetDateAndTime(datetime) {
            if (datetime) {
                this.date = datetime.split(' ')[0];
                this.time = datetime.split(' ')[1];
            }
        },

        /**
         * 切换为选择日期
         */
        selectDate() {
            this.showCalendar = true;
        },

        /**
         * 切换为选择时间
         */
        selectTime() {
            this.showCalendar = false;
        },

        show() {
            this.$refs.sheet.show();
        },

        dismiss() {
            this.$refs.sheet.dismiss();
        },

        /**
         * 取消选择回调
         */
        onSelectCancel() {
            this.resetDateAndTime(this.datetime);
        },

        /**
         * 选择日期的回调
         * @param {Object} dateInfo
         */
        onDateChange(dateInfo) {
            this.date = dateInfo.fulldate;
        },

        /**
         * 选择时间的回调
         * @param {Object} time
         */
        onTimeChange(time) {
            this.time = time;
        },

        /**
         * 点击确定按钮的回调
         */
        onSelectConfirm() {
            this.dismiss();
            this.showCalendar = true;
            if (this.datetime !== this.selectedDatetime) {
                this.$emit('confirm', this.selectedDatetime);
            }
        }
    }
};
</script>

<style scoped>
.date-time-picker {
    width: 100%;
    height: 100%;
}

.datetime-board {
    color: #999;
    font-size: 32rpx;
    padding: 0 10rpx;
    line-height: 72rpx;
}

.datetime-board-active {
    color: #333;
}

.datetime-board-active:after {
    display: block;
    content: '';
    width: 100%;
    height: 4rpx;
    background-color: #333;
}

.datetime-picker-confirm {
    margin-left: auto;
    margin-right: 16rpx;
}
</style>
