<!-- 操作菜单对话框 -->
<template>
	<bottom-sheet ref="dialog" bgColor="transparent">
		<view class="operate-dialog-content">
			<!-- <scroll-view :scroll-y="true"
				             :style="pageListStyle"> -->
			<view class="flex-column-layout operate-menu-list">
				<template v-for="(menu, index) in menus">
					<view v-if="index > 0" class="list-divider" :key="menu.id"/>
					<text 
						class="operate-menu-item"
						:key="index"
						@click.stop="onMenuClick(menu)">
						{{ menu[nameKey] || menu }}
					</text>
				</template>
			</view>
			<!-- </scroll-view> -->
			<view style="height: 32rpx;"/>
			<view
				class="operate-menu-item operate-cancel" 
				@click.stop="onCancelClick">
				取消
			</view>
		</view>
	</bottom-sheet>
</template>

<script>
	import bottomSheet from '@/pages/component/bottom-sheet.vue';
	
	export default {
		name: 'OperateMenuDialog',
		components: {
			bottomSheet
		},
		
		props: {
			menus: {
				type: [Array,Object],
				default: () => {
					return [];
				}
			},
			
			nameKey: {
				type: String,
				default: 'name'
			}
		},

				computed: {
			//计算页面可滚动距离的高度
			pageListStyle: function() {
				return {
					height: 'calc(100vh - 300upx)',
				}
			}
		},
		
		methods: {
			show() {
				this.$refs.dialog.show();
			},
			
			dismiss() {
				this.$refs.dialog.dismiss();
			},
			
			onMenuClick(menu) {
				this.$refs.dialog.dismiss();
				this.$emit('menuClick', menu);
			},
			
			onCancelClick() {
				this.$refs.dialog.dismiss();
			}
		}
	}
</script>

<style scoped>
	.operate-dialog-content {
		width: calc(100% - 64rpx);
		padding: 32rpx;
	}
	
	.operate-menu-list {
		border-radius: 10rpx;
	}
	
	.operate-menu-item {
		width: 100%;
		height: 90rpx;
		line-height: 90rpx;
		text-align: center;
		font-size: 32rpx;
		color: #333;
		background-color: #fff;
	}
	
	.operate-menu-item:first-child {
		border-top-left-radius: 10rpx;
		border-top-right-radius: 10rpx;
	}
	
	.operate-menu-item:last-child {
		border-bottom-left-radius: 10rpx;
		border-bottom-right-radius: 10rpx;
	}
	
	.operate-menu-item:active {
		background-color: #007AFF;
		color: #fff;
	}
	
	.operate-cancel {
		border-radius: 10rpx;
	}
</style>
