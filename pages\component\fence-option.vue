<template>
	<view class="flex-row-layout fence-option">
		<template
			v-for="(option, index) in options">
			<view v-if="index > 0" class="fence-option__divider" :style="dividerStyle"/>
			<text 
				class="fence-option__item"
				 :key="index"
				:style="optionStyle(index)"
				@click="onOptionClick(index)">
				{{ option.name }}
			</text>
		</template>
	</view>
</template>

<script>
	export default {
		name: 'FenceOption',
		props: {
			color: {
				type: String,
				default: '#2095EF'
			},
			
			options: {
				type: Array,
				default: () => {
					return [
						{code: '', name: ''}
					]
				}
			}
		},
		
		data() {
			return {
				active: 0
			}
		},
		
		computed: {
			dividerStyle: function() {
				return {
					'background-color': this.color
				}
			}
		},
		
		methods: {
			optionStyle(index) {
				let color = this.active === index ? this.color : '#666'
				return {
					color
				}
			},
			
			onOptionClick(index) {
				if(this.active === index) {
					return
				}
				this.active = index
				this.$emit('check', index, this.options[index])
			}
		}
	}
</script>

<style scoped>
	.fence-option {
		justify-content: center;
	}
	
	.fence-option__divider {
		width: 1px;
		height: 36rpx;
	}
	
	.fence-option__item {
		padding: 20rpx 16rpx;
		font-size: 16px;
		color: #666;
	}
</style>
