<script>
	import loadMoreList from '@/pages/component/list/load-more-list.vue';
	export default {
		//继承自load-more-list
		extends: loadMoreList,
		data() {
			return {
				//指定用于展示列表Item的组件
				itemVue: ''
			}
		},
		methods: {
			/**
			 * 实现oad-more-list的loadMore方法，编写加载指定页面的数据逻辑，返回一个Promise
			 * @param {Object} pageIndex 加载指定页的数据
			 */
			loadMore(pageIndex){
				let _self = this;
				return new Promise((resolve, reject) => {
					//模拟网络异步加载数据
					setTimeout(() => {
						//必须设置数据总数
						_self.total = 98;
						
						//模板接口返回数据
						let listData = [];
						for(let i = pageIndex * 10; i < 10; i++ ){
							listData.push(i);
						};
						//把数据交给Promise处理
						resolve(listData);
					});
				});
			}
		}
	}
</script>

<style>

</style>
