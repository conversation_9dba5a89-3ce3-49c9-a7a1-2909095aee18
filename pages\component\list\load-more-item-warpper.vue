<!-- 由于小程序不支持动态组件（component）标签，只能通过这种方式适配 -->
<template>
	<dyna-list-item v-if="isDynaList" :item-data="itemData"/>
</template>

<script>
	import loadMoreListItem from './load-more-list-item.js';
	import dynaListItem from '@/pages/book/dyna-list-item.vue';
	
	export default {
		mixins: [loadMoreListItem],
		components: {
			dynaListItem
		},
		
		computed: {
			isDynaList: function() {
				return this.itemVue === 'dyna-list-item';
			}
		}
	}
</script>

<style>

</style>
