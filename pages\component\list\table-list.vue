<template>
	<view class="flex-column-layout table-list">
		<view class="flex-row-layout table-list__row table-list__header">
			<view 
				class="flex-column-layout"
				v-for="(column, index) in headers"
				:key="index"
				:style="resolveColumnWidth(index)">
				<text style="text-align: center;">{{ column.name }}</text>
			</view>
		</view>
		<view 
			class="flex-row-layout table-list__row"
			v-for="(row, rowIndex) in listData"
			:key="rowIndex">
			<view 
				class="flex-column-layout"
				v-for="(column, columnIndex) in headers"
				:key="columnIndex"
				:style="resolveColumnWidth(columnIndex)">
				<slot v-bind:row="row" v-bind:column="column">
					<text :style="resolveCellStyle(row, column)">{{ resolveCellText(row, column) }}</text>
				</slot>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		name: 'TableList',
		props: {
			headers: {
				type: Array,
				default: () => {
					return [
						{key: 'factor', name: '因子'},
						{key: 'discharge', name: '标准值'},
						{key: 'standard', name: '标准值'},
						{key: 'status', name: '状态'}
					]
				}
			},
			
			listData: {
				type: Array,
				default: () => {
					return [
						{factor: '总磷', discharge: '5.12mg/L', standard: '4mg/L', status: '超标'},
						{factor: '化学需氧量', discharge: '30.32mg/L', standard: '400mg/L', status: '正常'},
						{factor: 'PH值', discharge: '30.32mg/L', standard: '400mg/L', status: '正常'},
						{factor: '氨氮', discharge: '30.32mg/L', standard: '400mg/L', status: '正常'},
						{factor: '总氮', discharge: '30.32mg/L', standard: '400mg/L', status: '正常'}
					]
				}
			},
			
			columnWidth: {
				type: Array,
				default: () => {
					return [30, 30, 25, 15]
				}
			},
			
			cellRender: {
				type: Function,
				default: () => {
					return function(row, column) {
						return {}
					}
				}
			}
		},
		
		computed: {
			/**
			 * 有效的列宽
			 */
			effectiveColumnWidth: function() {
				if(this.columnWidth.length > 0) {
					return this.columnWidth
				}
				let effective = [this.headers.length]
				effective.fill(1)
				return effective
			}
		},
		
		methods: {
			resolveColumnWidth(index) {
				let weight = this.effectiveColumnWidth[index]
				return {
					flex: weight
				}
			},
			
			resolveCellStyle(row, column) {
				let cellStyle = this.cellRender()(row, column)
				return cellStyle
			},
			
			// 解析单元格文本
			resolveCellText(row, column) {
				let key = column.key
				return row[key]
			}
		}
	}
</script>

<style scoped>
	.table-list {
		
	}
	
	.table-list__header {
		background-color: #F0F9FF;
	}
	
	.table-list__row {
		height: 84rpx;
		font-size: 16px;
	}
</style>
