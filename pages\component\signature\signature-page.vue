<template>
    <Page title="签名" :mainStyle="mainStyle">
        <template v-slot:bar>
            <view></view>
        </template>
        <signature
            ref="signature"
            :width="width"
            :height="height"
            :hiddenCanvasHeight="hiddenCanvasHeight"
        />
        <!-- <view class="flex-row-layout" style="margin-top: 20rpx">
            <view
                class="power-button power-button-primary clear-button"
                @click="onClickClear"
                >清除</view
            >
            <view
                class="power-button power-button-primary"
                style="margin-left: auto"
                @click="onClickConfirm"
                >确定</view
            >
        </view> -->

        <view class="flex-row-layout">
            <view
                class="power-button power-button-primary clear-button2"
                @click="onClickClear"
                ><span>清除</span></view
            >
            <view
                class="power-button power-button-primary clear-button"
                @click="goBack"
                ><span>取消</span></view
            >
            <view
                class="power-button power-button-primary"
                @click="onClickConfirm"
                ><span>保存</span></view
            >
        </view>

        <uni-popup ref="tipsPop" type="dialog" :maskClick="false">
            <clean-government-cultivation-tips
                @closeDialog="closeDialog"
                @confirm="confirmAndClose"
            >
            </clean-government-cultivation-tips>
        </uni-popup>
    </Page>
</template>

<script>
import Page from '@/pages/component/Page.vue';
import signature from './signature.vue';
import CleanGovernmentCultivationTips from '../clean-government-cultivation-tips.vue';
import styleUtil from '@/common/style.js';
import { postBase64 } from '@/api/file-service.js';

export default {
    components: {
        Page,
        signature,
        CleanGovernmentCultivationTips
    },

    data() {
        return {
            signatureDialogType: false,
            recordId: '',
            signatureTelValue: '', //签名返回的电话号码
            width: 300,
            height: 220,
            pageHeight: 700,
            hiddenCanvasHeight: 1
        };
    },

    onLoad(options) {
        this.recordId = options.recordId;
        this.signatureDialogType = options.dialogType === 'true' ? true : false;
    },

    computed: {
        mainStyle: function () {
            let style = {
                width: 'calc(100% - 20px)',
                height: `${this.pageHeight}px`,
                padding: '0',
                'background-color': '#fff',
                'margin-top': '0',
                display: 'flex',
                'flex-direction': 'row-reverse',
                'align-items': 'center'
            };
            return style;
        }
    },

    mounted() {
        let _self = this;
        styleUtil.getScreenLayout().then(layout => {
            _self.width = layout.width - 20;
            _self.pageHeight = layout.height;
        });
        //在允许弹窗的情况下，省级用户自动弹出提示
        let personOrgId = uni.getStorageSync('userInfo').orgid;
        if (personOrgId === '370000' && this.signatureDialogType) {
            this.$refs.tipsPop.open();
        }
    },

    methods: {
        goBack() {
            uni.navigateBack({
                delta: 1
            });
        },
        onClickConfirm() {
            uni.showLoading({
                title: '生成中',
                mask: true
            });

            this.hiddenCanvasHeight = this.pageHeight;
            setTimeout(() => {
                this.getSignature();
            }, 500);
        },

        //生成签名
        getSignature() {
            this.$refs.signature
                .generate()
                .then(image => {
                    let id = this.recordId;
                    postBase64({
                        LXDM: 'QMFJ',
                        ZLXDM: 'QMFJ_FJ',
                        YWSJID: id,
                        base64Img: image.tempFilePath.split(',')[1]
                    }).then(uploadFileRes => {
                        let attachs = JSON.parse(uploadFileRes.wd_data);
                        let number = attachs.WDBH;
                        if (this.signatureTelValue) {
                            uni.$emit(
                                'signatureTelValue',
                                this.signatureTelValue
                            );
                        }
                        uni.$emit('signatureConfirm', number);
                        uni.navigateBack({
                            delta: 1
                        });
                    });
                })
                .catch(() => {
                    uni.hideLoading();
                    this.hiddenCanvasHeight = 1;
                    uni.showToast({
                        title: '请签名',
                        duration: 2000,
                        icon: 'none'
                    });
                });
        },

        onClickClear() {
            this.$refs.signature.reset();
        },
        //关闭弹窗
        closeDialog(val) {
            this.$refs.tipsPop.close();
            uni.navigateBack({
                delta: 1
            });
        },
        //确认已读并关闭
        confirmAndClose(val) {
            this.signatureTelValue = val.signatureTel;
            // if(val){
            // 	uni.setStorageSync(`${this.recordId}hasRead`,true)
            // }
            this.$refs.tipsPop.close();
        }
    }
};
</script>

<style scoped>
.clear-button {
    background-color: #cccccc;
    color: #fff;
    border: 1px solid #cccccc;
}
.clear-button2 {
    background-color: #ff007f;
    color: #fff;
    border: 1px solid #ff007f;
}
.power-button {
    line-height: 140rpx;
    padding: 0;
    width: 80rpx;
    height: 340rpx;
    display: flex;
    justify-content: center;
    align-items: center;
}
.power-button span {
    transform: rotate(90deg);
    display: block;
}
.flex-row-layout {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 80px;
    flex-direction: column;
    height: 90%;
}
</style>
