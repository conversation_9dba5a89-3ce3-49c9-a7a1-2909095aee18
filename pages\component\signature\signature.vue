<template>
	<view class="signacont">
		<canvas class="handWritingShadow"
				:style="`width:${hiddenCanvasHeight}px;height:${width}px;`"
				canvas-id="handWritingShadow"
				id="handWritingShadow"
		>
		</canvas>
		<canvas
			class="signature"
			id="signature"
			ref="signature"
			canvas-id="signature"
			:style="style"
			@touchstart="touchStart"
			@touchmove="touchMove"
			@touchend="touchEnd"
		/>
		
	</view>
</template>

<script>
	import styleUtil from '@/common/style.js';
	// #ifndef MP-ALIPAY
	import Handwriting from './signature.js'
	// #endif
	
	export default {
		name: 'Signature',
		props: {
			width: {
				type: Number,
				default: 300
			},
			
			hiddenCanvasHeight:{
 				type: Number
 			},
			
			height: {
				type: Number,
				default: 160
			},
			
			lineWidth: {
				type: Number,
				default: 2
			},
			
			lineColor: {
				type: String,
				default: '#000000'
			},
			
			bgColor: {
				type: String,
				default: ''
			},
			
			isCrop: {
				type: Boolean,
				default: false
			}
			
		},
		
		data() {
			return {
				hasDrew: false,
				resultImg: '',
				points: [],
				left: 0,
				top: 0,
				startX: 0,
				startY: 0,
				isDrawing: false,
				sratio: 1,
				canvasCtx: null,
				handwriting: null,
				context:null
			}
		},
		
		computed: {
			style: function() {
				let style = {
					width: 'calc(100vw - 80px)',
					height: 'calc(100vh - 30px)',
				}
				return styleUtil.styleObjectToString(style);
			},
			
			myBg: function() {
				return this.bgColor ? this.bgColor : 'rgba(255, 255, 255, 0)'
			}
		},
		
		mounted() {
			let _self = this;
			this.canvasCtx = uni.createCanvasContext('signature', this);
			this.context = uni.createCanvasContext('handWritingShadow', this);
			
			// #ifndef MP-ALIPAY
			this.handwriting = new Handwriting({
				ctx: _self.canvasCtx,
				context:_self.context,
				containerSelector: '.signature',
				lineColor: this.lineColor,
				slideValue: this.lineWidth,
				canvasName: 'signature',
				canvasNameShadow: 'handWritingShadow',
				dimenResolver: () => {
					return new Promise((resolve, reject) => {
						var query = uni.createSelectorQuery().in(_self);
						query.select('.signature').boundingClientRect(rect => {
							resolve(rect)
						}).exec();
					});
				}
			})
			// #endif
			
			this.canvasCtx.strokeStyle = this.lineColor
			this.canvasCtx.lineCap = 'round'
			this.canvasCtx.lineJoin = 'round'
			this.canvasCtx.lineWidth = this.lineWidth * this.sratio
		},
		
		watch: {
			'myBg': function (newVal) {
			  this.$refs.canvas.style.background = newVal
			}
		},
		
		methods: {
			touchStart(e) {
				this.hasDrew = true
				// #ifndef MP-ALIPAY
				this.handwriting.uploadScaleStart(e)  
				// #endif
				// #ifdef MP-ALIPAY
				e.preventDefault()
				if (e.touches.length === 1) {
					let obj = {
						x: e.changedTouches[0].x,
						y: e.changedTouches[0].y
					}
					this.drawStart(obj)
				}
				// #endif
			},
			
			touchMove(e) {
				// #ifndef MP-ALIPAY
				this.handwriting.uploadScaleMove(e) 
				// #endif
				// #ifdef MP-ALIPAY
				e.preventDefault()
				if (e.touches.length === 1) {
					let obj = {
						x: e.changedTouches[0].x,
						y: e.changedTouches[0].y
					}
					this.drawMove(obj)
				}
				// #endif
			},
			
			touchEnd(e) {
				// #ifndef MP-ALIPAY
				this.handwriting.uploadScaleEnd(e)
				// #endif
				// #ifdef MP-ALIPAY
				e.preventDefault()
				if (e.changedTouches.length === 1) {
					let obj = {
						x: e.changedTouches[0].x,
						y: e.changedTouches[0].y
					}
					this.drawEnd(obj)
				}
				// #endif
			},
			
			// 绘制
			drawStart (obj) {
				this.startX = obj.x
				this.startY = obj.y
				this.canvasCtx.beginPath()
				this.canvasCtx.moveTo(this.startX, this.startY)
				this.canvasCtx.stroke()
				this.canvasCtx.draw(true);
				this.points.push(obj)
			},
			
			drawMove (obj) {
				this.canvasCtx.moveTo(this.startX, this.startY)
				this.canvasCtx.lineTo(obj.x, obj.y)
				this.canvasCtx.stroke()
				this.canvasCtx.draw(true);
				this.startY = obj.y
				this.startX = obj.x
				this.points.push(obj)
			},
			
			drawEnd (obj) {
				this.canvasCtx.moveTo(this.startX, this.startY)
				this.canvasCtx.lineTo(obj.x, obj.y)
				this.canvasCtx.stroke()
				this.canvasCtx.draw(true);
				this.points.push(obj)
				this.points.push({x: -1, y: -1})
				
			},
			
			// 操作
			generate () {
				if (!this.hasDrew) {
					return new Promise((resolve, reject) => {
						reject('Warning: Not Signned!')
					})
				}
				// #ifndef MP-ALIPAY
				return this.handwriting.saveCanvas(this);
				// #endif
				// #ifdef MP-ALIPAY
				const pm =  new Promise((resolve, reject) => {
					var resImgData = this.canvasCtx.getImageData(0, 0, this.$refs.signature.width, this.$refs.signature.height)
					this.canvasCtx.globalCompositeOperation = "destination-over"
					this.canvasCtx.fillStyle = this.myBg
					this.canvasCtx.fillRect(0,0,this.$refs.signature.width ,this.$refs.signature.height)
					this.resultImg = this.$refs.signature.toDataURL()
					var resultImg = this.resultImg
					this.canvasCtx.clearRect(0, 0, this.$refs.signature.width ,this.$refs.signature.height)
					this.canvasCtx.putImageData(resImgData, 0, 0)
					this.canvasCtx.globalCompositeOperation = "source-over"
					if (this.isCrop) {
						const crop_area = this.getCropArea(resImgData.data)
						var crop_canvas = document.createElement('canvas')
						const crop_ctx = crop_canvas.getContext('2d')
						crop_canvas.width = crop_area[2] - crop_area[0]
						crop_canvas.height = crop_area[3] - crop_area[1]
						const crop_imgData = this.canvasCtx.getImageData(...crop_area)
						crop_ctx.globalCompositeOperation = "destination-over"
						crop_ctx.putImageData(crop_imgData, 0, 0)
						crop_ctx.fillStyle = this.myBg
						crop_ctx.fillRect(0, 0, crop_canvas.width , crop_canvas.height)
						resultImg = crop_canvas.toDataURL()
						crop_canvas = null
					}
					resolve(resultImg)
				})
				return pm
				// #endif
			},
			
			reset () {
				this.canvasCtx.clearRect(
					0,
					0,
					this.width,
					this.height
				)
				this.$emit('update:bgColor', '')
				this.points = []
				this.hasDrew = false
				this.resultImg = ''
				this.canvasCtx.draw()
			},
			
			getCropArea (imgData) {
				var topX = this.width; 
				var btmX = 0; 
				var topY = this.height; 
				var btnY = 0
				for (var i = 0; i < this.width; i++) {
					for (var j = 0; j < this.height; j++) {
						var pos = (i + this.width * j) * 4
						if (imgData[pos] > 0 || imgData[pos + 1] > 0 || imgData[pos + 2] || imgData[pos + 3] > 0) {
							btnY = Math.max(j, btnY)
							btmX = Math.max(i, btmX)
							topY = Math.min(j, topY)
							topX = Math.min(i, topX)
						}
					}
				}
				topX++
				btmX++
				topY++
				btnY++
				const data = [topX, topY, btmX, btnY]
				return data
			}
		}
	}
</script>

<style scoped>
	
	.signature {
		border: 1px solid #008EF6;
		border-radius: 5px;
	}
	.signacont{
		display: flex;
		justify-content: center;
	}
</style>
