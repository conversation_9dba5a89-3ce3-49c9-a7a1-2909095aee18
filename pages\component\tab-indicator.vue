<template>
	<view class="flex-row-layout tab-indicator">
		<view 
			v-for="(option, index) in options"
			:key="index"
			:class="resolveTabClass(index)"
			@click.stop="onTabClick(index)">
			<slot v-bind:option="option"/>
		</view>
	</view>
</template>

<script>
	export default {
		name: 'TabIndicator',
		props: {
			options: {
				type: Array,
				default: () => {
					return []
				}
			}
		},
		
		data() {
			return {
				active: 0
			}
		},
		
		methods: {
			onTabClick(index) {
				if(this.active === index) {
					return
				}
				this.active = index
				this.$emit('onTabSelected', this.active, this.options[index])
			},
			
			resolveTabClass(index) {
				return this.active === index ? 'tab-indicator__item-active' : 'tab-indicator__item'
			}
		}
	}
</script>

<style scoped>
	.tab-indicator {
		background-color: #fff;
	}
	
	.tab-indicator__item {
		display: flex;
		flex-flow: column;
		align-items: center;
		flex: 1;
	}
	
	.tab-indicator__item-active {
		display: flex;
		flex-flow: column;
		align-items: center;
		flex: 1;
		color: #0077FF;
	}
	
	.tab-indicator__item-active::after {
		content: "";
		background-color: #0077FF;
		width: 100%;
		height: 2px;
	}
</style>
