<template>
	<view class="flex-row-layout tag-group">
		<text 
			class="tag-group__tag"
			v-for="(tag, index) in tags"
			:key="index"
			:style="resolveTagStyle(index)"
			@click="onTagClick(index)">
			{{ tag.name }}
		</text>
	</view>
</template>

<script>
	export default {
		name: 'TagGroup',
		props: {
			color: {
				type: String,
				default: '#2095EF'
			},
			
			tagRadius: {
				type: [Number, String],
				default: '50px'
			},
			
			tags: {
				type: Array,
				default: () => {
					return [
						{code: 'TP', name: '总磷'},
						{code: 'COD', name: '化学需氧量'}
					]
				}
			},
			
			multiCheck: {
				type: Boolean,
				default: false
			}
		},
		
		data() {
			return {
				actives: []
			}
		},
		
		computed: {
			tagFixedStyle: function() {
				let style = {
					color: this.color,
					border: `1px solid ${this.color}`
				}
				let radius = typeof this.tagRadius === 'string' ? this.tagRadius : `${this.tagRadius}px`
				style['border-radius'] = radius
				return style
			}
		},
		
		mounted() {
			this.resetActives()
		},
		
		watch: {
			tags: function(val) {
				this.resetActives()
			}
		},
		
		methods: {
			resetActives() {
				this.actives = [this.tags.length]
				this.actives.fill(false)
				if(this.actives.length > 0) {
					this.actives[0] = true
				}
			},
			
			resolveTagStyle(index) {
				let fixedStyle = Object.assign({}, this.tagFixedStyle)
				let active = this.actives[index]
				if(active) {
					fixedStyle.color = '#FFF'
					fixedStyle['background-color'] = this.color
				}
				if(index > 4) {
					fixedStyle['margin-top'] = '10px'
				}
				return fixedStyle
			},
			
			onTagClick(index) {
				let active = this.actives[index]
				if(!this.multiCheck) {
					this.actives.fill(false)
				}
				let toggleActive = !active
				this.actives.splice(index, 1, toggleActive)
				this.$emit('check', index, toggleActive, this.tags[index])
			}
		}
	}
</script>

<style scoped>
	.tag-group {
		width: auto;
		justify-content: center;
		flex-wrap: wrap;
	}
	
	.tag-group__tag {
		padding: 10rpx 20rpx;
		font-size: 16px;
	}
	
	.tag-group__tag:not(:first-child) {
		margin-left: 20rpx;
	}
</style>
