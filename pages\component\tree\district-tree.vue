<template>
	<Page id="tree" title="请选择" style="width: 100%;" ref="page" :mainStyle="mainStyle">
		<template v-slot:bar>
			<NaviBar
				title="请选择"
				textRight="确定"
				@optionClick="onSelectConfirm"
			/>
		</template>
		<!-- #ifndef MP -->
			<ly-tree
				ref="districtTree"
				lazy
				v-if="isReady"
				node-key="code"
				:props="props"
				:show-radio="!multiCheck"
				:show-checkbox="multiCheck"
				:default-checked-keys="defaultCheckKeys"
				:expand-on-check-node="true"
				:checkOnClickNode="false"
				:load="loadDistricts"
				@setLazyLoader="setLazyLoader">
			</ly-tree>
		<!-- #endif -->
		<!-- #ifdef MP -->
		<scroll-view :style="treeStyle" scroll-y="true">
			<ly-tree
				ref="districtTree"
				lazy
				v-if="isReady"
				node-key="code"
				:props="props"
				:show-radio="!multiCheck"
				:show-checkbox="multiCheck"
				:default-checked-keys="defaultCheckKeys"
				:expand-on-check-node="true"
				:checkOnClickNode="false"
				:load="loadDistricts"
				@setLazyLoader="setLazyLoader">
			</ly-tree>
		</scroll-view>
		<view 
			id="confirm"
			style="height: 80rpx; line-height: 80rpx;z-index: 999; border-radius: 0px;"
			class="power-button power-button-primary"
			@click="onSelectConfirm">
			确定
		</view>
		<!-- #endif -->
	</Page>
</template>

<script>
	import NaviBar from '@/pages/component/NaviBar.vue'
	import Page from '@/pages/component/Page.vue';
	import LyTree from '@/components/ly-tree/ly-tree.vue'
	
	import districtService from '@/api/district-service.js';
	import { deepCopyObject } from '@/common/merge.js';
	
	// #ifdef MP
	import styleUtil from '@/common/style.js';
	// #endif
	
	let _self = null;
	
	export default {
		components: {
			NaviBar, Page, LyTree
		},
		
		data() {
			return {
				mainStyle: {
					width: '100%',
					height: '100%',
					padding: '0'
				},
				props: {
					label: 'name',
					children: 'children',
					isLeaf: (data, node) => {
						return !data.hasChild;
					}
				},
				isReady: false,
				multiCheck: false,
				defaultCheckKeys: [],
				// #ifdef MP
				treeHeight: 680,
				confirmHeight: 42
				// #endif
			}
		},
		
		onLoad(options) {
			this.multiCheck = options.multi === "true" ? true : false;
			if(options.selected){
				this.defaultCheckKeys.push(options.selected);
			}
			_self = this;
			this.isReady = true;
		},
		
		// #ifdef MP
		computed: {
			treeStyle: function() {
				let style = {
					'background-color': '#fff'
				};
				style.height = `${this.treeHeight}px`;
				return styleUtil.styleObjectToString(style);
			}
		},
		
		mounted() {
			let layouts = [];
			layouts.push(styleUtil.getScreenLayout());
			layouts.push(styleUtil.getNodeLayout(this, '#confirm'));
			Promise.all(layouts)
				.then(layoutArray => {
					this.treeHeight = layoutArray[0].height - layoutArray[1].height;
				})
		},
		// #endif
		
		methods: {
			setLazyLoader(callback) {
				// #ifdef MP-ALIPAY
				callback(this.loadDistricts);
				// #endif
			},
			
			loadDistricts(node, treeResolve){
				if(node.level === 0){
					let chain = new Promise((resolve, reject) => {
						resolve();
					});
					chain.then(districtService.getRootDistrict)
						.then(districtService.getChildDistricts)
						.then(children => {
							treeResolve(children);
						})
				} else {
					districtService.getChildDistricts(node.data)
						.then(children => {
							treeResolve(children);
						})
				}
			},
			
			onSelectConfirm(){
				// #ifdef MP-ALIPAY
				let checkedNodes = this.$refs.page.$refs.districtTree.getCheckedNodes();
				// #endif
				
				// #ifndef MP-ALIPAY
				let checkedNodes = this.$refs.districtTree.getCheckedNodes();
				// #endif
				if(this.multiCheck){
					uni.$emit('onDistrictSelected', checkedNodes);
				} else {
					uni.$emit('onDistrictSelected', checkedNodes.length > 0 ? checkedNodes[0] : null);
				}
				uni.navigateBack({
					delta: 1
				});
			}
		}
	}
</script>
