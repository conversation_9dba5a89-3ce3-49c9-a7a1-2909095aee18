<template>
	<Page 
		id="tree" title="请选择" 
		style="width: 100%;" 
		ref="page" 
		:mainStyle="mainStyle"
		@layoutAware="onPageLayoutFiexed">
		<template v-slot:bar>
			<NaviBar
				title="请选择"
				textRight="确定"
				@optionClick="onSelectConfirm"
			/>
		</template>
		<scroll-view :style="treeStyle" scroll-y="true">
			<ly-tree
				ref="industryTree"
				style="padding: 0px;"
				lazy
				v-if="isReady"
				node-key="code"
				:props="props"
				:show-radio="!multiCheck"
				:show-checkbox="multiCheck"
				:default-checked-keys="defaultCheckKeys"
				:expand-on-check-node="true"
				:checkOnClickNode="false"
				:load="loadIndustries"
				@setLazyLoader="setLazyLoader">
			</ly-tree>
		</scroll-view>
		<!-- #ifdef MP -->
		<view
			id="confirm"
			style="height: 80rpx; line-height: 80rpx;z-index: 999; border-radius: 0px;"
			class="power-button power-button-primary"
			@click="onSelectConfirm">
			确定
		</view>
		<!-- #endif -->
	</Page>
</template>

<script>
	import NaviBar from '@/pages/component/NaviBar.vue'
	import Page from '@/pages/component/Page.vue';
	import LyTree from '@/components/ly-tree/ly-tree.vue'
	
    import industryselect from '@/api/industryselect-service.js';
	import { deepCopyObject } from '@/common/merge.js';
	
	import styleUtil from '@/common/style.js';
	
	let _self = null;
	
	export default {
		components: {
			NaviBar, Page, LyTree
		},
		
		data() {
			return {
				mainStyle: {
					width: '100%',
					height: '100%',
					padding: '0'
				},
				props: {
					label: 'name',
					children: 'children',
					isLeaf: (data, node) => {
						return !data.hasChild;
					}
				},
				isReady: false,
				multiCheck: false,
				defaultCheckKeys: [],
				treeHeight: 680,
				confirmHeight: 42
			}
		},
		
		onLoad(options) {
			this.multiCheck = options.multi === "true" ? true : false;
			if(options.selected){
				this.defaultCheckKeys.push(options.selected);
			}
			_self = this;
			this.isReady = true;
		},
		 
		computed: {
			treeStyle: function() {
				let style = {
					'background-color': '#fff'
				};
				style.height = `${this.treeHeight}px`;
				return styleUtil.styleObjectToString(style);
			}
		},
		
		methods: {
			onPageLayoutFiexed(layout) {
				let confirmButtonHeight = 0
				// #ifdef MP
				styleUtil.getNodeLayout(this, '#confirm')
					.then(confirmButtonLayout => {
						if(confirmButtonLayout) {
							confirmButtonHeight = confirmButtonLayout.height
						}
					})
				// #endif
				//20是页面内边距，上下各10
				this.treeHeight = layout.height - confirmButtonHeight - 20
			},
			
			setLazyLoader(callback) {
				// #ifdef MP-ALIPAY
				callback(this.loadIndustries);
				// #endif
			},
			
			loadIndustries(node, treeResolve){
				if(node.level === 0){
					let chain = new Promise((resolve, reject) => {
						resolve();
					});
					chain.then(industryselect.getRootIndustry)
						.then(industryselect.getChildIndustry)
						.then(children => {
							treeResolve(children);
						})
				} else {
					industryselect.getChildIndustry(node.data)
						.then(children => {
							treeResolve(children);
						})
				}
			},
			
			onSelectConfirm(){
				// #ifdef MP-ALIPAY
				let checkedNodes = this.$refs.page.$refs.industryTree.getCheckedNodes();
				// #endif
				// #ifndef MP-ALIPAY
				let checkedNodes = this.$refs.industryTree.getCheckedNodes();
				// #endif
				
				if(this.multiCheck){
					uni.$emit('onDistrictSelected', checkedNodes);
				} else {
					uni.$emit('onDistrictSelected', checkedNodes.length > 0 ? checkedNodes[0] : null);
				}
				uni.navigateBack({
					delta: 1
				});
			}
		}
	}
</script>
