<template>
	<Page id="tree" title="请选择" style="width: 100%;" ref="page" :mainStyle="mainStyle">
		<template v-slot:bar>
			<NaviBar
				title="请选择"
				textRight="确定"
				@optionClick="onSelectConfirm"
			/>
		</template>
		<scroll-view scroll-y="true" style="height: calc(100vh - 96rpx);">
			<!-- #ifndef MP -->
				<ly-tree
					ref="districtTree"
					v-if="isReady"
					node-key="treeId"
					:props="props"
					:show-radio="!multiCheck"
					:show-checkbox="multiCheck"
					:default-checked-keys="defaultCheckKeys"
					:expand-on-check-node="true"
					:checkOnClickNode="false"
					:tree-data="treeData" 
					@setLazyLoader="setLazyLoader">
				</ly-tree>
			<!-- #endif -->
			<!-- #ifdef MP -->
			<scroll-view :style="treeStyle" scroll-y="true">
				<ly-tree
					ref="districtTree"
					v-if="isReady"
					node-key="treeId"
					:props="props"
					:show-radio="!multiCheck"
					:show-checkbox="multiCheck"
					:default-checked-keys="defaultCheckKeys"
					:expand-on-check-node="true"
					:checkOnClickNode="false"
					:tree-data="treeData" 
					@setLazyLoader="setLazyLoader">
				</ly-tree>
			</scroll-view>
			<view 
				id="confirm"
				style="height: 80rpx; line-height: 80rpx;z-index: 999; border-radius: 0px;"
				class="power-button power-button-primary"
				@click="onSelectConfirm">
				确定
			</view>
			<!-- #endif -->
		</scroll-view>
		
	</Page>
</template>

<script>
	import NaviBar from '@/pages/component/NaviBar.vue'
	import Page from '@/pages/component/Page.vue';
	import LyTree from '@/components/ly-tree/ly-tree.vue'
	
	import districtService from '@/api/district-service.js';
	import { deepCopyObject } from '@/common/merge.js';
	
	import { postDynamicQuery	} from '@/api/book.js'
	
	// #ifdef MP
	import styleUtil from '@/common/style.js';
	// #endif
	
	let _self = null;
	
	export default {
		components: {
			NaviBar, Page, LyTree
		},
		
		data() {
			return {
				id: '',
				treeData: [],
				mainStyle: {
					width: '100%',
					height: '100%',
					padding: '0'
				},
				props: function() {
					return {
						// 这里的label就可以使用函数进行自定义的渲染了
						label(data, node) {
							return node.data.JGSXMC;
						},
						// label: 'personName', // 指把数据中的‘personName’当做label也就是节点名称
						children: 'children' // 指把数据中的‘childs’当做children当做子节点数据
					}
				},
				isReady: false,
				multiCheck: false,
				defaultCheckKeys: [],
				// #ifdef MP
				treeHeight: 680,
				confirmHeight: 42
				// #endif
			}
		},
		
		onLoad(options) {
			this.multiCheck = options.multi === "true" ? true : false;
			if(options.selected){
				options.selected.split(',').forEach(item=>{
					this.defaultCheckKeys.push(item);
				})

			}
			_self = this;
			this.isReady = true;
			this.id = options.id;
			
			
			// console.log(this.resData);
			// ;
			postDynamicQuery({
				service:  'DYNAMIC_QUERY_TREE',
				xh: this.id
			}).then((res)=>{
				this.dealTreeData(res.datas);
			})
		},
		
		// #ifdef MP
		computed: {
			treeStyle: function() {
				let style = {
					'background-color': '#fff'
				};
				style.height = `${this.treeHeight}px`;
				return styleUtil.styleObjectToString(style);
			}
		},
		
		mounted() {
			let layouts = [];
			layouts.push(styleUtil.getScreenLayout());
			layouts.push(styleUtil.getNodeLayout(this, '#confirm'));
			Promise.all(layouts)
				.then(layoutArray => {
					this.treeHeight = layoutArray[0].height - layoutArray[1].height;
				})
		},
		// #endif
		
		methods: {
			// 处理树形结构
			dealTreeData(res){
				function findChild(arr,treeId) {
				    let childs = [];
				    arr.forEach(v=>{
				        if (v.treePid == treeId){
				            childs.push(v);
				        }
				    });
				    return childs;
				}
				function build_tree(treeId) {
				   let childs=  findChild(res,treeId);
				   if (childs.length==0){
				       return null;
				   }
				
				   // 对于父节点为0的进行循环，然后查出父节点为上面结果id的节点内容
				   childs.forEach((v,k)=>{
				        let buildTree = build_tree(v.treeId);
				        if (null != buildTree){
				            v['children'] = buildTree;
				        }
				   });
				    return childs;
				}
				
				this.treeData = build_tree(null);
			},
			
			
			
			setLazyLoader(callback) {
				// #ifdef MP-ALIPAY
				callback(this.loadDistricts);
				// #endif
			},
			
			loadDistricts(node, treeResolve){
				if(node.level === 0){
					let chain = new Promise((resolve, reject) => {
						resolve();
					});
					chain.then(districtService.getRootDistrict)
						.then(districtService.getChildDistricts)
						.then(children => {
							treeResolve(children);
						})
				} else {
					districtService.getChildDistricts(node.data)
						.then(children => {
							treeResolve(children);
						})
				}
			},
			
			onSelectConfirm(){
				
				// #ifdef MP-ALIPAY
				let checkedNodes = this.$refs.page.$refs.districtTree.getCheckedNodes();
				// #endif
				
				// #ifndef MP-ALIPAY
				let checkedNodes = this.$refs.districtTree.getCheckedNodes();
				// #endif

				if(this.multiCheck){
					uni.$emit('onTreeSelected', checkedNodes);
				} else {
					uni.$emit('onTreeSelected', checkedNodes.length > 0 ? checkedNodes[0] : null);
				}
				uni.navigateBack({
					delta: 1
				});
			}
		}
	}
</script>


<style>
	.ly-tree-node__label{
		overflow-x: auto;
	}
</style>
