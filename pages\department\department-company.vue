<!-- @format -->

<template>
    <Page title="企业列表">
        <template v-slot:bar>
            <!-- <NaviBar title="企业列表">
                <template v-slot:option>
                    <view @click="addCompany()"> 新增 </view>
                </template>
            </NaviBar> -->
        </template>
        <view class="search-view">
            <uni-search-bar
                placeholder="请输入污染源、联系人关键字"
                cancelButton="none"
                cancelText=""
                clearButton="always"
                bgColor="#F7F7F7"
                :radius="50"
                @input="handleKeyUp"
            />
        </view>
        <dl class="pd-dlbx1">
            <dt>
                <span
                    >企业数<i>（{{ total }}）</i></span
                >
            </dt>
        </dl>
        <scroll-view
            class="record-task-list"
            scroll-y="true"
            style="height: calc(100vh - 340rpx)"
            @scrolltolower="onReachBottomEvent"
            v-if="list.length > 0"
        >
            <dl class="pd-dlbx1" v-for="(item, index) in list" :key="index">
                <div>
                    <dd @click="showDetail(item)">
                        <image
                            src="../../static/app/images/lstic1a.png"
                            class="imgic"
                        />
                        <h2 v-html="item.WRYMC">{{ item.WRYMC }}</h2>
                        <p><em>地址：</em><span v-html="item.WRYDZ"></span></p>
                        <!-- <small></small> -->
                    </dd>
                    <dd>
                        <ul class="pd-ulbx2">
                            <!-- <li><i class="lsts1">导航</i></li> -->
                            <!-- <li><i class="lsts2">执法检查</i></li> -->
                        </ul>
                    </dd>
                </div>
            </dl>
        </scroll-view>
        <noData v-if="noDataType"></noData>
    </Page>
</template>

<script>
import NaviBar from '@/pages/component/NaviBar.vue';
import noData from '@/components/no-data.vue';
import Page from '@/pages/component/Page.vue';
import { postQueryWryjcTz } from '@/api/record.js';
import noDataAdd from '@/components/no-data-add.vue';
export default {
    components: {
        noDataAdd,
        Page,
        noData,
        NaviBar
    },
    data() {
        return {
            noDataType: false,
            search: '',
            type: 'WRY',
            pageSize: 10,
            pageNum: 1,
            list: [],
            total: 0,
            searchDebounceTimer: null
        };
    },
    // watch: {
    // 	search: 'handleKeyUp'
    // },

    onLoad(options) {
        this.id = options.id;
    },

    mounted() {
        this.searchData();
    },
    methods: {
        showDetail(item) {
            uni.$emit('form_companyData', item);
            uni.navigateBack({
                delta: 1
            });
        },
        back() {
            uni.navigateBack();
        },

        handleKeyUp(params) {
            let self = this;
            if (this.searchDebounceTimer) {
                clearTimeout(self.searchDebounceTimer);
            }
            self.searchDebounceTimer = self.startKeywordSearch(params);
        },

        /**
         * 触发关键字过滤搜索
         */
        startKeywordSearch(params) {
            let self = this;
            return setTimeout(() => {
                self.searchDebounceTimer = null;
                self.search = params.value;
                self.pageNum = 1;
                self.list = [];
                self.searchData();
            }, 400);
        },

        onReachBottomEvent() {
            if (!this.reachType) {
                this.pageNum++;
                this.searchData();
            }
        },

        searchData() {
            this.noDataType = false;

            postQueryWryjcTz({
                searchText: this.search,
                service: 'QUERY_WRYXX_SERVICE',
                pageSize: 20,
                pageNum: this.pageNum
            }).then(res => {
                if (this.pageNum > res.data_json.pages) {
                    this.total = res.data_json.total;
                    this.reachType = true;
                } else {
                    this.list.push(...res.data_json.list);
                    this.total = res.data_json.total;
                    if (this.list.length === 0) {
                        this.noDataType = true;
                    }
                }
            });
        }
    }
};
</script>

<style>
.pd-main {
    width: 100vw;
}

.pd-dlbx1 + .pd-dlbx1 {
    margin-top: 0px;
}
</style>
