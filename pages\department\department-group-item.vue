<template>
	<view 
		class="flex-row-layout department-group-item"
		@click.stop="onGroupClick">
		<radio 
			v-if="checkable && !multiCheck" 
			class="power-checkbox"
			:checked="checked"
			@click.stop="clickCheckbox"
		/>
		<checkbox 
			v-if="checkable && multiCheck" 
			class="power-checkbox"
			:checked="checked"
			@click.stop="clickCheckbox"
		/>
		<view class="department-group-title">
			<span>{{nodeData.ZZJC}}</span>
			<span 
				v-if="showCount"
				style="margin-left: 10rpx;">
				({{childCount}})
			</span>
		</view>
		<text 
			v-if="selectedCount > 0"
			class="department-selected-count">
			{{selectedCount}}
		</text>
		<image 
			v-if="showCount"
			class="department-sub-indicator"
			mode="aspectFit"
			:src="iconArrow"
		/>
	</view>
</template>

<script>
	import iconArrow from '@/static/img/navi_next_icon.png';
	
	import departmentNode from './department-node.js';
	
	const mixins = [departmentNode];
	
	export default {
		// #ifdef MP-ALIPAY
		mixins: mixins.map(item => ({ ...item, props: {} })),
		// #endif
		
		// #ifndef MP-ALIPAY
		mixins: mixins,
		// #endif
		props: {
			// #ifdef MP-ALIPAY
			...mixins.reduce((prev, curr) => ({ ...prev, ...(curr.props || {}) }), {}),
			// #endif
			
			/**
			 * 子节点数量
			 */
			childCount: {
				type: Number,
				default: 0
			},
			
			//子节点选中数量
			selectedCount: {
				type: Number,
				default: 0
			},
			
			//是否可选，选择人员时不可选
			checkable: {
				type: Boolean,
				default: false
			}
		},
		
		data() {
			return {
				iconArrow,
				nodeKey: 'ZZBH'
			}
		},
		
		computed: {
			showCount: function(){
				return !this.checkable && this.childCount > 0;
			}
		},
		
		methods: {
			clickCheckbox(){
				this.$emit('checkChange', this.nodeData, !this.checked);
			},
			
			onGroupClick() {
				this.$emit('groupClick', this.nodeData);
			}
		}
	}
</script>

<style scoped>
	.department-group-item {
		width: calc(100% - 40rpx);
		height: 64rpx;
		padding: 10rpx 20rpx;
		white-space: nowrap;
	}
	
	.department-group-item:active {
		background-color: #0077FF;
	}
	
	.department-group-title {
		flex: 1;
		font-size: 16px;
		color: #333;
		overflow: hidden;
		text-overflow: ellipsis;
		margin-right: 20rpx;
	}
	
	.department-group-title:active {
		color: #fff;
	}
	
	.department-sub-indicator {
		width: 32rpx;
		height: 32rpx;
	}
	
	.department-selected-count {
		width: 42rpx;
		line-height: 42rpx;
		margin-right: 10rpx;
		border-radius: 100rpx;
		text-align: center;
		background-color: #007AFF;
		color: #fff;
		font-size: 28rpx;
	}
</style>
