<template>
	<view class="flex-row-layout department-item" @click.stop="checkChange">
		<radio
			v-if="!multiCheck"
			style="zoom: 68%;"
			:checked="checked"
		/>
		<checkbox
			v-if="multiCheck"
			style="zoom: 68%;"
			:checked="checked"
		/>
		<text class="department-item-name">{{nodeData.YHMC}}</text>
	</view>
</template>

<script>
	import departmentNode from './department-node.js';
	
	const mixins = [departmentNode];
	
	export default {
		// #ifdef MP-ALIPAY
		mixins: mixins.map(item => ({ ...item, props: {} })),
		// #endif
		
		// #ifndef MP-ALIPAY
		mixins: mixins,
		// #endif
		
		props: {
			// #ifdef MP-ALIPAY
			...mixins.reduce((prev, curr) => ({ ...prev, ...(curr.props || {}) }), {}),
			// #endif
			
			nodeData: {
				type: Object,
				default: () => {
					return {
						YHID: '部门编号',
						YHMC: '用户名称',
						BMBH: '父部门Id',
						ZZJC: '组织名称',
						order: 999
					};
				}
			}
		},
		
		data() {
			return {
				nodeKey: 'YHID'
			}
		},
		
		methods: {
			checkChange(){
				this.$emit('checkChange', this.nodeData, !this.checked);
			}
		}
	}
</script>

<style scoped>
	.department-item {
		padding: 0px 10px;
		width: calc(100% - 20px);
		height: 84rpx;
	}
	
	.department-item:active {
		background-color: #0077FF;
	}
	
	.department-item-name {
		flex: 1;
		margin-left: 10px;
		font-size: 16px;
		color: #333;
	}
	
	.department-item-name:active {
		color: #fff;
	}
</style>
