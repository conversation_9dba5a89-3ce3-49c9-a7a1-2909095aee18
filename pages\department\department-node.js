export default {
	props: {
		//节点绑定的数据
		nodeData: {
			type: Object
		},
		
		checkedNodes: {
			type: Array,
			default: () => {
				return [];
			}
		},
		
		multiCheck: {
			type: Boolean,
			default: false
		}
	},
	
	data(){
		return {
			nodeKey: ''
		}
	},
	
	computed: {
		nodeId: function() {
			return this.nodeData[this.nodeKey];
		},
		
		checked: function() {
			let idIndex = this.checkedNodes.indexOf(this.nodeId);
			return idIndex !== -1;
		}
	}
}