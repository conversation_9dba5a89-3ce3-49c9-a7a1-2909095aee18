<template>
    <Page class="power-page" id="orgPage" :title="title" :mainStyle="mainStyle">
        <!-- #ifdef H5 || APP-PLUS -->
        <template v-slot:bar>
            <NaviBar
                style="position: fixed"
                :title="title"
                textRight="确定"
                @naviClick="selectCancel"
                @optionClick="selectConfirm"
            />
        </template>
        <!-- #endif -->

        <!-- 搜索条件及导航 -->

        <view
            style="
                background-color: #fff;
                position: fixed;
                width: 100%;
                height: 100px;
                z-index: 9;
            "
        >
            <uni-search-bar
                placeholder="请输入部门或用户名称关键字"
                cancelButton="none"
                cancelText=""
                clearButton="always"
                bgColor="#F7F7F7"
                :radius="50"
                @input="searchByKeyword"
            />

            <view class="department-list">
                <scroll-view
                    class="department-navi-layout"
                    :style="naviNodesLayoutStyle"
                    :scroll-x="true"
                >
                    <view class="navi-nodes-layout">
                        <template v-for="(depart, index) in naviDepart">
                            <text
                                v-if="index > 0"
                                class="department-navi-node-next"
                                >></text
                            >
                            <text
                                class="department-navi-node-text"
                                :key="index"
                                @click="naviToNode(depart)"
                            >
                                {{ depart.ZZJC }}
                            </text>
                        </template>
                    </view>
                </scroll-view>
            </view>
            <view style="width: 100%; height: 10px; background-color: #eee" />
        </view>

        <!-- 部门和用户列表 -->
        <scroll-view
            :style="scrollStyle"
            v-if="!showSearchList"
            :scroll-y="true"
        >
            <view class="department-group-list">
                <template v-for="(department, index) in currentDepartments">
                    <view class="department-list-divider" v-if="index > 0" />
                    <department-group-item
                        :key="index"
                        :checkable="!isSelectPerson"
                        :multiCheck="multiCheck"
                        :node-data="department"
                        :checked-nodes="checkedNodes"
                        :childCount="childrenAmounts[index]"
                        :selectedCount="getLeafCheckedAmount(department)"
                        @checkChange="onGroupCheckChange"
                        @groupClick="onGroupClick(department, index)"
                    />
                </template>
            </view>

            <view class="department-leaf-list" :style="leafStyle">
                <template v-for="(menber, index) in currentMenbers">
                    <view
                        v-if="currentDepartments.length > 0 || index > 0"
                        class="department-list-divider"
                    />
                    <department-item
                        :key="index"
                        :multiCheck="multiCheck"
                        :node-data="menber"
                        :checked-nodes="checkedNodes"
                        @checkChange="onLeafCheckChange"
                    />
                </template>
            </view>
        </scroll-view>

        <!-- 搜索结果列表 -->
        <scroll-view
            :style="scrollStyle"
            v-if="showSearchList"
            :scroll-y="true"
        >
            <view class="department-leaf-list" style="margin-top: 0px">
                <template v-for="(menber, index) in searchMenbers">
                    <view class="department-list-divider" v-if="index > 0" />
                    <department-item
                        :key="index"
                        :multiCheck="multiCheck"
                        :node-data="menber"
                        :checked-nodes="checkedNodes"
                        @checkChange="onLeafCheckChange"
                    />
                </template>
            </view>
        </scroll-view>

        <!-- #ifdef MP -->
        <view class="flex-row-layout select-confirm-layout">
            <text>{{ selectNodesText }}</text>
            <view
                style="margin-left: auto"
                class="power-button power-button-primary"
                @click="selectConfirm"
            >
                确定
            </view>
        </view>
        <!-- #endif -->
    </Page>
</template>

<script>
import Page from '@/pages/component/Page.vue';
import NaviBar from '@/pages/component/NaviBar.vue';
import iconArrow from '@/static/img/navi_next_icon.png';
import DepartmentGroupItem from './department-group-item.vue';
import DepartmentItem from './department-item.vue';
import orgService from '@/api/organization-service.js';
import contactService from '@/api/contact-service.js';
import styleUtil from '@/common/style.js';
import { deepCopyObject } from '@/common/merge.js';
import loginService from '@/api/login-service.js';
import {
    MODE_SELECT_PERSON,
    MODE_SELECT_DEPARTMENT,
    EVENT_SELECT_CONFIRM,
    EVENT_SELECT_CANCEL
} from './department.js';

export default {
    components: {
        Page,
        NaviBar,
        DepartmentGroupItem,
        DepartmentItem
    },

    data() {
        return {
            title: '选择部门',
            iconArrow,
            mainStyle: {
                width: '100%',
                padding: '0px',
                'background-color': '#eee'
            },
            mode: 'person',
            multiCheck: false,
            showSearchList: false,
            leafItemName: 'department-item',
            naviDepart: [],
            departments: [], //所有部门
            menbers: [], //所有用户
            departmentIndexs: {}, //部门索引
            menberIndexs: {}, //用户索引
            currentDepartments: [],
            currentMenbers: [],
            searchMenbers: [],
            parentWidth: 300,
            listHeight: 500,
            checkedNodes: [],
            childrenAmounts: [], //子节点数量
            nodeCheckedAmounts: {} //节点选中数量
        };
    },

    computed: {
        // #ifdef MP
        selectNodesText: function () {
            let nodes = this.getSelectedNodes(this.checkedNodes);
            return nodes.map(n => n.YHMC || n.ZZJC).join(',');
        },
        // #endif

        scrollStyle: function () {
            let style = {
                width: '100%',
                'margin-top': '100px',
                'background-color': '#eee',
                height: `calc(100vh - 320rpx)`
            };
            return styleUtil.styleObjectToString(style);
        },

        naviNodesLayoutStyle: function () {
            return {
                width: '100%'
            };

            return styleUtil.styleObjectToString({
                width: '100vh' - `${this.parentWidth - 20}px`
            });
        },

        leafStyle: function () {
            return styleUtil.styleObjectToString({
                'margin-top':
                    this.currentDepartments.length > 0 ? '10px' : '0px'
            });
        },

        listStyle: function () {
            let style = {
                width: '100%',
                'margin-top': '100px',
                'background-color': '#eee',
                height: `${this.listHeight}px`,
                overflow: 'scroll'
            };
            return styleUtil.styleObjectToString(style);
        },

        /**
         * 是否选择人员模式
         */
        isSelectPerson: function () {
            return MODE_SELECT_PERSON === this.mode;
        }
    },

    watch: {
        checkedNodes(val) {
            // console.log(val);
        },

        /**
         * 监听部门数据，初始化部门选择数量
         * @param {Object} allDepartments
         */
        departments(allDepartments) {
            if (this.checkedNodes.length === 0) {
                return;
            }
        },

        /**
         * 监听当前显示部门变化，修改子部门数量
         * @param {Object} departments
         */
        currentDepartments(departments) {
            let amounts = new Array(departments.length);
            amounts.fill(0);
            let departmentIdIndexs = {};
            for (let i = 0; i < departments.length; i++) {
                departmentIdIndexs[departments[i].ZZBH] = i;
            }
            this.departments.forEach(d => {
                let parentId = d.SJZZXH;
                let index = departmentIdIndexs[parentId];
                if (typeof index !== 'undefined') {
                    amounts[index]++;
                }
            });
            this.menbers.forEach(m => {
                let parentId = m.BMBH;
                let index = departmentIdIndexs[parentId];
                if (typeof index !== 'undefined') {
                    amounts[index]++;
                }
            });
            this.childrenAmounts = amounts;
        }
    },

    /**
     * 处理调用页面传递的参数
     * @param {Object} option
     */
    onLoad(option) {
        this.title = option.title || this.title;
        this.mode = option.mode || this.mode;
        let multi = option.multi || 'false';
        this.multiCheck = multi === 'true';
        let defaultNodeIds = option.checked || '';
        if (defaultNodeIds) {
            defaultNodeIds.split(',').forEach(nodeId => {
                this.checkedNodes.push(nodeId);
            });
        }
    },

    mounted() {
        let _self = this;
        // #ifdef H5 || APP-PLUS
        this.parentWidth = this.$el.clientWidth;
        let pageHeight = this.$el.clientHeight;
        //列表可用高度由页面高度减去标题栏和搜索、导航栏高度
        this.listHeight = pageHeight - 48;
        // #endif

        // #ifdef MP
        styleUtil
            .getScreenLayout()
            .then(sureLayout => {
                if (sureLayout) {
                    this.parentWidth = sureLayout.width;
                    //列表高度需要减去搜索、导航栏、底部确定栏高度
                    this.listHeight = sureLayout.height - 100 - 48;
                }
            })
            .catch(error => {
                _self.log(error, '获取页面高度出错');
            });
        // #endif

        this.renderDepartmentTree(true);
    },

    //下拉刷新
    onPullDownRefresh() {
        let _self = this;
        uni.startPullDownRefresh({
            success() {
                _self.renderDepartmentTree(true);
            }
        });
    },

    methods: {
        renderDepartmentTree(refresh = false) {
            if (!refresh) {
                uni.showLoading({
                    title: '数据加载中...',
                    mask: true
                });
            }
            this.loadDepartmentAndUser(refresh)
                .then(this.buildOrganizationNodeIndexs)
                .then(this.initOrganizationData)
                .then(this.countDepartmentCheckedAmounts)
                .then(this.renderRootDepartment)
                .then(() => {
                    console.log(`初始化完毕`);
                })
                .catch(error => this.log(`renderDepartmentTree`, error))
                .finally(() => {
                    if (refresh) {
                        uni.stopPullDownRefresh();
                    } else {
                        uni.hideLoading();
                    }
                });
        },

        /**
         * 加载部门和用户数据
         */
        loadDepartmentAndUser(refresh) {
            return new Promise((resolve, reject) => {
                let dataServices = [orgService.loadDepartments(refresh)];
                if (this.isSelectPerson) {
                    dataServices.push(contactService.loadUsers(refresh));
                }
                Promise.all(dataServices)
                    .then(respArray => {
                        let departments = (respArray[0] || []).filter(d => {
                            return (d || null) !== null;
                        });
                        let menbers = respArray[1];
                        resolve({
                            departments,
                            menbers
                        });
                    })
                    .catch(error => reject(`获取组织机构数据出错：${error}`));
            });
        },

        /**
         * 构建部门和用户的数据索引，用于优化计算部门已选择子节点数量
         */
        buildOrganizationNodeIndexs(nodeInfo) {
            return new Promise((resolve, reject) => {
                try {
                    let departmentIndexs = this.buildNodesIndex(
                        nodeInfo.departments,
                        'ZZBH'
                    );
                    nodeInfo.departmentIndexs = departmentIndexs;
                    let menberIndexs = this.buildNodesIndex(
                        nodeInfo.menbers,
                        'YHID'
                    );
                    nodeInfo.menberIndexs = menberIndexs;
                    resolve(nodeInfo);
                } catch (error) {
                    reject(`构建组织机构数据索引出错：${error}`);
                }
            });
        },

        /**
         * 使用节点的Array数据，转换成节点id为属性，节点在Array中的索引为值的对象，方便快速查找数据
         * @param {Object} nodes 待建立索引的节点集合
         * @param {Object} nodeIdName 节点的ID属性名
         */
        buildNodesIndex(nodes, nodeIdName) {
            let indexs = {};
            let index = 0;
            nodes.forEach(node => {
                let nodeId = node[nodeIdName];
                indexs[nodeId] = index;
                index++;
            });
            return indexs;
        },

        /**
         * 初始化组织机构部门、用户及及其索引数据，在加载完数据后调用
         * @param {Object} nodeInfo
         */
        initOrganizationData(nodeInfo) {
            return new Promise((resolve, reject) => {
                this.departments = nodeInfo.departments;
                this.departmentIndexs = nodeInfo.departmentIndexs;
                if (this.isSelectPerson) {
                    this.menbers = nodeInfo.menbers;
                    this.menberIndexs = nodeInfo.menberIndexs;
                }
                resolve(nodeInfo);
            });
        },

        /**
         * 统计部门的选中子节点总数
         * @param {Object} nodeInfo
         */
        countDepartmentCheckedAmounts(nodeInfo) {
            return new Promise((resolve, reject) => {
                this.countCheckedAmount();
                resolve(nodeInfo);
            });
        },

        /**
         * 渲染根节点数据
         * @param {Object} nodeInfo
         */
        renderRootDepartment(nodeInfo) {
            let parentChain = orgService.resolveParentChain(
                nodeInfo.departments
            );
            console.log(parentChain);
            let chainLength = parentChain.length;
            if (chainLength > 0) {
                this.naviDepart.splice(0, chainLength, ...parentChain);
                let directDepartment = parentChain[chainLength - 1];
                this.currentDepartments = this.getValidSubDepartments(
                    nodeInfo.departments,
                    directDepartment
                );
                let menbers = this.getDepartmentMenbers(
                    nodeInfo.menbers,
                    directDepartment.ZZBH
                );
                this.checkStatus = new Array(menbers.length);
                this.checkStatus.fill(false);
                this.currentMenbers = menbers;
            }
        },

        /**
         * 通过选中结点向父节点递归统计父节点下选中部门或人员数量
         */
        countCheckedAmount() {
            if (this.checkedNodes.length === 0) {
                return {};
            }
            let amounts = {};
            this.checkedNodes.forEach(nodeId => {
                this.incParentCheckedAmount(nodeId, amounts);
            });
            this.nodeCheckedAmounts = amounts;
        },

        /**
         * 通过递归的方式使父节点选中节点数+1
         * @param {Object} nodeId 选中节点ID
         * @param {Object} result 存放节点选中数量对象
         */
        incParentCheckedAmount(nodeId, result) {
            let node = null;
            if (this.departmentIndexs.hasOwnProperty(nodeId)) {
                let nodeIndex = this.departmentIndexs[nodeId];
                node = this.departments[nodeIndex];
            }

            if (this.menberIndexs.hasOwnProperty(nodeId)) {
                let nodeIndex = this.menberIndexs[nodeId];
                node = this.menbers[nodeIndex];
            }
            if (node === null) {
                return;
            }
            let parentId = node.SJZZXH || node.BMBH || null;
            if (parentId === null) {
                return;
            }
            if (!this.departmentIndexs.hasOwnProperty(parentId)) {
                return;
            }
            //记录父节点选中+1
            let parentAmount = result[parentId] || 0;
            parentAmount++;
            result[parentId] = parentAmount;
            this.incParentCheckedAmount(parentId, result);
        },

        naviToNode(depart) {
            // console.log(depart.SJQX, loginService.getUserOrgid());
            
            if(depart.SJQX < loginService.getUserOrgid()) {
                return;
            }
            
            if (this.showSearchList) {
                return;
            }

            //点击节点是最后一个节点时，不做任何操作
            let lastNode = this.naviDepart[this.naviDepart.length - 1];
            if (depart.ZZBH === lastNode.ZZBH) {
                return;
            }

            let index = this.naviDepart.indexOf(depart);
            if (index != -1) {
                let delCount = this.naviDepart.length - index - 1;
                this.naviDepart.splice(index + 1, delCount);
                this.currentDepartments = this.getValidSubDepartments(
                    this.departments,
                    depart
                );
                this.currentMenbers = this.getDepartmentMenbers(
                    this.menbers,
                    depart.ZZBH
                );
            }
        },

        /**
         * 部门节点被点击
         * @param {Object} parentNode
         */
        onGroupClick(parentNode, index) {
            console.log();
            
            if (this.isSelectPerson) {
                //选择人员模式，导航到下一级
                this.naviToSubLevel(parentNode, index);
            } else {
                //选择部门模式，部门有子部门，导航至下级部门，反之，直接变更部门选中状态
                let childCount = this.childrenAmounts[index];
                if (childCount > 0) {
                    //
                    this.naviToSubLevel(parentNode, index);
                } else {
                    let nodeId = parentNode.ZZBH;
                    let checked = this.checkedNodes.indexOf(nodeId) !== -1;
                    this.onNodeCheckChange(nodeId, !checked);
                }
            }
        },

        /**
         * 导航至下一层级
         * @param {Object} parentNode
         */
        naviToSubLevel(parentNode, index) {
            if (this.childrenAmounts[index] === 0) {
                return;
            }

            this.naviDepart.splice(this.naviDepart.length, 0, parentNode);
            this.currentDepartments = this.getValidSubDepartments(
                this.departments,
                parentNode.ZZBH
            );
            this.currentMenbers = this.getDepartmentMenbers(
                this.menbers,
                parentNode.ZZBH
            );
        },

        /**
         * 获取部门有效节节点
         * @param {Object} departments
         * @param {Object} parent
         */
        getValidSubDepartments(departments, parent) {
            let children = orgService.getSubDepartments(departments, parent);
            this.sortNodes(children);
            return children;
        },

        /**
         * 获取部门下用户
         * @param {Object} menbers
         * @param {Object} parentId
         */
        getDepartmentMenbers(menbers, parentId) {
            let children = orgService.getOrgMembers(menbers, parentId);
            this.sortNodes(children);
            return children;
        },

        /**
         * 对部门列表进行正向排序
         * @param {Object} departments 要进行排序的部门列表
         * @param {Object} orderBy 排序字段，默认使用PXH
         */
        sortNodes(departments, orderBy = 'PXH') {
            departments.sort((one, another) => {
                return one[orderBy] - another[orderBy];
            });
            return departments;
        },

        /**
         * 部门节点选中状态变更
         * @param {Object} department
         * @param {Object} checked
         */
        onGroupCheckChange(department, checked) {
            if (this.isSelectPerson) {
            } else {
                let departmentId = department.ZZBH;
                this.onNodeCheckChange(departmentId, checked);
            }
        },

        /**
         * 叶子（用户）节点选中状态变更
         * @param {Object} node
         * @param {Object} checked
         */
        onLeafCheckChange(node, checked) {
            let nodeId = node.YHID;
            this.onNodeCheckChange(nodeId, checked);
        },

        /**
         * 节点选中状态变更，修改选中节点数据
         * @param {Object} nodeId 节点ID
         * @param {Object} checked 节点是否选中
         */
        onNodeCheckChange(nodeId, checked) {
            if (checked) {
                if (this.multiCheck) {
                    this.checkedNodes.push(nodeId);
                } else {
                    this.checkedNodes.length = 0;
                    this.checkedNodes.push(nodeId);
                }
            } else {
                let index = this.checkedNodes.indexOf(nodeId);
                this.checkedNodes.splice(index, 1);
            }
            this.countCheckedAmount();
        },

        getLeafCheckedAmount(node) {
            let nodeId = node.ZZBH;
            let amount = this.nodeCheckedAmounts[nodeId] || 0;
            return amount;
        },

        searchByKeyword(params) {
            if (params) {
                this.showSearchList = true;
                let matchMenbers = this.filterMenberByKeyword(params);
                console.log(matchMenbers);
                
                this.searchMenbers = matchMenbers;
            } else {
                this.showSearchList = false;
                this.searchMenbers = [];
            }
        },

        selectConfirm() {
            //触发一个事件供调用者监听
            let nodes = this.getSelectedNodes(this.checkedNodes);
            uni.$emit('selectConfirm', deepCopyObject(nodes));
            uni.navigateBack({
                delta: 1
            });
        },

        selectCancel() {
            uni.$emit('selectCancel');
        },

        getSelectedNodes(nodeIds) {
            let result = [];
            nodeIds.forEach(nodeId => {
                let isDepartment = false;
                for (let department of this.departments) {
                    if (nodeId === department.ZZBH) {
                        result.push(deepCopyObject(department));
                        isDepartment = true;
                        break;
                    }
                }
                for (let person of this.menbers) {
                    if (nodeId === person.YHID) {
                        result.push(deepCopyObject(person));
                        break;
                    }
                }
            });
            return result;
        },

        /**
         * 使用查询的数据按关键字过滤
         * @param {Object} keyword
         */
        filterMenberByKeyword(keyword) {
            console.log(this.naviDepart);
            
            if (this.menbers) {
                return this.menbers.filter(item => {
                    if(this.naviDepart.length <= 1){
                        return item.YHMC.indexOf(keyword) !== -1;
                    } else {
                        return item.YHMC.indexOf(keyword) !== -1 && item.BMBH == this.naviDepart[this.naviDepart.length - 1].ZZBH;

                    }
                  
                });
            }
            return [];
        }
    }
};
</script>

<style scoped>
.department {
}

.department-list {
    padding: 0px 20rpx;
}

.department-navi-layout {
    /* width: calc(100% + 60rpx); */

    height: 36px;
    white-space: nowrap;
    /* overflow-x: scroll; */
}

.navi-nodes-layout {
    width: 100%;
    height: 100%;
    display: flex;
    /* flex-flow: row nowrap;  */
    align-items: center;
}

.department-navi-node-text {
    /* white-space: nowrap;
		text-overflow: ellipsis;
		overflow: hidden; */
    font-size: 16px;
    color: #333;
}

.department-navi-node-text:active {
    border-radius: 3px;
    padding: 5px;
    background-color: #0077ff;
    color: #fff;
}

.department-navi-node-text:last-child {
    color: #1e8eef;
}

.department-sub-indicator {
    width: 32rpx;
    height: 32px;
}

.department-list-divider {
    width: 100%;
    height: 1px;
    background-color: #ddd;
}

.department-group-list {
    width: 100%;
    background-color: #fff;
}

.department-leaf-list {
    width: 100%;
    margin-top: 10px;
    background-color: #fff;
}

.select-confirm-layout {
    width: calc(100% - 40rpx);
    height: 96rpx;
    padding: 0 20rpx;
    background-color: #fff;
    z-index: 999;
    border-top: 1px solid #ccc;
}

.department-navi-node-next {
    padding: 0 16rpx;
}
</style>
