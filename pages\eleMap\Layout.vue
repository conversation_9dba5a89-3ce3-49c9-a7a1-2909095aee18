<template>
	<Page class="power-page" style="width:100%;height:100vh" :title="title" padding="0">
		<view style="width: 100%;height:100vh">
			<input type="text" class="pd-inpsrh1" placeholder="请输入污染源" @click="go()" style="box-shadow: 1px 2px 1px rgba(0,0,0,.15);background: #eee;position: fixed;z-index: 1000;left: 20rpx;top:130rpx"/>
			<!-- <map
				:style="mapStyle"
				:latitude="latitude"
				:longitude="longitude"
				:markers="markers"
			/> -->
			
			<div id="amap" :style="mapStyle"></div>
			
			<div id="lng"></div>
		</view>
	</Page>
</template>

<script>
	import styleUtil from '@/common/style.js'
	import Page from '@/pages/component/Page.vue';
	export default {
		components: {Page},
		
		data(){
			return {
				latitude: '',
				longitude: '',
				title: '电子地图'
			}
		},
		
		computed:{
			mapStyle: function() {
				let style = {
					width: '100%',
					height: '100%'
				}
				return style;
			},
			
			markers: function() {
				let points = []
				if(this.latitude && this.longitude) {
					points.push({
						id: 0,
						latitude: this.latitude,
						longitude: this.longitude,
						title: '当前位置',
						width: 32,
						height: 64,
						iconPath: '../../static/img/record/icon_location_marker.png'
					})
				}
				return points
			}
		},
		
		mounted(){
		},
		
		methods:{
			go(){
				let view = uni.createSelectorQuery().in(this).select("#lng");
				
				view.fields({
					context: true,
					dataset: true,
					size: true,
					scrollOffset: true
				}, data => {
					let lat = JSON.stringify(data.dataset.lat).replace(/\"/g, "")
					let lng = JSON.stringify(data.dataset.lng).replace(/\"/g, "")
					
					uni.setStorageSync('ele-lng', lng)
					uni.setStorageSync('ele-lat', lat)
					
					uni.navigateTo({
						url: './SearchPollution'
					})
					
				}).exec();
				
			}
			
		}
	}
</script>



<script module="amap" lang="renderjs">
window._AMapSecurityConfig = {
	securityJsCode:'107bd01de399ea7dfe44cfcf0b891b43',
}
const selectedStart = 'static/ITkoala-amap/selectedStart.png' //选中的图片

export default {
	data() {
		return {
			marker: null,
			map: null,
			ownerInstanceObj: null //service层对象
		}
	},
	
	mounted() {
		if (typeof window.AMap === 'function') {
			this.initAmap()
		} else {
			// 动态引入较大类库避免影响页面展示
			const script = document.createElement('script')
			script.src = 'https://webapi.amap.com/maps?v=1.4.15&key=fb9b0e9bb1b2cfaeab2e17dc0f0003cc&plugin=AMap.Geocoder'
			script.onload = this.initAmap.bind(this)
			document.head.appendChild(script)
		}
	},
	
	onLoad(){
		console.log('show');
		if(localStorage.getItem('e-lng')){
			let lng = localStorage.getItem('e-lng');
			let lat = localStorage.getItem('e-lat');
			this.addMarker(lng, lat);
			this.map.setCenter([lng, lat]); 
		}
	},
	
	methods: {
		addMarker(lng, lat){
			// if(this.marker){
			// 	this.map.remove(this.marker)
			// }
			var marker = new AMap.Marker({
			    icon: "https://webapi.amap.com/theme/v1.3/markers/n/mark_b.png",
			    position: [lng, lat]
			});
			this.marker = marker;
			this.map.add(marker);
		},
		
		geoAddress(lnglat){
			var geocoder = new AMap.Geocoder({
				radius: 1000 //范围，默认：500
			});
			
			geocoder.getAddress(lnglat, function(status, result) {
				if (status === 'complete'&&result.regeocode) {
					var address = result.regeocode.formattedAddress;
					console.log(address);
					 document.getElementById('lng').setAttribute('data-address',  address);
					 document.getElementById('address').innerHTML = address;
				}else{
					log.error('根据经纬度查询地址失败')
				}
			});
		},
		
		initAmap() {
			let self = this;
			
			let map = new AMap.Map('amap', {
				resizeEnable: true,
				center: [116.397428, 39.90923],
				layers: [ //使用多个图层
					// new AMap.TileLayer.Satellite() //使用卫星图
				],
				zooms: [4, 18], //设置地图级别范围
				zoom: 16
			})
			
			this.map = map;
			
			// map.on('click', function(e){
				
			// 	self.geoAddress([e.lnglat.getLng(), e.lnglat.getLat()])
			// 	self.addMarker(e.lnglat.getLng(), e.lnglat.getLat())
				
			// 	document.getElementById('lng').setAttribute('data-lng',  e.lnglat.getLng())
			// 	document.getElementById('lng').setAttribute('data-lat', e.lnglat.getLat())
			// 	document.getElementById('lng').innerHTML = e.lnglat.getLng();
			// 	document.getElementById('lat').innerHTML = e.lnglat.getLat();
			// })
			
			
		// 	AMap.plugin('AMap.Geolocation', function() {
		// 		var geolocation = new AMap.Geolocation({
		// 			enableHighAccuracy: false,//是否使用高精度定位，默认:true
		// 			timeout: 10000,          //超过10秒后停止定位，默认：5s
		// 			buttonPosition:'RB',    //定位按钮的停靠位置
		// 			buttonOffset: new AMap.Pixel(10, 20),//定位按钮与设置的停靠位置的偏移量，默认：Pixel(10, 20)
		// 			zoomToAccuracy: true,   //定位成功后是否自动调整地图视野到定位点
		
		// 		});
		// 		// map.addControl(geolocation);
				// geolocation.getCurrentPosition(function(status,result){
				// 	if(status=='complete'){
				// 		onComplete(result)
				// 	}else{
				// 		onError(result)
				// 	}
				// });
		// 	});
		
		
			var options = {
		        'showButton': true,//是否显示定位按钮
				'buttonPosition': 'LB',//定位按钮的位置
				/* LT LB RT RB */
				'buttonOffset': new AMap.Pixel(10, 20),//定位按钮距离对应角落的距离
				'showMarker': true,//是否显示定位点
				'markerOptions':{//自定义定位点样式，同Marker的Options
				  'offset': new AMap.Pixel(-18, -36),
				  'content':'<img src="https://a.amap.com/jsapi_demos/static/resource/img/user.png" style="width:36px;height:36px"/>'
				},
				'showCircle': true,//是否显示定位精度圈
				'circleOptions': {//定位精度圈的样式
					'strokeColor': '#0093FF',
					'noSelect': true,
					'strokeOpacity': 0.5,
					'strokeWeight': 1,
					'fillColor': '#02B0FF',
					'fillOpacity': 0.25
				}
		    }
		    AMap.plugin(["AMap.Geolocation"], function() {
		        var geolocation = new AMap.Geolocation(options);
		        map.addControl(geolocation);
				   geolocation.getCurrentPosition(function(status,result){
					if(status=='complete'){
						onComplete(result)
					}else{
						onError(result)
					}
				   });
		    });
			//解析定位结果
			function onComplete(data) {
				
				self.addMarker(data.position.lng, data.position.lat);
				
				
				// 如果有本地存储，那么就跳转到本地存储的点位，退出后清除本地存储
				if(localStorage.getItem('e-lng')){
					let lng = localStorage.getItem('e-lng');
					let lat = localStorage.getItem('e-lat');
					self.addMarker(lng, lat);
					map.setCenter([lng, lat]); 
				}else{
					map.setCenter([data.position.lng, data.position.lat]); 
				}
				
				
				document.getElementById('lng').setAttribute('data-lng', data.position.lng)
				document.getElementById('lng').setAttribute('data-lat', data.position.lat)
				// document.getElementById('lng').innerHTML = data.position.lng;
				// document.getElementById('lat').innerHTML = data.position.lat;
			}
			//解析定位错误信息
			function onError(data) {
				
			}
		}
	}
}
</script>
<style scoped>
	.power-page-main{
		margin-top: 0px!important;
	}
</style>
