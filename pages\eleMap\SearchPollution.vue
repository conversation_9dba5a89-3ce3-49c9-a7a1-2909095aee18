<template>
	<view>
		<header class="pd-header">
			<i class="goback" @click="back()"></i>
			<div class="pd-srh1a">
				<input type="text" placeholder="请输入污染源" v-model="search" v-on:keyup.13="handleKeyUp" @keydown="handleKeyUp" />
			</div>
		</header>
		<section class="pd-main">
			<div class="pd-inner pt2" style="padding-top: 80rpx;">
				<scroll-view class="record-task-list" scroll-y="true"  style="height: calc(100vh - 100rpx);"  @scrolltolower="onReachBottomEvent" v-if="list.length>0">
				<!-- <div class="pd-tips">
					查询到 <i>5</i>条污染源记录
				</div> -->
				<dl class="pd-dlbx1" v-if="list.length>0">
					<dt><span>污染源<i>（{{total}}）</i></span></dt>
				</dl>
				<dl class="pd-dlbx1" v-for="item in list">
					<div >
						<dd>
							<image src="../../static/app/images/lstic1a.png" class="imgic" />
							<h2 v-html="item.WRYMC">{{item.WRYMC}}</h2>
							<p><em>地址：</em><span v-html="item.WRYDZ"></span></p>
							<!-- <small></small> -->
						</dd>
						<dd>
							<ul class="pd-ulbx2" v-if="item.JD">
								<li><i class="lsts1" @click="navigateWithMap(item.JD, item.WD)">导航</i></li>
								<li><i class="lsts2" @click="searchMap(item.JD, item.WD)">查看</i></li>
							</ul>
						</dd>
					</div>
					
				</dl>
				
				</scroll-view>
				<noData v-if="list.length==0"></noData>
			</div>
		</section>
	</view>
</template>

<script>
	// import { getAllSearch } from '@/api/letter.js'
	import { postQueryWrylx } from '@/api/new.js'
	import noData from '@/components/no-data.vue'
	import loginService from '@/api/login-service.js'
	
	export default {
		components: {noData},
		data() {
			return {
				search: '',
				pageSize: 10,
				type: 'WRY',
				pageNum: 1, 
				list: [],
				total: 0
			}
		},
		mounted(){
			this.searchData();
		},
		methods: {
			searchMap(lng, lat){
				uni.setStorageSync('e-lng', lng)
				uni.setStorageSync('e-lat', lat)
				uni.navigateBack()
			},
			back(){
				uni.navigateBack()
			},
			handleKeyUp(){
				this.pageNum = 1;
				this.list = [];
				this.searchData();
			},
			onReachBottomEvent (){
				this.pageNum++;
				this.searchData();
			},
			searchData(){
				let userId = loginService.getAuthUserId()
				
				postQueryWrylx({
					service: 'QUERY_WRYXX_SERVICE',
					searchText: this.search,
					pageSize: this.pageSize,
					pageNum:this.pageNum
				}).then((res) => {
					this.list.push(...res.data_json.list);
					this.total = res.data_json.total;
				})
				
			},
			navigateWithMap(lng, lat){
				
			  let eleLng = uni.getStorageSync('ele-lng');
			  let eleLat = uni.getStorageSync('ele-lat');
			  
			  // console.log(eleLng, lat, lng, lat)
			  // 设置目标位置坐标点和其实位置坐标点
			  var dst = new plus.maps.Point(lng, lat); // 天安门 
			  var src = new plus.maps.Point(eleLng, eleLat); // 大钟寺
			  // 调用系统地图显示 
			  plus.maps.openSysMap(dst, '污染源路线', src);
			}
		}
	}
</script>

<style>
.pd-main{
	width: 100vw;
}
.pd-dlbx1 + .pd-dlbx1{
	margin-top:0px;
}
.pd-dlbx1 dd + dd {
    border-top: 1px solid #ededed;
    border-bottom: 1px solid #ededed;
}
</style>

