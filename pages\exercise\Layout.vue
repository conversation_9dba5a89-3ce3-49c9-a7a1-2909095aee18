<template>
	<Page class="power-page" :title="title" @layoutAware="onFixedHeight">
		<header class="pd-header xxks">
			<i class="goback"></i>
			<h1 class="pd-title">学习考试</h1>
			<div class="gap"></div>
			<div class="gap"></div>
			<div class="gap"></div>
			<dl class="pd-dlbx3">
				<dt><image src="../../static/app/images/hdpic1.png" alt=""></dt>
				<dd>
					<h1>{{userName|| 'SDSZFJ'}}</h1>
					<!-- <p><i>执法骨干</i><i>124积分</i></p> -->
				</dd>
				<!-- <a class="phb"></a> -->
			</dl>
		</header>
		<section class="pd-main">
			<div class="pd-inner xxks">
				<ul class="pd-ulbx5 otr">
					<li @click="go('./exercise/Layout')">
						<image src="../../static/app/images/cdic1.png" alt="">
						<p>练习</p>
						<!-- <p>0<sub>/1000</sub></p> -->
					</li>
					<li @click="go('./exam/Layout')">
						<image src="../../static/app/images/cdic2.png" alt="">
						<p>考试列表</p>
					</li>
					<li @click="go('./error/Layout')">
						<image src="../../static/app/images/cdic3.png" alt="">
						<p>错题库</p>
					</li>
					<li>
						<image src="../../static/app/images/cdic2.png" alt="">
						<p>我的试卷</p>
					</li>
				</ul>
			</div>
		</section>
	</Page>
</template>

<script>
	import Page from '@/pages/component/Page.vue';
	
	export default {
		components: {Page},
		data() {
		    return {
				userName: '', //用户名
		        title: '学习考试'
			}
		},
		mounted(){
			this.userName = uni.getStorageSync('user_id')
		},
		methods:{
			onFixedHeight(layout) {
				this.pageHeight = layout.height;
			},
			go(url){
				if(url) {
					uni.navigateTo({
						url: url
					})
				}else{
					alert('正在规划中')
				}
			}
		}
	}
</script>

<style>
</style>
