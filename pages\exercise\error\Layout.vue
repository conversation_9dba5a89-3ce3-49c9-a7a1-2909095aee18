<template>
	<Page class="power-page" :title="title" @layoutAware="onFixedHeight">
		<section class="pd-main">
			<div class="pd-inner" style="padding-bottom: 0;margin-top: 0;padding: 0px 0 45px;">
				<ul class="pd-ulbx6">
					<li>
						<h1>错题来源：</h1>
						<p><i class="on">练习</i></p>
					</li>
					<li>
						<h1>题型：</h1>
						<p>
							<i :class="{on : type=='DXT'}" @click="changeType('DXT')">单选题</i>
							<i :class="{on : type=='PDT'}" @click="changeType('PDT')">判断题</i>
							<i :class="{on : type=='FXT'}" @click="changeType('FXT')">多选题</i>
						</p>
					</li>
				</ul>
				<scroll-view class="record-task-list" scroll-y="true"  style="height: calc(100vh - 280rpx);"  @scrolltolower="onReachBottomEvent" v-if="list.length>0">
					<div v-for="item in list">
						<div class="gap"></div>
						<dl class="pd-dlbx4">
							<dt>{{item.TMNR}}</dt>
							<dd>
								<ul class="xuanxiang">
									<li :class="{correct: i.SFDA === '1', error: !(i.SFDA === '1')}" v-for="i in item.DA">{{i.DANR}}</li>
									<!-- <li class="error">B.  错误</li> -->
								</ul>
							</dd>
							<dd>
								<p>{{item.ZTJX}}</p>
							</dd>
						</dl>
					</div>
				</scroll-view>
				<noData v-if="list.length==0"></noData>
			</div>
		</section>
	</Page>
</template>

<script>
	import { getErrorList } from '@/api/exercise.js'
	import Page from '@/pages/component/Page.vue';
	import noData from '@/components/no-data.vue'
	export default {
		components: {Page, noData},
		data() {
		    return {
				pageSize: 10,
				pageNum: 1,
				source: 'LX', // LX KS
				type: 'DXT',//FXT 复选  DXT 单选  PDT判断
		        title: '错题库',
				list:  [],
				totoa: 0
			}
		},
		mounted(){
			this.getData();
		},
		methods:{
			onFixedHeight(layout) {
				this.pageHeight = layout.height;
			},
			onReachBottomEvent (){
				this.pageNum++;
				this.getData();
			},
			changeType(type){
				this.type = type;
				this.pageNum = 1;
				this.list = [];
				this.getData();
			},
			getData(){
				 if(this.list.length < this.total || this.list.length == 0){
					getErrorList({'TMLX':this.type,'CTLY':this.source,'pageSize':this.pageSize,'pageNum':this.pageNum}).then(res=>{
						this.list.push(...res.data_json.list);
						this.total = res.data_json.total;
					})
				 }
			}
		}
	}
</script>

<style>
</style>

			