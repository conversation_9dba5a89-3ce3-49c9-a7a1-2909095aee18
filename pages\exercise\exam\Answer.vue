<template>
	<view class="dati-body">
		<header class="pd-header">
			<i class="goback" @click="back()"></i>
			<h1 class="pd-title">考试</h1>
		</header>
		<section class="pd-main page-datiku" style="width: 100vw;">
			<div class="pd-inner">
				<p class="til">考试时间 <span>{{ `${hr}: ${min}: ${sec}` }}</span></p>
		
				<div class="timu timu-num">
					<div class="xuhao">{{item.ST.TMLXMC || ''}}</div>
					<p class="ti"><em>第{{num+1}}题</em><i> · 共{{list.length}}题</i></p>
					<p class="zi">{{item.ST.TMNR}}</p>
					
					
					<ul class="xuanxiang" v-show="item.ST.TMLX!=='FXT'">
						
						<li v-for="(i,index) in item.DA"  :class="{  cur: selectKey === index }" @click="changeSelectKey(index, item)">{{i.DANR}}</li>
					</ul>
					
					<ul class="xuanxiang" v-show="item.ST.TMLX==='FXT' && !subMul">
						<li v-for="(i,index) in item.DA" :class="{  cur: selectArr.includes(index)}" @click="changeSelectArr(index, item)">{{i.DANR}}</li>
					</ul>
					
					<!-- <ul class="xuanxiang" v-show="item.ST.TMLX==='FXT' && subMul">
						<li v-for="(i,index) in item.DA">{{i.DANR}}</li>
					</ul> -->
				</div>
		
				<div class="xiayiti" @click="next()" v-show="num==0">
					<p>下一题</p>
				</div>
				
				<div class="btns" v-show="num!==0">
					<p class="fanhui" @click="prev()">上一题</p>
					<p class="jixu" @click="next()" v-show="num !== (list.length-1)">下一题</p>
					<p class="jixu" @click="next()" v-show="num == (list.length-1)">提交</p>
				</div>
				
			</div>
		</section>
		
		<div class="mask" style="display: block;" v-if="showScore"></div>
		
		<div class="pd-dlg dlg1" v-if="showScore">
			<h1>考试结束</h1>
			<h2>得分</h2>
			<h3>{{rightScore}}<sub>分</sub></h3>
			<p>共{{list.length}}题，答对<i>{{rightNum}}题</i></p>
			<small class="dlgcls" @click="back()"></small>
		</div>
	</view>
</template>

<script>
	import { getUserExamList, AddExam, UpadateExamItem, UpadateExam } from '@/api/exercise.js'
	export default {
		data(){
			return {
				hasTime: 0,
				rightScore: 0,
				rightNum: 0,
				selScore: 0,
				isRight: 0, //是否答对 0 打错 1
				scoreArr: [], // 分数的数组
				cacheArr: [], // 缓存答案的数组
				number: '', // 序号
				num: 0,
				selectArr: [], // 选择的数组
				selectKey: '',
				item: {
					ST: {
						TMLXMC: '432',
						TMNR: '',
						TMLX: ''
					}
				},
				list:  [],
				showScore: false,
				time: '',
				hr: 3,
				min: 30,
				sec: 0,
			}
		},
		onLoad(options){
			this.number = options.number;
			this.getData();
			
			this.time = (Date.parse(new Date()) + ((3.5 * 60 * 60)) * 1000);
			this.now = Date.parse(new Date());
			// 开始执行倒计时
			this.countdown();
		},
		methods: {
			 countdown() {
				const end = this.time; // 定义结束时间
				const now = Date.parse(new Date()); // 获取本地时间
				const msec = end - now; // 定义总共所需的时间
				const hasTime = now - this.now;
				
				let hrHasTime = parseInt(hasTime / 1000 / 60 / 60 % 24);
				let minHasTime = parseInt(hasTime / 1000 / 60 % 60);
				let secHasTime = parseInt(hasTime / 1000 % 60);
				this.hasTime = (hrHasTime>9?hrHasTime : '0' + hrHasTime) + ':' + (minHasTime>9?minHasTime : '0' + minHasTime) + ':' + (secHasTime>9?secHasTime : '0' + secHasTime);
				
				// 将时间戳进行格式化
				let hr = parseInt(msec / 1000 / 60 / 60 % 24);
				let min = parseInt(msec / 1000 / 60 % 60);
				let sec = parseInt(msec / 1000 % 60);
				// 倒计时结束时的操作
				const that = this;
				if (this.hr == 0 && this.min == 0 && this.sec == 0) {
					console.log('时间已经结束，答题完毕');
					
					this.submitExam();
					this.hr = 3;
					this.min = 30;
					this.sec = 0;
				} else {
					// 如时间未归零则继续在一秒后执行
					this.hr = hr > 9 ? hr : '0' + hr;
					this.min = min > 9 ? min : '0' + min;
					this.sec = sec > 9 ? sec : '0' + sec;
					setTimeout(that.countdown, 1000)
				}
			},
			back(){
				uni.navigateBack();
			},
			getData(){
				AddExam({'SJXH': this.number}).then(res=>{
					this.list = res.data_json;
					
					this.item = this.list[this.num];
				})
			},
			changeSelectKey(key, item){
				this.selectKey = key;
				
				console.log(key);
				
				// 缓存答案
				this.cacheArr[this.num] = key;
			},
			next(){
							
				// 单选的时候
				if(this.item.ST.TMLX !== 'FXT'){
					if(this.key != this.selectKey && this.selectKey!=''){
						// this.errorItem();
						// 这里要发送到后台
						this.selScore = 0;
						this.isRight = 0;
					}else{
						this.selScore = this.item.ST.FS;
						this.isRight = 1;
					}
				}else{
					this.subMul = true;
					let selArr = this.selectArr.sort();
					let anArr = [];
					this.item.DA.forEach((item,index)=>{
						if(item.SFDA === '1'){
							anArr.push(index);
						}
					})
					
					if(selArr.toString() != anArr.toString()){
						// 判断多选题是否答对一半
						let isHalt = this.isContained(anArr, selArr);
						// this.errorItem();
						// 这里要发送到后台
						if(isHalt){
							this.selScore = this.item.ST.FS / 2 ;
						}else{
							this.selScore = 0;
						}
						
						this.isRight = 0;
					}else{
						this.selScore = this.item.ST.FS;
						this.isRight = 1;
					}
				}
				
				// 分数数组
				this.scoreArr[this.num] = {
					score : this.selScore,
					isRight: this.isRight
				}
				// 提交答案
				this.postExam();
				
				if(this.num<(this.list.length-1)){
					this.num++;
					this.item = this.list[this.num];
					this.selectKey = '';
					this.selectArr = [];
					this.subMul = false;
					this.dealKey();
				}else{
					this.submitExam();
				}
				
				// 还原缓存
				if(this.item.ST.TMLX === 'FXT'){
					this.selectArr = this.cacheArr[this.num] || [];
				}else{
					this.selectKey = this.cacheArr[this.num] || '';
				}
			},// 判断子集
			isContained(aa,bb){
			    if(!(aa instanceof Array)||!(bb instanceof Array)||((aa.length < bb.length))){
			        return false;
			    }
			    var aaStr = aa.toString();
			    for (var i = 0 ;i < bb.length;i++) {
			        if(aaStr.indexOf(bb[i]) < 0) return false;
			    }
			    return true;
			},
			// 提交答案
			postExam(){
				UpadateExamItem({
					'SJXH': this.number,
					'STXH': this.item.ST.XH,
					'ANSWER': this.item.ST.TMLX === 'FXT' ? this.selectArr.toString() : this.selectKey,
					'SCORE': this.item.ST.FS,
					'ACTUALSCORE': this.selScore,
					'ISRIGHT': this.isRight
					}
				).then(res=>{
					console.log(res);
				})
			},
			prev(){
				if(this.num >0){
					this.num--;
					this.item = this.list[this.num];
					this.dealKey();
				}
				
				// 还原缓存
				if(this.item.ST.TMLX === 'FXT'){
					this.selectArr = this.cacheArr[this.num] || [];
				}else{
					this.selectKey = this.cacheArr[this.num];
				}
			},// 处理答案
			errorItem(){
				
			},
			// 提交考试
			submitExam(){
				this.scoreArr.forEach(item=>{
					if(item.isRight === 1){
						this.rightNum = this.rightNum+1;
					}
					this.rightScore += parseFloat(item.score);
				})
				this.showScore = true;
				
				UpadateExam({
					'KSSJID': this.number,
					'FS': this.rightScore,
					'KSYS': this.hasTime
				}).then(res=>{
					console.log(res);
				})
			},
			dealKey(){
				if(this.item.type == 'single'){
					// this.item.DA.forEach((item, index)=>{
					// 	if(item.SFDA === '1'){
					// 		this.key = index;
					// 	}
					// })
				}
			},
			// 多选题
			changeSelectArr(key, item){
				let hasSel = this.selectArr.includes(key);
				// 判断是否选中
				if(hasSel){
					let newArr = [];
					this.selectArr.forEach(item=>{
						if(item != key){
							newArr.push(item);
						}
					})
					this.selectArr = newArr;
				}else{
					this.selectArr.push(key);
				}
				
				// 缓存答案
				this.cacheArr[this.num] = this.selectArr;
			},
		}
	}
</script>

<style>
</style>
