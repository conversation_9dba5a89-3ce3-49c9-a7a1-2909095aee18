<template>
	<Page class="power-page" :title="title" @layoutAware="onFixedHeight">
		<template v-slot:bar>
			<NaviBar :title="title"  />
		</template>
		<section class="pd-main">
			<div class="pd-inner" style="padding-top: 0;">
				<scroll-view class="record-task-list" scroll-y="true"  style="height: calc(100vh - 200rpx);"  @scrolltolower="onReachBottomEvent" v-if="list.length>0">
					<dl class="pd-dlbx1" v-for="item in list" @click="addExam(item)">
						<div>
							<dd>
								<h2 class="important-title" v-html="item.KSMC">{{item.KSMC}}</h2>
								<p></p>
								<p class="left-num"></p>
								<p class="left-type">{{item.KSZT}}</p>
							</dd>
						</div>
					</dl>
				</scroll-view>
				<noData v-if="list.length==0"></noData>
			</div>
		</section>
	</Page>
</template>

<script>
	import { getUserExamList, AddExam } from '@/api/exercise.js'
	import Page from '@/pages/component/Page.vue';
	import noData from '@/components/no-data.vue';
	import NaviBar from '@/pages/component/NaviBar.vue';
	export default {
		components: {
			Page,
			NaviBar,
			noData
		}, 
		data() {
		    return {
		        title: '考试列表',
				list: [],
				item: {
					ST: {
						TMLXMC: '',
						TMNR: '',
						TMLX: ''
					}
				}
			}
		},
		mounted(){
			getUserExamList().then(res=>{
				this.list = res.data_json.list;
			})
		},
		methods:{
			onFixedHeight(layout) {
				this.pageHeight = layout.height;
			},
			addExam(item){
				if(item.KSZT != '已结束'){
					uni.navigateTo({
						url: './Answer?number=' + item.XH
					})
				}else{
					uni.showToast({
						icon:'none',
						title:"当前考试已结束" 
					})
				}
				
				// //console.log(item)
				// AddExam({'SJXH': item.XH}).then(res=>{
				// 	console.log(res);
				// })
				// //{'SJXH':'�Ծ�XH'}
			}
		}
	}
</script>

<style scoped>
	.left-type{
		color:#4FB053;
		padding-left: 30rpx;
	}
	.pd-dlbx1 dd h2{
		padding-left: 30rpx;
	}
</style>
