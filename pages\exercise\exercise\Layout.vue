<template>
	<Page class="power-page" :title="title" @layoutAware="onFixedHeight" >
		<section class="pd-main page-datiku">
			<div class="pd-inner">
				<p class="til" style="height: 20rpx;"></p>
	
				<div class="timu timu-num">
					<div class="xuhao">{{item.ST.TMLXMC || ''}}</div>
					<p class="ti"><em>第{{num+1}}题</em><i> · 共{{list.length}}题</i></p>
					<p class="zi">{{item.ST.TMNR}}</p>
					<ul class="xuanxiang" v-show="item.ST.TMLX!=='FXT'">
						<li v-for="(i,index) in item.DA" :class="{  cur: selectKey === index, error: key !== selectKey && selectKey!=='' && selectKey === index , correct: selectKey!=='' && key == index }" @click="changeSelectKey(index, item)">{{i.DANR}}</li>
					</ul>
					
					<ul class="xuanxiang" v-show="item.ST.TMLX==='FXT' && !subMul">
						<li v-for="(i,index) in item.DA" :class="{  cur: selectArr.includes(index)}" @click="changeSelectArr(index, item)">{{i.DANR}}</li>
					</ul>
					
					<ul class="xuanxiang" v-show="item.ST.TMLX==='FXT' && subMul">
						<li v-for="(i,index) in item.DA" :class="{correct: i.SFDA === '1', error: !(i.SFDA === '1') && selectArr.includes(index) }">{{i.DANR}}</li>
					</ul>
					
					
					<div class="laiyuan">
						<p @click="showAnswer = true" v-show="selectKey!=='' || (item.ST.TMLX==='FXT' && subMul)">查看答案解析</p>
					</div>
				</div>
				<div class="mask" style="display: block;" v-show="showAnswer"></div>
					
				<div class="pd-dlg dlg2" v-show="showAnswer">
					<!-- <h1>正确答案：A</h1> -->
					<p>{{item.ST.ZTJX}}</p>
					<small class="dlgcls" @click="showAnswer= false"></small>
				</div>
				
				
				<div class="btns">
					<p class="fanhui" @click="prev()">上一题</p>
					<p class="jixu" @click="submitItem()" v-show="item.ST.TMLX==='FXT' && !subMul">提交</p>
					<p class="jixu" @click="next()" v-show="item.ST.TMLX!=='FXT' || (item.ST.TMLX=='FXT' && subMul)">下一题</p>
				</div>
			</div>
		</section>
	
		
	
	</Page>
</template>

<script> 
	import { getExerciseList, addErrorExercise } from '@/api/exercise.js'
	// correct error cur
	import Page from '@/pages/component/Page.vue';
	export default {
		components: {Page}, 
		data() {
		    return {
				num: 0, // 第几道题
				key: 0,
				selectArr: [], // 选择的数组
				selectKey: '',
		        title: '练习',
				showAnswer: false,
				subMul: false, // 显示答案
				item: {
					ST: {
						TMLXMC: '',
						TMNR: '',
						TMLX: ''
					}
				},
				list:  []
			}
		},
		mounted(){
			this.fetchData();
		},
		methods:{
			fetchData(){
				getExerciseList().then(res=>{
					this.list = res.data_json;
					
					this.item = this.list[this.num];
					this.dealKey();
				})
				
			},
			onFixedHeight(layout) {
				this.pageHeight = layout.height;
			},
			next(){
				if(this.num<(this.list.length-1)){
					this.num++;
					this.item = this.list[this.num];
					this.selectKey = '';
					this.selectArr = [];
					this.subMul = false;
					this.dealKey();
				}else{
					alert('最后一道题了')
				}
			},
			prev(){
				if(this.num >0){
					this.num--;
					this.item = this.list[this.num];
					this.dealKey();
				}
			},// 处理答案
			dealKey(){
				if(this.item.type == 'single'){
					this.item.DA.forEach((item, index)=>{
						if(item.SFDA === '1'){
							this.key = index;
						}
					})
				}
				
				console.log(this.key);
			},
			changeSelectKey(key, item){
				// 没有选择的时候可以选
				if(this.selectKey === ''){
					this.selectKey = key;
					
					if(this.key != this.selectKey && this.selectKey!=''){
						this.errorItem();
						// 这里要发送到后台
					}
				}
			},
			// 多选题
			changeSelectArr(key, item){
				let hasSel = this.selectArr.includes(key);
				// 判断是否选中
				if(hasSel){
					let newArr = [];
					this.selectArr.forEach(item=>{
						if(item != key){
							newArr.push(item);
						}
					})
					this.selectArr = newArr;
				}else{
					this.selectArr.push(key);
				}
			},
			// 多选题确认
			submitItem(){
				this.subMul = true;
				let selArr = this.selectArr.sort();
				let anArr = [];
				this.item.DA.forEach((item,index)=>{
					if(item.SFDA === '1'){
						anArr.push(index);
					}
				})
				
				if(selArr.toString() != anArr.toString()){
					this.errorItem();
					// 这里要发送到后台
				}
			},
			errorItem(){
				addErrorExercise({
					'STXH':this.item.ST.XH,
					'STXXXH':'',
					'CTLY':'LX',
					'CTLYMC':'练习'}
				).then(res=>{
					console.log('发送到后台')
				})
			}
		}
	}
</script>

<style>
</style>
