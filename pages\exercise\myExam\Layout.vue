<template>
	<Page class="power-page" :title="title" @layoutAware="onFixedHeight">
		<template v-slot:bar>
			<NaviBar :title="title"  />
		</template>
		<section class="pd-main">
			<div class="pd-inner" style="padding-top: 0;">
				<scroll-view class="record-task-list" scroll-y="true"  style="height: calc(100vh - 200rpx);"  @scrolltolower="onReachBottomEvent" v-if="list.length>0">
					<dl class="pd-dlbx1" v-for="item in list">
						<div>
							<dd>
								<h2 class="important-title" v-html="item.KSMC">{{item.KSMC}}</h2>
								<p></p>
								<p class="left-num"></p>
								<p class="left-type">{{item.FS || '-'}}</p>
							</dd>
						</div>
					</dl>
				</scroll-view>
				<noData v-if="list.length==0"></noData>
			</div>
		</section>
	</Page>
</template>

<script>
	import { GetExamList } from '@/api/exercise.js'
	import Page from '@/pages/component/Page.vue';
	import noData from '@/components/no-data.vue';
	import NaviBar from '@/pages/component/NaviBar.vue';
	export default {
		components: {
			Page,
			NaviBar,
			noData
		}, 
		data() {
		    return {
				totoa: 0,
				pageSize: 10,
				pageNum: 1,
		        title: '我的考试',
				list: [],
				item: {
					ST: {
						TMLXMC: '',
						TMNR: '',
						TMLX: ''
					}
				}
			}
		},
		mounted(){
			this.getData();
			
		},
		methods:{
			onReachBottomEvent (){
				this.pageNum++;
				this.getData();
			},
			onFixedHeight(layout) {
				this.pageHeight = layout.height;
			},
			getData(){
				if(this.list.length < this.total || this.list.length == 0){
					GetExamList({'pageSize':this.pageSize,'pageNum':this.pageNum}).then(res=>{
						this.list.push(...res.data_json.list);
						this.total = res.data_json.total;
					})
				}
			}
		}
	}
</script>

<style scoped>
	.left-type{
		color:#4FB053;
		padding-left: 30rpx;
	}
	.pd-dlbx1 dd h2{
		padding-left: 30rpx;
	}
</style>
