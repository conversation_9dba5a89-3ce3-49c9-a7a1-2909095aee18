import {deepCopyObject} from '@/common/merge.js';
import fileService from '@/api/file-service.js'

/**
 * 元素类型定义，对应表单配置中单个元素的type属性值
 */
const ELEMENT_TYPE_GROUP = 'GROUP_ELEMENT';
const ELEMENT_TYPE_TITLE = 'TITLE_ELEMENT';
const ELEMENT_TYPE_TEXT = 'TEXT_ELEMENT';
const ELEMENT_TYPE_TEXT_AREA = 'TEXT_AREA_ELEMENT';
const ELEMENT_TYPE_DYNA_GROUP = 'dynagroup';
const ELEMENT_TYPE_DISTRICT = 'DIVISION_TREE_ELEMENT';
const ELEMENT_TYPE_DISTRICT_CASCADE = 'REGIONSELECT_ELEMENT';
const ELEMENT_TYPE_CODE = 'CODESET_ELEMENT';
const ELEMENT_TYPE_DATE = 'DATE_ELEMENT';
const ELEMENT_TYPE_RADIO = 'RADIO_ELEMENT';
const ELEMENT_TYPE_CHECKBOX = 'CHECKBOX_ELEMENT';
const ELEMENT_TYPE_SELECT = 'SELECT_ELEMENT';
const ELEMENT_TYPE_PERSON_RADIO = 'PERSON_ELEMENT_RADIO';
const ELEMENT_TYPE_ATTACH = 'ATTACH_ELEMENT';
const ELEMENT_TYPE_INDUSTRYSELECT = "INDUSTRYSELECT_ELEMENT";
const ELEMENT_TYPE_DYNAMIC_DATA_SELECT_ELEMENT = "DYNAMIC_DATA_SELECT_ELEMENT"
const ELEMENT_TYPE_TREE = "TREE_DATA_SELECT_ELEMENT";
const ELEMENT_TYPE_INDUSTRY = "INDUSTRY_ELEMENT"
const ELEMENT_TYPE_PERSON = "PERSON_ELEMENT";
const ELEMENT_TYPE_DEPARTMENT = "DEPARTMENT_ELEMENT";
const ELEMENT_TYPE_LATITUDE_LONGITUDE = "LATITUDE_LONGITUDE_ELEMENT";
const ELEMENT_TYPE_SIGNATURE = "YDZF_QM_ELEMENT";

const ELEMENT_TYPE_CODESET = "CODESET_ELEMENT";
const ELEMENT_TYPE_TAG = "TAG_ELEMENT";

//执法表单特有的类型
const ELEMENT_TYPE_ENFORCER = "YDZF_PERSON_ELEMENT";
const ELEMENT_TYPE_PATTERN = "YDZF_CYY_ELEMENT";
const ELEMENT_TYPE_DESCRIPTION = "DESCRIPTION_ELEMENT";

//元素关于附件元素大类、小类属性名称定义
const ATTACH_MAJOR_CATEGORY_ATTR = 'uploadFileBigClass'
const ATTACH_MINOR_CATEGORY_ATTR = 'uploadFileClass'

//表单元素变更监听事件
export const EVENT_FROM_VALUE_CHANGE = 'onFormValueChange'
export const EVENT_ELEMENT_MOUNTED = 'onElementMounted'

/**
 * 对外暴露的元素类型集合，便于统一管理
 */
export const ELEMENT_TYPE = {
	codeset: ELEMENT_TYPE_CODESET,
	group: ELEMENT_TYPE_GROUP,
	dynaGroup: ELEMENT_TYPE_DYNA_GROUP,
	title: ELEMENT_TYPE_TITLE,
	text: ELEMENT_TYPE_TEXT,
	textArea: ELEMENT_TYPE_TEXT_AREA,
	district: ELEMENT_TYPE_DISTRICT,
	districtCascade: ELEMENT_TYPE_DISTRICT_CASCADE,
	code: ELEMENT_TYPE_CODE,
	date: ELEMENT_TYPE_DATE,
	radio: ELEMENT_TYPE_RADIO,
	checkbox: ELEMENT_TYPE_CHECKBOX,
	personRadio: ELEMENT_TYPE_PERSON_RADIO,
	attach: ELEMENT_TYPE_ATTACH,
	select: ELEMENT_TYPE_SELECT,
	industry: ELEMENT_TYPE_INDUSTRY,
	tree: ELEMENT_TYPE_TREE,
	industryselect : ELEMENT_TYPE_INDUSTRYSELECT,
	person: ELEMENT_TYPE_PERSON,
	department: ELEMENT_TYPE_DEPARTMENT,
	latitudeLongitude: ELEMENT_TYPE_LATITUDE_LONGITUDE,
	signature: ELEMENT_TYPE_SIGNATURE,
	tag: ELEMENT_TYPE_TAG,
	dataSelect: ELEMENT_TYPE_DYNAMIC_DATA_SELECT_ELEMENT,
	
	//移动执法特有类型
	enforcer: ELEMENT_TYPE_ENFORCER,
	pattern: ELEMENT_TYPE_PATTERN,
	description: ELEMENT_TYPE_DESCRIPTION,
}

//动态元素组数据标记字段，用于追踪更新字段所属对象
export const MARK_FIELD = '_mark_field';

/**
 * 公共代码值元素适配到单选、多选、下拉框元素
 */
const codesetComponentWrapper = (element) => {
	let codesetSubtype = element.dmjkjlx;
	let wrappedComponent;
	 switch(codesetSubtype){
		 case 'radio':
			wrappedComponent = 'radio-element';
			break;
		case 'checkbox':
			wrappedComponent = 'checkbox-element';
			break;
		case 'tag':
			wrappedComponent = 'tag-element'
			break
		default: 
			wrappedComponent = 'select-element';
	 }
	 return wrappedComponent;
}

/**
 * 元素组件与元素类型的映射关系
 */
export const ELEMENT_COMPONENT_DICT = new Map([
	[ELEMENT_TYPE.group, 'element-group'],
	[ELEMENT_TYPE.dynaGroup, 'dyna-group-element'],
	[ELEMENT_TYPE.title, 'title-element'],
	[ELEMENT_TYPE.text, 'text-element'],
	[ELEMENT_TYPE.textArea, 'textarea-element'],
	[ELEMENT_TYPE.district, 'district-tree-element'],
	[ELEMENT_TYPE.tree, 'tree-element'],
	[ELEMENT_TYPE.districtCascade, 'district-element'],
	[ELEMENT_TYPE.codeset, 'codeset-element'],
	[ELEMENT_TYPE.code, codesetComponentWrapper],
	[ELEMENT_TYPE.radio, 'radio-element'],
	[ELEMENT_TYPE.checkbox, 'checkbox-element'],
	[ELEMENT_TYPE.personRadio, 'contacts-element'],
	[ELEMENT_TYPE.attach, 'attach-group'],
	[ELEMENT_TYPE.select, 'select-element'],
	[ELEMENT_TYPE.date, 'date-element'],
	[ELEMENT_TYPE.industry, 'industry-tree-element'],
	[ELEMENT_TYPE.industryselect, 'industryselect-element'],
	[ELEMENT_TYPE.person, 'person-element'],
	[ELEMENT_TYPE.department, 'department-element'],
	[ELEMENT_TYPE.latitudeLongitude, 'latitude-longitude-element'],
	[ELEMENT_TYPE.dataSelect, 'data-select-element'],
	
	//执法表单特有的类型
	[ELEMENT_TYPE.enforcer, 'person-element'],
	[ELEMENT_TYPE.pattern, 'textarea-element'],
	[ELEMENT_TYPE.description, 'description-element'],
	[ELEMENT_TYPE.signature, 'signature-element'],
	
	
]);

/**
 * 元素是否元素组
 */
export const isElementGroup = (element) => {
	return element.type === ELEMENT_TYPE.group 
		|| element.type === ELEMENT_TYPE.dynaGroup
		|| element.type === ELEMENT_TYPE.attach;
}

/**
 * 元素是否附件元素
 */
export const isAttachElement = (element) => {
	return element.datasource === 'FILE_UPLOAD';
}

/**
 * 表单是否配置了元素组，只判断根元素下元素
 * @param {Object} element
 */
export const hasElementGroup = (element) => {
	let has = false;
	if(element.child && element.child.length > 0){
		for(let i = 0; i < element.child.length; i++){
			let child = element.child[i];
			has = isElementGroup(child);
			if(has){
				break;
			}
		}
	}
	return has;
}

/**
 * 创建一个包含附件动态元素组的空模板，因为平台部开发的动态表单，附件都是单独接口
 */
export const createMockAttachTemplate = (templateName, attachElement) => {
	let templateSample = {
		templateName: "模板表单",
		child: [

		]
	}
	
	//原始模板中存在附件元素才添加附件元素组
	if(attachElement){
		let attachGroup = {
			type: "dynagroup",
			label: "附件",
			dynatable: "T_ATTACH",
			child: [
				{
					type: "ATTACH_ELEMENT",
					label: "",
					dbcolum: ""
				}
			]
		}
		templateSample.child.push(attachGroup);
	}
	return templateSample;
}

/**
 * 查找表单根节点上的附件元素
 * @param {Object} template
 * @param {Boolean} deleteIt 是否删除该元素
 */
export const resolveAttachElement = (template, deleteIt = false) => {
	let attachElement = null;
	if(template.child && template.child.length > 0){
		let index = -1;
		for(let i = 0; i < template.child.length; i++){
			let child = template.child[i];
			if(isAttachElement(child)){
				index = i;
				break;
			}
		}
		if(index !== -1){
			if(deleteIt){
				attachElement = template.child.splice(index, 1)[0];
			} else {
				attachElement = template.child[index];
			}
		}
	}
	return attachElement;
}

/**
 * 使用标题元素创建一个元素组
 * @param {Object} element
 */
const createGroupByTitleElement = (element) => {
	return {
		type: ELEMENT_TYPE.group,
		name: element.label,
		label: element.label,
		show: element.show,
		child: []
	}
}

const createGroupByName = (name) => {
	return {
		type: ELEMENT_TYPE.group,
		name: name,
		label: name,
		show: '1',
		child: []
	}
}
/**
 * 把根节点下同层级的元素打包进元素组。由于后台对于不是子表的元素，没有元素组概念，
 * 但是当表单元素较多时，不对元素进行分组，表单没有层次感，视觉效果不佳
 */
const packPeerElementIntoGroup = (template) => {
	let templateCopy = deepCopyObject(template);
	let elements = templateCopy.child;
	let groupedElements = [];
	let group = null;
	elements.forEach(element => {
		if(element.type === ELEMENT_TYPE.title){
			//标题元素做为元素组的开始，直到下一个标题元素的所有元素
			//都放进一个组
			group = createGroupByTitleElement(element);
			groupedElements.push(group);
		} else if(isElementGroup(element)){
			groupedElements.push(element);
			group = null;
		} else {
			//前置元素不是标题也不是元素组，创建一个虚拟的元素组包裹
			if(group === null){
				group = createGroupByName('');
				groupedElements.push(group);
			}
			group.child.push(element);
		}
	});
	templateCopy.child = groupedElements;
	return templateCopy;
}

/**
 * 查找模板配置中只设置了大类的附件，自动补全大类下各小类附件
 */
export const alignTemplateMajorAttachGroup = (template) => {
	return new Promise((resolve) => {
		let attachElement = resolveAttachElement(template)
		if(attachElement === null) {
			resolve(template)
		}
		
		let majorCateCode = attachElement[ATTACH_MAJOR_CATEGORY_ATTR] || ''
		let minorCateCode = attachElement[ATTACH_MINOR_CATEGORY_ATTR] || ''
		if(majorCateCode === '' || minorCateCode !== '') {
			//大类为空或大类、小类都不为空，直接返回
			resolve(template)
		}
		
		let cloneTemplate = deepCopyObject(template)
		fileService.queryAttachCategory(majorCateCode)
			.then(majorCate => {
				if(majorCate && majorCate.children) {
					let alignElements = []
					majorCate.children.forEach(cate => {
						let cloneElement = deepCopyObject(attachElement)
						cloneElement.label = cate.name
						cloneElement.YSMC = cate.name
						cloneElement[ATTACH_MINOR_CATEGORY_ATTR] = cate.code
						alignElements.push(cloneElement)
					})
					if(alignElements.length > 0) {
						replaceAttachElement(cloneTemplate, attachElement, alignElements)
					}
				}
			})
			.catch(error => {
				console.log(`获取附件类型出错：${error}`)
			})
			.finally(() => {
				resolve(cloneTemplate)
			})
	})
}

const replaceAttachElement = (template, target, replaces) => {
	let children = template.child || []
	for(let index = 0; index < children.length; index++ ) {
		let element = children[index]
		if(target.id === element.id) {
			let cloneReplaces = deepCopyObject(replaces)
			cloneReplaces.unshift(index, 1)
			Array.prototype.splice.apply(children, cloneReplaces)
			return true
		}
		let match = replaceAttachElement(element, target, replaces)
		if(match) {
			return match
		}
	}
	return false
}

export const updateFormData = (state, data) => {
	let target = state.formData;
	if(data[MARK_FIELD]) {
		target = retriveMarkObject(target, data[MARK_FIELD]) || target;
	}
	Object.assign(target, data);
}

const retriveMarkObject = (parent, mark) => {
	if(parent[MARK_FIELD] && parent[MARK_FIELD] === mark) {
		return parent;
	} else {
		for(let field in parent) {
			let value = parent[field];
			if(value) {
				if(Array.isArray(value)){
					for(let item of value) {
						let target = retriveMarkObject(item, mark);
						if(target){
							return target;
						}
					}
				} else if (typeof value === 'object') {
					let target = retriveMarkObject(value, mark);
					if(target){
						return target;
					}
				}
			}
		}
		return null;
	}
}

export default {
	hasElementGroup,
	createMockAttachTemplate,
	resolveAttachElement,
	packPeerElementIntoGroup,
	alignTemplateMajorAttachGroup,
	
	/**
	 * 重组表单模板，之所以需要重组表单，是因为很多情况下，表单未配置元素组，在渲染上不好表达
	 * @param {Object} originalTemplate
	 */
	recombinTemplate(originalTemplate){
		let result = null;
		if(hasElementGroup(originalTemplate)){
			result = originalTemplate;
		} else {
			let attachElement = resolveAttachElement(originalTemplate);
			//未配置动态表单
			let mockAttachTemplate = createMockAttachTemplate(originalTemplate.templateName, attachElement);
			//没有配置元素组的情况下，需要把当前表单当成一个元素组
			originalTemplate.type = 'GROUP_ELEMENT';
			//移除附件元素节点，因为构造的根节点已经包含
			resolveAttachElement(originalTemplate, true);
			mockAttachTemplate.child.unshift(originalTemplate);
			result = mockAttachTemplate;
		}
		return result;
	}
}

