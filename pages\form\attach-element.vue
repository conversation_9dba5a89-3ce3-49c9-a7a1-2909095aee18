<template>
    <view>
        <show-modal></show-modal>
        <view
            :id="'templateId_' + template.id"
            class="flex-row-layout attach-element"
        >
            <view
                class="flex-row-layout attach-element-icon__wrapper"
                @click="downloadAttach"
            >
                <image
                    class="attach-element-icon"
                    :src="icon"
                    mode="aspectFill"
                />
            </view>
            <view
                class="flex-column-layout"
                style="
                    width: calc(100% - 240rpx);
                    margin-left: 20rpx;
                    position: relative;
                "
            >
                <view class="attach-element-title">
                    <view>
                        <!-- <input class="attach-element-name"
							   :focus="focusVal"
							   :disabled="editNameType"
							   @blur="enterBlur"
							   @input="formDataInput"
							   v-model="formData.WDMC"
							   :name="attachName" /> -->
                        <text class="attach-element-name">{{
                            getFormDataName(formData.WDMC) || attachName
                        }}</text>
                    </view>
                </view>
                <view class="flex-row-layout" style="margin-top: 30rpx">
                    <text class="attach-element-script">
                        上传时间：{{ formData.SCSJ || formData.CJSJ }}
                    </text>
                </view>
                <view class="flex-row-layout" style="margin-top: 30rpx">
                    <text class="attach-element-script">
                        文件大小：{{ formData.WDDX }}
                    </text>
                </view>
                <view v-if="editable" class="attach-element-change">
                    <image
                        v-if="template.id !== TEMPLATE_PHONEID"
                        class="attach-element-editicons"
                        v-show="editNameType"
                        @click="editAttachName()"
                        :src="attchEditIcon"
                    />
                    <!-- <image class="attach-element-editicon" v-show="!editNameType" @click="enterAttachName()" :src="attchDetermineIcon" /> -->
                    <image
                        class="attach-element-editicon"
                        :src="attchDetermineIcon"
                        @click.stop="deleteIcon(formData)"
                    />
                </view>
            </view>
        </view>
        <uni-popup ref="attachPopup" type="bottom">
            <view class="attach-dialog">
                <view class="attach-list">
                    <!-- <image :src="icon" mode="aspectFit" class="attach-dialog-img"/> -->
                    <!-- <view> -->
                    <uni-forms-item label="照片名称" name="name">
                        <uni-easyinput
                            type="text"
                            class="attach-input"
                            v-model="attachName"
                            :clearable="true"
                            align="left"
                            placeholder="请输入照片名称"
                        />
                    </uni-forms-item>
                    <uni-forms-item label="照片描述" name="age">
                        <uni-easyinput
                            type="textarea"
                            autoHeight
                            maxlength="100"
                            class="attach-input"
                            align="left"
                            v-model="attachCaption"
                            placeholder="请输入照片描述"
                        />
                    </uni-forms-item>
                    <!-- </view> -->
                </view>
                <view class="attach-button">
                    <button
                        class="attach-buttons"
                        type="default"
                        @click="closePopup()"
                    >
                        取消
                    </button>
                    <button
                        class="attach-buttons"
                        type="primary"
                        @click="enterAttachName()"
                    >
                        确定
                    </button>
                </view>
            </view>
        </uni-popup>
        <uni-popup ref="videoPopup">
            <video id="myVideo" controls :src="vidioUrl"></video>
            <!-- <yy-video-player
			:auto-play="true"
			:url="vidioUrl"
			:show-back-btn="true"
		></yy-video-player> -->
        </uni-popup>
        <view class="attach-footer">
            <view class="attach-caption" v-if="formData.REMARKS"
                >照片描述：{{ formData.REMARKS }}</view
            >
            <view v-if="progress !== 0 && progress !== 100">
                <text>下载进度</text>
                <progress
                    :percent="progress"
                    show-info
                    activeColor="rgb(15, 174, 255)"
                    stroke-width="8"
                />
            </view>
        </view>
    </view>
</template>

<script>
import yyVideoPlayer from '@/components/yy-video-player/yy-video-player.nvue';
import { deleteElectronicFile, editAttachNameFile } from '@/api/record.js';
import iconvideo from '@/static/img/file/video.png';
import iconDoc from '@/static/img/file/word.png';
import iconExcel from '@/static/img/file/excel.png';
import iconPpt from '@/static/img/file/ppt.png';
import iconPdf from '@/static/img/file/pdf.png';
import attchEditIcon from '@/static/img/file/attach_edit.png';
import attchDetermineIcon from '@/static/img/file/attach_delete.png';
import iconEdit from '@/static/img/operate/icon_edit.png';
import circleProgress from '@/pages/component/circle-progress.vue';
import element from './element.js';
import fileService from '@/api/file-service.js';
const EDIT_RENAME = 0;
const EDIT_DEL = 1;
const TEMPLATE_PHONEID = '202103272044469f822fa94bdf47e99a8290aa3c566e98';
const mixins = [element];

export default {
    name: 'attach-element',
    // #ifdef MP-ALIPAY
    mixins: mixins.map(item => ({
        ...item,
        props: {}
    })),
    props: {
        ...mixins.reduce(
            (prev, curr) => ({
                ...prev,
                ...(curr.props || {})
            }),
            {}
        )
    },

    // #endif
    // props: {
    // 	progress: {
    // 		type: Number
    // 	},
    // },

    // #ifndef MP-ALIPAY
    mixins: mixins,
    // #endif
    components: {
        circleProgress,
        yyVideoPlayer
    },

    data() {
        return {
            TEMPLATE_PHONEID,
            focusVal: false,
            iconEdit,
            attchEditIcon,
            attchDetermineIcon,
            editNameType: true,
            attachName: '', //附件的名称
            attachCaption: '', //附件的描述
            path: null,
            vidioUrl: '',
            progress: 0
        };
    },

    computed: {
        suffix: function () {
            if ((this.formData && this.formData.WDHZ) || this.formData.WDMC) {
                let name = this.formData.WDHZ || this.formData.WDMC;
                let lastDotIndex = name.lastIndexOf('.');
                if (lastDotIndex !== -1) {
                    return name.substring(lastDotIndex + 1).toLowerCase();
                } else {
                    return name;
                }
            } else {
                return 'unkown';
            }
        },

        icon: function () {
            switch (true) {
                case /doc|docx/g.test(this.suffix):
                    return iconDoc;
                case /xls|xlsx/g.test(this.suffix):
                    return iconExcel;
                case /ppt/g.test(this.suffix):
                    return iconPpt;
                case /pdf/g.test(this.suffix):
                    return iconPdf;
                case /jpg|jpeg|png/g.test(this.suffix):
                    if (this.formData.url) {
                        return this.formData.url;
                    } else if (this.formData.WDBH) {
                        return fileService.getFileUrl(this.formData.WDBH);
                    }
                default:
                    return iconvideo;
            }
        },

        isPicture: function () {
            return /jpg|jpeg|png/g.test(this.suffix) ? true : false;
        },

        isVideo: function () {
            return /mp4|avi/g.test(this.suffix) ? true : false;
        }
    },

    mounted() {
        this.attachName = this.formData.WDMC;
        this.attachCaption = this.formData.REMARKS;
    },

    methods: {
        // #ifdef MP
        /**
         * 小程序需要主动更新附件ID，直接在父组件修改不会影响到子组件接收的对象
         * @param {Object} attachId
         */
        resetAttachId(refId, attachId) {
            if (this.formData.refId === refId) {
                this.formData.WDBH = attachId;
            }
        },
        // #endif

        // #ifdef MP-ALIPAY
        updateProgress(refId, progress) {
            if (this.formData.refId === refId) {
                this.formData.progress = progress;
                this.$refs.progress.updateProgress(progress);
                // this.progress = progress;
            }
        },
        // #endif

        //删除附件
        deleteIcon(formData) {
            // this.$emit('deleteAttachD',formData)
            let self = this;
            uni.showModal({
                title: '提示',
                content: '确定删除吗？',
                cancelText: '确认',
                confirmText: '取消',
                cancelColor: 'rgb(15, 174, 255)',
                confirmColor: '#666',

                success: function (res) {
                    if (res.confirm) {
                    } else if (res.cancel) {
                        //烟台的特别判断，如果是烟台的操作就走另外一个方法
                        let type = '';
                        let id = uni.getStorageSync('YANTAI_TEMPLATE').id;
                        if (
                            id ==
                            '202112270908216bfab732e8c941cf9b4b312e06044af6'
                        ) {
                            type = 'yantai';
                        } else {
                            type = '';
                        }
                        deleteElectronicFile(
                            {
                                WDBH: formData.WDBH
                            },
                            type
                        ).then(res => {
                            self.$emit('deleteFile', formData.WDBH);
                            // self.getElectronicList()
                        });
                    }
                }
            });

            // this.$showModal({
            // 	title: '提示',
            // 	content: '确定删除吗？',
            // 	success: function(res) {
            // 		if (res.confirm) {
            // 			deleteElectronicFile({
            // 				WDBH: formData.WDBH
            // 			}).then((res) => {
            // 				self.$emit('deleteFile', formData.WDBH)
            // 				// self.getElectronicList()
            // 			})
            // 		} else if (res.cancel) {
            // 			('用户点击取消');
            // 		}
            // 	}
            // });
        },

        downloadAttach() {
            if (this.progress !== 0 && this.progress !== 100) {
                uni.showToast({
                    icon: 'none',
                    duration: 2000,
                    title: '正在下载中！'
                });
                return;
            }
            uni.$on('downloadTaskProgress' + this.formData.WDBH, res => {
                this.progress = res;
            });
            if (this.isPicture) {
                this.$emit('lookAttach', this.formData);
                return;
            } else {
                let fileId = this.formData.WDBH;
                let localUrl = uni.getStorageSync(fileId);
                if (this.isVideo) {
                    this.vidioUrl = fileService.getFileUrl(this.formData.WDBH);

                    // #ifdef H5
                    this.playVideo(this.vidioUrl);
                    // #endif

                    // #ifdef APP-PLUS
                    if (localUrl) {
                        this.playVideo(localUrl);
                    } else {
                        fileService.openDocument(
                            this.formData.WDBH,
                            this.formData.WDMC
                        );
                    }
                    // #endif
                } else {
                    if (localUrl) {
                        uni.openDocument({
                            filePath: localUrl
                        });
                    } else {
                        fileService.openDocument(
                            this.formData.WDBH,
                            this.formData.WDMC
                        );
                    }
                }
            }
        },

        //播放视频
        playVideo(url) {
            uni.navigateTo({
                url: `/pages/media/video-player?videoUrl=${url}`
            });
        },

        editAttachName() {
            this.attachName = this.getFormDataName(this.formData.WDMC); //用于不保存的情况下初始化输入内容，如果不需要可以进行删除
            this.attachCaption = this.formData.REMARKS; //用于不保存的情况下初始化输入内容，如果不需要可以进行删除
            this.$refs.attachPopup.open();
        },

        formDataInput() {},

        enterAttachName() {
            let name = this.formData.WDMC.substring(
                this.formData.WDMC.lastIndexOf('.') + 1
            ).toLowerCase();
            this.$set(this.formData, 'WDHZ', '.' + name);
            this.focusVal = false;

            //烟台的特别判断，如果是烟台的操作就走另外一个方法
            let type = '';
            let id = uni.getStorageSync('YANTAI_TEMPLATE').id;
            if (id == '202112270908216bfab732e8c941cf9b4b312e06044af6') {
                type = 'yantai';
            } else {
                type = '';
            }
            editAttachNameFile(
                {
                    wjbh: this.formData.WDBH,
                    remarks: this.attachCaption,
                    wjmc: this.attachName + '.' + name
                },
                type
            ).then(res => {
                this.editNameType = true;
                this.attachName = this.attachName;
                this.$set(this.formData, 'REMARKS', this.attachCaption);
                this.$set(this.formData, 'WDMC', this.attachName + '.' + name);
                this.$refs.attachPopup.close();
            });
        },

        enterBlur() {},

        showOperateMenu() {
            this.$refs.operateDialog.show();
        },

        closePopup() {
            this.$refs.attachPopup.close();
        },

        //关闭视频
        closeVideo() {
            this.vidioUrl = '';
        },

        onMenuClick(menu) {
            if (menu.id === EDIT_RENAME) {
                this.$emit('rename', this.formData);
            } else if (menu.id === EDIT_DEL) {
                this.$emit('delete', this.formData);
            }
        },

        //格式化附件的名称
        getFormDataName(name) {
            let index = name.lastIndexOf('.');
            name = name.substring(0, index);
            return name;
        }
    }
};
</script>

<style scoped>
.attach-element {
    position: relative;
    padding: 8px 0;
    overflow: visible;
}

.attach-element-icon__wrapper {
    position: relative;
    justify-content: center;
    width: 240rpx;
    height: 220rpx;
    border-radius: 10rpx;
    /* background-color: rgba(0, 0, 0, .3); */
}

.attach-element-icon__mask {
    border-radius: 10rpx;
    background-color: rgba(0, 0, 0, 0.3);
}

.attach-element-icon {
    width: 220rpx;
    height: 220rpx;
    position: absolute;
    left: 0;
    top: 0;
}

.attach-element-title {
    width: 100%;
    display: flex;
    /* align-items: center; */
    /* justify-content: space-between; */
}

.attach-element-name {
    width: 100%;
    font-size: 28rpx;
    color: #333;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

.attach-element-editicon {
    width: 50rpx;
    height: 50rpx;
}

.attach-element-editicons {
    width: 40rpx;
    height: 40rpx;
}

.attach-element-script {
    font-size: 24rpx;
    color: #888;
}

.attach-element-edit {
    width: 32rpx;
    height: 32rpx;
    position: absolute;
    top: 0rpx;
    right: 0rpx;
    z-index: 999;
}

.attach-delete {
    position: absolute;
    right: 0;
    color: #bbb;
}

.attach-dialog {
    height: 50vh;
    background-color: #ffffff;
    position: relative;
    z-index: 100000;
}

.attach-list {
    padding: 30rpx;
    border-radius: 10rpx;
    height: 35vh;
    /* display: flex; */
    /* align-items: center; */
    /* justify-content: space-around; */
    /* width: 90vw; */
}

.attach-button {
    display: flex;
    align-items: center;
    justify-content: space-around;
    width: 100%;
}

.attach-buttons {
    width: 38%;
}

.attach-caption {
    font-size: 28rpx;
    color: #aaa;
}

.attach-footer {
    border-bottom: 1px dashed #ddd;
}

.attach-input {
    text-align: left;
}

.attach-element-change {
    justify-content: space-evenly;
    align-items: center;
    display: flex;
    width: 100%;
}

.attach-dialog-img {
    width: 220rpx;
    height: 220rpx;
}
</style>
