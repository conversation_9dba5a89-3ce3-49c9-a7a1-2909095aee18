<template>
    <PCard
        :id="'templateId_' + template.id"
        ref="attachGroup"
        class="form-element-group"
        :title="template.label + `(${attachs.length})`"
        divider
        :contentStyle="contentStyle"
    >
        <template v-slot:option>
            <image
                class="show-image"
                v-show="!showType"
                @click="closeAttachs"
                :src="showIcon"
            />
            <image
                class="close-image"
                v-show="showType"
                @click="showAttachs"
                :src="closeIcon"
            />
        </template>
        <attach-element
            class="attach-group-item"
            v-show="showType"
            v-for="(item, index) in attachs"
            ref="attachElement"
            :key="index"
            :style="{ 'margin-top': index === 0 ? '0' : '20rpx' }"
            :record-id="recordId"
            :template="template"
            :form-data="item"
            :editable="editable"
            @rename="renameAttach"
            @deleteFile="delAttach"
            @lookAttach="lookAttach"
        />
        <view v-show="progress !== 0 && progress !== 100">
            <text>上传进度</text>
            <progress
                :percent="progress"
                show-info
                activeColor="rgb(15, 174, 255)"
                stroke-width="8"
            />
        </view>
        <text v-if="showEmptyTip" style="font-size: 16px; padding: 5px 0px">
            无
        </text>
        <view
            v-if="editable"
            class="form-element-button"
            @click="onUploadButtonClick"
        >
            <image :src="iconFile" class="file-downimage" />
            <view>上传</view>
        </view>
        <operate-menu-dialog
            ref="chooseMenuDialog"
            :menus="attachGroupMenus"
            @menuClick="onChooseMenuClick"
        />
    </PCard>
</template>

<script>
const TEMPLATE_PHONEID = '202103272044469f822fa94bdf47e99a8290aa3c566e98';
const PICTURE_TYPE = 'PICTURE'; // 照片的类型定义，这样的定义有风险，只是暂时这样写
const VIDEO_TYPE = 'VIDEO'; // 视频的类型定义，这样的定义有风险，只是暂时这样写
const DOCUMENT_TYPE = 'DOCUMENT'; // 文件的类型定义，这样的定义有风险，只是暂时这样写
import { ATTACH_DATA_KEY } from '@/api/form-service.js';
import { getURlUpload, getAttachList } from '@/api/record.js';
import element from './element.js';
import PCard from '@/pages/component/PCard.vue';
import attachElement from './attach-element.vue';
import operateMenuDialog from '@/pages/component/dialog/operate-menu-dialog.vue';
import webtype from '@/common/webtype.js';
import dayjs from 'dayjs';
import http from '@/common/net/http.js';
import fileUtil from '@/common/file.js';
import { guid } from '@/common/uuid.js';
import { deepCopyObject } from '@/common/merge.js';
import iconFile from '@/static/img/record/upfile.png';
import fileService from '@/api/file-service.js';
import showIcon from '@/static/app/images/arwrt1.png';
import closeIcon from '@/static/app/images/arwbot.png';
const mixins = [element];

export default {
    name: 'AttachGroup',
    components: {
        PCard,
        attachElement,
        operateMenuDialog
    },
    // #ifdef MP-ALIPAY
    mixins: mixins.map(item => ({ ...item, props: {} })),
    props: {
        ...mixins.reduce(
            (prev, curr) => ({ ...prev, ...(curr.props || {}) }),
            {}
        )
    },
    // #endif

    // #ifndef MP-ALIPAY
    mixins: mixins,
    // #endif
    data() {
        return {
            upUrl: '',
            imagesList: [],
            contentStyle: {
                width: 'calc(100% - 40rpx)',
                padding: '20rpx'
            },
            progress: 0, //进度条
            showType: true, //附件列表是否隐藏
            showIcon,
            closeIcon,
            iconFile,
            attachs: [],
            typeId: '',
            chooseImageType: '', //选择的图片上传方式：album:相册、camera:相机
            attachGroupMenus: [], //选择了的类型
            choosePhotoMenus: [
                //照片类型的上传
                { id: 'album', name: '相册' },
                { id: 'camera', name: '拍照' }
            ],
            chooseVideoMenus: [
                //照片类型的上传
                { id: 'album_video', name: '视频相册' },
                { id: 'camera_video', name: '视频拍摄' }
            ],
            chooseDocumentMenus: [
                //文件类型的上传
                { id: 'document', name: '文件' }
            ],
            chooseAlltMenus: [
                //基本类型的上传
                { id: 'album', name: '相册' },
                { id: 'camera', name: '拍照' },
                { id: 'album_video', name: '视频相册' },
                { id: 'camera_video', name: '视频拍摄' },
                { id: 'document', name: '文件' }
            ],
            EvidenceMenus: [
                //带有执法证据类型的上传
                { id: 'evidence', name: '执法证据' },
                { id: 'album', name: '相册' },
                { id: 'camera', name: '拍照' }
            ],
            drawFileMenus: [
                //勘查示意图的上传
                { id: 'draw', name: '画板' },
                { id: 'album', name: '相册' },
                { id: 'camera', name: '拍照' }
            ]
        };
    },

    computed: {
        //附件大类
        attachMajorCate: function () {
            return this.template.uploadFileBigClass;
        },

        //附件小类
        attachMinorCate: function () {
            return this.template.uploadFileClass || '';
        },

        showEmptyTip: function () {
            let attachs = this.attachs || [];
            return !this.editable && attachs.length === 0;
        },

        attachField: function () {
            return `${this.attachMajorCate}_${this.attachMinorCate}`;
        },

        //是否绘制勘验示意图
        isDrawDiagram: function () {
            return this.template.isDrawDiagram === '1';
        }
    },

    watch: {
        formData: function (val) {
            let loadedAttachs =
                val[this.attachField] || val[ATTACH_DATA_KEY] || [];
            this.attachs = deepCopyObject(loadedAttachs);
        }
    },

    destroyed() {
        uni.$off('attach_otherData');
    },

    mounted() {
        let id = uni.getStorageSync('YANTAI_TEMPLATE').id;
        if (id == '202112270908216bfab732e8c941cf9b4b312e06044af6') {
            this.upUrl = http.yantai;
        } else {
            this.upUrl = http.loginUrl;
        }
        console.log(this.upUrl, '123');
        let data = this.template.uploadFileClassInformation;
        if (data) {
            data.forEach(element => {
                if (
                    element.lxdm === this.attachMajorCate &&
                    element.zlxdm === this.attachMinorCate
                ) {
                }
            });
        }
    },

    methods: {
        innerGroupStyle(index) {
            return {
                'margin-top': index === 0 ? '0px' : '10px'
            };
        },

        onUploadButtonClick() {
            let id = this.template.id;
            if (webtype.webTypeTest()) {
                if (this.isDrawDiagram) {
                    this.attachGroupMenus = this.drawFileMenus;
                } else if (id === TEMPLATE_PHONEID) {
                    //定义查看是否要带上执法证据的数据
                    this.attachGroupMenus = this.EvidenceMenus;
                    uni.removeStorageSync('TEMPLATE_PHONEID'); //带上之后需要清除，不然下次点击别的上传附件还会带出执法证据来
                } else if (this.attachMinorCate === PICTURE_TYPE) {
                    this.attachGroupMenus = this.choosePhotoMenus;
                } else if (this.attachMinorCate === VIDEO_TYPE) {
                    this.attachGroupMenus = this.chooseVideoMenus;
                } else if (this.attachMinorCate === DOCUMENT_TYPE) {
                    this.attachGroupMenus = this.chooseDocumentMenus;
                } else {
                    this.attachGroupMenus = this.chooseAlltMenus;
                }
                this.$refs.chooseMenuDialog.show();
            } else {
                uni.showToast({
                    title: '当前无网络，无法上传',
                    duration: 2000,
                    icon: 'none'
                });
            }
        },

        onChooseMenuClick(menu) {
            if (menu.id === 'album_video' || menu.id === 'camera_video') {
                this.chooseImageType = menu.id;
                this.chooseVideos();
            }
            if (menu.id === 'album') {
                this.chooseImageType = 'album';
                this.chooseImages();
            }

            if (menu.id === 'camera') {
                this.chooseImageType = 'camera';
                this.chooseImages();
            }
            if (menu.id === 'evidence') {
                uni.$once('chooseEvidenceData', r => {
                    r.forEach(element => {
                        getAttachList({
                            wjid: element.WDBH,
                            lxdm: this.attachMajorCate,
                            zlxdm: this.attachMinorCate,
                            recordid: this.recordId
                        }).then(res => {
                            let attach = JSON.parse(res.wd_data);
                            element.WDBH = attach[0].WDBH;
                            element.WDMC = attach[0].WDMC;
                            (element.LXDM = this.attachMajorCate),
                                (element.ZLXDM = this.attachMinorCate),
                                this.updateFormAttachData(element);
                        });
                    });
                });
                uni.navigateTo({
                    url: `/pages/record/record-attach-choose-list`
                });
            }

            if (menu.id === 'document') {
                this.chooseDocumentFile();
            }
            if (menu.id === 'draw') {
                this.drawDiagram();
            }
        },

        drawDiagram() {
            let _self = this;
            uni.$once('kcsyt_print', r => {
                let time = dayjs().format('YYYY-MM-DD');
                // let id = r.substring(r.lastIndexOf("/") + 1);
                let attach = {
                    refId: guid(),
                    recordId: _self.recordId,
                    upLoad: r.tempFilePath + '',
                    progress: 100,
                    // WDMC: id,
                    SCSJ: time
                };
                getURlUpload({
                    LXDM: _self.attachMajorCate,
                    ZLXDM: _self.attachMinorCate,
                    YWSJID: attach.recordId,
                    base64Img: attach.upLoad
                }).then(uploadFileRes => {
                    let attachs = JSON.parse(uploadFileRes.wd_data);
                    attach.WDBH = attachs.WDBH;
                    attach.WDMC = attachs.WDMC;
                    _self.updateFormAttachData(attach);
                });
                // _self.uploadAttach(attach);
            });
            uni.navigateTo({
                url: `/pages/component/drawBoard/signature-page`
            });
        },

        /**
         * 选择word、excel等类型的文件
         */
        chooseDocumentFile() {
            let _self = this;
            let FileChooser = plus.android.importClass(
                'com.bovosz.webapp.file.FileChooser'
            );
            let listener = plus.android.implements(
                'com.bovosz.webapp.file.WebFileChooseListener',
                {
                    onFileChoose: (filePath, size) => {
                        let time = dayjs().format('YYYY-MM-DD');
                        let attach = {
                            refId: guid(),
                            recordId: _self.recordId,
                            url: filePath,
                            progress: 0,
                            WDMC: fileUtil.parseFileName(filePath),
                            SCSJ: time,
                            WDDX: fileUtil.bitSizeToElegantSize(size)
                        };
                        _self.updateFormAttachData(attach);
                        let urls = `${_self.upUrl}/webapp/uploadFile`;
                        _self.uploadAttach(attach, urls);
                    }
                }
            );
            FileChooser.webChooseFile(
                plus.android.runtimeMainActivity(),
                listener
            );
        },

        /**
         * 选择图片
         */
        chooseImages() {
            let _self = this;
            uni.chooseImage({
                sizeType: ['compressed'], //可以指定是原图还是压缩图，默认二者都有
                sourceType: [this.chooseImageType], //从相册选择
                success(chooseInfo) {
                    // _self.log(chooseInfo, '选择的图片信息')
                    let now = dayjs();
                    let time = now.format('YYYY-MM-DD');
                    let timestamp = now.format('YYYY-MM-DD_HHmmss');
                    chooseInfo.tempFilePaths.forEach((fileUrl, index) => {
                        let id = fileUrl.substring(
                            fileUrl.lastIndexOf('/') + 1
                        );
                        // #ifndef MP-ALIPAY
                        let file = chooseInfo.tempFiles[index];
                        // #endif

                        // #ifdef MP-WEIXIN
                        let name = `${
                            _self.attachMajorCate
                        }_${timestamp}_${index}.${fileUtil.parseFileSuffix(
                            file.path
                        )}`;
                        // #endif
                        // #ifdef MP-ALIPAY
                        //钉钉小程序读取文件信息字段使用files
                        let file = chooseInfo.files[index];
                        let name = `${_self.attachMajorCate}_${timestamp}_${index}.${file.fileType}`;
                        // #endif
                        // #ifndef MP
                        let name = file.name;
                        // #endif
                        let size = file.size;
                        let attach = {
                            refId: guid(),
                            recordId: _self.recordId,
                            url: fileUrl,
                            progress: 0,
                            WDMC: name || id,
                            SCSJ: time,
                            WDDX: fileUtil.bitSizeToElegantSize(size)
                        };

                        // #ifdef APP-PLUS
                        if (_self.chooseImageType === 'camera') {
                            uni.saveImageToPhotosAlbum({
                                filePath: chooseInfo.tempFilePaths[0],
                                success: function (r) {}
                            });
                        }
                        // #endif

                        let urls = `${_self.upUrl}/webapp/uploadFile`;
                        _self.uploadAttach(attach, urls);
                    });
                },
                fail(error) {
                    _self.error(error || '', '选择图片出错');
                }
            });
        },

        /**
         * 选择视频
         */
        chooseVideos() {
            let _self = this;
            uni.chooseVideo({
                sizeType: ['compressed'], //可以指定是原图还是压缩图，默认二者都有
                sourceType:
                    this.chooseImageType === 'album_video'
                        ? ['album']
                        : ['camera'], //从相册选择
                success(chooseInfo) {
                    let now = dayjs();
                    let time = now.format('YYYY-MM-DD');
                    let timestamp = now.format('YYYY-MM-DD_HHmmss');
                    // chooseInfo.tempFilePaths.forEach((fileUrl, index) => {
                    let id = chooseInfo.tempFilePath.substring(
                        chooseInfo.tempFilePath.lastIndexOf('/') + 1
                    );
                    // #ifndef MP-ALIPAY
                    let file = chooseInfo.tempFilePath;
                    // #endif

                    // #ifdef MP-WEIXIN
                    let name = `${
                        _self.attachMajorCate
                    }_${timestamp}_${index}.${fileUtil.parseFileSuffix(
                        file.path
                    )}`;
                    // #endif
                    // #ifdef MP-ALIPAY
                    //钉钉小程序读取文件信息字段使用files
                    let file = chooseInfo.files[index];
                    let name = `${_self.attachMajorCate}_${timestamp}_${index}.${file.fileType}`;
                    // #endif
                    // #ifndef MP
                    let name = file.name;
                    // #endif
                    let size = chooseInfo.size;
                    let attach = {
                        refId: guid(),
                        recordId: _self.recordId,
                        url: chooseInfo.tempFilePath,
                        progress: 0,
                        WDMC: name || id,
                        SCSJ: time,
                        WDDX: fileUtil.bitSizeToElegantSize(size)
                    };
                    let urls = `${_self.upUrl}/webapp/uploadFile`;
                    _self.uploadAttach(attach, urls);

                    // })
                },
                fail(error) {
                    _self.error(error || '', '选择视频出错');
                }
            });
        },

        /**
         * 上传附件
         * @param {Object} attach
         */
        uploadAttach(attach, url) {
            let _self = this;
            let formData = {
                LXDM: _self.attachMajorCate,
                ZLXDM: _self.attachMinorCate,
                YWSJID: attach.recordId,
                WJMC: attach.WDMC,
                WJLX: fileUtil.parseFileSuffix(attach.WDMC)
            };
            let recordData = uni.getStorageSync('record-data');
            let recordData2 = uni.getStorageSync('choose_company');
            //针对水印添加的信息
            if (recordData) {
                formData.ISWATERMARK = true;
                formData.FIRSTWATERMARK =
                    recordData.RWBT ||
                    recordData2.WRYMC ||
                    '' +
                        '(' +
                        Math.floor(recordData.JD || recordData2.JD || 0 * 100) /
                            100 +
                        ',' +
                        Math.floor(recordData.WD || recordData2.WD || 0 * 100) /
                            100 ||
                    '' + ')';
                formData.SECONDWATERMARK =
                    '拍摄时间：' +
                    dayjs().format('YYYY-MM-DD') +
                    ' ' +
                    dayjs().format('HH:mm');
                formData.THIRDWATERMARK = '检 查 人：' + recordData.JCRMC;
                formData.LAYOUTWATERMARK = 'lowerLight';
                formData.COLORWATERMARK = '#CC3300';
            }
            let uploadTask = uni.uploadFile({
                url: url,
                filePath: attach.url || attach.upLoad,
                name: 'file',
                formData: formData,
                success(uploadFileRes) {
                    let resp = JSON.parse(uploadFileRes.data);
                    let attachs = JSON.parse(resp.wd_data);
                    let successAttach = attachs[0];
                    let attachId = successAttach.WDBH;
                    attach.WDBH = attachId;
                    _self.updateFormAttachData(attach);

                    // #ifdef APP-PLUS
                    if (attach.url && attach.url.startsWith('file://')) {
                        uni.setStorageSync(attach.WDBH, attach.url);
                    }
                    // #endif

                    // #ifdef MP
                    let attachElements = _self.findAttachElements();
                    attachElements.forEach(e => {
                        e.resetAttachId(attach.refId, attachId);
                        // #ifdef MP-ALIPAY
                        e.updateProgress(attach.refId, 100);
                        // #endif
                    });
                    // #endif
                },
                fail() {
                    console.log('123');
                }
            });

            uploadTask.onProgressUpdate(async res => {
                attach.progress = await res.progress;
                this.progress = await res.progress;
                // console.log(this.progress);
            });
        },

        // #ifdef MP
        findAttachElements() {
            let _self = this;
            // #ifdef MP-ALIPAY
            let attachElements = _self.$refs.attachGroup.$refs['attachElement'];
            // #endif

            // #ifdef MP-WEIXIN
            let attachElements = _self.$refs['attachElement'];
            // #endif
            return attachElements;
        },
        // #endif

        /**
         * 更新表单附件信息
         * @param {Object} attach
         */
        updateFormAttachData(attach) {
            // console.log(attach);
            let _self = this;
            this.$nextTick(function () {
                _self.attachs.unshift(attach);
                _self.$set(_self.formData, _self.attachField, _self.attachs);
                // #ifdef MP
                _self.updateFormFieldValue(_self.attachField, _self.attachs);
                // #endif

                this.showType = true;
                uni.$emit('onFormValueChange', _self.attachs);
                uni.$emit('chooseRemarksData', _self.attachs);
            });
        },

        renameAttach(attach) {
            // this.log(attach, '重合名')
        },

        delAttach(attach) {
            this.attachs.forEach((element, index) => {
                if (attach === element.WDBH) {
                    this.attachs.splice(index, 1);
                }
            });

            this.formData[this.attachField].forEach((element, index) => {
                if (attach === element.WDBH) {
                    this.formData[this.attachField].splice(index, 1);
                }
            });

            uni.$emit('onFormValueChange', this.formData[this.attachField]);
        },

        //预览图片
        lookAttach(data) {
            // console.log(data);
            let attach = [];
            let index = 0;
            this.attachs.forEach((element, i) => {
                if (data.WDBH === element.WDBH) {
                    index = i;
                }
                attach.push(fileService.getFileUrl(element.WDBH));
                this.imagesList = attach;
            });
            uni.navigateTo({
                url: `/pages/component/attach-preview?list=${encodeURIComponent(
                    JSON.stringify(this.imagesList)
                )}&index=${index}`
            });
            // this.$refs.previewImage.open(index)
        },

        //展示状态的切换
        showAttachs() {
            this.showType = false;
        },

        //展示状态的切换
        closeAttachs() {
            this.showType = true;
        }
    }
};
</script>

<style scoped>
.attach-group-item:first-child {
    margin-top: 0;
    padding-top: 0;
}

.form-element-button {
    display: flex;
    align-items: center;
    justify-content: center;
    color: #5094f2;
}

.file-downimage {
    height: 26rpx;
    width: 26rpx;
}

.show-image {
    width: 20rpx;
    height: 30rpx;
}

.close-image {
    width: 30rpx;
    height: 20rpx;
}
</style>
