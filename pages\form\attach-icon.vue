<template>
	<view class="flex-column-layout evidence-item-layout">
		<image class="evidence-item"
		       mode="aspectFit"
		       :src="src" />
		<image class="evidence-delete"
		       mode="aspectFit"
		       :src="iconDel" />
	</view>
</template>

<script>
	import iconDoc from '@/static/img/file/word.png';
	import iconExcel from '@/static/img/file/excel.png';
	import iconPpt from '@/static/img/file/ppt.png';
	import iconPdf from '@/static/img/file/pdf.png';
	import iconImage from '@/static/img/file/image.png';
	import iconFile from '@/static/img/file/file.png';

	import mediaUtil from '@/common/media.js'

	export default {
		name: 'EvidenceItem',
		props: {
			attach: {
				type: String
			}
		},

		computed: {
			src: function() {
				switch (true) {
					case mediaUtil.isPicture(this.attach):
						return this.attach
					case mediaUtil.isVideo(this.attach):
						return `${this.attach}?x-oss-process=video/snapshot,t_0,f_jpg`
					case mediaUtil.isAudio(this.attach):
						return this.attach
					case /doc|docx/g.test(this.suffix):
						return iconDoc;
					case /xls|xlsx/g.test(this.suffix):
						return iconExcel;
					case /ppt/g.test(this.suffix):
						return iconPpt;
					case /pdf/g.test(this.suffix):
						return iconPdf;
					default:
						return iconFile
				}
			}
		}
	}
</script>

<style scoped>
	.evidence-item-layout {
		position: relative;
		width: 82px;
		height: 82px;
	}

	.evidence-item {
		width: 64px;
		height: 64px;
		background-color: #eee;
	}

	.evidence-delete {
		position: absolute;
		top: 0;
		right: 0;
		width: 18px;
		height: 18px;
	}
</style>
