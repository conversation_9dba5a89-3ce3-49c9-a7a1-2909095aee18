<template>
	<view class="flex-column-layout form-checkbox-element"
		:id="'templateId_' + template.id">
		<view>
			<span v-if="required" class="form-label-required">{{ requiredSymbol }}</span>
			<span class="form-label">{{ label }}</span>
		</view>
		<view class="uni-list">
			<checkbox-group 
				class="uni-checkbox"
				@change="checkboxChange">
				<label style="margin-right: 20rpx; padding-left: 0px; padding-right: 0px;"
					class="uni-list-cell uni-list-cell-pd"
					v-for="item in options"
					:key="item.value">
					<view>
						<checkbox 
							style="transform:scale(0.7); padding-left: 0px; padding-right: 0px;"
							:disabled="disable"
							:value="item.value"
							:checked="item.checked" 
						/>
					</view>
					<view class="form-value">{{item.text}}</view>
					<view v-if="itemshow"></view>
				</label>
			</checkbox-group>
		</view>
	</view>
</template>

<script>
	import optionElement from './option-element.js';
	export default {
		name: 'CheckboxElement',
		mixins: [optionElement],
		data() {
			return {
				dataList: [] //用于存放多选情况的数据
			};
		},

		computed: {
			itemshow() {
				if (this.value && this.value.length > 0) {
					let str = this.value.split(',')
					for (let i in this.options) {
						for (let z in str) {
							if (this.options[i].value == str[z]) {
								this.$set(this.options[i], 'checked', true)
							}
						}
					}
				}
				return false
			}
		},

		methods: {
			checkboxChange: function(e) {
				this.dataList = []
				var items = this.options,
					values = e.detail.value;

				values.forEach(element => {
					let obj = {}
					items.forEach(e => {
						if (element === e.value) {
							obj.name = e.text
							obj.value = element
							this.dataList.push(obj)
						}
					});
				});
				let str = ''
				let names = ''
				this.dataList.forEach(i => {
					str += i.value + ','
					names += i.name + ','
				});
				if (str.length > 0) {
					str = str.substr(0, str.length - 1);
					names = names.substr(0, names.length - 1);
				}
				this.value = str
				this.displayValue = names
				for (var i = 0, lenI = items.length; i < lenI; ++i) {
					const item = items[i]
					if (values.includes(item.value)) {
						this.$set(item, 'checked', true)
					} else {
						this.$set(item, 'checked', false)
					}
				}
			},

			resolveDisplayValue(data){
				if(this.value){
					let texts = [];
					let checkList = this.value.split(',')
					this.options.forEach(e => {
						checkList.forEach(element => {
							if(element === e.value){
								this.$set(e, 'checked', true)
								texts.push(e.text)
							}else{
								this.$set(e, 'checked', false)
							}
						});
					});
					// names = names.substr(0, names.length - 1);
					this.displayValue = texts.join(', ');
				}
			}
		}
	};
</script>

<style scoped>
	.form-checkbox-element {
		align-items: flex-start;
		padding-top: 10rpx;
		padding-bottom: 10rpx;
	}

	.radio-list {
		padding: 0 18upx;
	}

	.uni-list-cell {
		justify-content: center;
		display: flex;
		align-items: center;
		padding: 6upx 10upx;
	}

	.uni-checkbox {
		display: flex;
		margin-left: auto;
		font-size: 28rpx;
		color: #333;
		flex-wrap: wrap;
	}
</style>
