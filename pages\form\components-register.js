/*
 * @Author: your name
 * @Date: 2021-04-19 10:30:40
 * @LastEditTime: 2021-05-25 16:31:30
 * @LastEditors: your name
 * @Description: In User Settings Edit
 * @FilePath: /UNI_APP_ShanDong/pages/form/components-register.js
 */
import Vue from 'vue';

import elementResolver from './element-resolver.vue';
import elementGroup from './element-group.vue';
import dynaGroupElement from './dyna-group-element.vue';
import innerGroupElement from './inner-group-element.vue';
import labelElement from './label-element.vue';
import textElement from './text-element.vue';
import textareaElement from './textarea-element.vue';
import attachGroup from './attach-group.vue';
import radioElement from './radio-element.vue';
import checkboxElement from './checkbox-element.vue';
import selectElement from './select-element.vue';
import dateElement from './date-element.vue';
import industryTreeElement from './industry-tree-element.vue';
import industryselectElement from './industryselect-element';
import personElement from './person-element.vue';
import departmentElement from './department-element.vue';
import districtElement from './district-element.vue';
import districtTreeElement from './district-tree-element.vue';
import latitudeLongitudeElement from './latitude-longitude-element.vue';
import tagElement from './tag-element.vue';
import dataSelect from './data-select-element.vue';

let elements = new Map([
	['CodesetElement', tagElement],
	['ElementResolver', elementResolver],
	['ElementGroup', elementGroup],
	['DynaGroupElement', dynaGroupElement],
	['InnerGroupElement', innerGroupElement],
	['LabelElement', labelElement],
	['TextElement', textElement],
	['TextareaElement', textareaElement],
	['AttachGroup', attachGroup],
	['RadioElement', radioElement],
	['CheckboxElement', checkboxElement],
	['SelectElement', selectElement],
	['DateElement', dateElement],
	['IndustryTreeElement', industryTreeElement],
	['IndustryselectElement', industryselectElement],
	['PersonElement', personElement],
	['DepartmentElement', departmentElement],
	['DistrictElement', districtElement],
	['DistrictTreeElement', districtTreeElement],
	['latitudeLongitudeElement', latitudeLongitudeElement],
	['dataSelectElement', dataSelect]
]);

elements.forEach((component, name) => {
	Vue.component(name, component);
});