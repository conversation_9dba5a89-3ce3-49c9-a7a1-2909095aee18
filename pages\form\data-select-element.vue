<!-- @format -->

<!--
 * @Author: your name
 * @Date: 2021-05-25 16:26:31
 * @LastEditTime: 2022-10-02 15:35:41
 * @LastEditors: 姚进玺 <EMAIL>
 * @Description: In User Settings Edit
 * @FilePath: /UNI_APP_ShanDong/pages/form/data-select-element.vue
-->

<template>
    <label-element
        :id="'templateId_' + template.id"
        :label="label"
        :required="required"
        :editable="editable"
        @click.native="turnToSelectPage"
    >
        <text
            class="form-value data-select-element__text"
            @click.stop="turnToSelectPage"
        >
            {{ displayValue }}
        </text>
        <image
            style="margin-right: 40rpx; width: 32rpx; height: 32rpx"
            v-if="!readonly && editable"
            mode="aspectFit"
            :src="naviNextIcon"
        />
    </label-element>
</template>

<script>
import naviNextIcon from '@/static/img/navi_next_icon.png';
import labelElement from './label-element.vue';
import element from './element.js';
const mixins = [element];
export default {
    name: 'data-select-element',
    components: {
        labelElement
    },
    // #ifdef MP-ALIPAY
    mixins: mixins.map(item => ({ ...item, props: {} })),
    props: {
        ...mixins.reduce(
            (prev, curr) => ({ ...prev, ...(curr.props || {}) }),
            {}
        )
    },
    // #endif

    // #ifndef MP-ALIPAY
    mixins: mixins,
    // #endif

    data() {
        return {
            naviNextIcon,
            focusVal: false,
            lineCount: 1
        };
    },
    computed: {},
    methods: {
        resolveDisplayValue(data) {
            this.displayValue = data[this.displayField];
        },

        turnToSelectPage() {
            if (!this.readonly && this.editable) {
                this.select();
            }
        },

        select() {
            uni.$once('form_companyData', nodes => {
                this.onSelected(nodes);
            });
            // console.log(this.template);
            // debugger;
            let url = `/pages/department/department-company?id=${this.template.dynamicId}`;
            uni.navigateTo({
                url
            });
        },

        onSelected(nodes) {
            setTimeout(() => {
                this.value = nodes.WRYBH;
                this.displayValue = nodes.WRYMC;
            }, 100);
            console.log(nodes);
            this.value = nodes.WRYBH;
            this.displayValue = nodes.WRYMC;
            this.formdata.WRYDZ = nodes.WRYDZ;
            // this.$set(this.formdata, 'WRYDZ', nodes.WRYDZ);
            let cols = this.template.columnValueRelationList; //对应关系
            // cols.forEach(v => {
            //     if (v.FORM_COLUMN !== this.field) {
            //         this.$nextTick(() => {
            //             this.$delete(this.formData, v.FORM_COLUMN);
            //             this.$set(
            //                 this.formData,
            //                 v.FORM_COLUMN,
            //                 nodes[v.QUERY_COLUMN]
            //             );
            //         });
            //     } else {
            //         this.$nextTick(function () {
            //             this.$set(
            //                 this.formData,
            //                 this.displayField,
            //                 nodes.WRYMC
            //             );
            //             this.$set(
            //                 this.formData,
            //                 this.field,
            //                 nodes[v.QUERY_COLUMN]
            //             );
            //             /*  this.value = nodes[v.QUERY_COLUMN];
            //             this.displayValue = nodes.WRYMC; */
            //         });
            //     }
            // });
        }
    }
};
</script>

<style scoped>
.data-select-element__text {
    text-align: right;
    line-height: 56rpx;
    min-height: 56rpx;
    margin-right: 20px;
}
</style>
