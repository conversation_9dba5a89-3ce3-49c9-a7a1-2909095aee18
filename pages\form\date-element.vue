<template>
    <view>
        <label-element
            :id="'templateId_' + template.id"
            ref="labelElement"
            :label="label"
            :required="required"
        >
            <!-- #ifdef MP-ALIPAY -->
            <text
                v-if="isSelectYearMonth"
                class="form-value date-element-selector"
                @click="selectYearMonth"
            >
                {{ selectValue }}
            </text>
            <!-- #endif -->

            <!-- #ifndef MP-ALIPAY -->
            <picker
                class="form-value date-element-selector"
                v-if="isSelectYearMonth"
                mode="date"
                :disabled="disable"
                :fields="yearMonthType"
                :value="selectValue"
                @change="onSelectConfirm"
            >
                <view class="form-value form-date-content">{{
                    selectValue
                }}</view>
            </picker>
            <!-- #endif -->

            <picker
                class="form-value date-element-selector"
                v-if="isSelectTime"
                mode="time"
                :disabled="disable"
                :value="selectValue"
                @change="onSelectConfirm"
            >
                <view class="form-value form-date-content">{{
                    selectValue
                }}</view>
            </picker>

            <text
                class="form-value date-element-selector"
                v-if="isSelectDate"
                @click="selectDate"
            >
                {{ selectValue }}
            </text>

            <text
                class="form-value date-element-selector"
                v-if="isSelectDatetime"
                @click="selectDatetime"
            >
                {{ selectValue }}
            </text>

            <image
                v-if="editable"
                style="width: 16px; height: 16px"
                mode="aspectFit"
                :src="calendarIcon"
            />
            <uni-calendar
                ref="datePicker"
                :start-date="startDate"
                :end-date="endDate"
                v-if="isSelectDate"
                :insert="false"
                @confirm="onSelectConfirm"
            >
            </uni-calendar>
            <datetime-picker
                ref="datetimePicker"
                :datetime="value"
                :startDate="startDate"
                :endDate="endDate"
                @confirm="onSelectDatetimeConfirm"
            />
            <!-- #ifdef MP-ALIPAY -->
            <year-month-picker
                v-if="isSelectYearMonth"
                ref="yearMonthPicker"
                :fields="yearMonthType"
                :value="selectValue"
                @confirm="onSelectConfirm"
            />
            <!-- #endif -->
        </label-element>
    </view>
</template>

<script>
import labelElement from './label-element.vue';
import calendarIcon from '@/static/img/calendar_icon.png';
import clockIcon from '@/static/img/clock_icon.png';

import dayjs from 'dayjs';
import element from './element.js';

import datetimePicker from '@/pages/component/date/datetime-picker.vue';
// #ifdef MP-ALIPAY
import yearMonthPicker from '@/pages/component/date/year-month-picker.vue';
// #endif

const mixins = [element];

export default {
    name: 'date-elelment',
    // #ifdef MP-ALIPAY
    components: {
        labelElement,
        datetimePicker,
        yearMonthPicker
    },
    mixins: mixins.map(item => ({ ...item, props: {} })),
    props: {
        ...mixins.reduce(
            (prev, curr) => ({ ...prev, ...(curr.props || {}) }),
            {}
        )
    },
    // #endif

    // #ifndef MP-ALIPAY
    mixins: mixins,
    components: {
        labelElement,
        datetimePicker
    },
    // #endif

    data() {
        return {
            calendarIcon,
            clockIcon,
            selectValue: '请选择'
        };
    },

    computed: {
        startDate() {
            if (this.template.future_time === '$system_date$') {
                return dayjs(new Date()).format('YYYY-MM-DD');
            }
        },

        endDate() {
            if (this.template.past_time === '$system_date$') {
                return dayjs(new Date()).format('YYYY-MM-DD');
            }
        },

        datetimeFormat: function () {
            return this.template.SJGS;
        },

        isSelectYearMonth: function () {
            let regex = /^(yyyy|yyyy-MM)$/g;
            return regex.test(this.datetimeFormat);
        },

        isSelectTime: function () {
            return 'HH:mm' === this.datetimeFormat;
        },

        isSelectDate: function () {
            return 'yyyy-MM-dd' === this.datetimeFormat;
        },

        isSelectDatetime: function () {
            return (
                'yyyy-MM-dd HH:mm:ss' === this.datetimeFormat ||
                'yyyy-MM-dd HH:mm' === this.datetimeFormat
            );
        },

        yearMonthType: function () {
            if ('yyyy' === this.datetimeFormat) {
                return 'year';
            } else {
                return 'month';
            }
        }
    },

    methods: {
        // #ifdef MP-ALIPAY
        selectYearMonth() {
            if (this.disable) {
                return;
            }

            let picker = this.$refs['labelElement'].$refs['yearMonthPicker'];
            if (picker) {
                picker.show();
            } else {
                this.log('未找到年、月选择器');
            }
        },
        // #endif

        selectDate() {
            if (this.disable) {
                return;
            }

            // #ifdef MP-ALIPAY
            let picker = this.$refs['labelElement'].$refs['datePicker'];
            if (picker) {
                picker.open();
            }
            // #endif

            // #ifndef MP-ALIPAY
            this.$refs['datePicker'].open();
            // #endif
        },

        selectDatetime() {
            if (this.disable) {
                return;
            }

            // #ifdef MP-ALIPAY
            let datetimePicker =
                this.$refs['labelElement'].$refs['datetimePicker'];
            if (datetimePicker) {
                datetimePicker.show();
            } else {
                this.log('未找到时间日期选择器');
            }
            // #endif

            // #ifndef MP-ALIPAY
            this.$refs.datetimePicker.show();
            // #endif
        },

        onSelectConfirm(params) {
            if (typeof params === 'object') {
                if (params.detail) {
                    this.selectValue = params.detail.value;
                } else {
                    this.selectValue = params.fulldate;
                }
                this.value = this.selectValue;
            } else {
                this.selectValue = params;
                this.value = params;
            }
        },

        /**
         * 选择日期时间的回调
         */
        onSelectDatetimeConfirm(datetime) {
            //console.log(datatime);
            this.selectValue = datetime;
            this.value = datetime;
        },

        resolveDisplayValue(data) {
            let value = data[this.field] || '';
            if (value) {
                if (this.datetimeFormat == 'HH:mm') {
                    this.selectValue = value;
                } else {
                    this.selectValue = dayjs(value).format(
                        this.datetimeFormat
                            .replace('yyyy', 'YYYY')
                            .replace('dd', 'DD')
                            .replace('hh', 'HH')
                    );
                }
            }
        }
    }
};
</script>

<style scoped>
.date-element-selector {
    flex: 1;
    margin-right: 5px;
    text-align: right;
    width: 100%;
}

/* #ifdef MP-ALIPAY */
.form-date-content {
    width: 100%;
    text-align: end;
    margin-right: 5px;
}
/* #endif */
</style>
