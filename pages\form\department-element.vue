<template>
	<label-element 
		:id="'templateId_' + template.id"
		:label="label"
		@click.native="turnToSelectPage">
		<!-- <text class="form-verify">{{verify}}</text> -->
		<text
			class="form-value"
			style="flex: 1; text-align: right; line-height: 56rpx; min-height: 56rpx;"
			@click.stop="turnToSelectPage">
			{{displayValue}}
		</text>
		<image
			style="margin-left: 10rpx; width: 32rpx; height: 32rpx;"
			v-if="editable"
			mode="aspectFit"
			:src="naviNextIcon"
		/>
	</label-element>
</template>

<script>
	import naviNextIcon from '@/static/img/navi_next_icon.png';
	import labelElement from './label-element.vue';

	import { MODE_SELECT_DEPARTMENT } from '@/pages/department/department.js';
	import {
		EVENT_SELECT_CONFIRM,
		EVENT_SELECT_CANCEL
	} from '@/pages/department/department.js';
	
	import element from './element.js';
	const mixins = [element];
	
	import orgService from '@/api/organization-service.js';
	
	export default {
		name: 'DepartmentElement',
		components: {
			labelElement
		},
		
		// #ifdef MP-ALIPAY
		mixins: mixins.map(item => ({ ...item, props: {} })),
		props: {
			...mixins.reduce((prev, curr) => ({ ...prev, ...(curr.props || {}) }), {})
		},
		// #endif
		
		// #ifndef MP-ALIPAY
		mixins: mixins,
		// #endif
		
		data(){
			return {
				naviNextIcon,
				separator: ','
			}
		},
		
		computed: {
			isMultiCheck: function(){
				return 'true' === this.template.SFDX;
			}
		},
		
		methods: {
			turnToSelectPage(){
				if(this.disable){
					return
				}

				uni.$on(EVENT_SELECT_CONFIRM, (nodes) => {
					uni.$off(EVENT_SELECT_CONFIRM);
					this.onSelected(nodes);
				});
				
				uni.$on(EVENT_SELECT_CANCEL, () => {
					uni.$off(EVENT_SELECT_CANCEL);
				})
				
				//调用实际元素的跳转选择页面方法
				this.select();
			},
			
			select(){
				let url = `/pages/department/department?mode=${MODE_SELECT_DEPARTMENT}&multi=${this.isMultiCheck}`;
				if(this.value){
					url += `&checked=${this.value}`;
				}
				uni.navigateTo({
					url
				})
			},
			
			/**
			 * 选择部门后返回回调
			 * @param {Object} departments
			 */
			onSelected(departments){
				let departmentIds = [];
				let names = [];
				departments.forEach(d => {
					let id = d.ZZBH
					let name = d.ZZQC
					departmentIds.push(id);
					names.push(name);
				})
				this.displayValue = names.join(this.separator);
				this.value = departmentIds.join(this.separator);
			},
			
			resolveDisplayValue(data){
				if(this.value){
					orgService.loadDepartments()
						.then(departments => {
							let departmentIds = this.value.split(this.separator);
							let filteredDepartments = orgService.filterDepartmentByIds(departments, departmentIds);
							let names = filteredDepartments.map(d => {
								return d.ZZQC;
							});
							this.displayValue = names.join(this.separator);
						})
				}
			},
			
			emitConstraintEvent() {
				let _self = this
				let params = {
					element: this.template
				}
				if(this.value) {
					orgService.loadDepartments()
						.then(departments => {
							let departmentIds = this.value.split(this.separator);
							let filteredDepartments = orgService.filterDepartmentByIds(departments, departmentIds);
							
							let values = filteredDepartments.map(d => {
								d[_self.field] = d.ZZBH
								if(_self.displayField) {
									d[_self.displayField] = d.ZZQC
								}
								return d;
							});
							params.value = values
							uni.$emit(this.elementId, params)
							console.log(`部门变更：${JSON.stringify(params, null, 4)}`)
						})
				} else {
					params.value = []
					uni.$emit(this.elementId, params)
					console.log(`部门变更：${JSON.stringify(params, null, 4)}`)
				}
			}
		}
	}
</script>
