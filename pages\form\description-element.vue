<template>
	<!-- <textarea 
		class="form-label description-element"
		auto-height
		disabled="true"
		v-model="description"
	/> -->
	<view class="content-view">{{ displayValue }}</view>
</template>

<script>
	import element from './element.js';
	const mixins = [element];
	
	export default {
		name: 'DescriptionElement',
		// #ifdef MP-ALIPAY
		mixins: mixins.map(item => ({ ...item, props: {} })),
		props: {
			...mixins.reduce((prev, curr) => ({ ...prev, ...(curr.props || {}) }), {})
		},
		// #endif
		
		// #ifndef MP-ALIPAY
		mixins: mixins,
		// #endif
		
		data() {
			return {
				description: '',
				constraintFields : [],
				constraintValues: {},
			}
		},
		
		mounted() {
			this.description = this.template.MS;
			this.initConstraintFields()
			this.replaceConstraintValues()
		},
		
		methods: {
			initConstraintFields() {
				if(this.description) {
					let fieldRegex = /\{\{dynamic\.[a-zA-Z0-9]+\}\}/g
					let fieldConfig = fieldRegex.exec(this.description)
					while(fieldConfig) {
						let field = fieldConfig[0].split('.')[1].replace('}}', '')
						this.constraintFields.push(field)
						fieldConfig = fieldRegex.exec(this.description)
					}
				}
			},
			
			onConstraintValueChange(rule, params) {
				if(this.constraintFields.length === 0) {
					return
				}
				let value = params.value
				this.constraintFields.forEach((field) => {
					if(this.isElementContainField(params.element, field)) {
						let calcValue = ''
						if(Array.isArray(value)) {
							value.forEach(item => {
								calcValue += `,${item[field] || ''}`
							})
							if(calcValue.startsWith(',')) {
								calcValue = calcValue.substring(1)
							}
							let colnReg = /^,+$/g
							if(colnReg.test(calcValue)) {
								calcValue = ''
							}
						} else {
							calcValue = params.value[field]
						}
						this.constraintValues[field] = calcValue || '____'
					}
				})
				this.replaceConstraintValues()
			},
			
			replaceConstraintValues() {
				if(this.constraintValues.length === 0) {
					this.displayValue = this.description
				} else {
					let original = this.description;
					for(let field of this.constraintFields) {
						let value = this.constraintValues[field] || '____'
						original = original.replace('{{dynamic.' + field + '}}', value)
					}
					this.displayValue = original
				}
			}
		}
	}
</script>

<style scoped>
	.description-element {
		/* #ifdef MP-WEIXIN */
		width: 100%;
		/* #endif */
		padding-top: 16rpx;
	}
	
	.description-element + .form-element-divider {
		display: none;
	}
	
	.content-view{
		width: calc(100% - 80rpx);
		padding: 20rpx 40rpx;
		color: #999;
		word-break: break-all;
	}
</style>
