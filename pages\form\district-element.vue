<template>
	<label-element 
		ref="root" 
		:required="required"
		:label="label" :id="'templateId_' + template.id">
		<!-- #ifdef MP-ALIPAY -->
		<text
			class="form-value cascade-picker-text"
			@click="showPicker">
			{{displayValue || '请选择'}}
		</text>
		<district-cascade-picker ref="picker" @confirm="onCascadePickerConfirm"/>
		<!-- #endif -->
		
		<!-- #ifndef MP-ALIPAY -->
		<picker 
			style="flex: 1; text-align: right;"
			mode="multiSelector"
			class="form-value"
			:class="actionSlotClass"
			:range="columns"
			:range-key="optionTextKey"
			:value="selectedIndexs"
			:disabled="!editable"
			@columnchange="onSelectChange"
			@change="onSelectConfirm">
			<view>{{displayValue || '请选择'}}</view>
		</picker>
		<!-- #endif -->
		<image
			v-if="editable"
			class="form-select-action-indicator"
			mode="aspectFit"
			:src="naviNextIcon"
		/>
	</label-element>
</template>

<script>
	import element from './element.js';
	
	import naviNextIcon from '@/static/img/navi_next_icon.png';
	import labelElement from './label-element.vue';
	
	import { deepCopyObject } from '@/common/merge.js'
	import districtService from '@/api/district-service.js';
	import { trimZero } from '@/api/district-service.js';
	import { ELEMENT_TYPE } from './Form.js';
	
	// #ifdef MP-ALIPAY
	import districtCascadePicker from '@/pages/component/cascade/district-cascade-picker.vue'
	// #endif
	
	//读取元素配置的行政区划省、市、县(区)、镇(乡)字段的键
	const COLUMN_KEY_SUFFIX = '_column';
	const COLUMN_KEY_PROVINCE = 'province';
	const COLUMN_KEY_CITY = 'city';
	const COLUMN_KEY_COUNTY = 'area';
	const COLUMN_KEY_TOWN = 'town';
	
	const COLUMN_KEY_PREFFIXS = [
		COLUMN_KEY_PROVINCE,
		COLUMN_KEY_CITY,
		COLUMN_KEY_COUNTY,
		COLUMN_KEY_TOWN
	];
	
	const mixins = [element];
	
	export default {
		name: 'district-element',
		// #ifdef MP-ALIPAY
		mixins: mixins.map(item => ({ ...item, props: {} })),
		props: {
			...mixins.reduce((prev, curr) => ({ ...prev, ...(curr.props || {}) }), {})
		},
		components: {
			labelElement, districtCascadePicker
		},
		// #endif
		
		// #ifndef MP-ALIPAY
		mixins: mixins,
		components: {
			labelElement
		},
		// #endif
		
		data() {
			return {
				naviNextIcon,
				optionTextKey: 'name',
				columns: [],
				selectedIndexs: []
			}
		},
		
		computed: {
			/**
			 * 是否有值
			 */
			selected: function() {
				return typeof this.displayValue !== 'undefined'
					this.displayValue !== null && this.displayValue.length > 0;
			},
			
			actionSlotClass: function(){
				return {
					'form-label': !this.selected,
					'form-value': this.selected
				}
			},
			
			//级联层级数
			cascadeLevelCount: function(){
				let configedColumns = [];
				COLUMN_KEY_PREFFIXS.forEach(preffix => {
					let key = `${preffix}${COLUMN_KEY_SUFFIX}`;
					if(this.template[key]){
						configedColumns.push(this.template[key]);
					}
				})
				return configedColumns.length;
			}
		},
		
		mounted(){
			this.initDistrictData();
			this.resolveDisplayValue(this.formData);
		},
		
		methods: {
			// #ifdef MP-ALIPAY
			showPicker(){
				this.$refs.root.$refs.picker.show();
			},
			// #endif
			
			initDistrictData(){
				districtService.getRootDistrict()
					.then(root => {
						if(root){
							this.buildDefaultCascadeData(root)
						}
					})
			},
			
			/**
			 * 构建默认展示的级联数据
			 * @param {Object} parent
			 */
			buildDefaultCascadeData(parent){
				if(this.columns.length >= this.cascadeLevelCount){
					return;
				}
				districtService.getChildDistricts(parent)
					.then(children => {
						if(children.length > 0) {
							this.selectedIndexs.push(0);
							this.columns.push(children)
							this.buildDefaultCascadeData(children[0]);
						}
					})
			},
			
			/**
			 * 级联列选择回调事件
			 * @param {Object} event
			 */
			onSelectChange(event){
				let columnInfo = event.detail;
				let {column, value} = columnInfo;
				this.$set(this.selectedIndexs, columnInfo.column, columnInfo.value);
				let selectedDistrict = this.columns[column][value];
				this.buildNextCascadeData(selectedDistrict, column + 1);
			},
			
			/**
			 * 选择某一列数据时，重新构建下一列数据
			 * @param {Object} parent
			 * @param {Object} column
			 */
			buildNextCascadeData(parent, column){
				if(column >= this.cascadeLevelCount){
					return;
				}
				districtService.getChildDistricts(parent)
					.then(children => {
						if(children.length > 0) {
							this.selectedIndexs.splice(column, 1, 0);
							this.columns.splice(column, 1, children);
							this.buildNextCascadeData(children[0], column + 1);
						} else {
							if(column < this.columns.length){
								this.columns.splice(column, 1);
							}
						}
					})
			},
			
			// #ifdef MP-ALIPAY
			onCascadePickerConfirm(indexs, columns) {
				this.selectedIndexs = indexs;
				this.columns = columns;
				this.onSelectConfirm();
			},
			// #endif
			
			/**
			 * 行政区划级联对话框点击确定回调
			 */
			onSelectConfirm(){
				let selectedColumns = [];
				this.selectedIndexs.forEach((item, index) => {
					selectedColumns.push(this.columns[index][item]);
				})
				
				this.displayValue = '';
				selectedColumns.forEach(district => {
					this.displayValue = this.displayValue + district.name;
					let code = district.code;
					let columnPreffix = this.getColumnFieldPreffix(code);
					let field = this.template[`${columnPreffix}${COLUMN_KEY_SUFFIX}`];
					this.updateFormFieldValue(field, code);
				});
				if(this.template.province_value){
					this.updateFormFieldValue(this.template.province_column, this.template.province_value);
				}
			},
			
			/**
			 * 获取行政区划代码获取相应的存储字段前缀，根据行政区划代码的定义规则来区分：
			 * 省、市、县代码值长度六位数字，省级1、2位、市级3、4位、县级5、6位，不足6位时补零，镇级8位
			 * @param {Object} code
			 */
			getColumnFieldPreffix(code){
				let trimTailZeroCode = trimZero(code);
				let columnPreffix = '';
				switch(trimTailZeroCode.length){
					case 1: case 2: 
						columnPreffix = COLUMN_KEY_PROVINCE;
						break;
					case 3: case 4: 
						columnPreffix = COLUMN_KEY_CITY;
						break;
					case 5: case 6:
						columnPreffix = COLUMN_KEY_COUNTY;
						break;
					default:
						columnPreffix = COLUMN_KEY_TOWN;
						break;
				}
				return columnPreffix;
			},
			
			resolveDisplayValue(data){
				districtService.loadDistrictSource()
					.then(districtSource => {
						let columnPreffixs = [
							COLUMN_KEY_PROVINCE,
							COLUMN_KEY_CITY,
							COLUMN_KEY_COUNTY,
							COLUMN_KEY_TOWN
						];
						this.displayValue = '';
						columnPreffixs.forEach(preffix => {
							let field = this.template[`${preffix}${COLUMN_KEY_SUFFIX}`];
							
							if(field){
								let value = data[field];
								if(value){
									let district = districtService.getDistrictByCodeSync(value, districtSource);
									if(district){
										this.displayValue = this.displayValue + district.name;
									}
								}
							}
						})
					})
			},
			
			getSegmentFields() {
				let effectiveFields = []
				let prefixs = deepCopyObject(COLUMN_KEY_PREFFIXS).reverse()
				for(let prefix of prefixs) {
					let field = this.template[`${prefix}${COLUMN_KEY_SUFFIX}`]
					if(field) {
						effectiveFields.push(field)
						break
					}
				}
				return effectiveFields
			}
		}
	}
</script>

<style scoped>
	.form-select-action-indicator {
		transform: rotate(90deg);
		margin-left: 5px;
		width: 16px;
		height: 16px;
	}
	/* #ifdef MP-ALIPAY */
	.cascade-picker-text {
		flex: 1;
		margin-right: 5px;
		text-align: right;
		width: 100%;
	}
	/* #endif */
</style>
