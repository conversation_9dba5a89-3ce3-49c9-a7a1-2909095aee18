<template>
	<label-element 
		:id="'templateId_' + template.id"
		:label="label"
		:required="required"
		@click.native="openDistrictTree">
		<!-- #ifdef MP-ALIPAY -->
		<text 
			style="width: 100%; text-align: end; margin-right: 5px;"
			class="form-value" 
			@click="openDistrictTree">
			{{displayValue || '请选择'}}
		</text>
		<!-- #endif -->
		<!-- #ifndef MP-ALIPAY -->
		<text class="form-value">{{displayValue || '请选择'}}</text>
		<!-- #endif -->
		<image
			v-if="editable"
			class="form-select-action-indicator"
			mode="aspectFit"
			:src="naviNextIcon"
		/>
	</label-element>
</template>

<script>
	import labelElement from './label-element.vue';
	import naviNextIcon from '@/static/img/navi_next_icon.png';
	
	import element from './element.js';
	import districtService from '@/api/district-service.js';
	
	const mixins = [element];
	
	export default {
		name: 'DistrictTreeElement',
		// #ifdef MP-ALIPAY
		mixins: mixins.map(item => ({ ...item, props: {} })),
		props: {
			...mixins.reduce((prev, curr) => ({ ...prev, ...(curr.props || {}) }), {})
		},
		// #endif
		
		// #ifndef MP-ALIPAY
		mixins: mixins,
		// #endif
		components: {
			labelElement
		},
		
		data() {
			return {
				naviNextIcon
			}
		},
		
		mounted() {
			if(this.editable) {
				
			} else {
				if(this.value){
					
				}
			}
		},
		
		methods: {
			openDistrictTree(){
				if(!this.editable){
					return
				}
				uni.$once('onDistrictSelected', (district) => {
					if(district){
						this.value = district.code;
						this.displayValue = district.name;
					} else {
						console.log(`未选中节点`)
					}
				});
				districtService.selectDistrict(false, this.value);
			},
			
			resolveDisplayValue(data){
				if(this.value){
					districtService.getDistrictByCode(this.value)
						.then(district => {
							if(district){
								this.displayValue = district.name;
							}
						})
				}
			}
		}
	}
</script>

<style scoped>

</style>
