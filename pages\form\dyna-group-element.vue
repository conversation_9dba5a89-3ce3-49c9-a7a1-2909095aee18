<template>
	<PCard class="form-element-group"
	       :title="template.label || template.name"
	       divider
	       :contentStyle="contentStyle">
		<template 
			v-if="constraintShow"
			v-for="(item, index) in modules">
			<view 
				v-if="index > 0"
				style="width: 100%; height: 20rpx;" 
			/>
			<inner-group-element 
				:editable="editable"
				:record-id="recordId"
				:template="template"
				:form-data="item" 
			/>
			<view 
				v-if="editable && constraintShow"
				class="flex-row-layout group-add-del-layout">
				<text 
					class="group-add-del-button dyna-group-add-button"
					type="primary"
					plain="true"
					@click="addModule(index)"
				>
					+
				</text>
				<view style="width: 1px; height: 30px; background-color: #007AFF;" />
				<text 
					class="group-add-del-button dyna-group-del-button"
					type="primary"
					plain="true"
					@click="deleteModule(index)"
				>
					-
				</text>
			</view>
		</template>
		<show-modal></show-modal>
		<text 
			v-if="showEmptyTip && constraintShow"
			style="font-size: 16px; padding: 5px 0px;text-align: center;"
		>
			无
		</text>
		<button 
			v-if="isInquireDynaGroup && editable && constraintShow"
			class="power-button power-button-primary textarea-pattern-button"
			style="margin: 20rpx;"
			@click="addNewModule"
		>
			添加模板
		</button>
	</PCard>
</template>

<script>
	import element from './element.js';
	import elementConstraint from './element-constraint.js'
	import PCard from '@/pages/component/PCard.vue';
	import innerGroupElement from './inner-group-element.vue';
	import { guid } from '@/common/uuid.js';
	import { MARK_FIELD } from '@/pages/form/Form.js';

	const mixins = [element, elementConstraint];

	export default {
		name: 'dyna-group-element',
		components: {
			PCard,
			innerGroupElement
		},
		// #ifdef MP-ALIPAY
		mixins: mixins.map(item => ({ ...item,
			props: {}
		})),
		props: {
			...mixins.reduce((prev, curr) => ({ ...prev,
				...(curr.props || {})
			}), {})
		},
		// #endif

		// #ifndef MP-ALIPAY
		mixins: mixins,
		// #endif
		data() {
			return {
				contentStyle: {
					width: 'calc(100% - 40rpx)',
					padding: '20rpx'
				},

				modules: []
			};
		},

		computed: {
			childTable: function() {
				return this.template.dynatable;
			},

			showEmptyTip: function() {
				return this.modules.length === 0;
			},
			
			//是否询问笔录动态模块
			isInquireDynaGroup: function() {
				return 'T_YDZF_XCZF_XWBL_WDSUB' === this.childTable
			}
		},

		mounted() {
			this.initModules(this.formData);

			uni.$on('chooseDynaItem', res => {
				if (this.isInquireDynaGroup) {
					res.list.forEach((item, index) => {
						item[MARK_FIELD] = guid();
						this.modules.splice(index + this.modules.length, 0, item);
					});
				}
			});
		},

		watch: {
			formData: function(val) {
				this.modules = [];
				this.initModules(val);
			}
		},

		methods: {
			addNewModule() {
				// 选取新模板
				uni.navigateTo({
					url: '/pages/form/textarea-template-language?selectType=answer'
				});
			},

			innerGroupStyle(index) {
				return {
					'margin-top': index === 0 ? '0px' : '10px'
				};
			},

			/**
			 * 初始化模块数据
			 * @param {Object} formData
			 */
			initModules(formData) {
				let childTable = this.childTable;
				if (formData.hasOwnProperty(childTable)) {
					if (formData[childTable] === null) {
						formData[childTable] = this.modules;
					} else {
						this.modules = formData[childTable];
					}
				} else {
					this.$set(formData, childTable, this.modules);
				}
				
				this.log(`元素组数据`, this.modules)

				// #ifdef MP
				//对动态添加的每一组数据添加一个标记字段，便于更新数据时跟踪，否则动态元素组
				//的数据会全部更新到根节点上
				this.modules.forEach(module => {
					module[MARK_FIELD] = guid();
				});
				this.updateFormFieldValue(childTable, this.modules);
				// #endif

				if (this.editable && this.modules.length === 0) {
					this.addModule();
				}
			},

			/**
			 * 创建一个新模板数据对象
			 */
			createModuleData() {
				let module = {};
				// #ifdef MP
				//小程序必须添加一个数据标记字段，用于记录该组数据所属对象，以便数据更新时进行追踪
				module[MARK_FIELD] = guid();
				// #endif
				return module;
			},

			addModule(index = 0) {
				this.modules.splice(index + 1, 0, this.createModuleData());
			},

			deleteModule(index) {
				let self = this;
				this.$showModal({
					title: '提示',
					content: '是否确认删除',
					success: function(res) {
						if (res.confirm) {
							self.modules.splice(index, 1);
						} else if (res.cancel) {
							console.log('用户点击取消');
						}
					}
				});
			},
			
			onElementHide(event) {
				let targetField = event.targetField
				if(targetField !== this.childTable) {
					return
				}
				this.constraintShow = event.value
			},
		}
	};
</script>

<style scoped>
	.group-add-del-layout {
		margin-top: 8px;
		justify-content: flex-end;
	}

	.group-add-del-button {
		width: 48px;
		height: 28px;
		line-height: 28px;
		border: 1px solid #007aff;
		margin: 0px;
		text-align: center;
		border-radius: 10rpx;
		color: #007aff;
	}

	.group-add-del-button:active {
		color: #fff;
		background-color: #007aff;
	}

	.dyna-group-add-button {
		border-top-right-radius: 0px;
		border-bottom-right-radius: 0px;
		border-right: none;
	}

	.dyna-group-del-button {
		border-top-left-radius: 0px;
		border-bottom-left-radius: 0px;
		border-left: none;
	}
</style>
