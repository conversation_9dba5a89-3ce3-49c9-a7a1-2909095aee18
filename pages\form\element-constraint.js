//元素之间约束关系处理
export default {
	props: {
		constraints: {
			type: Array,
			default: () => {
				return []
			}
		}
	},
	
	data() {
		return {
			constraintShow: true,
			listeningEvents: {}
		}
	},
	
	computed: {
		triggerEvents: function() {
			return (this.template.trigger_events || '').split(';')
		},
		
		acceptEvents: function() {
			let eventNames = this.template.listen_events || ''
			if(eventNames === '' && this.type === 'dynagroup') {
				//动态元素组总是监听隐藏事件
				eventNames = 'hide'
			}
			return eventNames.split(',')
		}
	},

	watch: {
		constraints(){
			this.applyElementConstraints()
		}
	},
	
	mounted() {
		//受控约束
		this.applyElementConstraints()
		this.listenEvents(this.acceptEvents)
	},
	
	methods: {
		/**
		 * 应用元素依赖规则，添加依赖监听
		 */
		applyElementConstraints(){
			let _self = this;
			this.constraints.filter(constraint => {
				return constraint.follow_id === this.elementId;
			}).forEach(constraint => {
				uni.$on(constraint.active_id, (value) => {
					_self.onConstraintValueChange(constraint.rule, value);
				})
			})
		},

		/**
		 * 受控元素在主控元素的值发生变化时，触发受控元素该方法
		 * 对具体的元素，可以重写该方法自动定响应规则
		 * @param {Object} rule
		 * @param {Object} params
		 */
		onConstraintValueChange(rule, params) {
			if(rule) {
				let activeElement = params.element;
				let activeElementField = activeElement.dbcolum
				if(rule.startsWith('hide=')) {
					let activeValue = rule.split('=')[1];
					let activeReg = new RegExp(activeValue);
					this.constraintShow = !activeReg.test(params.value[activeElementField]);
				} else if(rule.startsWith('attr=')) {
					let attrName = rule.split('=')[1];
					let values= params.value
					if(Array.isArray(values)) {
						let joinStr = '';
						values.forEach(v => {
							let attrValue = v[attrName] || ''
							joinStr += `,${attrValue}`
						})
						this.value = joinStr.substring(1)
					}
				}
			}
		},
		
		/**
		 * 触发元素关联事件，需要独立处理的元素需要重写该方法
		 * @param {Object} constraintValue
		 */
		emitConstraintEvent() {
			let emitValue = {}
			emitValue[this.field] = this.value
			if(this.displayFiled) {
				emitValue[this.displayField] = this.displayValue
			}
			
			let params = {
				element: this.template,
				value: emitValue
			}
			uni.$emit(this.elementId, params)
		},
		
		/**
		 * 判断主控元素的值是否包含指定字段
		 * @param {Object} element
		 * @param {Object} field
		 */
		isElementContainField(element, field) {
			return element.dbcolum === field 
				|| element.name_column === field
				|| element.QTXXZD === field
		},
		
		/**
		 * 触发元素关联约束事件，当值发生改变时触发
		 */
		triggerConstraintEvents() {
			if(this.triggerEvents.length === 0) {
				return
			}
			
			this.triggerEvents.forEach(rule => {
				this.dispatchConstraintEvent(rule)
			})
		},
		
		dispatchConstraintEvent(rule) {
			if(rule.startsWith('hide=')) {
				let ruleValue = rule.split('=')[1].split('@')
				let hideValue = ruleValue[0]
				let targetField = ruleValue[1]
				let shown = this.value !== hideValue
				let triggerValue = {
					targetField,
					value: shown
				}
				uni.$emit('onElementHide', triggerValue)
			}
		},
		
		listenEvents(eventNames) {
			if(eventNames.length === 0) {
				return
			}
			eventNames.forEach(event => {
				if(event === 'hide') {
					uni.$on('onElementHide', this.onElementHide)
					this.listeningEvents.onElementHide = this.onElementHide
				}
			})
		},
		
		onElementHide(event) {
			let targetField = event.targetField
			if(targetField !== this.field) {
				return
			}
			this.constraintShow = event.value
		},
		
		/**
		 *获取子元素节点引用，对于无法通过ref="childElement"获取到子节点元素的元素，
		 * 需要重写该方法，返回一个节点数据，在校验必填性、获取表单数据方法遍历子节点
		 * 时都通过该方法获取子节点元素
		 */
		childElementRefs() {
		    return this.$refs.childElement || null;
		},
		
		releaseListeningEvents() {
			console.log(`开始执行`)
			if(this.childElementRefs()) {
				console.log(`子节点长度：${this.childElementRefs().length}`)
				this.childElementRefs().forEach(e => {
					if(e.releaseListeningEvents) {
						e.releaseListeningEvents()
					}
				})
			} 
			for(let eventName in this.listeningEvents) {
				console.log(`移动监听：${eventName}`)
				uni.$off(eventName, this.listeningEvents[eventName])
			}
		},
		
		createElementDomId(element) {
			return `elementId_${element.id}`
		}
	}
}