<template>
	<PCard
		id="elementGroup"
		class="form-element-group"
		divider
		:round="round"
		:showTitle="showTitle"
		:title="groupName"
		:contentStyle="contentStyle">
		<template v-for="(element, index) in childElements">
			<view 
				class="form-element-divider"
				v-if="showDivider(index, element) && resolveElementType(element) && element.showDivider"
			/>
			
			<!-- #ifdef MP-ALIPAY -->
			<!-- 钉钉小程序会把group-element-resolver的样式完全抹掉，所以需要特殊处理 -->
			<view 
				v-if="showElement(element) && isElementDeclare(element.type)"
				class="form-element-style">
				<group-element-resolver
					class="form-element-style"
					:key="index"
					:editable="editable"
					:record-id="recordId"
					:template="element"
					:constraints="constraints"
					:form-data="formData"
				/>
			</view>
			<!-- #endif -->
			
			<!-- #ifndef MP-ALIPAY -->
			<group-element-resolver
				v-if="showElement(element) && isElementDeclare(element.type)"
				class="form-element-style"
				:key="index"
				:editable="editable"
				:record-id="recordId"
				:template="element"
				:constraints="constraints"
				:form-data="formData"
			/>
			<!-- #endif -->
		</template>
	</PCard>
</template>

<script>
	import { ELEMENT_TYPE } from './Form.js';
	
	import PCard from '@/pages/component/PCard.vue';
	import groupElementResolver from './group-element-resolver.vue';
	
	import styleUtil from '@/common/style.js';
	import element from './element.js';
	import group from './group.js';
	
	const mixins = [element, group];
		
	export default {
		name: 'ElementGroup',
		components: {
			PCard, groupElementResolver
		},
		
		// #ifdef MP-ALIPAY
		mixins: mixins.map(item => ({ ...item, props: {} })),
		props: {
			...mixins.reduce((prev, curr) => ({ ...prev, ...(curr.props || {}) }), {})
		},
		// #endif
		
		// #ifndef MP-ALIPAY
		mixins: mixins,
		// #endif
		
		data() {
			return {
				contentStyle: {
					width: '100%'
				}
			}
		},
		
		computed: {
			groupName: function(){
				if(this.template.label){
					return this.template.label;
				}
				if(this.template.templateName){
					return this.template.templateName;
				}
				return '';
			},
			
			showTitle: function(){
				return this.groupName.length > 0;
			}
		}
	}
</script>
