<template>
	<view class="flex-column-layout form__element-layout">
		<view v-if="required" class="flex-row-layout" style="width: 100%;">
			<text class="form-label-required">*</text>
			<text class="form-label">{{ label }}</text>
		</view>
		<text v-else class="form-label">{{ label }}</text>
		<slot />
	</view>
</template>

<script>
	export default {
		name: 'ElementLayout',
		props: {
			label: {
				type: String,
				default: '元素名称'
			},
			
			required:  {
				type: Boolean,
				default: false
			}
		}
	}
</script>

<style scoped>
	.form__element-layout {
		align-items: flex-start;
		padding-top: 10rpx;
		padding-bottom: 10rpx;
	}
</style>
