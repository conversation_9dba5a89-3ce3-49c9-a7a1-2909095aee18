<template>
	<!-- #ifdef H5 || APP-PLUS -->
		<element-group v-if="isGroup" :round="round" :template="template" :constraints="constraints" :editable="editable" :record-id="recordId" :form-data="formData" />
		<dyna-group-element v-else-if="isDynaGroup" :template="template" :constraints="constraints" :editable="editable" :record-id="recordId" :form-data="formData"/>
		<text-element v-else-if="isText" :template="template" :editable="editable" :record-id="recordId" :form-data="formData"/>
		<textarea-element v-else-if="isTextarea" :template="template" :editable="editable" :record-id="recordId" :form-data="formData"/>
		<radio-element v-else-if="isRadio" :template="template" :editable="editable" :record-id="recordId" :form-data="formData"/>
		<checkbox-element v-else-if="isCheckbox" :template="template" :editable="editable" :record-id="recordId" :form-data="formData"/>
		<select-element v-else-if="isSelect" :template="template" :editable="editable" :record-id="recordId" :form-data="formData"/>
		<date-element v-else-if="isDate" :template="template" :editable="editable" :record-id="recordId" :form-data="formData"/>
		<person-element v-else-if="isPerson" :template="template" :editable="editable" :record-id="recordId" :form-data="formData"/>
		<department-element v-else-if="isDepartment" :template="template" :editable="editable" :record-id="recordId" :form-data="formData"/>
		<industry-tree-element v-else-if="isIndustryTree" :template="template" :editable="editable" :record-id="recordId" :form-data="formData"/>
		<industryselect-element v-else-if="isIndustryCascade" :template="template" :editable="editable" :record-id="recordId" :form-data="formData"/>
		<district-element v-else-if="isDistrictCascade" :template="template" :editable="editable" :record-id="recordId" :form-data="formData"/>
		<district-tree-element v-else-if="isDistrictTree" :template="template" :editable="editable" :record-id="recordId" :form-data="formData"/>
		<latitude-longitude-element v-else-if="isLocation" :template="template" :editable="editable" :record-id="recordId" :form-data="formData"/>
		<attach-group v-else-if="isAttachGroup":round="round" :template="template" :editable="editable" :record-id="recordId" :form-data="formData"/>
		<tag-element v-else-if="isTag" :template="template" :editable="editable" :record-id="recordId" :form-data="formData"/>
	<!-- #endif -->
	
	<!-- #ifndef H5 ||APP-PLUS -->
	<view v-if="isGroup" :style="groupStyle">
		<element-group :round="round" :template="template" :editable="editable" :record-id="recordId" :form-data="formData" />
	</view>
	<view v-else-if="isDynaGroup" :style="groupStyle">
		<dyna-group-element  :template="template" :editable="editable" :record-id="recordId" :form-data="formData"/>
	</view>
	<text-element v-else-if="isText" style="width: 100%; background-color: #fff;" :template="template" :editable="editable" :record-id="recordId" :form-data="formData"/>
	<textarea-element v-else-if="isTextarea" style="width: 100%;" :template="template" :editable="editable" :record-id="recordId" :form-data="formData"/>
	<radio-element v-else-if="isRadio" style="width: 100%;" :template="template" :editable="editable" :record-id="recordId" :form-data="formData"/>
	<checkbox-element v-else-if="isCheckbox" style="width: 100%;" :template="template" :editable="editable" :record-id="recordId" :form-data="formData"/>
	<select-element v-else-if="isSelect" style="width: 100%;" :template="template" :editable="editable" :record-id="recordId" :form-data="formData"/>
	<date-element v-else-if="isDate" style="width: 100%;" :template="template" :editable="editable" :record-id="recordId" :form-data="formData"/>
	<person-element v-else-if="isPerson" style="width: 100%;" :template="template" :editable="editable" :record-id="recordId" :form-data="formData"/>
	<department-element v-else-if="isDepartment" style="width: 100%;" :template="template" :editable="editable" :record-id="recordId" :form-data="formData"/>
	<industry-tree-element v-else-if="isIndustryTree" style="width: 100%;" :template="template" :editable="editable" :record-id="recordId" :form-data="formData"/>
	<industryselect-element v-else-if="isIndustryCascade" style="width: 100%;" :template="template" :editable="editable" :record-id="recordId" :form-data="formData"/>
	<district-element v-else-if="isDistrictCascade" style="width: 100%;" :template="template" :editable="editable" :record-id="recordId" :form-data="formData"/>
	<district-tree-element v-else-if="isDistrictTree" style="width: 100%;" :template="template" :editable="editable" :record-id="recordId" :form-data="formData"/>
	<latitude-longitude-element v-else-if="isLocation" style="width: 100%;" :template="template" :editable="editable" :record-id="recordId" :form-data="formData"/>
	<attach-group v-else-if="isAttachGroup" style="width: 100%;" :round="round" :template="template" :editable="editable" :record-id="recordId" :form-data="formData"/>
	<tag-element v-else-if="isTag" style="width: 100%;" :template="template" :editable="editable" :record-id="recordId" :form-data="formData"/>
	
	<!-- #endif -->
		
</template>

<script>
	import { ELEMENT_TYPE } from '@/pages/form/Form.js';
	import element from './element.js';
	import elementTypeResolver from './element-type-resolver.js';
	
	import elementGroup from './element-group.vue';
	import dynaGroupElement from './dyna-group-element.vue';
	import textElement from './text-element.vue';
	import textareaElement from './textarea-element.vue';
	import radioElement from './radio-element.vue';
	import tagElement from './tag-element.vue';
	import checkboxElement from './checkbox-element.vue';
	import selectElement from './select-element.vue';
	import dateElement from './date-element.vue';
	import industryTreeElement from './industry-tree-element.vue';
	import industryselectElement from './industryselect-element';
	import personElement from './person-element.vue';
	import departmentElement from './department-element.vue';
	import districtElement from './district-element.vue';
	import districtTreeElement from './district-tree-element.vue';
	import latitudeLongitudeElement from './latitude-longitude-element.vue';
	import attachGroup from './attach-group.vue';
	
	const mixins = [element, elementTypeResolver];
	
	export default {
		name: 'ElementResolver',
		// #ifndef MP-ALIPAY
		mixins: mixins,
		// #endif
		// #ifdef MP-ALIPAY
		mixins: mixins.map(item => ({ ...item, props: {} })),
		// #endif
		components: {
			elementGroup,
			dynaGroupElement,
			textElement,
			textareaElement,
			radioElement,
			checkboxElement,
			selectElement,
			dateElement,
			industryTreeElement,
			industryselectElement,
			personElement,
			departmentElement,
			districtElement,
			districtTreeElement,
			latitudeLongitudeElement,
			attachGroup,
			tagElement
		},
		
		props: {
			// #ifdef MP-ALIPAY
			...mixins.reduce((prev, curr) => ({ ...prev, ...(curr.props || {}) }), {}),
			// #endif
			marginTop: {
				type: Number,
				default: 0
			}
		},
		
		computed: {
			groupStyle: function() {
				return `margin-top: ${this.marginTop}px;`;
			}
		}
	
	}
</script>
