/*
 * @Author: your name
 * @Date: 2021-04-19 10:30:40
 * @LastEditTime: 2021-05-25 16:39:02
 * @LastEditors: your name
 * @Description: In User Settings Edit
 * @FilePath: /UNI_APP_ShanDong/pages/form/element-type-resolver.js
 */
import { ELEMENT_TYPE } from './Form.js';
	
export default {
	computed: {
		isGroup: function() {
			return ELEMENT_TYPE.group === this.type;
		},
		
		isDynaGroup: function() {
			return ELEMENT_TYPE.dynaGroup === this.type;
		},
		
		isText: function() {
			return ELEMENT_TYPE.text === this.type;
		},
		
		isTextarea: function() {
			return ELEMENT_TYPE.textArea === this.type
				|| ELEMENT_TYPE.pattern === this.type;
		},
		
		isRadio: function() {
			return ELEMENT_TYPE.radio === this.type
				|| (ELEMENT_TYPE.code === this.type && this.template.dmjkjlx === 'radio');
		},
		
		isCheckbox: function() {
			return ELEMENT_TYPE.checkbox === this.type
				|| (ELEMENT_TYPE.code === this.type && this.template.dmjkjlx === 'checkbox');
		},
		
		isSelect: function() {
			return ELEMENT_TYPE.select === this.type
				|| (ELEMENT_TYPE.code === this.type && this.template.dmjkjlx === 'select');
		},
		
		isTag: function() {
			return ELEMENT_TYPE.tag === this.type
				|| (ELEMENT_TYPE.code === this.type && this.template.dmjkjlx === 'tag');
		},
		
		isDate: function() {
			return ELEMENT_TYPE.date === this.type;
		},
		
		isPerson: function() {
			return ELEMENT_TYPE.person === this.type
				|| ELEMENT_TYPE.enforcer === this.type;
		},
		
		isDepartment: function() {
			return ELEMENT_TYPE.department === this.type;
		},
		
		isIndustryTree: function() {
			return ELEMENT_TYPE.industry === this.type;
		},
		
		isIndustryCascade: function() {
			return ELEMENT_TYPE.industryselect === this.type;
		},
		
		isDistrictCascade: function() {
			return ELEMENT_TYPE.districtCascade === this.type;
		},
		
		isDistrictTree: function() {
			return ELEMENT_TYPE.district === this.type;
		},
		
		isLocation: function(){
			return ELEMENT_TYPE.latitudeLongitude === this.type;
		},
		
		isAttachGroup: function() {
			return ELEMENT_TYPE.attach === this.type;
		},
		
		isDescription: function() {
			return ELEMENT_TYPE.description === this.type;
		},
		
		isSignature: function() {
			return ELEMENT_TYPE.signature === this.type;
		},
		
		isTree: function() {
			return ELEMENT_TYPE.tree === this.type;
		},
		
		isCodeset: function() {
			return ELEMENT_TYPE.codeset === this.type;
		},

		isdataSelect: function() {
			return ELEMENT_TYPE.dataSelect === this.type;
		},
	}
}