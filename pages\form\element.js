import {
    ELEMENT_TYPE,
    ELEMENT_COMPONENT_DICT,
    EVENT_ELEMENT_MOUNTED,
    isElementGroup
} from './Form.js';
import codeService from '@/api/code-service.js';
import districtService from '@/api/district-service.js';
import industryselect from '@/api/industryselect-service.js';
import elementConstraint from './element-constraint.js';
import store from '../../store';

import { mapMutations } from 'vuex';
import { MARK_FIELD } from '@/pages/form/Form.js';

const mixins = [elementConstraint];

export default {
    // #ifdef MP-ALIPAY
    mixins: mixins.map(item => ({ ...item, props: {} })),
    // #endif

    // #ifndef MP-ALIPAY
    mixins: mixins,
    // #endif
    props: {
        // #ifdef MP-ALIPAY
        ...mixins.reduce(
            (prev, curr) => ({ ...prev, ...(curr.props || {}) }),
            {}
        ),
        // #endif
        editable: {
            type: Boolean
        },

        template: {
            type: Object,
            default: () => {
                return {};
            }
        },

        recordId: {
            type: String
        },

        formData: {
            type: Object,
            default: () => {
                return {
                    mark: '默认数据'
                };
            }
        },

        round: {
            type: Boolean,
            default: true
        }
    },

    data() {
        return {
            requiredSymbol: '*',
            value: '', //填写的数据
            displayValue: this.formData[this.field] || '', //不能填写的属性
        };
    },

    computed: {
        elementId: function () {
            return this.template.id;
        },

        //元素类型
        type: function () {
            return this.template.type;
        },

        //元素标签
        label: function () {
            return this.template['label'] || '';
        },

        required: function () {
            return this.template['must'] === '1';
        },

        //元素取值字段名
        field: function () {
            return (
                this.template.dbcolum ||
                this.template.latitude_column ||
                this.template.longitude_column ||
                ''
            );
        },

        /**
         * 存储元素显示值的字段
         */
        displayField: function () {
            return this.template.name_column || this.template.QTXXZD || '';
        },

        readonly: function () {
            return this.template.readonly === '1';
        },

        disable: function () {
            return this.readonly || !this.editable;
        },

        //元素必填项校验
        verify: function () {
            let verifyYype = this.template.must === '1' ? '*' : '';
            return verifyYype;
        },

        layoutOrientation: function () {
            return this.template['orientation'] || 'horizontal';
        },

        isVerticalLayout: function () {
            return 'vertical' === this.layoutOrientation;
        },

        pattern: function () {
            return this.template.pattern || this.template.BZYJ || '';
        },

        isPattern: function () {
            return (
                ELEMENT_TYPE.pattern === this.type || this.pattern.length > 0
            );
        }
    },

    mounted() {
        if (this.field || this.getSegmentFields().length > 0) {
            //挂载必填项和渲染数据
            this.value = this.formData[this.field] || '';
            this.getVerifyRule();
        }
        uni.$emit(EVENT_ELEMENT_MOUNTED, this.template);
    },

    watch: {
        formData: function (val) {
            if (this.field) {
                if (val.hasOwnProperty(this.field)) {
                    this.value = val[this.field];
                } else {
                    this.value = '';
                }
            } else if (!isElementGroup(this.template)) {
                this.resolveDisplayValue(this.formData);
            }
        },

        template() {
            this.resolveDisplayValue(this.formData);
        },

        value: function (val) {
            let self = this;
            if (this.field) {
                // #ifdef MP
                this.updateFormFieldValue(this.field, val);
                if (this.displayField) {
                    this.updateFormFieldValue(
                        this.formData,
                        this.displayField,
                        this.displayValue
                    );
                }
                // #endif

                // this.$set(this.formData, this.field, val);
                this.formData[this.field] = val;
                // if (this.displayField) {
                //     this.formData[this.displayField] = this.displayValue;
                // }
                this.resolveDisplayValue(this.formData);
                //元素值改变时，发出全局事件

                this.emitConstraintEvent();
                this.emitValueChangeEvent();
				this.triggerConstraintEvents()
            }
        },

        displayValue(val) {
            if (this.displayField) {
                this.formData[this.displayField] = this.displayValue;
            }
        }
    },

    methods: {
        // #ifdef MP
        ...mapMutations(['resetFormData', 'updateFormData']),
        // #endif

        //加载默认值
        defaultMustText() {
            if (this.template.MRZ) {
                let val = this.template.MRZ;
                // console.log(val);
                let userInfo = uni.getStorageSync('userInfo');
                let reVal = '';
                switch (val) {
                    case 'CURRDEPT_XH':
                        if (userInfo) {
                            reVal = userInfo.bmbh;
                        }
                        break;
                    case 'CURRDEPT_NAME':
                        if (userInfo) {
                            reVal = userInfo.bmbh;
                        }
                        break;
                    case 'CURRUSER_XH':
                        if (userInfo) {
                            reVal = userInfo.id;
                        }
                        break;
                    case 'CURRUSER_NAME':
                        if (userInfo) {
                            reVal = userInfo.id;
                        }
                        break;
                    // 默认时间
                    case '$system_curr_date$':
                        let dateType = this.template.SJGS.replace(
                            'yyyy',
                            'YYYY'
                        ).replace('dd', 'DD');
                        reVal = this.$dayjs(new Date()).format(dateType);
                        break;
                    default:
                        reVal = val;
                        break;
                }
                if (reVal !== '') {
                    console.log(this.template.SJGS, '333');
                    // 如果有值就用之前的值，没有就用默认值
                    this.value = this.value ? this.value : reVal;
                }
            }
        },

        isElementDeclare(type) {
            let declared = false;
            for (let typeKey in ELEMENT_TYPE) {
                if (ELEMENT_TYPE[typeKey] === type) {
                    declared = true;
                    break;
                }
            }
            return declared;
        },

        //是否元素组
        isElementGroup(element) {
            return isElementGroup(element);
        },

        /**
         * 确定元素组件名
         * @param {Object} element
         */
        resolveElementType(element) {
            // debugger
            if (this.editable) {
                let component = ELEMENT_COMPONENT_DICT.get(element.type);
                if ('function' === typeof component) {
                    return component(element);
                } else {
                    return component;
                }
            } else {
                if (element.type === ELEMENT_TYPE.dynaGroup) {
                    return 'dyna-group-element';
                } else if (element.type === ELEMENT_TYPE.group) {
                    return 'element-group';
                } else if (element.datasource === 'FILE_UPLOAD') {
                    return 'attach-group';
                } else if (element.type === ELEMENT_TYPE.textArea) {
                    return 'textarea-element';
                } else if (element.type === ELEMENT_TYPE.attach) {
                    return 'attach-element';
                } else if (element.type === ELEMENT_TYPE.select) {
                    return 'select-element';
                } else if (element.type === ELEMENT_TYPE.district) {
                    return 'district-tree-element';
                } else if (element.type === ELEMENT_TYPE.districtCascade) {
                    return 'district-element';
                } else {
                    return 'text-element';
                }
            }
        },

        /**
         * 是否显示元素分割线
         * @param {Object} index
         * @param {Object} element
         */
        showDivider(index, element) {
            return index !== 0 && this.showElement(element);
        },

        /**
         * 是否显示元素
         * @param {Object} element
         */
        showElement(element) {
            return element.isshow !== '0';
        },

        resolveDisplayValue(data) {
            // debugger
            if (typeof data === 'undefined' || data === null) {
                return;
            }
            let original = data[this.field] || '';
            if (original) {
                let type = this.template.type;
                if (type === ELEMENT_TYPE.code) {
                    this.setCodeDisplayValue(original);
                } else if (type === ELEMENT_TYPE.district) {
                    this.setDistrictDisplayValue(original);
                } else if (type === ELEMENT_TYPE.radio) {
                    this.resolveOptionDisplayValue(original);
                } else if (type === ELEMENT_TYPE.checkbox) {
                    this.resolveOptionDisplayValue(original);
                } else if (type === ELEMENT_TYPE.industry) {
                    this.setIndustryTreeDisplayValue(original);
                } else {
                    if (this.displayField) {
                        this.displayValue = data[this.displayValue] || '';
                    }
                }
            } else {
                let type = this.template.type;
                //行业类型联动显示值
                if (type === ELEMENT_TYPE.industryselect) {
                    let industrySelectArr = [];
                    let originalList = [
                        'province_column',
                        'city_column',
                        'area_column',
                        'town_column'
                    ];
                    originalList.forEach(element => {
                        if (this.template[element]) {
                            industrySelectArr.push(this.template[element]);
                        }
                    });
                    let industryIndexArr = [];
                    industrySelectArr.forEach(el => {
                        industryIndexArr.push(data[el]);
                    });
                    this.setIndustryDisplayValue(industryIndexArr);
                }
                //经纬度选择的的显示值
                if (type === ELEMENT_TYPE.latitudeLongitude) {
                    let locationList = [];
                    let originalList = ['longitude_column', 'latitude_column'];
                    originalList.forEach(element => {
                        if (this.template[element]) {
                            locationList.push(this.template[element]);
                        } else {
                            locationList.push('');
                        }
                    });
                    let lng = data[locationList[0]]
                        ? '经度:' + data[locationList[0]] + ','
                        : '';
                    let lat = data[locationList[1]]
                        ? '纬度:' + data[locationList[1]]
                        : '';

                    this.displayValue = lng + lat;
                }
            }
            // uni.hideLoading();
        },

        /**
         * 设置公共代码值元素的显示值
         * @param {Object} code
         */
        setCodeDisplayValue(code) {
            if (code) {
                let setCode = this.template.sourcevalue;
                codeService
                    .getCode(setCode, code)
                    .then(code => {
                        this.displayValue = code.name;
                    })
                    .catch(error => {});
            }
        },

        //设置行业类型的元素值
        setIndustryDisplayValue(arr) {
            let nameList = [];
            arr.forEach(element => {
                industryselect
                    .retrieveIndustryChainByCode(element)
                    .then(industryChain => {
                        if (industryChain && industryChain.length > 0) {
                            industryChain.forEach(el => {
                                if (el.code === element) {
                                    nameList.push(el.name);
                                }
                            });
                        }
                        this.displayValue = nameList.join('>');
                    });
            });
        },

        setIndustryTreeDisplayValue(code) {
            industryselect
                .retrieveIndustryChainByCode(code)
                .then(industryChain => {
                    if (industryChain && industryChain.length > 0) {
                        this.displayValue = industryChain
                            .map(industry => {
                                return industry.name || '';
                            })
                            .reduce((preview, next) => {
                                return `${preview}${next}`;
                            });
                    }
                });
        },

        /**
         * 设置行政区划元素显示值
         * @param {Object} code
         */
        setDistrictDisplayValue(code) {
            districtService
                .retrieveDistrictChainByCode(code)
                .then(districtChain => {
                    if (districtChain && districtChain.length > 0) {
                        this.displayValue = districtChain
                            .map(district => {
                                return district.name || '';
                            })
                            .reduce((preview, next) => {
                                return `${preview}${next}`;
                            });
                    }
                })
                .catch(error => {
                    console.log(
                        `查询行政区划链出错：${JSON.stringify(error, null, 4)}`
                    );
                });
        },

        resolveOptionDisplayValue(code, multi = false) {
            let itemsDefine = this.template.item;
            if (code && itemsDefine && itemsDefine.length > 0) {
                let valueArray = code.split(',');
                let texts = [];
                itemsDefine.forEach(item => {
                    if (valueArray.indexOf(item.value) !== -1) {
                        texts.push(item.text);
                    }
                });
                if (texts.length > 0) {
                    this.displayValue = texts.join(', ');
                }
            }
        },

        /**
         * 更新表单字段值，分H5和小程序两个平台做兼容处理，H5直接修改根节点绑定的formData的字段属性，
         * 但是小程序中，父组件传递给子组件的对象，在子组件中是一个新对象（虽然属性值一样），所以在子组件中更新父组件
         * 传递的对象的属性值，在父组件中是无效的，而通过.sync修饰符语法只能更新一层，有两层嵌套时，会产生子组件修改属性对象的问题，
         * 所以在小程序中是通过vuex来保存保存、更新表单的数据。
         * @param {String} field
         * @param {Object} value
         */
        updateFormFieldValue(field, value) {
            // #ifdef MP
            let updatePayload = {};
            updatePayload[field] = value;
            if (this.formData[MARK_FIELD]) {
                updatePayload[MARK_FIELD] = this.formData[MARK_FIELD];
            }
            this.updateFormData(updatePayload);
            // #endif

            // #ifndef MP
            this.$set(this.formData, field, value);
            // #endif
        },

        //表单节流，让表单能够持续渲染动画
        getFormThrottle() {
            let throttle = true;
            store.commit('getTemplateForm');
        },

        //每次切换表单都要重新加载必填项
        getVerifyRule() {
            let mrzInfo = {};
            this.$store.state.verifyList = [];
            let checkFormat = this.template.YSSJLX;
            if (this.required || checkFormat) {
                let fields = [this.field];
                let segmentFields = this.getSegmentFields() || [];
                let requiredFields = fields.concat(segmentFields);

                requiredFields.forEach(field => {
                    if (field) {
                        let verifyInfo = {
                            id: `templateId_${this.elementId}`,
                            field,
                            name: this.label
                        };
                        if (checkFormat) {
                            verifyInfo.type = checkFormat;
                        }
                        this.$store.state.verifyList.push(verifyInfo);
                    }
                });
            }

            if (this.template.YDZF_LSSJ === '1') {
                let verifyInfo = {
                    field: this.field,
                    name: this.label
                };
                this.$store.state.historyList.push(verifyInfo);
            }

            let defaultValue = this.template.MRZ;
            if (defaultValue) {
                mrzInfo.field = this.field;
                mrzInfo.name = this.label;
                mrzInfo.mrz = defaultValue;
                this.$store.state.mrzList.push(mrzInfo);
            }
        },

        /**
         * 触发元素值变更事件
         */
        emitValueChangeEvent() {
            let emitValue = {};
            emitValue[this.field] = this.value;
            if (this.displayFiled) {
                emitValue[this.displayField] = this.displayValue;
            }

            let params = {
                element: this.template,
                value: emitValue
            };
            uni.$emit('onFormValueChange', params);
        },

        /**
         * 获取分段字段集合，对于级联元素、经纬度等元素而言，用一个字段存储数据不便
         */
        getSegmentFields() {
            //多字段的元素实现该方法，默认返回空数组
            return [];
        }
    }
};
