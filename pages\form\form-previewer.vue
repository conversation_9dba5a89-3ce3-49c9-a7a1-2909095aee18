<template>
	<Page class="power-page" :title="title">
		<template-form 
			:parentHeight="formContainerHeight"
			:editable="false"
			:template="template"
			:form-data="formData"
		/>
	</Page>
</template>

<script>
	import Page from '@/pages/component/Page.vue';
	import templateForm from '@/pages/form/template-form.vue';
	
	import formService from '@/api/form-service.js';
	import formUtil from '@/pages/form/Form.js';
	import styleUtil from '@/common/style.js';
	
	export default {
		components: {
			Page, templateForm
		},
		
		data() {
			return {
				title: '详情',
				formContainerHeight: -1,
				templateId: null,
				recordId: null,
				template: {},
				formData: {}
			}
		},
		
		onLoad(options) {
			this.title = options.title;
			this.templateId = options.templateId;
			this.recordId = options.recordId;
		},
		
		mounted() {
			if(this.templateId){
				this.loadTemplateConfig(this.templateId);
			}
			
			//非H5应用需要指定表单窗口高度
			// #ifndef H5
				let layouts = [];
				//页面尺寸
				layouts.push(styleUtil.getScreenLayout());
				//标题栏高度
				layouts.push(styleUtil.getNodeLayout(this, '#naviBar'));
				
				Promise.all(layouts)
					.then(datas => {
						//Page组件的插槽内边上下内边距之和
						let pageSlotPadding = 16;
						let totalHeight = datas[0].height;
						let naviBarHeight = 0;
						if(datas[1]){//小程序没有标题栏
							naviBarHeight = datas[1].height;
						}
						this.formContentHeight = totalHeight - naviBarHeight - pageSlotPadding;
					})
			// #endif
		},
		
		methods: {
			loadTemplateConfig(templateId){
				formService.getTemplateById(templateId)
					.then(templateJson => {
						let reorganizeTemplate = formUtil.packPeerElementIntoGroup(templateJson);
						// console.log(`原始模板信息：${JSON.stringify(templateJson, null, 4)}`)
						// console.log(`模板信息：${JSON.stringify(reorganizeTemplate, null, 4)}`)
						this.template = reorganizeTemplate;
						this.onTemplateLoaded(templateId);
					})
					.catch(error => {
						console.log(`获取模板出错：${error}`)
					})
			},
			
			onTemplateLoaded(templateId){
				formService.getRecordData(templateId, this.recordId)
					.then(recordData => {
						this.formData = recordData;
						console.log(`表单数据：${JSON.stringify(recordData, null, 4)}`)
					})
					.catch(error => {
						console.log(`获取表单数据出错：${JSON.stringify(error, null, 4)}`);
					})
			}
		}
	}
</script>

<style>

</style>
