<template>
	<!-- #ifdef H5 || APP-PLUS -->
	<text-element v-if="isText && constraintShow" :template="template" :constraints="constraints" :editable="editable" :record-id="recordId" :form-data="formData"/>
	<textarea-element v-else-if="isTextarea && constraintShow" :template="template" :constraints="constraints" :editable="editable" :record-id="recordId" :form-data="formData"/>
	<radio-element v-else-if="isRadio && constraintShow" :template="template" :constraints="constraints" :editable="editable" :record-id="recordId" :form-data="formData"/>
	<checkbox-element v-else-if="isCheckbox && constraintShow" :template="template" :constraints="constraints" :editable="editable" :record-id="recordId" :form-data="formData"/>
	<select-element v-else-if="isSelect && constraintShow" :template="template" :constraints="constraints" :editable="editable" :record-id="recordId" :form-data="formData"/>
	<date-element v-else-if="isDate && constraintShow" :template="template" :constraints="constraints" :editable="editable" :record-id="recordId" :form-data="formData"/>
	<person-element v-else-if="isPerson && constraintShow" :template="template" :constraints="constraints" :editable="editable" :record-id="recordId" :form-data="formData"/>
	<department-element v-else-if="isDepartment && constraintShow" :template="template" :constraints="constraints" :editable="editable" :record-id="recordId" :form-data="formData"/>
	<tree-element v-else-if="isTree && constraintShow" :template="template" :constraints="constraints" :editable="editable" :record-id="recordId" :form-data="formData"/>
	<industry-tree-element v-else-if="isIndustryTree && constraintShow" :template="template" :constraints="constraints" :editable="editable" :record-id="recordId" :form-data="formData"/>
	<industryselect-element v-else-if="isIndustryCascade && constraintShow" :template="template" :constraints="constraints" :editable="editable" :record-id="recordId" :form-data="formData"/>
	<district-element v-else-if="isDistrictCascade && constraintShow" :template="template" :constraints="constraints" :editable="editable" :record-id="recordId" :form-data="formData"/>
	<district-tree-element v-else-if="isDistrictTree && constraintShow" :template="template" :constraints="constraints" :editable="editable" :record-id="recordId" :form-data="formData"/>
	<latitude-longitude-element v-else-if="isLocation && constraintShow" :template="template" :constraints="constraints" :editable="editable" :record-id="recordId" :form-data="formData"/>
	<attach-group v-else-if="isAttachGroup && constraintShow" :template="template" :constraints="constraints" :editable="editable" :record-id="recordId" :form-data="formData"/>
	<description-element v-else-if="isDescription && constraintShow" :template="template" :constraints="constraints" :editable="editable" :record-id="recordId" :form-data="formData"/>
	<signature-element v-else-if="isSignature && constraintShow" :template="template" :constraints="constraints" :editable="editable" :record-id="recordId" :form-data="formData"/>
	<tag-element v-else-if="isTag && constraintShow" :template="template" :constraints="constraints" :editable="editable" :record-id="recordId" :form-data="formData"/>
	<data-select-element v-else-if="isdataSelect && constraintShow" style="width: 100%;" :template="template" :constraints="constraints" :editable="editable" :record-id="recordId" :form-data="formData"/>
	<!-- #endif -->
	
	<!-- #ifndef H5 || APP-PLUS -->
	<text-element v-if="isText && constraintShow" style="width: 100%; background-color: #fff;" :template="template" :constraints="constraints" :editable="editable" :record-id="recordId" :form-data="formData"/>
	<textarea-element v-else-if="isTextarea && constraintShow" style="width: 100%;" :template="template" :constraints="constraints" :editable="editable" :record-id="recordId" :form-data="formData"/>
	<radio-element v-else-if="isRadio && constraintShow" style="width: 100%;" :template="template" :constraints="constraints" :editable="editable" :record-id="recordId" :form-data="formData"/>
	<checkbox-element v-else-if="isCheckbox && constraintShow" style="width: 100%;" :template="template" :constraints="constraints" :editable="editable" :record-id="recordId" :form-data="formData"/>
	<select-element v-else-if="isSelect && constraintShow" style="width: 100%;" :template="template" :constraints="constraints" :editable="editable" :record-id="recordId" :form-data="formData"/>
	<date-element v-else-if="isDate && constraintShow" style="width: 100%;" :template="template" :constraints="constraints" :editable="editable" :record-id="recordId" :form-data="formData"/>
	<person-element v-else-if="isPerson && constraintShow" style="width: 100%;" :template="template" :constraints="constraints" :editable="editable" :record-id="recordId" :form-data="formData"/>
	<department-element v-else-if="isDepartment && constraintShow" style="width: 100%;" :template="template" :constraints="constraints" :editable="editable" :record-id="recordId" :form-data="formData"/>
	<tree-element v-else-if="isTree && constraintShow" style="width: 100%;" :template="template" :constraints="constraints" :editable="editable" :record-id="recordId" :form-data="formData"/>
	<industry-tree-element v-else-if="isIndustryTree && constraintShow" style="width: 100%;" :template="template" :constraints="constraints" :editable="editable" :record-id="recordId" :form-data="formData"/>
	<industryselect-element v-else-if="isIndustryCascade && constraintShow" style="width: 100%;" :template="template" :constraints="constraints" :editable="editable" :record-id="recordId" :form-data="formData"/>
	<district-element v-else-if="isDistrictCascade && constraintShow" style="width: 100%;" :template="template" :constraints="constraints" :editable="editable" :record-id="recordId" :form-data="formData"/>
	<district-tree-element v-else-if="isDistrictTree && constraintShow" style="width: 100%;" :template="template" :constraints="constraints" :editable="editable" :record-id="recordId" :form-data="formData"/>
	<latitude-longitude-element v-else-if="isLocation && constraintShow" style="width: 100%;" :template="template" :constraints="constraints" :editable="editable" :r∂ecord-id="recordId" :form-data="formData"/>
	<attach-group v-else-if="isAttachGroup && constraintShow" style="width: 100%;" :template="template" :constraints="constraints" :editable="editable" :record-id="recordId" :form-data="formData"/>
	<description-element v-else-if="isDescription && constraintShow" style="width: 100%;" :template="template" :constraints="constraints" :editable="editable" :record-id="recordId" :form-data="formData"/>
	<signature-element v-else-if="isSignature && constraintShow" style="width: 100%;" :template="template" :constraints="constraints" :editable="editable" :record-id="recordId" :form-data="formData"/>
	<tag-element v-else-if="isTag && constraintShow" style="width: 100%;" :template="template" :constraints="constraints" :editable="editable" :record-id="recordId" :form-data="formData"/>
	<data-select-element v-else-if="isdataSelect && constraintShow" style="width: 100%;" :template="template" :constraints="constraints" :editable="editable" :record-id="recordId" :form-data="formData"/>
	<view v-else style="display: none;"/>
	<!-- #endif -->
</template>

<script>
	import { ELEMENT_TYPE } from '@/pages/form/Form.js';
	import element from './element.js';
	import elementTypeResolver from './element-type-resolver.js';
	import textElement from './text-element.vue';
	import textareaElement from './textarea-element.vue';
	import radioElement from './radio-element.vue';
	import checkboxElement from './checkbox-element.vue';
	import selectElement from './select-element.vue';
	import tagElement from './tag-element.vue';
	import dateElement from './date-element.vue';
	import TreeElement from './tree-element.vue';
	import industryTreeElement from './industry-tree-element.vue';
	import industryselectElement from './industryselect-element';
	import personElement from './person-element.vue';
	import departmentElement from './department-element.vue';
	import districtElement from './district-element.vue';
	import districtTreeElement from './district-tree-element.vue';
	import latitudeLongitudeElement from './latitude-longitude-element.vue';
	import attachGroup from './attach-group.vue';
	import dataSelectElement from './data-select-element.vue';
	
	import descriptionElement from './description-element.vue';
	import signatureElement from './signature-element.vue'
	
	const mixins = [element, elementTypeResolver];
	
	export default {
		name: 'GroupElementResolver',
		// #ifdef MP-ALIPAY
		mixins: mixins.map(item => ({ ...item, props: {} })),
		props: {
			...mixins.reduce((prev, curr) => ({ ...prev, ...(curr.props || {}) }), {})
		},
		// #endif
		
		// #ifndef MP-ALIPAY
		mixins: mixins,
		// #endif
		components: {
			textElement,
			textareaElement,
			radioElement,
			checkboxElement,
			selectElement,
			dateElement,
			industryTreeElement,
			industryselectElement,
			personElement,
			departmentElement,
			districtElement,
			districtTreeElement,
			latitudeLongitudeElement,
			attachGroup,
			descriptionElement,
			signatureElement,
			TreeElement,
			tagElement,
			dataSelectElement
		}
	}
</script>
