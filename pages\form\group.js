import  { ELEMENT_TYPE } from './Form.js';

export default {
	
	computed: {
		childElements: function() {
			let children = this.template.child;
			let previous = null;
			children.forEach(e => {
				if(ELEMENT_TYPE.description === previous) {
					//当前一个元素是描述元素时，不显示分割线
					e.showDivider = false;
				} else {
					e.showDivider = true;
				}
				previous = e.type;
			});
			return children;
		}
	}
	
}