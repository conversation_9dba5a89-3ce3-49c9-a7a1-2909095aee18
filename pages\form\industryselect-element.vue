<template>
	<label-element 
		ref="root" 
		:label="label" 
		:required="required"
		:id="'templateId_' + template.id">
		<!-- #ifdef MP-ALIPAY -->
		<text
			class="form-value cascade-picker-text"
			@click="showPicker">
			{{filter(displayValue) || '请选择'}}
		</text>
		<industry-cascade-picker ref="picker" @confirm="onCascadePickerConfirm"/>
		<!-- #endif -->
		
		<!-- #ifndef MP-ALIPAY -->
		<picker 
			style="flex: 1; text-align: right;"
			mode="multiSelector"
			class="form-value"
			:class="actionSlotClass"
			:range="columns"
			:range-key="optionTextKey"
			:value="selectedIndexs"
			:disabled="!editable"
			@columnchange="onSelectChange"
			@change="onSelectConfirm">
			<view>{{filter(displayValue) || '请选择'}}</view>
		</picker>
		<!-- #endif -->
		<image
			v-if="editable"
			class="form-select-action-indicator"
			mode="aspectFit"
			:src="naviNextIcon"
		/>
	</label-element>
</template>

<script>
	import element from './element.js';
	import naviNextIcon from '@/static/img/navi_next_icon.png';
	import labelElement from './label-element.vue';
	import industryselect from '@/api/industryselect-service.js';
	import { trimZero } from '@/api/district-service.js';
	import { ELEMENT_TYPE } from './Form.js';
	
	// #ifdef MP-ALIPAY
	import industryCascadePicker from '@/pages/component/cascade/industry-cascade-picker.vue'
	// #endif
	
	//读取元素配置的行政区划省、市、县(区)、镇(乡)字段的键
	const COLUMN_KEY_SUFFIX = '_column';
	const COLUMN_KEY_PROVINCE = 'province';//第一级
	const COLUMN_KEY_CITY = 'city';//第二级
	const COLUMN_KEY_COUNTY = 'area';
	const COLUMN_KEY_TOWN = 'town';
	
	//存值后缀
	const VALUE_KEY_SUFFIX = '_column';
	
	const mixins = [element];
	
	export default {
		name: 'IndustryselectElement',
		// #ifdef MP-ALIPAY
		mixins: mixins.map(item => ({ ...item, props: {} })),
		props: {
			...mixins.reduce((prev, curr) => ({ ...prev, ...(curr.props || {}) }), {})
		},
		components: {
			labelElement, industryCascadePicker
		},
		// #endif
		
		// #ifndef MP-ALIPAY
		mixins: mixins,
		components: {
			labelElement
		},
		// #endif
		
		data() {
			return {
                naviNextIcon,
                selectLevel:4,//可选等级的控制
				optionTextKey: 'name',
				columns: [],
				selectedIndexs: []
			}
		},
		
		computed: {
			/**
			 * 是否有值
			 */
			selected: function() {
				return typeof this.displayValue !== 'undefined'
					this.displayValue !== null && this.displayValue.length > 0;
			},
			
			actionSlotClass: function(){
				return {
					'form-label': !this.selected,
					'form-value': this.selected
				}
			}
		},
		
		mounted(){
			this.initIndustryData();
			this.resolveDisplayValue(this.formData);
		},
		
		methods: {
			// #ifdef MP-ALIPAY
			showPicker(){
				this.$refs.root.$refs.picker.show();
			},
			// #endif
			
			initIndustryData(){
				industryselect.getRootIndustry()
					.then(root => {
						if(root){
							this.buildDefaultCascadeData(root)
						}
					})
			},
			
			/**
			 * 构建默认展示的级联数据
			 * @param {Object} parent
			 */
			buildDefaultCascadeData(parent){
				industryselect.getChildIndustry(parent)
					.then(children => {
						if(children.length > 0) {
                            if(this.columns.length < this.selectLevel){
                                this.selectedIndexs.push(0);
                            this.columns.push(children)
                            }
							this.buildDefaultCascadeData(children[0]);
                            	
                        }
					})
			},
			
			/**
			 * 级联列选择回调事件
			 * @param {Object} event
			 */
			onSelectChange(event){
				let columnInfo = event.detail;
                let {column, value} = columnInfo;
                this.$set(this.selectedIndexs, columnInfo.column, columnInfo.value);
                let selectedDistrict = this.columns[column][value];
                this.buildNextCascadeData(selectedDistrict, column + 1);
                
			},
			
			/**
			 * 选择某一列数据时，重新构建下一列数据
			 * @param {Object} parent
			 * @param {Object} column
			 */
			buildNextCascadeData(parent, column){       
				industryselect.getChildIndustry(parent)
					.then(children => {
						if(children.length > 0) {
                            if(column < this.selectLevel){
                                this.selectedIndexs.splice(column, 1, 0);
                                this.columns.splice(column, 1, children);
                                this.buildNextCascadeData(children[0], column + 1);
                            }
						}
                    })
                    
			},
			
			// #ifdef MP-ALIPAY
			onCascadePickerConfirm(indexs, columns) {
				this.selectedIndexs = indexs;
				this.columns = columns;
				this.onSelectConfirm();
			},
			// #endif
			
			/**
			 * 行业类型联动级联对话框点击确定回调
			 */
			onSelectConfirm(){
				let selectedColumns = [];
				this.selectedIndexs.forEach((item, index) => {
					selectedColumns.push(this.columns[index][item]);
				})
				this.displayValue = '';
				let nameArr = []
				selectedColumns.forEach(district => {
					nameArr.push(district.name);
					this.displayValue = nameArr.join('>')
					let code = district.code;
                    let columnPreffix = this.getColumnFieldPreffix(code);
					let field = this.template[`${columnPreffix}${COLUMN_KEY_SUFFIX}`];
					this.updateFormFieldValue(field, code);
				});
			},
			
			/**
			 * 获取行业类型联动的前缀代码，根据行业类型联动代码的定义规则来区分：
			 * 行业类型值长度可能有1-4位数，1级为大写的英文字母，2级为2位数字，3级为3位数，4级为4位数字
			 * @param {Object} code
			 */
			getColumnFieldPreffix(code){
                let trimTailZeroCode = trimZero(code);
				let columnPreffix = '';
				switch(trimTailZeroCode.length){
					case 1:
						columnPreffix = COLUMN_KEY_PROVINCE;
						break;
					case 2: 
						columnPreffix = COLUMN_KEY_CITY;
						break;
					case 3:
						columnPreffix = COLUMN_KEY_COUNTY;
						break;
					case 4:
						columnPreffix = COLUMN_KEY_TOWN;
						break;
				}
				return columnPreffix;
			},
			
			resolveDisplayValue(data){
				industryselect.loadIndustrySource()
					.then(districtSource => {
						let columnPreffixs = [
							COLUMN_KEY_PROVINCE,
							COLUMN_KEY_CITY,
							COLUMN_KEY_COUNTY,
							COLUMN_KEY_TOWN
						];
                        this.displayValue = '';
                        let nameArr = []
                        let fieldList =[]
						columnPreffixs.forEach(preffix => {
							let field = this.template[`${preffix}${COLUMN_KEY_SUFFIX}`];
							if(field){
                                fieldList.push(preffix)
                                this.selectLevel = fieldList.length
                                let value = data[field];
								if(value){
                                    let district = industryselect.getIndustryByCodeSync(value, districtSource);
                                    nameArr.push(district.name);
									if(district){
										this.displayValue = nameArr.join('>')
									}
								}
							}
						})
					})
            },
            
            filter(index){
                return index
            }
		}
	}
</script>

<style scoped>
	.form-select-action-indicator {
		transform: rotate(90deg);
		margin-left: 5px;
		width: 16px;
		height: 16px;
	}
	
	/* #ifdef MP-ALIPAY */
	.cascade-picker-text {
		flex: 1;
		margin-right: 5px;
		text-align: right;
		width: 100%;
	}
	/* #endif */
</style>
