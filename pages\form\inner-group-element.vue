<template>
	<view class="inner-group-element">
		<!-- #ifdef H5 ||APP-PLUS -->
		<template
			v-for="(element, index) in template.child">
			<view
				class="form-element-divider"
				v-if="index !== 0 && resolveElementType(element)"
			/>
			<group-element-resolver
				v-if="showElement(element) && isElementDeclare(element.type) "
				class="form-element-style"
				:editable="editable"
				:record-id="recordId" 
				:template="element"
				:form-data="formData"
			/>	
		</template>
		<!-- #endif -->
		
		<!-- #ifndef H5 ||APP-PLUS -->
		<!-- 小程序需要套一层view，给动态模块边框样式 -->
		<view class="inner-group-element">
			<template
				v-for="(element, index) in template.child">
				<view
					class="form-element-divider"
					v-if="index !== 0 && resolveElementType(element)"
				/>
				<!-- #ifdef MP-ALIPAY -->
				<!-- 钉钉小程序会把group-element-resolver的样式完全抹掉，所以需要特殊处理 -->
				<view
					v-if="isElementDeclare(element.type) && showElement(element)"
					class="form-element-style">
					<group-element-resolver
						class="form-element-style"
						:editable="editable"
						:record-id="recordId"
						:template="element"
						:form-data="formData"
					/>
				</view>
				<!-- #endif -->
				
				<!-- #ifndef MP-ALIPAY -->
				<group-element-resolver
					class="form-element-style"
					v-if="showElement(element) && isElementDeclare(element.type)"
					:editable="editable"
					:record-id="recordId"
					:template="element"
					:form-data="formData"
				/>
				<!-- #endif -->
			</template>
		</view>
		<!-- #endif -->
	</view>
</template>

<script>
	import element from './element.js';
	import groupElementResolver from './group-element-resolver.vue';
	
	const mixins = [element];
	
	export default {
		name: 'inner-group-element',
		// #ifdef MP-ALIPAY
		mixins: mixins.map(item => ({ ...item, props: {} })),
		props: {
			...mixins.reduce((prev, curr) => ({ ...prev, ...(curr.props || {}) }), {})
		},
		// #endif
		
		// #ifndef MP-ALIPAY
		mixins: mixins,
		// #endif
		components: {
			groupElementResolver
		},
		data() {
			return {
				addAndDel: ['+', '-']
			}
		}
	}
</script>

<style scoped>
	.inner-group-element {
		box-shadow: 0px 0px 10rpx #eee;
	}
	
	.element-style {
		width: calc(100% - 40rpx);
		padding-left: 20rpx;
		padding-right: 20rpx;
	}
</style>
