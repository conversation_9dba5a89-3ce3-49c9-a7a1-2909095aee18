<template>
  <uni-popup type="center" ref="missPop" :mask-click="false">
    <view class="dialog">
      <view>
        <!-- <image
          src="/static/img/icon_delete_corner.png"
          @click="closeDialog"
          class="imgic"
        ></image> -->
        <view class="dialog-title">廉洁执法须知</view>
        <view class="dialog-main">
          <div class="content-box">
            <scroll-view scroll-y="true" style="height: 700rpx">
              <p>
                （1）严格审批程序。立案、销案的采取履行审批手续，立案、销案、对案件线索的终止调查必须上报主管局长、局长直至局长办公会审批。建立案件线索处理台帐。对案件来源、批转及处理结果进行登记,部门领导要签字把关,每季度监督检查。
              </p>
              <p>
                （2）严格执法监督。坚持2人以上办案,调查结果办案人员分别签字，互相监督，互相把关，办案领导对调查取证的过程和内容进行把关，和知情人、举报人核实相关情况。
              </p>
              <p>
                （3）坚持集体定案。坚持法律法规适用、自由裁量权使用的集体研究制。
              </p>
              <p>
                （4）严格罚没款物管理。对扣留罚没款物的数量、质量、品种、规格进行严格登记,由专人负责保管,不得公款私存、截留截挪用。在做出解除封存，上缴财务科处理时,主管局长要负责查验核对。对销毁物资,由2人以上签字，主管局长到场。
              </p>
              <p>
                （5）坚持定期监督。每季度组织相关部门,对集体定案案卷，扣留物资以及防范措施落实情况。
              </p>
            </scroll-view>
          </div>
          <!-- <uni-forms-item label="手机号">
                    <uni-easyinput
                        type="text"
                        class="attach-input"
                        v-model="signatureTel"
                        :clearable="true"
                        align="left"
                        placeholder="请输入手机号"
                    />
                </uni-forms-item> -->
          <!-- <view class="signature-tel">
            <text>手机号：</text>
            <uni-easyinput
              type="number"
              class="attach-input"
              v-model="signatureTel"
              :clearable="true"
              maxlength="11"
              placeholder="请输入现场负责人手机号"
          /></view> -->

          <div class="has-read-box">
            <checkbox-group @change="checkboxChange">
              <label> <checkbox value="已读" :checked="hasRead" />已读 </label>
            </checkbox-group>
            <button
              type="default"
              class="confirm-button"
              :disabled="!hasRead"
              @click="confirm()"
            >
              确认
            </button>
          </div>
        </view>
      </view>
    </view>
  </uni-popup>
</template>

<script>
export default {
  name: "integrityDeclaration",

  components: {},
  props: {},

  data() {
    return {
      hasRead: false,
      signatureTel: "", //签名使用的手机号
    };
  },

  computed: {
    pageMainStyle() {
      return {
        height: "600rpx",
      };
    },
  },

  mounted() {
    let personOrgId = uni.getStorageSync("userInfo").orgid;
    if (personOrgId === "370000") {
      this.$refs.missPop.open();
    }
  },

  methods: {
    closeDialog() {
      this.$refs.missPop.close();
    },
    confirm() {
      // if (this.signatureTel.length !== 11) {
      //     uni.showToast({
      //         title: '电话号码未输入或输入不正确',
      //         duration: 2000,
      //         icon: 'none'
      //     });
      //     return;
      // }
      // let list = {};
      // list.signatureTel = this.signatureTel;
      // list.hasRead = this.hasRead;
      // this.$emit("confirm", list);
      this.$refs.missPop.close();
    },
    checkboxChange: function (e) {
      let values = e.detail.value;
      if (values.length) {
        this.hasRead = true;
      } else {
        this.hasRead = false;
      }
    },
  },
};
</script>

<style scoped>
.dialog {
  background-color: #ffffff;
  border-radius: 16rpx;
  width: 660rpx;
  position: relative;
  z-index: 1000;
}

.dialog-title {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  font-size: 40rpx;
}

.dialog-main {
  padding: 22rpx;
}

.dialog-main view {
  padding: 20rpx 0;
}

.dialog-main-list {
  border-bottom: 1rpx dotted #ccc;
}

.dialog-main-type {
  color: #52b4ff;
}

.dialog-main-info {
  background-color: #eee;
}

.dialog-main-check {
  margin-top: 10rpx;
}

.imgic {
  float: right;
  width: 40rpx;
  height: 40rpx;
}

.dialog-main-router {
  font-size: 26rpx;
  color: blue;
  float: right;
}
.time-count-line {
  width: 100%;
  text-align: center;
  margin-top: 20rpx;
  line-height: 60rpx;
  height: 60rpx;
}
.has-read-box {
  width: 100%;
  margin-top: 20rpx;
  line-height: 60rpx;
  height: 60rpx;
  display: flex;
  justify-content: space-between;
}
.confirm-button {
  background-color: #0099ff;
  color: #fff;
  border-radius: 10rpx;
  width: 140rpx;
  line-height: 60rpx;
}
.content-box {
  height: 700rpx;
  /* overflow-y: auto; */
}

.signature-tel {
  display: flex;
  justify-content: center;
  align-items: center;
}

.attach-input {
  text-align: left;
}
</style>
