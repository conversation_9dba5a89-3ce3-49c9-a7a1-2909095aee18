<template>
	<view class="flex-row-layout" :id="id">
		<span v-if="required" class="form-label-required">*</span>
		<text class="form-label">{{label}}</text>
		<view class="flex-row-layout form-element-slot">
			<slot />
		</view>
	</view>
</template>

<script>
	export default {
		name: 'label-element',
		props: {
			label: {
				type: String,
				default: ''
			},
			verify: {
				type: String,
				default: ''
			},
			id: {
				type: String,
				default: ''
			},
			required: {
				type: Boolean,
				default: false
			}
		},
		
		data() {
			return {
				
			}
		}
	}
</script>

<style scoped>
	.form-element-slot {
		flex: 1;
		justify-content: flex-end;
		margin-left: 10px;
	}
</style>
