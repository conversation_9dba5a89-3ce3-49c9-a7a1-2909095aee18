<template>
    <view
        :id="'templateId_' + template.id"
        class="flex-column-layout"
        style="align-items: flex-start; padding-top: 10rpx; position: relative"
    >
        <view>
            <text class="form-label-required">{{ requiredSymbol }}</text>
            <text class="form-label">
                {{ label }}
            </text>
        </view>
        <view class="flex-row-layout">
            <view class="form-location-field">
                <text
                    class="form-value"
                    style="height: 44rpx; line-height: 44rpx"
                    >经度</text
                >
                <input
                    style="height: 44rpx; line-height: 44rpx"
                    class="form-value form-location-input"
                    v-if="editable"
                    type="digit"
                    v-model="longitude"
                    maxlength="10"
                    disabled="true"
                    @blur="longitudeFilter()"
                />
                <input
                    style="height: 44rpx; line-height: 44rpx"
                    class="form-value form-location-input"
                    v-if="!editable"
                    type="digit"
                    v-model="longitude"
                    disabled="true"
                    maxlength="10"
                />
            </view>

            <view style="margin-left: 20rpx" class="form-location-field">
                <text class="form-value">纬度</text>
                <input
                    class="form-value form-location-input"
                    v-if="editable"
                    maxlength="9"
                    type="digit"
                    v-model="latitude"
                    @blur="latitudeFilter()"
                    disabled="true"
                />
                <input
                    class="form-value form-location-input"
                    v-if="!editable"
                    maxlength="9"
                    type="digit"
                    disabled="true"
                    v-model="latitude"
                    @blur="latitudeFilter()"
                />
            </view>
            <!-- <view v-if="displayValue">{{'123'}}</view> -->
            <view
                style="width: 64rpx; height: 64rpx; margin-left: 20rpx"
                class="flex-fix-center-layout"
            >
                <image
                    style="width: 44rpx; height: 44rpx"
                    class="location-img"
                    @click="changeLocation"
                    v-if="editable"
                    mode="aspectFit"
                    :src="location"
                />
                <image
                    style="width: 44rpx; height: 44rpx"
                    class="location-img"
                    v-if="!editable"
                    mode="aspectFit"
                    :src="location"
                />
            </view>
        </view>
    </view>
</template>

<script>
import optionElement from './option-element.js';
import { MARK_FIELD } from '@/pages/form/Form.js';

export default {
    name: 'latitudeLongitudeElement',
    mixins: [optionElement],
    data() {
        return {
            location: require('@/static/img/location.png'),
            latitude: '',
            longitude: '',
            longitudeType: false,
            latitudeType: false
        };
    },

    computed: {
        latitudeField: function () {
            return this.template.latitude_column;
        },

        longitudeField: function () {
            return this.template.longitude_column;
        }
    },

    mounted() {
        if (this.formData[this.latitudeField]) {
            this.latitude = this.formData[this.latitudeField];
            this.longitude = this.formData[this.longitudeField];
        }
        this.autoFixLocation();
    },

    watch: {
        latitude() {
            this.updateFormFieldValue(this.latitudeField, this.latitude);
        },

        longitude() {
            this.updateFormFieldValue(this.longitudeField, this.longitude);
        }
    },

    methods: {
        autoFixLocation() {
            if (!this.editable) {
                return;
            }
            let _self = this;
            let locationInfo = uni.getStorageSync('APP_location');
            if (locationInfo) {
                _self.longitude = locationInfo.longitude;
                _self.latitude = locationInfo.latitude;
            }
            uni.getLocation({
                success(location) {
                    _self.longitude = location.longitude;
                    _self.latitude = location.latitude;
                },
                fail(error) {
                    console.log(`定位失败：${JSON.stringify(error, null, 4)}`);
                }
            });
        },

        //跳转选择地图中的经纬度，并接收返回的经纬度
        changeLocation() {
            // this.longitudeFilter();
            // this.latitudeFilter();
            // if(this.longitudeType && this.latitudeType){
            uni.$once('onLocationSelect', location => {
                this.latitude = location.lat;
                this.longitude = location.lng;
                this.updateFormFieldValue(this.latitudeField, this.latitude);
                this.updateFormFieldValue(this.logitudeField, this.longitude);
            });
            uni.navigateTo({
                url:
                    '/pages/component/location/location-index?lat=' +
                    this.latitude +
                    '&lng=' +
                    this.longitude
            });
            // }
        },

        //经度的校验（不允许超过中国的范围）
        longitudeFilter() {
            if (this.longitude > 135.4) {
                this.filterTips(this.longitude);
                this.longitudeType = false;
                this.longitude = '';
            } else if (this.longitude < 73.4) {
                this.filterTips(this.longitude);
                this.longitudeType = false;
                this.longitude = '';
            } else {
                this.longitudeType = true;
            }
        },

        //纬度的校验（不允许超过中国的范围）
        latitudeFilter() {
            if (this.latitude > 53.6) {
                this.filterTips(this.latitude);
                this.latitudeType = false;
                this.latitude = '';
            } else if (this.latitude < 3.8) {
                this.filterTips(this.latitude);
                this.latitudeType = false;
                this.latitude = '';
            } else {
                this.latitudeType = true;
            }
        },

        //超出经纬度范围后的提示
        filterTips(index) {
            uni.showToast({
                icon: 'none',
                duration: 4000,
                title: '该经纬度格式不对或超出中国的经纬度范围,请重新输入正确的经纬度'
            });
        },

        resolveDisplayValue(data) {
            //经纬度元素并不能对显示做操作
        },

        getSegmentFields() {
            return [this.longitudeField, this.latitudeField];
        }
    }
};
</script>

<style scoped>
.form-location-field {
    flex: 1;
    display: flex;
    align-items: center;
}

.form-location-input {
    flex: 1;
    margin-left: 10rpx;
}

.location-img {
    width: 30upx;
    height: 48upx;
}

.form-verify {
    position: absolute;
    top: 15px;
}
</style>
