import element from './element.js';
import codeService from '@/api/code-service.js';

export default {
	mixins: [element],
	data(){
		return {
			codeset: [],
			pCodeset: [] // 有父亲节点的公共代码值
		}
	},
	
	created() {
		let sourcevalue = this.template.sourcevalue;
		let parentCode = '';
		try{
			parentCode = this.template.fdm;  // 父代码
		}catch(e){
			parentCode = '';
		}
		
		if(sourcevalue){
			//初始化选项值
			codeService.getCodeSet(sourcevalue)
				.then(codes => {
					let codeset = codes.map(item => {
						return {
							text: item.name,
							value: item.code
						}
					})
					this.codeset = codeset;
				})
		}
		
		
		if(parentCode) {
			codeService.queryParentCode(sourcevalue, parentCode)
				.then(codes => {
					let pCodeset = codes.data.map(item => {
						return {
							text: item.DMMC,
							value: item.DM
						}
					})
					this.pCodeset = pCodeset;
				})
		}
		
	},
	
	computed: {
		options: function() {
			return this.codeset || this.template.datas || [];
		},
		pOptions: function() {
			return this.pCodeset || [];
		}
	},
	
	watch: {
		codeset: function(){
			this.resolveDisplayValue(this.formData);
		},
		pCodeset: function(){
			this.resolveDisplayValue(this.formData);
		}
	},
	
	methods: {
		resolveDisplayValue(data){
			if(typeof data === 'undefined' || data === null){
				return;
			}
			let value = data[this.field] || '';
			if(value && this.options.length > 0){
				for(let option of this.options){
					if(value === option.value){
						this.displayValue = option.text;
						break;
					}
				}
			} else {
				this.displayValue = '';
			}
		}
	}
}