<template>
	<label-element 
		:label="label"
		:required="required"
		@click.native="turnToSelectPage">
		<text
			class="form-value"
			style="flex: 1; text-align: right;">
			{{displayValue}}
		</text>
		<image
			style="margin-left: 10rpx; width: 32rpx; height: 32rpx;"
			v-if="editable"
			mode="aspectFit"
			:src="naviNextIcon"
		/>
	</label-element>
</template>

<script>
	import naviNextIcon from '@/static/img/navi_next_icon.png';
	import labelElement from './label-element.vue';
	
	import element from './element.js';
	
	import {
		EVENT_SELECT_CONFIRM,
		EVENT_SELECT_CANCEL
	} from '@/pages/department/department.js';
	
	const mixins = [element];
	
	export default {
		name: 'OrganizationElement',
		// #ifdef MP-ALIPAY
		mixins: mixins.map(item => ({ ...item, props: {} })),
		props: {
			...mixins.reduce((prev, curr) => ({ ...prev, ...(curr.props || {}) }), {})
		},
		// #endif
		
		// #ifndef MP-ALIPAY
		mixins: mixins,
		// #endif
		components: {
			labelElement
		},
		
		data() {
			return {
				naviNextIcon,
				separator: ','
			}
		},
		
		computed: {
			isMultiCheck: function(){
				return 'true' === this.template.SFDX;
			}
		},
		
		methods: {
			turnToSelectPage(){
				uni.$on(EVENT_SELECT_CONFIRM, (nodes) => {
					uni.$off(EVENT_SELECT_CONFIRM);
					this.onSelected(nodes);
				});
				
				uni.$on(EVENT_SELECT_CANCEL, () => {
					uni.$off(EVENT_SELECT_CANCEL);
				})
				
				//调用实际元素的跳转选择页面方法
				this.select();
			},
			
			select(){
				//在具体实现元素中重写该方法
				uni.navigateTo({
					url: '/pages/department/department'
				})
			},
			
			onSelected(nodes){
				//在具体元素中重写方法，实现相应逻辑
			}
		}
	}
</script>