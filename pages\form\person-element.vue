<template>
    <label-element
        :id="'templateId_' + template.id"
        :label="label"
        :required="required"
        :editable="editable"
        @click.native="turnToSelectPage"
    >
        <text
            class="form-value"
            style="
                flex: 1;
                text-align: right;
                line-height: 56rpx;
                min-height: 56rpx;
            "
            @click.stop="turnToSelectPage"
        >
            {{ displayValue }}
        </text>
        <image
            style="margin-left: 10rpx; width: 32rpx; height: 32rpx"
            v-if="!readonly && editable"
            mode="aspectFit"
            :src="naviNextIcon"
        />
    </label-element>
</template>

<script>
import naviNextIcon from '@/static/img/navi_next_icon.png';
import labelElement from './label-element.vue';
import { MODE_SELECT_PERSON } from '@/pages/department/department.js';
import {
    EVENT_SELECT_CONFIRM,
    EVENT_SELECT_CANCEL
} from '@/pages/department/department.js';
import element from './element.js';
const mixins = [element];
import contactService from '@/api/contact-service.js';

export default {
    name: 'PersonElement',
    components: {
        labelElement
    },
    // #ifdef MP-ALIPAY
    mixins: mixins.map(item => ({ ...item, props: {} })),
    props: {
        ...mixins.reduce(
            (prev, curr) => ({ ...prev, ...(curr.props || {}) }),
            {}
        )
    },
    // #endif

    // #ifndef MP-ALIPAY
    mixins: mixins,
    // #endif

    data() {
        return {
            naviNextIcon,
            separator: ','
        };
    },

    computed: {
        isMultiCheck: function () {
            return 'true' === this.template.SFDX;
        }
    },

    methods: {
        turnToSelectPage() {
            if (this.disable) {
                return;
            }
            uni.$on(EVENT_SELECT_CONFIRM, nodes => {
                uni.$off(EVENT_SELECT_CONFIRM);
                this.onSelected(nodes);
            });

            uni.$on(EVENT_SELECT_CANCEL, () => {
                uni.$off(EVENT_SELECT_CANCEL);
            });

            //调用实际元素的跳转选择页面方法
            this.select();
        },

        select() {
            let url = `/pages/department/department?mode=${MODE_SELECT_PERSON}&multi=${this.isMultiCheck}`;
            if (this.value) {
                url += `&checked=${this.value}`;
            }
            uni.navigateTo({
                url
            });
        },

        /**
         * 选择用户后返回回调
         * @param {Object} users
         */
        onSelected(users) {
            let userIds = [];
            let names = [];
            users.forEach(user => {
                userIds.push(user.YHID);
                names.push(user.YHMC);
            });

            this.$nextTick(function () {
                this.value = userIds.join(this.separator);
                this.displayValue = names.join(this.separator);
                // console.log(this.displayValue,'selectValue');
            });
        },

        resolveDisplayValue(data) {
            if (this.value) {
                contactService.loadUsers().then(users => {
                    let userIds = this.value.split(this.separator);
                    let filteredUsers = contactService.filterUsersByIds(
                        users,
                        userIds
                    );
                    let names = filteredUsers.map(user => {
                        return user.YHMC;
                    });
                    this.displayValue = names.join(this.separator);
                });
            }
        },

        /**
         * 人员选择元素需要单独定义元素关联触发逻辑
         * @param {Object} constraintValue
         */
        emitConstraintEvent() {
            let _self = this;
            let params = {
                element: this.template
            };
            if (this.value) {
                contactService.loadUsers().then(users => {
                    let userIds = _self.value.split(_self.separator);
                    let filteredUsers = contactService.filterUsersByIds(
                        users,
                        userIds
                    );

                    let values = filteredUsers.map(user => {
                        user[_self.field] = user.YHID;
                        if (_self.displayField) {
                            user[_self.displayField] = user.YHMC;
                        }
                        let certNum = user.ZFZHSD || user.ZFZHHBB;
                        if (certNum && !user.hasOwnProperty('ZFZH')) {
                            user.ZFZH = certNum;
                        }
                        return user;
                    });
                    params.value = values;
                    uni.$emit(this.elementId, params);
                });
            } else {
                params.value = [];
                uni.$emit(this.elementId, params);
            }
        }
    }
};
</script>
