<template>
	<label-element 
		:id="'templateId_' + template.id"
		:label="label"
		:required="required"
		@click.native="selectPollutant">
		<!-- #ifdef MP-ALIPAY -->
		<text 
			style="width: 100%; text-align: end; margin-right: 5px;"
			class="form-value" 
			@click="selectPollutant">
			{{displayValue || '请选择'}}
		</text>
		<!-- #endif -->
		<!-- #ifndef MP-ALIPAY -->
		<text class="form-value">{{displayValue || '请选择'}}</text>
		<!-- #endif -->
		<image
			v-if="editable"
			class="form-select-action-indicator"
			mode="aspectFit"
			:src="naviNextIcon"
		/>
	</label-element>
</template>

<script>
	import labelElement from './label-element.vue';
	import naviNextIcon from '@/static/img/navi_next_icon.png';
	
	import element from './element.js';
	
	const mixins = [element];
	
	export default {
		name: 'PollutantElement',
		// #ifdef MP-ALIPAY
		mixins: mixins.map(item => ({ ...item, props: {} })),
		props: {
			...mixins.reduce((prev, curr) => ({ ...prev, ...(curr.props || {}) }), {})
		},
		// #endif
		
		// #ifndef MP-ALIPAY
		mixins: mixins,
		// #endif
		components: {
			labelElement
		},
		
		data() {
			return {
				naviNextIcon
			}
		},
		
		methods: {
			selectPollutant() {
				if(!this.editable) {
					return
				}
				
				uni.$once('onPollutantSelected', (pollutant) => {
					
				})
			}
		}
	}
</script>

<style scoped>

</style>
