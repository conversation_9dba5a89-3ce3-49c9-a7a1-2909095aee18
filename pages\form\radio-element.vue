<template>
	<view 
		class="flex-column-layout form-radio-element" :id="'templateId_' + template.id">
		<view>
			<span 
				v-if="required"
				class="form-label-required">
				{{ requiredSymbol }}
			</span>
			<text class="form-label">{{ label }}</text>
		</view>
		<view class="form-value">
			<radio-group class="radio">
				<label style="padding: 0px;" @click="radioChange(item.value)" class="uni-list-cell uni-list-cell-pd" v-for="(item, index) in options" :key="index">
					<view class="radio-list">
						<view>
							<radio style="transform:scale(0.7)" :disabled="disable" :value="item.value" :checked="index === current" />
						</view>
						<view class="form-value">{{ item.text }}</view>
					</view>
				</label>
			</radio-group>
		</view>
	</view>
</template>

<script>
import optionElement from './option-element.js';

export default {
	name: 'RadioElement',
	mixins: [optionElement],
	computed: {
		current() {
			for (let i in this.options) {
				if (this.value === this.options[i].value) {
					return parseInt(i);
				}
			}
		}
	},

	mounted() {
		
	},

	methods: {
		radioChange: function(value) {
			if(this.disable){
				return
			}

			if (this.value == value) {
				this.value = '';
			} else {
				for (let i = 0; i < this.options.length; i++) {
					if (this.options[i].value === value) {
						this.value = value;
						this.displayValue = this.options[i].text
						break;
					}
				}
			}
		},
	}
};
</script>

<style scoped>
.form-radio-element {
	align-items: flex-start;
	padding-top: 10rpx;
	padding-bottom: 10rpx;
}

.radio-list {
	display: flex;
	align-items: center;
	justify-content: center;
}

.radio {
	display: flex;
	margin-left: auto;
	font-size: 28rpx;
	color: #333;
	flex-wrap: wrap;
}

.radio-list {
	margin-right: 20rpx;
	padding: 6rpx 0rpx;
}
</style>
