<template>
	<label-element :label="label" :required="required" :id="'templateId_' + template.id">
		<picker 
			style="flex: 1; text-align: right;"
			class="form-value"
			:disabled="disable"
			:class="actionSlotClass"
			:range="selectOptions"
			:range-key="optionTextKey"
			:value="selectedIndex"
			@change="onSelectChange">
			<view>{{displayValue || (editable ? '请选择' : '')}}</view>
		</picker>
		<image
			v-if="editable"
			class="form-select-action-indicator"
			mode="aspectFit"
			:src="naviNextIcon"
		/>
	</label-element>
</template>

<script>
	import codeService from '@/api/code-service.js';
	import optionElement from './option-element.js';
	
	import naviNextIcon from '@/static/img/navi_next_icon.png';
	import labelElement from './label-element.vue';
	
	export default {
		name: 'select-element',
		mixins: [optionElement],
		components: {
			labelElement
		},
		
		data() {
			return {
				naviNextIcon,
				optionTextKey: 'text',
				selectedIndex: 0,
				displayValue: '请选择',
				placeholdOption: [
					{
						text: '请选择',
						value: '-1'
					}
				]
			}
		},
		
		computed: {
			
			selectOptions: function(){
				return this.placeholdOption.concat(this.options);
			},
			
			selected: function() {
				return typeof this.displayValue !== 'undefined'
					this.displayValue !== null && this.displayValue.length > 0;
			},
			
			actionSlotClass: function(){
				return {
					'form-label': !this.selected,
					'form-value': this.selected
				}
			}
		},
		
		methods: {
			onSelectChange(event){
				if(this.disable) {
					return
				}

				let index = parseInt(event.detail.value);
				//在小程序平台上这个值是字符串
				this.selectedIndex = index;
				if(index === 0){
					this.value = '';
				} else {
					this.value = this.selectOptions[index].value;
					this.displayValue = this.selectOptions[index].text;
				}
			}
		},
		

	}
</script>

<style scoped>
	.form-select-action-indicator {
		transform: rotate(90deg);
		margin-left: 5px;
		width: 16px;
		height: 16px;
	}
</style>
