<template>
    <view
        class="flex-column-layout signature-element"
        :id="'templateId_' + template.id"
    >
        <text class="form-verify">{{ verify }}</text>
        <text>{{ label }} </text>
        <view class="flex-row-layout signature-image-layout">
            <div v-if="value">
                <image
                    class="signature-item"
                    mode="aspectFit"
                    v-for="(signature, index) in signatures"
                    :key="index"
                    :src="restoreSignature(signature)"
                    @click="previewSignature(signature, index)"
                />
            </div>
            <image
                v-if="editable"
                class="signature-add"
                :src="addIcon"
                @click="turnToSignature"
            />
            <view v-if="!editable">
                <image
                    class="signature-item"
                    mode="aspectFit"
                    v-for="(signature, index) in disSignatures"
                    :key="index"
                    :src="restoreSignature(signature)"
                />
            </view>
            <operate-menu-dialog
                ref="operateDialog"
                :menus="editMenus"
                @menuClick="onMenuClick"
            />
        </view>
    </view>
</template>

<script>
import addIcon from '@/static/img/add.png';
import {
    pathToBase64,
    base64ToPath
} from '@/js_sdk/gsq-image-tools/image-tools/index.js';
import styleUtil from '@/common/style.js';
import operateMenuDialog from '@/pages/component/dialog/operate-menu-dialog.vue';
import element from './element.js';
import { DOWNLOAD_URL } from '@/common/config.js';

//清除签名
const EVENT_CLEAR_SIGNATURE = 'clearSignature';
const mixins = [element];

export default {
    components: {
        operateMenuDialog
    },
    name: 'SignatureElement',
    // #ifdef MP-ALIPAY
    mixins: mixins.map(item => ({ ...item, props: {} })),

    props: {
        ...mixins.reduce(
            (prev, curr) => ({ ...prev, ...(curr.props || {}) }),
            {}
        )
    },
    // #endif

    // #ifndef MP-ALIPAY
    mixins: mixins,
    // #endif

    data() {
        return {
            addIcon,
            signatures: [],
            editMenus: [{ id: 'delete', name: '删除' }],
            chooseId: null //选择的ID
        };
    },

    computed: {
        disSignatures() {
            let data = this.displayValue.split(',');
            return this.displayValue.split(',');
        }
    },

    mounted() {
        uni.$once(EVENT_CLEAR_SIGNATURE, () => {
            this.clearSignature();
        });
    },

    destroyed() {
        //如果没有触发清除签名，需要注销监听器
        uni.$off(EVENT_CLEAR_SIGNATURE);
    },

    watch: {
        signatures: function () {
            this.value = this.signatures.join(',');
        }
    },

    methods: {
        turnToSignature() {
            if (this.disable) {
                return;
            }
            let recordId = this.recordId;
            //决定是否允许弹窗，SFYXTC===ture为允许弹窗
            uni.navigateTo({
                url: `/pages/component/signature/signature-page?recordId=${recordId}&dialogType=${this.template.SFYXTC}`
            });

            uni.$once('signatureConfirm', signature => {
                this.signatures.push(signature);
                this.signatures = Array.from(new Set(this.signatures));
            });
            uni.$once('signatureTelValue', signatureTelValue => {
                uni.setStorageSync(
                    'signatureTelFormValue' + this.recordId,
                    signatureTelValue
                );
            });
        },

        previewSignature(signature, index) {
            if (!this.editable) {
                return;
            }
            this.chooseId = index;
            this.$refs.operateDialog.show();
        },

        onMenuClick(menu) {
            if (menu.id === 'delete') {
                this.signatures.splice(this.chooseId, 1);
                if (this.signatures.length == 0) {
                    this.signatures = [];
                }
            }
        },

        resolveDisplayValue(data) {
            this.$nextTick(() => {
                let signatures = data[this.field] || '';
                let picArr = signatures.split(',');
                this.signatures = [];
                picArr.forEach(item => {
                    if (item) {
                        this.signatures.push(item);
                    }
                });
            });
        },
        /**
         * 因为存储时，只存储了图片的base64字符串，base64展示协议并未存储，
         * 所以恢复显示时需要加上协议头
         * @param {Object} signature
         */
        restoreSignature(signature) {
            return `${DOWNLOAD_URL}?wdbh=${signature}`;
            //return `data:image/png;base64,${signature}`
        },

        clearSignature() {
            this.signatures = [];
            this.value = '';
        }
    }
};
</script>

<style scoped>
.signature-element {
    align-items: flex-start;
    padding-top: 20rpx;
    padding-bottom: 20rpx;
}

.signature-image-layout {
    flex-wrap: wrap;
    margin-top: 10rpx;
}

.signature-item {
    width: 160rpx;
    height: 72rpx;
}

.signature-add {
    width: 72rpx;
    height: 72rpx;
}
</style>
