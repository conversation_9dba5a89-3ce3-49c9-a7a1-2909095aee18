<!--
 * @Author: your name
 * @Date: 2021-03-01 18:03:37
 * @LastEditTime: 2021-03-23 15:23:12
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /SmartFormWeb/pages/form/tag-element.vue
-->
<template>
	<view 
		style="display: block;padding: 10rpx;" 
		class="flex-column-layout" 
		:id="'templateId_' + template.id">
		<uni-tag v-for="(item, index) in pOptions" 
			:key="index" 
			:text="item.text" 
			:type="item.type || 'default'" 
			style="display: inline-block; margin: 1px;"
		/>
	</view>
</template>

<script>
import optionElement from './option-element.js';
export default {
	name: 'TagElement',
	mixins: [optionElement],
	data() {
		return {
			
		}
	},
	
	computed: {
		current() {
			for (let i in this.options) {
				if (this.value === this.options[i].value) {
					return parseInt(i);
				}
			}
		},
	},
	
	methods: {
		resolveDisplayValue(data) {
			let values = data[this.field] || []
			this.pOptions.forEach(option => {
				option.type = values.indexOf(option.value) !== -1 ? 'warning' : 'default'
			})
		}
	}
};
</script>

<style scoped>

</style>
