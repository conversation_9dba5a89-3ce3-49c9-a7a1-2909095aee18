<template>
	<scroll-view 
		:scroll-y="true"
		:style="scrollStyle"
		:scroll-top="scrollTop"
		@scroll="scrollEvent">
		<view class="template-form">
			<template v-for="(element, index) in template.child">
				<view class="form-element-divider"
					v-if="index !== 0"
					:key="index" 
				/>
				<element-resolver 
					v-if="isElementGroup(element)"
					class="group-style"
					:round="round"
					:style="groupStyle"
					:marginTop="index === 0 ? 0 : 10"
					:editable="editable"
					:record-id="recordId"
					:template="element"
					:constraints="constraints"
					:form-data="formData" 
				/>

				<element-resolver 
					v-if="!isElementGroup(element)"
					class="element-style"
					:editable="editable"
					:record-id="recordId"
					:template="element"
					:constraints="constraints"
					:form-data="formData" 
				/>
			</template>
			<slot />
		</view>
	</scroll-view>
</template>

<script>
	import element from './element.js';
	import { ELEMENT_TYPE, EVENT_ELEMENT_MOUNTED, EVENT_FROM_VALUE_CHANGE } from './Form.js';

	import dayjs from 'dayjs'
	import styleUtil from '@/common/style.js';

	import elementResolver from './element-resolver.vue';
	import elementGroup from './element-group.vue';

	const mixins = [element];

	export default {
		name: 'TemplateForm',
		// #ifdef MP-ALIPAY
		mixins: mixins.map(item => ({ ...item,
			props: {}
		})),
		// #endif

		// #ifndef MP-ALIPAY
		mixins: mixins,
		// #endif
		components: {
			elementResolver,
			elementGroup
		},

		props: {
			// #ifdef MP-ALIPAY
			...mixins.reduce((prev, curr) => ({ ...prev,
				...(curr.props || {})
			}), {}),
			// #endif
			parentHeight: {
				type: Number,
				default: -1
			},
			
			round: {
				type: Boolean,
				default: true
			}
		},

		data() {
			return {
				scrollTopNew: 0,  // 中间缓存的值，点击校验的时候，重新加上
				scrollTop: 0,
				'z-index': 9,
				scrollViewHeight: -1,
				mountTimer: null,
			};
		},

		computed: {
			scrollStyle: function() {
				let style = {
					height: `${this.scrollViewHeight}px`
				}
				if(this.round) {
					style['border-radius'] = '5px'
				}
				return styleUtil.styleObjectToString(style);
			},
			
			groupStyle: function() {
				let style = {
					'margin-top': '10px'
				}
				if(this.round) {
					style['border-radius'] = '5px'
				}
				return styleUtil.wrapStyleObject(style)
			}
		},

		watch:{
			parentHeight(val){
				// #ifdef H5
				this.scrollViewHeight = this.parentHeight;
				// #endif
				
				// #ifndef H5
				this.scrollViewHeight = val;
				// #endif
			}
		},

		mounted() {
			//动态计算滚动区域高度
			// #ifdef H5
			let parentDom = styleUtil.getParentDom(this.$el);
			let parentAvailableHeight = styleUtil.getParentAvailableHeight(parentDom);
			this.scrollViewHeight = parentAvailableHeight;
			// #endif
			
			// #ifndef H5
			//不是H5应用需要外部指定父节点高度
			setTimeout(() => {
				if (this.parentHeight !== -1) {
					this.scrollViewHeight = this.parentHeight;
				}
			}, 300);
			// #endif

			// #ifdef MP
			//小程序需要通过vuex来存储表单数据
			this.resetFormData(this.formData);
			// #endif
			this.listenElementMounted()
		},
		
		destroyed() {
			uni.$off(EVENT_FROM_VALUE_CHANGE)
			uni.$off('onElementHide')
		},

		methods: {
			listenElementMounted() {
				let _self = this
				uni.$on(EVENT_ELEMENT_MOUNTED, (element) => {
					if(_self.mountTimer) {
						clearTimeout(_self.mountTimer)
					}
					//超过300ms未接收到元素挂载事件，即认为元素挂载完毕
					_self.mountTimer = setTimeout(() => {
						uni.$off(EVENT_ELEMENT_MOUNTED)
						_self.$emit('mounted')
						_self.listenFormValueChange()
					}, 600)
				})
			},
			
			listenFormValueChange() {
				if(this.editable) {
					let listener = (params) => {
						this.$emit('formValueChange', params)
					}
					uni.$on(EVENT_FROM_VALUE_CHANGE, listener)
				}
			},
			
			scrollEvent(event) {
				this.scrollTopNew = event.detail.scrollTop;
			},

			changeScrollTop(height, fixHeight) {
				this.scrollTop = this.scrollTopNew + height - fixHeight;
				// this.scrollTop = height;
			},

			elementStyle: function(index) {
				if (index === 0) {
					return {
						'margin-top': '0px'
					}
				}
				return null;
			},

			elementClass: function(element) {
				return this.isElementGroup(element) ? 'group-style' : 'element-style';
			}
		}
	}
</script>

<style scoped>
	.template-form {
		background-color: #f4f4f4;
	}

	.group-style {
		margin-top: 20rpx;
	}

	.group-style:first-child {
		margin-top: 0;
	}

	.element-style {
		width: calc(100% - 40rpx);
		padding-left: 20rpx;
		padding-right: 20rpx;
	}
</style>
