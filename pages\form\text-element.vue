<template>
	<element-layout 
		v-show="template.isshow == 1 || template.isshow == ''" 
		:id="'templateId_' + template.id" 
		:label="label"
		:required="required"
		@click="isFocus()">	

		<view v-if="template.DW" class="flex-row-layout text-element-value">
			<textarea
				class="form-value"
				style="flex: 1;"
				auto-height
				v-model="value"
				:focus="focusVal"
				:disabled="readonly || !editable"
				:maxlength ="template.maxlength"
				@input="changeVal"
				@blur="blurFns"
			/>
			<view v-if="template.DW" style="width: 20rpx;"/>
			 {{template.DW}}
		</view>
		
		<textarea 
			v-else
			class="form-value text-element-value"
			auto-height
			v-model="value"
			:focus="focusVal"
			:disabled="readonly || !editable"
			:maxlength ="template.maxlength"
			@input="changeVal"
			@blur="blurFns"
		/>
	</element-layout>
</template>

<script>
	
	import elementLayout from './element-layout.vue'
	
	import onform from '@/api/onsaveForm.js';
	import styleUtil from '@/common/style.js'
	import element from './element.js';
	const mixins = [element];
	
	export default {
		name: 'TextElement',
		components: {
			elementLayout
		},
		// #ifdef MP-ALIPAY
		mixins: mixins.map(item => ({ ...item, props: {} })),
		props: {
			...mixins.reduce((prev, curr) => ({ ...prev, ...(curr.props || {}) }), {})
		},
		// #endif
		
		// #ifndef MP-ALIPAY
		mixins: mixins,
		// #endif
		
		data() {
			return {
				focusVal: false,
				lineCount: 1
			}
		},
		
		computed: {
			/**
			 * 动态修改文本输入框的文本对齐方式，单行左对齐，否则左对齐
			 */
			inputStyle: function() {
				let style = {
					'text-align': this.lineCount > 1 ? 'left' : 'right'
				}
				return styleUtil.styleObjectToString(style)
			}   
		},
		
		methods: {
			// 点击是否获取input
			isFocus(){
				if(this.editable){
					this.focusVal = true;
				}
			},
			
			changeVal(event){
				let obj = onform.inputFunc(this.template);
				if(obj.isChange){
					setTimeout(()=>{
						let val = event.detail.value.replace(obj.reg,'');
						this.value = val;
					}, 100)
				}
			},
			
			// 鼠标移开后判断
			blurFns() {
				this.focusVal = false;
				
				let template = this.template;
				let value = this.value;
				let label = this.label;
				// blur 校验
				onform.blurFunc(template, value, label);
			},
			
			/**
			 * 监听文本域行数变化
			 * @param {Object} event
			 */
			onLineChange(event) {
				this.lineCount = event.detail.lineCount
			}
		}
	};
</script>

<style scoped>
	.text-element-value {
		width: 100%;
		margin-top: 10rpx;
	}
</style>
