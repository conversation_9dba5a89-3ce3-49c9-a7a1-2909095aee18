<template>
	<view>
		<view class="flex-row-layout"
		      :id="'templateId_' + template.id"
		      v-if="template.YSMC=='问' || template.YSMC=='答'">
			  <view>
					<span class="form-label-required" v-if="required">{{ requiredSymbol }}</span>
					<span class="form-label">
						{{label}}
					</span>
			  </view>
			<text 
				v-if="!editable"
				class="form-value text-element-value">
				{{displayValue}}
			</text>

			<textarea 
				v-if="editable"
				auto-height
				class="form-value text-element-value"
				v-model="value" 
				:style="textareaStyle"
				:disabled="disable"
				@blur="blurFns"
				@input="changeVal"
			/>

			{{template.DW}}
			 <view v-if="isPattern && editable" @click="templateLanguageClick" class="power-button power-button-primary textarea-pattern-button">模板语句</view>
		</view>
		
		<view class="form-textarea-element" :id="'templateId_' + template.id" v-if="template.YSMC!='问' && template.YSMC!='答'">
			<view>
				<span class="form-label-required" v-if="required">{{ requiredSymbol }}</span>
				<span class="form-label textarea-element-label">{{ label }}</span>
			</view>
			<!-- <view @click="getFocus"> -->
			<textarea 
				class="form-value textarea-element-value" 
				v-model="value"
				auto-height 
				:style="textareaStyle"
				:maxlength="maxLength" 
				:disabled="readonly || !editable" 
				@blur="blurFns" 
				@input="changeVal"
			/>
			<!-- </view> -->
			<view v-if="isPattern && editable"
			      @click="templateLanguageClick"
			      class="power-button power-button-primary textarea-pattern-button">模板语句</view>
		</view>


	</view>


</template>

<script>
	import { ELEMENT_TYPE } from '@/pages/form/Form.js';
	import element from './element.js';
	import onform from '@/api/onsaveForm.js';
	import styleUtil from '@/common/style.js'

	const mixins = [element];

	export default {
		name: 'TextareaElement',
		// #ifdef MP-ALIPAY
		mixins: mixins.map(item => ({ ...item,
			props: {}
		})),
		props: {
			...mixins.reduce((prev, curr) => ({ ...prev,
				...(curr.props || {})
			}), {})
		},
		// #endif

		// #ifndef MP-ALIPAY
		mixins: mixins,
		// #endif


		data(){
			return{
				focusType:false
			}
		},

		computed: {
			maxLength: function() {
				let configLength = this.template.maxlength || this.template.ZDCD || 8000;
				return parseInt(configLength);
			},
			
			textareaStyle: function() {
				if(this.readonly || !this.editable) {
					return ''
				}
				let style = {
					'min-height': '120px'
				}
				if(this.isPattern) {
					style['margin-bottom'] = '26px'
				}
				return styleUtil.wrapStyleObject(style)
			}
		},

		mounted() {
			uni.$on('chooseItem', res => {
				if (this.isPattern) {
					this.value += res.text;
				}
			});
		},

		methods: {
			templateLanguageClick() {
				uni.navigateTo({
					url: '/pages/form/textarea-template-language?selectType=see'
				});
			},
			
			changeVal(event){
				let obj = onform.inputFunc(this.template);
				if(obj.isChange){
					setTimeout(()=>{
						let val = event.detail.value.replace(obj.reg,'');
						this.value = val;
					}, 100)
				}
			},
			
			blurFns() {
				let template = this.template;
				let value = this.value;
				let label = this.label;
				
				
				// blur 校验
				onform.blurFunc(template, value, label);
			},

			getFocus(){
				this.focusType = true
			}
		}
	};
</script>

<style scoped>
	.form-textarea-element {
		display: flex;
		flex-flow: column;
		justify-content: flex-start;
		align-items: flex-start;
		min-height: 144rpx;
		padding-top: 8px;
		padding-bottom: 8px;
		position: relative;
		width: 100%;
	}

	.textarea-element-label {
		width: 100%;
	}

	.textarea-element-value {
		flex: 1;
		width: 100%;
		min-height: 240rpx;
		margin-top: 24rpx;
	}

	.textarea-element-value-text {
		width: 100%;
		margin-top: 10rpx;
		/* margin-bottom: 45rpx; */
	}

	.textarea-pattern-button {
		position: absolute;
		top: 2px;
		right: 10px;
		padding-left: 20rpx;
		padding-right: 20rpx;
	}

	.text-element-value {
		flex: 1;
		margin-left: 20rpx;
		text-align: left;
	}
</style>
