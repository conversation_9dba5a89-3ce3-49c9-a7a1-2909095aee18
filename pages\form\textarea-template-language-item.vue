<template>
	<Page :padding="false">
		<template v-slot:bar>
			<NaviBar title="常用模板"
			         textRight="保存"
			         @optionClick="createNewRecordTask" />
		</template>
		<scroll-view scroll-y="true"
		             style="height: calc(100vh - 120rpx);"
		             class="scroll-region">
			<view class="department-leaf-list"
			      v-for="item in list"
			      :key="item.id"
			      @click="changeCheck(item)">
				<template>
					<view class="flex-row-layout department-item">
						<checkbox style="zoom: 68%;"
						          :checked="item.checked" />
						<text class="department-item-name"
						      v-show="selectType == 'see'">{{ item.MBYJ }}</text>
						<text class="department-item-name"
						      v-show="selectType == 'answer'">
							问：{{ item.WT }}
							<br />
							<br />
							答:{{ item.HD }}
						</text>
					</view>
				</template>
			</view>
			<noData v-if="list.length == 0"></noData>
		</scroll-view>
	</Page>
</template>

<script>
	import Page from '@/pages/component/Page.vue';
	import noData from '@/components/no-data.vue';
	import NaviBar from '@/pages/component/NaviBar.vue';
	import {
		getRecordLanguage,
		postIntelligentList
	} from '@/api/record.js';
	export default {
		components: {
			Page,
			NaviBar,
			noData
		},
		data() {
			return {
				itemData:{},
				recordId: '',
				templateId: '',
				selectType: '',
				type: '',
				pageNum: 1,
				list: []
			};
		},
		onLoad(options) {
			this.itemData = uni.getStorageSync('record-template-data')
			this.recordId = options.recordId;
			this.templateId = options.templateId;
			this.selectType = options.selectType;
			this.getData();
		},
		methods: {
			getData() {
				postIntelligentList({
					type: this.selectType === 'answer' ? 'xwblList': 'kcblList',
					WFXWXH: this.itemData.XH,
					pageSize: 100,
					pageNum: this.pageNum
				}).then(res => {
					let data = res.data_json.list
					data.forEach(item => {
						item.checked = true;
					});
					this.list = data;
					// this.total = res.data_json.total;
				});


				// getRecordLanguage({
				// 	service: 'QUERY_DYNAMICFORM_DATA',
				// 	recordId: this.recordId,
				// 	mbbh: this.templateId
				// }).then(res => {
				// 	let data = res.data.T_COMN_MBYJ_BZYJ;
				// 	data.forEach(item => {
				// 		item.checked = true;
				// 	});
				// 	this.list = data;
				// });
			},
			changeTask(item) {
				this.taskId = item.DM;
				this.getData();
			},
			createNewRecordTask() {
				let selectArr = [];
				let text = '';
				this.list.forEach(item => {
					if (item.checked) {
						selectArr.push(item);
						text += item.MBYJ + '\r\n';
					}
				});
				// 勘察的时候返回
				if (this.selectType == 'see') {
					// 判断选中的
					uni.$emit('chooseItem', {
						text: text
					});
				} else {
					uni.$emit('chooseDynaItem', {
						list: selectArr
					});
				}

				uni.navigateBack({
					delta: 2
				});
			},
			changeCheck(item) {
				item.checked = !item.checked;
			}
		}
	};
</script>

<style>
	.department-leaf-list {
		width: 100%;
		margin-top: 10px;
		background-color: #fff;
	}

	.department-item {
		padding: 10px 10px;
		width: calc(100% - 20px);
		height: auto;
	}

	.department-item-name {
		margin-left: 10px;
		font-size: 16px;
		color: #333;
	}

	.search-region {
		display: flex;
	}

	.search-region .left {
		width: 180rpx;
		margin-right: 20rpx;
		height: calc(100vh - 120rpx);
		background: white;
	}

	.search-region .left .left-li {
		text-align: center;
		padding: 20rpx 0rpx;
	}

	.search-region .left .active {
		background: rgb(244, 244, 244);
	}

	.serach-region .scroll-region {
		flex: 1;
		height: calc(100vh - 120rpx);
	}
</style>
