<template>
	<Page :padding="false">
		<template v-slot:bar>
			<NaviBar title="常用模板"
			         textRight="" />
		</template>

		<div class="search-region"
		     style="height: calc(100vh - 120rpx);">
			<div class="left">
				<ul>
					<li  v-for="(item,index)  in selectArr" class="left-li" :class="{ active: chooseindex === index }"  @click="chooseDm(index)" :key="item.is">
						{{item}}
					</li>
					<!-- <li class="left-li"
					    :class="{ active: item.DM == taskId }"
					    v-for="item in taskList"
					    :key="item.is"
					    @click="changeTask(item)">{{ item.DMNR }}</li> -->
				</ul>
			</div>
			<scroll-view scroll-y="true"
			             style="height: calc(100vh - 120rpx);"
			             class="scroll-region">
				<view class="department-leaf-list"
				      v-for="item in list"
				      :key="item.id"
				      @click="goItem(item)">
					<template>
						<view class="flex-row-layout department-item">
							<!-- <checkbox
								style="zoom: 68%;"
								:checked="item.checked"
							/> -->
							<text class="department-item-name">{{ item.WFXWMC }}</text>
						</view>
					</template>
				</view>
				<noData v-if="list.length == 0"></noData>
			</scroll-view>
		</div>
	</Page>
</template>

<script>
	import noData from '@/components/no-data.vue';
	import Page from '@/pages/component/Page.vue';
	import NaviBar from '@/pages/component/NaviBar.vue';
	import {
		getRecordLanguage,
		getRecordType,
		postIntelligentList
	} from '@/api/record.js';
	export default {
		components: {
			Page,
			NaviBar,
			noData
		},
		data() {
			return {
				chooseindex:1,
				selectArr: ['', '大气', '水', '土壤', '噪声', '海洋', '核辐射', '固体废物', '建设项目', '环评', '其他'],
				itemData:{},
				selectType: 'see', // 问答answer，勘察see
				templateId: '',
				taskList: [],
				taskId: '',
				type: '1',
				list: [],
				pageNum: 1,
			};
		},
		onLoad(options) {
			this.itemData = uni.getStorageSync('record-from-data')
			this.selectType = options.selectType;
		},
		mounted() {
			
			// getRecordType().then(res => {
			// 	this.taskList = res.data_array;
			// 	this.taskList.unshift({
			// 		DM: '',
			// 		DMNR: '全部'
			// 	});

				this.getData();
			// });
		},
		methods: {
			getData() {
				this.list = []
				postIntelligentList({
					type: 'wfxwList',
					WFXWLB: this.chooseindex.toString(),
					pageSize: 100,
					pageNum: this.pageNum
				}).then(res => {
					this.list.push(...res.data_json.list);
					this.total = res.data_json.total;
				});


				// getRecordLanguage({
				// 	service: 'QUERY_MBYJ',
				// 	YWLX: this.taskId
				// }).then(res => {
				// 	this.templateId = res.data_json.MBBH.MBBH;
				// 	let newData = [];
				// 	let data = res.data_json.MBYJ_JBXX;
				// 	data.forEach(item => {
				// 		if (item.MBLX == 'KCMB' && this.selectType == 'see') {
				// 			newData.push(item);
				// 		}

				// 		if (item.MBLX == 'XWMB' && this.selectType == 'answer') {
				// 			newData.push(item);
				// 		}
				// 	});
				// 	this.list = newData;
				// });
			},
			chooseDm(index) {
				this.chooseindex = index
				// this.taskId = item.DM;
				this.getData();
			},
			createNewRecordTask() {},
			goItem(item) {
				let templateId = this.templateId;
				let selectType = this.selectType;
				uni.setStorageSync('record-template-data', item)
				uni.navigateTo({
					url: `/pages/form/textarea-template-language-item?templateId=${templateId}&recordId=${item.XH}&selectType=${this.selectType}`
				});

				//item.checked = !item.checked;
			}
		}
	};
</script>

<style>
	.department-leaf-list {
		width: 100%;
		margin-top: 10px;
		background-color: #fff;
	}

	.department-item {
		padding: 10px 10px;
		width: calc(100% - 20px);
		height: auto;
	}

	.department-item-name {
		margin-left: 10px;
		font-size: 16px;
		color: #333;
	}

	.search-region {
		display: flex;
	}

	.search-region .left {
		width: 180rpx;
		margin-right: 20rpx;
		height: calc(100vh - 120rpx);
		background: white;
	}

	.search-region .left .left-li {
		text-align: center;
		padding: 20rpx 0rpx;
	}

	.search-region .left .active {
		background: rgb(244, 244, 244);
	}

	.serach-region .scroll-region {
		flex: 1;
		height: calc(100vh - 120rpx);
	}
</style>
