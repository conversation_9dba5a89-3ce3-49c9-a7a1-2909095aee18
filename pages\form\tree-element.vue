<template>
	<label-element :id="'templateId_' + template.id"
	               :label="label"
	               :required="required"
	               @click.native="openTree">
		<!-- #ifdef MP-ALIPAY -->
		<text style="width: 100%; text-align: end; margin-right: 5px;"
		      class="form-value"
		      @click="openTree">
			{{displayValue || '请选择'}}
		</text>
		<!-- #endif -->
		<!-- #ifndef MP-ALIPAY -->
		<text class="form-value">{{displayValue || '请选择'}}</text>
		<!-- #endif -->
		<image v-if="editable"
		       class="form-select-action-indicator"
		       mode="aspectFit"
		       :src="naviNextIcon" />
	</label-element>
</template>

<script>
	import labelElement from './label-element.vue';
	import {
		postDynamicQuery
	} from '@/api/book.js'
	import naviNextIcon from '@/static/img/navi_next_icon.png';

	import element from './element.js';

	const mixins = [element];

	export default {
		name: 'TreeElement',
		// #ifdef MP-ALIPAY
		mixins: mixins.map(item => ({ ...item,
			props: {}
		})),
		props: {
			...mixins.reduce((prev, curr) => ({ ...prev,
				...(curr.props || {})
			}), {})
		},
		// #endif

		// #ifndef MP-ALIPAY
		mixins: mixins,
		// #endif
		components: {
			labelElement
		},

		data() {
			return {
				naviNextIcon,
				separator: ','
			}
		},

		mounted() {
			if (this.editable) {} else {
				if (this.value) {}
			}
		},

		methods: {
			openTree() {
				if (this.readonly || !this.editable) {
					return
				}

				uni.$once('onTreeSelected', (res) => {
					let displayValueArr = [];
					let valueArr = [];
					if(res) {
						if (res.length) {
							res.forEach(item => {
								displayValueArr.push(item.treeName);
								valueArr.push(item.treeId)
							})
						} else {
							displayValueArr.push(res.treeName);
							valueArr.push(res.treeId)
						}
					}
					this.displayValue = displayValueArr.join(this.separator);
					this.value = valueArr.join(this.separator);
				});

				let multi = this.template.SFDX;
				let selected = this.value;
				// todo 打开树形节点
				uni.navigateTo({
					url: `/pages/component/tree/tree?multi=${multi}&selected=${selected}&id=${this.template.dynamicId}`
				})

			},

			resolveDisplayValue(data) {
				if (this.value) {
					let valueArr = this.value.split(this.separator);
					let displayValueArr = [];
					postDynamicQuery({
						service: 'DYNAMIC_QUERY_TREE',
						xh: this.template.dynamicId
					}).then((res) => {
						res.datas.forEach(item => {
							if (valueArr.indexOf(item.treeId) !== -1) {
								displayValueArr.push(item.JGSXMC);
							}
						})
						this.displayValue = displayValueArr.join(this.separator);
					})

				}
			}
		}
	}
</script>

<style scoped>

</style>
