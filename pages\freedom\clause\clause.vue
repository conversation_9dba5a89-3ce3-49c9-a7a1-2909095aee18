<template>
  <body style="background-color: #f7f7f7">
    <header class="header">
      <i class="ic-back" @click="goBack()"></i>
      <h1 class="title">违法条款选择</h1>
    </header>
    <section class="main">
      <div class="inner">
        <ul class="zy-list2">
          <li v-for="(item , index) in data " :key="index" @click="itemClick(item)">
            <label class="zy-checkbox zy-checkbox2">
              <input type="checkbox" checked />
              <i :class="{on : item.on }"></i>
              <div class="wenzi">
                <h3>{{ item.WFTK }}</h3>
                <p>
                  {{ item.WFTKNR }}
                </p>
              </div>
            </label>
          </li>
        </ul>
        <div class="gap"></div>
        <div class="zy-bot-btn"  @click="qdClick()"> 
          <p>确定</p>
        </div>
      </div>
    </section>
  </body>
</template>

<script>
import { getData } from "@/api/freedom.js";
export default {
  data() {
    return {
      XH: "",
	  data:[],
	  curObj:null
    };
  },
  onLoad(option) {
    // 获取上个页面传过来的参数  本场考试的基本信息
    this.XH = option.wfxw;
  },
  mounted(){
	  this.getWftk()
  },
  methods: {
	  // 确定
	  qdClick(){
		  // 回到上一个页面 并且修改上一个页面的参数,并且不刷新页面
      var pages = getCurrentPages();
      var prevPage = pages[pages.length - 2];
      prevPage.$vm.wftk = this.curObj;
      prevPage.$vm.sgtext = '';
      prevPage.$vm.getWfqj()
      uni.navigateBack({
        //返回
        delta: 1,
      });
	  },
	  // 获取违法条款
	  getWftk() {
      let pam = {};
      pam.method = "getWftk";
      pam.param = {};
      pam.param.wflx = this.XH;
      getData(pam).then((res) => {
        this.data = res.data_json;
        this.data.forEach((element) => {
          this.$set(element, "on", false);
        });
      });
    },
	itemClick(item){
		this.data.forEach((element) => {
        element.on = false;
      });
      item.on = true;
      this.curObj = item
	},
	// 返回
	goBack(){
		uni.navigateBack({
        //返回
        delta: 1,
      });
	}
  },
};
</script>

<style scoped>
.on {
  background: url(@/static/freedom/images/zy-checked.png);
  background-size: 100%;
}
</style>
