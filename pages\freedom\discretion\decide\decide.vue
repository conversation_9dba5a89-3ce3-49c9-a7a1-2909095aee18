<template>
  <body style="background-color: #f7f7f7">
    <header class="header">
      <i class="ic-back" @click="goBack()"></i>
      <h1 class="title">处罚判定标准</h1>
    </header>
    <section class="main">
      <div class="inner">
        <ul class="zy-list2">
           <li v-for="( item , index ) in data" :key="index" @click="itemClick( item )">
                    <label class="zy-checkbox zy-checkbox2">
                        <input type="checkbox" checked>
                        <i :class="{ on:item.on }"></i>
                        <div class="wenzi">
                            <p>{{ item.PDBZ }}</p>
                        </div>
                </label>
                </li>
        </ul>
        <div class="gap"></div>
        <div class="zy-bot-btn" @click="qdClick()">
          <p>确定</p>
        </div>
      </div>
    </section>
  </body>
</template>

<script>
import { getData } from "@/api/freedom.js";
export default {
  data() {
    return {
		data:[],
		XH:'',
    curObj:null, //当前选择
	};
  },
  mounted(){
	  this.getPdbz()
  },
   onLoad(option) {
    // 获取上个页面传过来的参数  本场考试的基本信息
    this.XH = option.clys;
  },
  methods: {
    // 确定
    qdClick(){
      var pages = getCurrentPages();
      var prevPage = pages[pages.length - 2];
      for(let i =0;i<prevPage.data.length;i++){
        if(this.XH == prevPage.data[i].XH){
          prevPage.data[i].pdbz = this.curObj
        }
      }
      uni.navigateBack({
        //返回
        delta: 1,
      });
    },
    // 获取判定标准
    getPdbz() {
      let pam = {};
      pam.method = "getPdbz";
      pam.param = {};
      pam.param.clys = this.XH;
      getData(pam).then((res) => {
        this.data = res.data_json;
        for (let i = 0; i < this.data.length; i++) {
          this.$set(this.data[i], "on", false);
        }
      });
    },
	itemClick(item){
    if(item.on == true){
      item.on = false
      this.curObj = null
    }else{
      this.data.forEach((element) => {
        element.on = false;
      });
      item.on = true;
      this.curObj = item
    }
			
	},
	// 返回上一层
	goBack(){
		uni.navigateBack({
        //返回
        delta: 1,
      });
	}
  },
};
</script>

<style scoped>
.on {
  background: url(@/static/freedom/images/zy-checked.png);
  background-size: 100%;
}
</style>
