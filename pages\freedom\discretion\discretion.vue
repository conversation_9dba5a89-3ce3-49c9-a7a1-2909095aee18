<template>
  <body style="background-color: #f7f7f7">
    <header class="header">
      <i class="ic-back" @click="goBack()"></i>
      <h1 class="title">通用裁量</h1>
      <!-- <span class="hd-span">重置</span> -->
    </header>
    <section class="main" style="width:100%">
      <div class="inner">
        <ul class="zy-list1">
          <li>
            <p class="zy-til1 til2">处罚裁量因素</p>
            <div class="label-group">
              <label
                class="zy-checkbox"
                v-for="(item, index) in data"
                :key="index"
                @click="cfclClick(item)"
                ><input type="checkbox" /><i :class="{ on: item.on }"></i
                ><span>{{ item.CLYS }}</span></label
              >
            </div>
          </li>
          <li>
            <p class="zy-til1 til3">处罚判定标准</p>
            <div class="label-group">
              <label
                class="zy-checkbox"
                v-for="(item, index) in pdbzData"
                :key="index"
                @click="pdbzClick(item)"
                ><input type="checkbox" checked /><i
                  :class="{ on: item.on }"
                ></i
                ><span>{{ item.PDBZ }} </span></label
              >
            </div>
          </li>
          <li>
            <p class="zy-til1 til4">已选择（ {{ curData.length }} ）</p>
            <div class="has-selected">
              <p v-for="(item, index) in curData"
                :key="index">{{ item.PDBZ }}</p>
            </div>
          </li>
        </ul>
        <div class="gap"></div>
        <div class="zy-bot-btn" @click="qdClick()">
          <p>确认</p>
        </div>
      </div>
    </section>
  </body>
</template>

<script>
import { getData } from "@/api/freedom.js";
export default {
  data() {
    return {
      data: [], // 处罚裁量因素数据
      pdbzData: [], //判定标准数据
      curData: [], // 以选择的数据
      XH: "", // 违法任务的序号
     
    };
  },
  mounted() {
    this.getClys();
  },
  onLoad(option) {
    // 获取上个页面传过来的参数  本场考试的基本信息
    this.XH = option.wfxw;
  },
  methods: {
    // 确定
    qdClick() {
      var pages = getCurrentPages();
      var prevPage = pages[pages.length - 2];
      if( this.curData.length == 0 ){
          prevPage.$vm.cfclAndPdbz = null
      }else{
        prevPage.$vm.cfclAndPdbz = this.curData;
      }
        
 

      uni.navigateBack({
        //返回
        delta: 1,
      });
    },
    // 获取处罚裁量
    getClys() {
      let pam = {};
      pam.method = "getClys";
      pam.param = {};
      pam.param.wflx = this.XH;
      getData(pam).then((res) => {
        this.data = res.data_json;
        for (let i = 0; i < this.data.length; i++) {
          if (i == 0) {
            this.$set(this.data[i], "on", true);
            this.getPdbz(this.data[i].XH);
          } else {
            this.$set(this.data[i], "on", false);
          }
        }
      });
    },
    // 获取判定标准
    getPdbz(XH) {
      let pam = {};
      pam.method = "getPdbz";
      pam.param = {};
      pam.param.clys = XH;
      getData(pam).then((res) => {
        this.pdbzData = res.data_json;
        for (let i = 0; i < this.pdbzData.length; i++) {
          this.$set(this.pdbzData[i], "on", false);
        }
      });
    },
    // 处罚裁量行点击事件
    cfclClick(item) {
      this.data.forEach((element) => {
        element.on = false;
      });
      item.on = true;
      this.getPdbz(item.XH);
    },
    // 判定标准行点击事件
    pdbzClick(item){
      // 判断是勾选 还是 取消勾选
      // 如果是取消勾选
      if(item.on == true){
        item.on = false
        
        for(let i =0;i<this.curData.length;i++){
         if(this.curData[i].XH == item.XH){
           this.curData.splice(i,1)
         }
       }


      // 如果是勾选
      }else {
        // 1. 完成勾选功能
        this.pdbzData.forEach((element) => {
        element.on = false;
      });
         item.on = true
  
       for(let i =0;i<this.curData.length;i++){
         if(this.curData[i].CLYSXH == item.CLYSXH){
           this.curData.splice(i,1)
         }
       }
    
      this.curData.push( item )
      }
     
    },
    // 返回上一层
    goBack() {
      uni.navigateBack({
        //返回
        delta: 1,
      });
    },
  },
};
</script>

<style scoped>
.on {
  background: url(@/static/freedom/images/zy-checked.png);
  background-size: 100%;
}
</style>
