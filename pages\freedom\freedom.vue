<template>
  <body style="background-color: #f7f7f7">
    <header class="header">
      <i class="ic-back" @click="goClick('/pages/main/portal')"></i>
      <h1 class="title">自由裁量</h1>
    </header>
    <section class="main">
      <div class="inner">
        <ul class="zy-list5">
          <li class="li-blue" @click="goClick('/pages/freedom/specialProject/specialProject')">
            <div class="top">
              <p>专项处罚裁量</p>
            </div>
            <div class="zy-til3">
              <p>金额计算特殊情形</p>
            </div>
            <p class="zy-txt3">
              当只有一个裁量因素时，A=B=裁量因素裁量等级，n=1;当有多个裁量因素时，A=裁量因素最高裁量等级，Bi=其余裁量因素裁量等级。若裁量等级最高的裁量因素同时有多个时，任选其中一个裁量因素作为A，其余的按Bi进行计算。法定处罚金额下限未明确的，N=0。
            </p>
            <div class="gap"></div>
            <div class="zy-til3">
              <p>处罚金额</p>
            </div>
            <p class="zy-txt3">
              生态环境主管部门裁定处罚金额时，首先确定裁量表中各项裁量因素的裁量等级，再通过计算公式确定处罚金额。罚款金额取整到“元”，不足1元的予以舍去。
            </p>
            <div class="gap"></div>
            <div class="gap"></div>
          </li>
          <li class="li-green" @click="goClick('/pages/freedom/universal/universal')">
            <div class="top">
              <p>通用处罚裁量</p>
            </div>
            <div class="zy-til3">
              <p>推荐计算公式</p>
            </div>
            <div class="gap"></div>
            <div class="zy-tu">
              <image  style="height: 73px;" src="@/static/freedom/images/gongshi.png" alt="" />
            </div>
            <div class="gongshi">
              <div class="zy-line">
                <p>X:处罚金额;</p>
                <p>A:首要裁量因素裁量等级;</p>
              </div>
              <div class="zy-line">
                <p>M:法定处罚金额上限;</p>
                <p>B:其余裁量因素裁量等级;</p>
              </div>
              <div class="zy-line">
                <p>N:法定处罚金额下限;</p>
                <p>n:其余裁量因素个数。</p>
              </div>
            </div>
            <div class="gap"></div>
            <div class="gap"></div>
          </li>
        </ul>
      </div>
    </section>
  </body>
</template>

<script>
export default {
  data() {
    return {};
  },
  methods: {
	  // 跳转
    goClick(url) {
      uni.navigateTo({
        url: url,
      });
    },
  },
};
</script>

<style>
</style>
