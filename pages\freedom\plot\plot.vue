<template>
  <body style="background-color: #f7f7f7">
    <header class="header">
      <i class="ic-back" @click="goClick()"></i>
      <h1 class="title">违法情节选择</h1>
    </header>
    <section class="main" style="width:100%">
      <div class="inner">
        <ul class="zy-list2">
          <li
            v-for="(item, index) in data"
            :key="index"
            @click="itemClick(item)"
          >
            <label class="zy-checkbox zy-checkbox2">
              <input type="checkbox" checked />
              <i :class="{ on: item.on }"></i>
              <div class="wenzi">
                <p>
                  {{ item.WFQJ }}
                </p>
              </div>
            </label>
          </li>
        </ul>
        <div class="gap"></div>
        <div class="zy-bot-btn" @click="qdClick()">
          <p>确定</p>
        </div>
      </div>
    </section>
  </body>
</template>

<script>
export default {
  data() {
    return {
      data: [],
      curObj: null,
    };
  },
  onLoad(option) {
    // 获取上个页面传过来的参数  本场考试的基本信息
    this.data = JSON.parse(decodeURIComponent(option.data));
    this.data.forEach((element) => {
      this.$set(element, "on", false);
    });
  },
  methods: {
	  // 确定
	  qdClick(){
		  // 回到上一个页面 并且修改上一个页面的参数,并且不刷新页面
      var pages = getCurrentPages();
      var prevPage = pages[pages.length - 2];
	  prevPage.$vm.wfjq = this.curObj
	  if(prevPage.$vm.wfjq.SFABCF == '0'){
           prevPage.$vm.min = prevPage.$vm.wfjq.CFXX * 10000
           prevPage.$vm.max = prevPage.$vm.wfjq.CFSX * 10000
       }else {
           prevPage.$vm.min = 0
           prevPage.$vm.max = 0
       }
	  uni.navigateBack({
        //返回
        delta: 1,
      });
	  },
    itemClick(item) {
      this.data.forEach((element) => {
        element.on = false;
      });
      item.on = true;
      this.curObj = item;
    },
	goClick(){
		uni.navigateBack({
        //返回
        delta: 1,
      });
	}
  },
};
</script>

<style scoped>
.on {
  background: url(@/static/freedom/images/zy-checked.png);
  background-size: 100%;
}
</style>
