<template>
    <body style="background-color: #f7f7f7">
        <header class="header">
            <i class="ic-back" @click="goClick('/pages/freedom/freedom')"></i>
            <h1 class="title">专项裁量</h1>
        </header>
        <section class="main">
            <div class="inner">
                <ul class="zy-list3">
                    <li>
                        <div class="zy-line">
                            <p class="zy-til1 til5">专项裁量分类</p>
                        </div>

                        <ul class="zy-list4">
                            <li
                                v-for="(item, index) in zxclData"
                                :key="index"
                                style="padding: 5px"
                                :class="{ cur: item.cur }"
                                @click="zxclClick(item)"
                            >
                                <p>{{ item.CLLX }}</p>
                            </li>
                        </ul>
                    </li>
                    <li>
                        <div class="zy-line" @click="goClickwfxw()">
                            <p class="zy-til1 til6">违法行为</p>
                            <span class="zy-more">请选择</span>
                        </div>
                        <div class="zy-selected" v-if="wfxw != null">
                            {{ wfxw.WFXW }}
                        </div>
                    </li>
                    <li>
                        <div class="zy-line" @click="goClickwftk()">
                            <p class="zy-til1 til7">违法条款</p>
                            <span class="zy-more">请选择</span>
                        </div>
                        <div class="zy-selected" v-if="wftk != null">
                            <span>{{ wftk.WFTK }}</span
                            >{{ wftk.WFTKNR }}
                        </div>
                    </li>
                    <li>
                        <div class="zy-line">
                            <p class="zy-til1 til2">处罚依据</p>
                        </div>
                        <div class="zy-selected" v-if="wftk != null">
                            <span>{{ wftk.CFYJ }}</span>
                        </div>
                    </li>
                    <li v-if="wfqjData.length > 1">
                        <div class="zy-line" @click="goClickwfqj()">
                            <p class="zy-til1 til7">违法情节</p>
                            <span class="zy-more">请选择</span>
                        </div>
                        <div class="zy-selected">
                            <i>{{ wfjq.WFQJ }}</i>
                        </div>
                    </li>
                    <li>
                        <div class="zy-line" @click="goClickCfcl()">
                            <p class="zy-til1 til3">处罚裁量因素及判定标准</p>
                            <span class="zy-more">请选择</span>
                        </div>
                        <div class="zy-selected" v-if="cfclAndPdbz != null">
                            <i
                                v-for="(item, index) in cfclAndPdbz"
                                :key="index"
                            >
                                <i>
                                    {{ item.PDBZ }}
                                    <br />
                                </i>
                            </i>
                        </div>
                    </li>
                    <li>
                        <div class="zy-line">
                            <p class="zy-til1 til1">罚违金额</p>
                        </div>
                        <div class="zy-fakuan">
                            <div class="line1">
                                <p>{{ min.toFixed(2) }} 元（最小值)</p>
                                <p>{{ max.toFixed(2) }} 元（最大值）</p>
                            </div>
                            <div class="line2" v-if="wfjq.SFABCF == '1'">
                                <input
                                    type="number"
                                    placeholder="人工输入金额"
                                    v-model="sgtext"
                                    @input="textInput()"
                                />
                                <span>元</span>
                            </div>
                        </div>
                    </li>

                    <li>
                        <div class="zy-line" @click="goClickElement()">
                            <p class="zy-til1 til3">修正因素</p>
                            <span class="zy-more">请选择</span>
                        </div>
                        <div class="zy-selected" v-if="xzysAndPdbz != null">
                            <i
                                v-for="(item, index) in xzysAndPdbz"
                                :key="index"
                            >
                                <i>
                                    {{ item.CLYZ }}
                                    <br />
                                </i>
                            </i>
                        </div>
                    </li>
                </ul>
                <div class="gap"></div>
                <div class="gap"></div>

                <div class="zy-bot-btn" @click="calculateClick()">
                    <p>计算</p>
                </div>
            </div>
        </section>
        <div class="mask" style="display: block" v-show="dialog"></div>
        <div class="cailiang-alert" v-show="dialog">
            <h3>裁量结果</h3>
            <div class="gap"></div>
            <div class="gap"></div>
            <p class="zy-til2">计算罚款（元）</p>
            <div class="gap"></div>
            <p class="zy-txt1">{{ ultimately.je }}</p>
            <div class="gap"></div>
            <p class="zy-til2">裁量说明</p>
            <div class="gap"></div>
            <p class="zy-txt2">
                {{ ultimately.jg }}
            </p>
            <div class="gap"></div>
            <div class="gap"></div>
            <!-- <div>
            <button class="zy-btn1">分享</button>
        </div> -->

            <i class="zy-close" @click="dialog = false"></i>
        </div>
    </body>
</template>

<script>
import { getData, getElementData } from '@/api/freedom.js';
export default {
    data() {
        return {
            dialog: false, // 弹框
            zxclData: [], // 专项裁量分类数据
            wfxw: null, //违法行为选择
            wftk: null, //违法条款选择
            cfclAndPdbz: null, // 处罚裁量及判定标准
            xzysAndPdbz: null, //修正的判定标准
            wfqjData: [], // 违法金钱数组，如果只有一条则隐藏，多条才展示
            wfjq: {}, // 当前选择的违法金钱
            min: 0, //最小值
            max: 0, //最大值
            sgtext: '', //手工输入值
            ultimately: {
                je: 0,
                jg: ''
            } //最终结果
        };
    },
    mounted() {
        uni.removeStorage({
            key: 'CLXH',
            success: function (res) {}
        });
        this.getCllx();
    },

    methods: {
        // 客户手工输入 计算最大值最小值
        textInput() {
            this.min = Number(this.sgtext) * this.wfjq.CFXX;
            this.max = Number(this.sgtext) * this.wfjq.CFSX;
        },
        //  根据违法条款获取 违法金钱
        getWfqj() {
            let pam = {};
            pam.method = 'getWfqj';
            pam.param = {};
            pam.param.wftk = this.wftk.XH;
            getData(pam).then(res => {
                this.wfqjData = res.data_json;
                this.wfjq = this.wfqjData[0];
                if (this.wfjq.SFABCF == '0') {
                    this.min = this.wfjq.CFXX * 10000;
                    this.max = this.wfjq.CFSX * 10000;
                } else {
                    this.min = 0;
                    this.max = 0;
                }
            });
        },
        // 转向裁量点击事件
        zxclClick(item) {
            if (item.cur == false) {
                this.wfxw = null;
                this.wftk = null;
                this.cfclAndPdbz = null;
                this.wfqjData = [];
                this.wfjq = {};
                this.min = 0;
                this.max = 0;
            }
            this.zxclData.forEach(element => {
                element.cur = false;
            });
            item.cur = true;
        },
        // 获取 专项裁量分类
        getCllx() {
            let pam = {};
            pam.method = 'getCllx';
            pam.param = {};
            getData(pam).then(res => {
                this.zxclData = res.data_json;
                for (let i = 0; i < this.zxclData.length; i++) {
                    this.$set(this.zxclData[i], 'cur', false);
                    if (i == 0) {
                        this.zxclData[i].cur = true;
                    }
                }
            });
        },
        // 计算按钮
        calculateClick() {
            if (this.wfxw == null) {
                uni.showToast({
                    title: '请选择违法行为',
                    icon: 'none',
                    duration: 2000
                });

                return;
            }

            if (this.wftk == null) {
                uni.showToast({
                    title: '请选择违法条款',
                    icon: 'none',
                    duration: 2000
                });

                return;
            }

            if (this.wfjq.SFABCF == '1' && this.sgtext == '') {
                uni.showToast({
                    title: '请手动输入金额',
                    icon: 'none',
                    duration: 2000
                });

                return;
            }

            if (this.cfclAndPdbz == null) {
                uni.showToast({
                    title: '请选择处罚裁量因素及判定标准',
                    icon: 'none',
                    duration: 2000
                });

                return;
            }

            let pam = {};

            pam.method = 'computeDiscretion';
            pam.param = {};
            pam.param.CFSX = Number(this.max);
            pam.param.CFXX = Number(this.min);
            let array = [];
            let array1 = []; //首要的
            let array2 = []; //次要的
            this.cfclAndPdbz.forEach(element => {
                array.push(element.XH);
                if (element.SFSYYS === '0') {
                    array2.push(element.CLDJ);
                } else {
                    array1.push(element.CLDJ);
                }
            });
            let xzclData = [];
            this.xzysAndPdbz.forEach(element => {
                xzclData.push(element.CLDJ);
            });
            pam.param.xzysdjs = xzclData.join(',');
            pam.param.WFXWXH = this.wfxw.XH;
            pam.param.PDBZXH = array.toString();
            pam.param.WFQJXH = this.wfjq.XH;
            if (this.sgtext != '') {
                pam.param.SRSZ = this.sgtext;
            }
            pam.param.syclys = [...array1].join(','); // 首要裁量因素

            for (let i = 0; i < array1.length; i++) {
                if (array1[i] == pam.param.syclys) {
                    array1.splice(i, 1);
                }
            }
            pam.param.otherClys = [...array2].join(','); // 次要裁量因素

            uni.getStorage({
                key: 'CLXH',
                success: function (res) {
                    pam.param.XH = res.data;
                }
            });
            getElementData(pam).then(res => {
                this.ultimately.je = res.data_json.discretionMoney;
                this.ultimately.jg = res.data_json.describe;
                this.dialog = true;
                // 将XH 保存 减轻 数据库负担
                uni.setStorage({
                    key: 'CLXH',
                    data: res.data_json.XH,
                    success: function () {}
                });
            });
        },
        // 跳转
        goClick(url) {
            uni.navigateTo({
                url: url
            });
        },
        //  前往违法行为
        goClickwfxw() {
            let cllx = '';
            this.zxclData.forEach(element => {
                if (element.cur == true) {
                    cllx = element.XH;
                }
            });
            uni.navigateTo({
                url: '/pages/freedom/unlawfulAct/unlawfulAct' + '?cllx=' + cllx
            });
        },
        // 前往违法条款
        goClickwftk() {
            if (this.wfxw == null) {
                uni.showToast({
                    title: '请先选择违法行为',
                    icon: 'none',
                    duration: 2000
                });

                return;
            }

            uni.navigateTo({
                url: '/pages/freedom/clause/clause' + '?wfxw=' + this.wfxw.XH
            });
        },
        // 前往处罚裁量
        goClickCfcl() {
            if (this.wfxw == null) {
                uni.showToast({
                    title: '请先选择违法行为',
                    icon: 'none',
                    duration: 2000
                });

                return;
            }

            uni.navigateTo({
                url:
                    '/pages/freedom/discretion/discretion' +
                    '?wfxw=' +
                    this.wfxw.XH
            });
        },

        goClickElement() {
            uni.navigateTo({
                url: '/pages/freedom/discretion/element'
            });
        },

        // 前往违法情节
        goClickwfqj() {
            uni.navigateTo({
                url:
                    '/pages/freedom/plot/plot' +
                    '?data=' +
                    encodeURIComponent(JSON.stringify(this.wfqjData))
            });
        }
    }
};
</script>

<style></style>
