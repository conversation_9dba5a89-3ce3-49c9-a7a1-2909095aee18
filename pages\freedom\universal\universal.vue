<template>
    <body style="background-color: #f7f7f7">
        <header class="header">
            <i class="ic-back" @click="goBack()"></i>
            <h1 class="title">通用裁量</h1>
            <!-- <span class="hd-span">重置</span> -->
        </header>
        <section class="main" style="width: 100%">
            <div class="inner">
                <ul class="zy-list1">
                    <li>
                        <p class="zy-til1 til1">罚违金额</p>
                        <div class="input-group">
                            <div class="item">
                                <input
                                    type="number"
                                    placeholder="请输入罚款最小值"
                                    v-model="min"
                                    pattern="[0-9]*"
                                />
                                <i class="clear" @click="min = ''"></i>
                                <span>元</span>
                            </div>
                            <div class="item">
                                <input
                                    type="number"
                                    placeholder="请输入罚款最大值"
                                    v-model="max"
                                    pattern="[0-9]*"
                                />
                                <i class="clear" @click="max = ''"></i>
                                <span>元</span>
                            </div>
                        </div>
                        <div class="gap"></div>
                        <div class="gap"></div>
                    </li>

                    <li>
                        <p class="zy-til1 til3">是否有处罚金额下限</p>
                        <div class="label-group">
                            <label
                                class="zy-checkbox"
                                v-for="(item, index) in cfjexxData"
                                :key="index"
                                @click="cfjexxClick(item)"
                                ><input type="checkbox" /><i
                                    :class="{ on: item.on }"
                                ></i
                                ><span>{{ item.name }}</span></label
                            >
                        </div>
                    </li>
                    <li>
                        <p class="zy-til1 til2">处罚裁量因素</p>
                        <div class="label-group">
                            <label
                                class="zy-checkbox"
                                v-for="(item, index) in data"
                                :key="index"
                                @click="cfclClick(item)"
                                ><input type="checkbox" /><i
                                    :class="{ on: item.on }"
                                ></i
                                ><span>{{ item.CLYS }}</span></label
                            >
                        </div>
                    </li>
                    <li>
                        <p class="zy-til1 til3">处罚判定标准</p>
                        <div class="label-group">
                            <label
                                class="zy-checkbox"
                                v-for="(item, index) in pdbzData"
                                :key="index"
                                @click="pdbzClick(item)"
                                ><input type="checkbox" checked /><i
                                    :class="{ on: item.on }"
                                ></i
                                ><span>{{ item.PDBZ }} </span></label
                            >
                        </div>
                    </li>
                    <li>
                        <div class="zy-line" @click="goClickElement()">
                            <p class="zy-til1 til3">修正因素</p>
                            <span class="zy-more">请选择</span>
                        </div>
                        <div class="zy-selected" v-if="xzysAndPdbz != null">
                            <i
                                v-for="(item, index) in xzysAndPdbz"
                                :key="index"
                            >
                                <i>
                                    {{ item.CLYZ }}
                                    <br />
                                </i>
                            </i>
                        </div>
                    </li>
                    <li>
                        <p class="zy-til1 til4">
                            已选择（ {{ curData.length }} ）
                        </p>
                        <div class="has-selected">
                            <p v-for="(item, index) in curData" :key="index">
                                {{ item.PDBZ }}
                            </p>
                        </div>
                    </li>
                </ul>
                <div class="gap"></div>
                <div class="zy-bot-btn" @click="calculateClick()">
                    <p>计算</p>
                </div>
            </div>
        </section>
        <div class="mask" style="display: block" v-show="dialog"></div>
        <div class="cailiang-alert" v-show="dialog">
            <h3>裁量结果</h3>
            <div class="gap"></div>
            <div class="gap"></div>
            <p class="zy-til2">计算罚款（元）</p>
            <div class="gap"></div>
            <p class="zy-txt1">{{ ultimately.je }}</p>
            <div class="gap"></div>
            <p class="zy-til2">裁量说明</p>
            <div class="gap"></div>
            <p class="zy-txt2">
                {{ ultimately.jg }}
            </p>
            <div class="gap"></div>
            <div class="gap"></div>
            <!-- <div>
            <button class="zy-btn1">分享</button>
        </div> -->

            <i class="zy-close" @click="dialog = false"></i>
        </div>
    </body>
</template>

<script>
import { getData } from '@/api/freedom.js';
export default {
    data() {
        return {
            cfjexxData: [
                {
                    name: '是',
                    value: '1'
                },
                {
                    name: '否',
                    value: '0'
                }
            ],
            cfjeValue: '',
            data: [], // 处罚裁量因素数据
            pdbzData: [], //判定标准数据
            curData: [], // 以选择的数据
            xzysAndPdbz: null, //修正的判定标准
            min: '',
            max: '',

            dialog: false,
            ultimately: {
                je: 0,
                jg: ''
            } //最终结果
        };
    },
    mounted() {
        uni.removeStorage({
            key: 'CLXH',
            success: function (res) {}
        });
        this.getClys();
    },

    methods: {
        //处罚金额的调整
        cfjexxClick(item) {
            this.cfjexxData.forEach(element => {
                this.$set(element, 'on', false);
            });
            this.$set(item, 'on', true);
            this.cfjeValue = item.value;
        },

        // 计算按钮
        calculateClick() {
            if (this.min == '' || this.max == '') {
                uni.showToast({
                    title: '请输入判罚金额',
                    icon: 'none',
                    duration: 2000
                });

                return;
            }

            if (this.curData.length == 0) {
                uni.showToast({
                    title: '请选择处罚裁量因素及判定标准',
                    icon: 'none',
                    duration: 2000
                });

                return;
            }

            let pam = {};
            pam.method = 'computeDiscretion';
            pam.param = {};
            pam.param.CFSX = Number(this.max);
            pam.param.CFXX = Number(this.min);
            let array = [];
            let array1 = [];
            this.curData.forEach(element => {
                array.push(element.XH);
                if (this.cfjeValue === '0') {
                    array1.push(element.CLDJ + 1);
                } else {
                    array1.push(element.CLDJ);
                }
            });
            let xzclData = [];
            this.xzysAndPdbz.forEach(element => {
                xzclData.push(element.CLDJ);
            });
            // pam.param.WFXWXH = this.wfxw.WFXW;
            pam.param.xzysdjs = xzclData.join(',');
            pam.param.PDBZXH = array.toString();
            pam.param.syclys = Math.max(...array1); // 首要裁量因素

            for (let i = 0; i < array1.length; i++) {
                if (array1[i] == pam.param.syclys) {
                    array1.splice(i, 1);
                }
            }
            pam.param.otherClys = array1.toString(); // 次要裁量因素

            uni.getStorage({
                key: 'CLXH',
                success: function (res) {
                    pam.param.XH = res.data;
                }
            });
            getData(pam).then(res => {
                this.ultimately.je = res.data_json.discretionMoney;
                this.ultimately.jg = res.data_json.describe;
                this.dialog = true;
                // 将XH 保存 减轻 数据库负担
                uni.setStorage({
                    key: 'CLXH',
                    data: res.data_json.XH,
                    success: function () {}
                });
            });
        },
        // 获取处罚裁量
        getClys() {
            let pam = {};
            pam.method = 'getClys';
            pam.param = {};
            pam.param.wflx = 'common';
            getData(pam).then(res => {
                this.data = res.data_json;
                for (let i = 0; i < this.data.length; i++) {
                    if (i == 0) {
                        this.$set(this.data[i], 'on', true);
                        this.getPdbz(this.data[i].XH);
                    } else {
                        this.$set(this.data[i], 'on', false);
                    }
                }
            });
        },
        // 获取判定标准
        getPdbz(XH) {
            let pam = {};
            pam.method = 'getPdbz';
            pam.param = {};
            pam.param.clys = XH;
            getData(pam).then(res => {
                this.pdbzData = res.data_json;
                for (let i = 0; i < this.pdbzData.length; i++) {
                    this.$set(this.pdbzData[i], 'on', false);
                }
            });
        },
        // 处罚裁量行点击事件
        cfclClick(item) {
            this.data.forEach(element => {
                element.on = false;
            });
            item.on = true;
            this.getPdbz(item.XH);
        },

        // 判定标准行点击事件
        pdbzClick(item) {
            // 判断是勾选 还是 取消勾选
            // 如果是取消勾选
            if (item.on == true) {
                item.on = false;

                for (let i = 0; i < this.curData.length; i++) {
                    if (this.curData[i].XH == item.XH) {
                        this.curData.splice(i, 1);
                    }
                }

                // 如果是勾选
            } else {
                // 1. 完成勾选功能
                this.pdbzData.forEach(element => {
                    element.on = false;
                });
                item.on = true;

                for (let i = 0; i < this.curData.length; i++) {
                    if (this.curData[i].CLYSXH == item.CLYSXH) {
                        this.curData.splice(i, 1);
                    }
                }

                this.curData.push(item);
            }
        },

        goClickElement() {
            uni.navigateTo({
                url: '/pages/freedom/discretion/element'
            });
        },

        // 返回上一层
        goBack() {
            uni.navigateBack({
                //返回
                delta: 1
            });
        }
    }
};
</script>

<style scoped>
.on {
    background: url(@/static/freedom/images/zy-checked.png);
    background-size: 100%;
}
</style>
