<template>
  <body style="background-color: #f7f7f7;" >
    <header class="header">
      <i class="ic-back" @click="goBack()"></i>
      <h1 class="title">违法行为选择</h1>
    </header>
    <section class="main" style="width:100%">
      <div class="inner">
        <div class="zy-input1">
          <input
            type="text"
            placeholder="请输入关键字"
            v-model="text"
            @input="textChange()"
          />
        </div>
        <ul class="zy-list2">
          <li
            v-for="(item, index) in data"
            :key="index"
            @click="itemClick(item)"
          >
            <label class="zy-checkbox zy-checkbox2"
              ><input type="checkbox" checked /><i :class="{ on: item.on }"></i>
              <div class="wenzi">
                <p>
                  {{ item.WFXW }}
                </p>
              </div></label
            >
          </li>
        </ul>
        <div class="gap"></div>
        <div class="zy-bot-btn" @click="qdClick()">
          <p>确定</p>
        </div>
      </div>
    </section>
  </body>
</template>

<script>
import { getData } from "@/api/freedom.js";
export default {
  data() {
    return {
      XH: "",
      data: [], // 当前数据
      Alldata: [], // 全部数据
      text: "",
      curObj: null, //当前选择的
    };
  },
  onLoad(option) {
    // 获取上个页面传过来的参数  本场考试的基本信息
    this.XH = option.cllx;
  },
  mounted() {
    this.getWflx();
  },
  methods: {
    // 确认按钮
    qdClick() {
      // 回到上一个页面 并且修改上一个页面的参数,并且不刷新页面
      var pages = getCurrentPages();
      var prevPage = pages[pages.length - 2];
      prevPage.$vm.wfxw = this.curObj;
      prevPage.$vm.wftk = null;
      prevPage.$vm.cfclAndPdbz = null;
      prevPage.$vm.wfqjData = [];
      prevPage.$vm.wfjq = {};
      prevPage.$vm.min = 0;
      prevPage.$vm.max = 0;
      prevPage.$vm.sgtext = '';
      uni.navigateBack({
        //返回
        delta: 1,
      });
    },
    // 搜索
    textChange() {
      let array = [];
      for (let i = 0; i < this.Alldata.length; i++) {
        if (this.Alldata[i].WFXW.indexOf(this.text) != "-1") {
          array.push(this.Alldata[i]);
        }
      }
      this.data = array;
    },
    // 勾选方法
    itemClick(item) {
      // 当我勾选的时候，因为 浅拷贝的原因，AllData 和 data 同一个选项的 on 都被赋值为true

      this.Alldata.forEach((element) => {
        element.on = false;
      });
      this.data.forEach((element) => {
        element.on = false;
      });
      item.on = true;
      this.curObj = item;
    },
    // 获取违法行为数据
    getWflx() {
      let pam = {};
      pam.method = "getWflx";
      pam.param = {};
      pam.param.cllx = this.XH;
      getData(pam).then((res) => {
        this.data = res.data_json;
        this.data.forEach((element) => {
          this.$set(element, "on", false);
        });
        this.Alldata = this.data;
      });
    },
    // 返回
    goBack() {
      uni.navigateBack({
        //返回
        delta: 1,
      });
    },
  },
};
</script>

<style scoped>
.on {
  background: url(@/static/freedom/images/zy-checked.png);
  background-size: 100%;
}
</style>
