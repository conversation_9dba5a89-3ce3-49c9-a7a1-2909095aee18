<template>
	<MainPage :padding="false"
	      title="地址修改">
		<view class="main">
		    <div class="zy-form">
		        <text class="changepass-title">服务地址</text>
		        <div class="item">
		            <input class="url-box" type="text" value="" v-model="preferApiServerUrl"/>
		        </div>
		        <show-modal></show-modal>
		        <button @click="savePreferApiServerUrl" class=".change-url_confirm">确定</button>
		    </div>
		</view>
	</MainPage>
</template>

<script>
	import { LOGIN_ULR_BASE } from '@/common/config.js'
	
	export default {
		components: {
		},
		
		data() {
			return {
				preferApiServerUrl: '',
			}
		},

		mounted() {
			this.preferApiServerUrl = LOGIN_ULR_BASE
		},

		methods: {
			savePreferApiServerUrl() {
				uni.setStorageSync('PERFER_API_SERVER_URL', this.preferApiServerUrl)
				
				// #ifdef APP-PLUS
				plus.runtime.quit();
				// #endif
			}
		}
	}
</script>

<style scoped>
	.change-url_confirm{
		margin-top: 300rpx;
	}
	.url-box{
		margin-top: 50rpx;
	}
	.main {
	    padding: 60rpx 40rpx 0 40rpx;
	}
	
	.changepass-title {
	    font-size: 30rpx;
	}
</style>
