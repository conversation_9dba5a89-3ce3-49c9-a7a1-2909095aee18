<template>
  <view style="width: 100%">
    <header class="pd-header log">
      <image
        style="z-index: 999"
        src="/static/login/images/log_logo.png"
        @touchstart="show_vconsole"
      ></image>
    </header>
    <div class="tips" @click="startNoPasswordLogin">
      <span style="position: relative; left: 14rpx"
        >本平台为互联网非涉密平台，<br />禁止处理、传输国家秘密</span
      >
    </div>
    <show-modal></show-modal>
    <section class="pd-main log">
      <div class="pd-inner" v-if="isH5">
        <dl class="pd-loginmod" v-if="userInfo.length > 1">
          <dd>
            <ul>
              <li class="relative-li">
                <a>请选择一个账号登录</a>
              </li>
              <li class="relative-li">
                <p>账号</p>
                <p style="text-align: center">手机号</p>
              </li>
              <li
                class="relative-li"
                v-for="(item, index) in userInfo"
                :key="index"
                @click="chooseLogin(item)"
              >
                <p>{{ item.XTZH }}</p>
                <p>{{ item.YHSJ }}</p>
              </li>
              <!-- <li class="relative-li">
                <input
                  required
                  type="text"
                  class="pd-ipt txt"
                  clearable
                  :focus="focusVal"
                  v-model="username"
                  placeholder="请输入账号"
                />
                <span
                  style="right: 0rpx"
                  v-show="username"
                  class="editable-clear-x"
                  @click.stop="clearName"
                  ><image
                    class="clear-img"
                    src="../../static/login/images/clear.png"
                    mode=""
                  ></image
                ></span>
              </li>
              <li class="relative-li">
                <input
                  required
                  class="pd-ipt pwd"
                  :type="passwordType"
                  v-model="password"
                  placeholder="请输入密码"
                />
                <span
                  v-show="password"
                  class="editable-clear-x"
                  @click="password = ''"
                  ><image
                    class="clear-img"
                    src="../../static/login/images/clear.png"
                    mode=""
                  ></image
                ></span>
                <image
                  src="../../static/login/images/eye_open.png"
                  v-show="passwordType == 'password'"
                  @click="passwordType = 'text'"
                />
                <image
                  src="../../static/login/images/eye_close.png"
                  v-show="passwordType == 'text'"
                  @click="passwordType = 'password'"
                />
              </li> -->
            </ul>
            <!-- <view class="login-row">
              <checkbox
                style="font-size: 46rpx; color: #aaa; zoom: 60%"
                :checked="savePass"
                @click="checkChange()"
              >
                记住密码
              </checkbox>
            </view>
            <button
              v-show="!username || !password"
              type="button"
              class="log-btn"
              style="background-color: #dddddd"
            >
              登录
            </button>
            <button
              v-show="username && password"
              type="button"
              class="log-btn"
              @tap="bindLogin"
            >
              登录
            </button> -->
          </dd>
        </dl>
        <!-- <p class="log-bot">技术支持：深圳市博安达信息技术股份有限公司</p> -->
      </div>
      <div class="pd-inner" v-else>
        <dl class="pd-loginmod">
          <dd>
            <ul>
              <li class="relative-li">
                <input
                  required
                  type="text"
                  class="pd-ipt txt"
                  clearable
                  :focus="focusVal"
                  v-model="username"
                  placeholder="请输入账号"
                />
                <span
                  style="right: 0rpx"
                  v-show="username"
                  class="editable-clear-x"
                  @click.stop="clearName"
                  ><image
                    class="clear-img"
                    src="../../static/login/images/clear.png"
                    mode=""
                  ></image
                ></span>
              </li>
              <li class="relative-li">
                <input
                  required
                  class="pd-ipt pwd"
                  :type="passwordType"
                  v-model="password"
                  placeholder="请输入密码"
                />
                <span
                  v-show="password"
                  class="editable-clear-x"
                  @click="password = ''"
                  ><image
                    class="clear-img"
                    src="../../static/login/images/clear.png"
                    mode=""
                  ></image
                ></span>
                <image
                  src="../../static/login/images/eye_open.png"
                  v-show="passwordType == 'password'"
                  @click="passwordType = 'text'"
                />
                <image
                  src="../../static/login/images/eye_close.png"
                  v-show="passwordType == 'text'"
                  @click="passwordType = 'password'"
                />
              </li>
            </ul>
            <view class="login-row">
              <checkbox
                style="font-size: 46rpx; color: #aaa; zoom: 60%"
                :checked="savePass"
                @click="checkChange()"
              >
                记住密码
              </checkbox>
            </view>
            <button
              v-show="!username || !password"
              type="button"
              class="log-btn"
              style="background-color: #dddddd"
            >
              登录
            </button>
            <button
              v-show="username && password"
              type="button"
              class="log-btn"
              @tap="bindLogin"
            >
              登录
            </button>
          </dd>
        </dl>
        <!-- <p class="log-bot">技术支持：深圳市博安达信息技术股份有限公司</p> -->
      </div>
      <div
        style="
          font-size: 28rpx;
          position: absolute;
          text-align: center;
          width: 100%;
          bottom: 100rpx;
        "
      >
        <span style="color: #999">主办单位：山东省生态环境厅</span>
      </div>
    </section>
    <view v-if="showUpgrade" class="flex-column-layout upgrade-tip-mask">
      <view class="flex-column-layout upgrade-progress-tip">
        <text style="width: 100%; text-align: left">正在更新数据</text>
        <view style="width: 100%">
          <progress
            :percent="upgradeProgress"
            active
            active-mode="forwards"
            stroke-width="3"
            show-info
          />
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import service from "../../service.js";
import { mapState, mapMutations } from "vuex";
import mInput from "../../components/m-input.vue";
import loginService from "@/api/login-service.js";
import { isH5 } from "@/common/config.js";
// #ifdef APP-PLUS
import appService from "@/api/app-service.js";
// #endif

// #ifdef H5
import vconsole from "vconsole";
// #endif

export default {
  components: {
    mInput,
  },
  data() {
    return {
      isH5: isH5,
      lastClickTime: 0,
      vconsoleCount: 0, //vonsole调试工具计数展示
      focusVal: false,
      passwordType: "password",
      loginType: 0,
      loginTypeList: ["密码登录", "免密登录"],
      mobile: "",
      code: "",
      providerList: [],
      hasProvider: false,
      username: "",
      password: "",
      positionTop: 0,
      isDevtools: false,
      codeDuration: 0,
      savePass: false,
      showUpgrade: false,
      upgradeProgress: 0,
      count: 0, //图标点击次数
      token: "",
      code: "",
      mobile: "",
      userId: "",
      userInfo: [], //使用手机号获取的用户信息
    };
  },

  created() {
    this.focusVal = this.username == "" ? true : false;
  },

  onLoad(params) {
    let _self = this;
    uni.getStorage({
      key: "save_password",
      success: function (r) {
        _self.savePass = r.data || false;
        if (_self.savePass) {
          uni.getStorage({
            key: "password",
            success: function (r) {
              _self.password = r.data;
            },
          });
        }
      },
    });
    uni.getStorage({
      key: "username",
      success: function (r) {
        _self.username = r.data;

        _self.focusVal = _self.username == "" ? true : false;
      },
    });
  },

  computed: mapState(["forcedLogin"]),

  onReady() {
    this.initPosition();

    // #ifdef MP-WEIXIN
    this.isDevtools = uni.getSystemInfoSync().platform === "devtools";
    // #endif
  },

  mounted() {
    uni.removeStorageSync("token");
    this.checkAppUpgrade();
    this.code = this.getQueryString("code");
    console.log(this.code);
    //开始免密登录
    this.startNoPasswordLogin();
  },

  methods: {
    ...mapMutations(["login"]),
    checkChange() {
      this.savePass = !this.savePass;
      uni.setStorage({
        key: "save_password",
        data: this.savePass,
        success: function (r) {},
      });
    },
    //跳转修改服务地址页面
    changeUrl() {
      this.count++;
      if (this.count > 5) {
        uni.navigateTo({
          url: "/pages/login/changeUrl",
        });
      }
    },
    initPosition() {
      /**
       * 使用 absolute 定位，并且设置 bottom 值进行定位。软键盘弹出时，底部会因为窗口变化而被顶上来。
       * 反向使用 top 进行定位，可以避免此问题。
       */
      this.positionTop = uni.getSystemInfoSync().windowHeight - 100;
    },
    loginByPwd() {
      /**
       * 客户端对账号信息进行一些必要的校验。
       * 实际开发中，根据业务需要进行处理，这里仅做示例。
       */
      // if (this.username.length < 3) {
      //   uni.showToast({
      //     icon: "none",
      //     title: "账号最短为 3 个字符",
      //   });
      //   return;
      // }

      uni.setStorage({
        key: "username",
        data: this.username,
        success: function (r) {},
      });

      uni.setStorage({
        key: "password",
        data: this.password,
        success: function (r) {},
      });

      const data = {
        username: this.username,
        password: this.password,
      };
      let _self = this;
      this.listenLogin();
      uni.showLoading({
        title: "登录中...",
      });
      loginService.loginByPassword(this.username, this.password);
    },

    listenLogin() {
      let _self = this;
      uni.$on("onLoginSuccess", (params) => {
        uni.$off("onLoginSuccess");
        uni.hideLoading();
        _self.onLoginSuccess();
      });

      uni.$on("onLoginFail", (error) => {
        uni.$off("onLoginFail");
        uni.hideLoading();
        let errorMsg =
          typeof error === "object"
            ? JSON.stringify(error.error_msg, null, 4)
            : "登录出错，请检查网络";
        this.$showModal({
          title: "提示",
          content: `登录失败: ${errorMsg || "登录出错，请检查网络"}`,
        });
      });
    },

    onLoginSuccess() {
      uni.setStorageSync("password", this.password);
      this.$store.commit("login", this.username);
      // uni.setStorage({
      // 	key: 'username',
      // 	data: this.username,
      // 	success: function(r) {}
      // });
      uni.redirectTo({
        url: "/pages/main/portal",
      });
    },

    onLoginFail(err) {},

    bindLogin() {
      switch (this.loginType) {
        case 0:
          this.loginByPwd();
          break;
        case 1:
          this.loginBySms();
          break;
        default:
          break;
      }
    },

    toMain(userName) {
      this.login(userName);
      /**
       * 强制登录时使用reLaunch方式跳转过来
       * 返回首页也使用reLaunch方式
       */
      if (this.forcedLogin) {
        uni.reLaunch({
          url: "pages/main/portal",
        });
      } else {
        uni.navigateBack();
      }
    },

    //获取版本信息
    checkAppUpgrade() {
      // #ifdef APP-PLUS
      let _self = this;
      appService.setProgressUpdater((percent) => {
        _self.showUpgrade = true;
        _self.upgradeProgress = percent;
        if (percent === 100) {
          _self.showUpgrade = false;
        }
      });
      appService.checkMPAppUpgrade();
      // #endif
    },

    clearName() {
      this.username = "";
      this.focusVal = true;
    },
    //山东通免登录
    startNoPasswordLogin() {
      // #ifdef H5
      this.mobileLogin();
      // #endif
    },
    //使用手机号获取账号信息
    mobileLogin() {
      uni.request({
        url: `http://172.20.234.137:8070/zhzf/thirdServer/getUserInfo`,
        data: {
          CODE: this.code,
        },
        method: "POST",
        header: {
          "content-type": "application/json",
        },
        success: (res) => {
          console.log(res);
          if (res.data.data_json.status_code !== "-1") {
            this.userInfo = res.data.data_json.userInfo;
            //只有一个账号的时候直接登录
            if (this.userInfo.length == 1) {
              this.username = this.userInfo[0].XTZH;
              this.password = this.userInfo[0].YHMM;
              this.loginByPwd();
            }
          } else {
            //没有账号信息
            uni.showToast({
              title: `${res.data.data_json.error_msg}`,
              icon: "none",
            });
          }
        },
        catch: (err) => {
          uni.showToast({
            title: `${err}`,
            icon: "none",
          });
        },
      });
    },
    //多个账号，手动选择一个账号登录
    chooseLogin(item) {
      this.username = item.XTZH;
      this.password = item.YHMM;
      this.loginByPwd();
    },
    //获取url中"?"符后的字符串并正则匹配
    getQueryString(name) {
      let reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
      let r = window.location.search.substring(1).match(reg);
      let context = "";
      if (r != null) context = decodeURIComponent(r[2]);
      reg = null;
      r = null;
      return context == null || context == "" || context == "undefined"
        ? ""
        : context;
    },
    //控制显示vconsole
    show_vconsole() {
      // #ifdef H5
      console.log(555);
      
      const nowTime = new Date().getTime();
      if (nowTime - this.lastClickTime < 3000) {
        this.vconsoleCount++;
      } else {
        this.vconsoleCount = 0;
      }
      this.lastClickTime = nowTime;
      if (this.vconsoleCount >= 10) {
        this.$vconsole = new vconsole();
        this.vconsoleCount = 0;
      }
      // #endif
    },
  },
};
</script>

<style>
.editable-clear-x .clear-img {
  width: 50rpx;
  height: 50rpx;
}
.relative-li {
  position: relative;
}
.relative-li .editable-clear-x {
  position: absolute;
  top: 20rpx;
  right: 40rpx;
  /* display: none; */
  width: 40rpx;
  height: 40rpx;
}

.login-row {
  display: flex;
  justify-content: space-between;
  flex-direction: row;
  align-items: center;
  padding: 8rpx 0px;
  width: 80%;
}

.login-input {
  width: 100%;
  padding: 10rpx 10rpx;
}
.pd-loginmod {
  min-height: 400rpx;
  max-height: 480rpx;
  overflow: scroll;
}
.pd-loginmod .relative-li {
  display: flex;
  justify-content: space-around;
  margin-top: 20rpx;
}
.pd-loginmod .relative-li p {
  text-align: left;
  width: 230rpx;
}
.pd-loginmod dd ul li .pd-ipt {
  border: none;
  border-bottom: 1px solid #eee;
}
.pd-loginmod dd ul li .pd-ipt:focus {
  border: none;
  border-bottom: 1px solid #eee;
  outline: medium;
}

.action-row {
  display: flex;
  flex-direction: row;
  justify-content: center;
}

.action-row navigator {
  color: #007aff;
  padding: 0 10px;
}

.upgrade-tip-mask {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: 9999;
  justify-content: flex-start;
}

.upgrade-progress-tip {
  width: 72%;
  padding: 20rpx;
  margin-top: 360rpx;
  border-radius: 6rpx;
  background-color: #fff;
  box-shadow: 0 0 20rpx 0 #ccc;
}

.tips {
  position: absolute;
  color: red;
  font-size: 32rpx;
  width: 100%;
  text-align: center;
  top: 380rpx;
  z-index: 999;
}
</style>
