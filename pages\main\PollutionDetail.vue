	<template>
	<Page :padding="false" class="power-page" :title="title" @layoutAware="onFixedHeight">
		<scroll-view
				:scroll-y="true"
			    :style="pageListStyle">
				<view>
		<!-- <PCard :title="companyData.WRYMC">
			<div class="company-item">统一社会信用代码：{{companyData.TYSHXYDM}}</div>
			<div class="company-item">行业类型：{{industryCategory|| '--'}}</div>
			<div class="company-item">生产状态：{{produceStatus || '--'}}</div>
			<div class="company-item">法人代表：{{companyData.FDDBR}}</div>
			<div class="company-item">法人联系电话：{{companyData.FRLXDH}}</div>
			<div class="company-item">经营场所地址：{{companyData.WRYDZ}}</div>
		</PCard> -->
		<PCard :title="companyData.WRYMC">
					<uni-list>
					<uni-list-item title="统一社会信用代码："
								   :rightText="companyData.TYSHXYDM"></uni-list-item>
					<uni-list-item title="行业类型："
								   :rightText="industryCategory || '--'"></uni-list-item>
					<uni-list-item title="生产状态："
								   :rightText="produceStatus || '--'"></uni-list-item>
					<uni-list-item title="法人代表："
								   :rightText="companyData.FDDBR"></uni-list-item>
                   <!-- <uni-list-item title="振动监控"
								   v-if="vibrationType" >
                   <template slot="footer">
							<button class="slot-image"
									@click="routerVibration()">跳转</button>
						</template></uni-list-item> -->
					<uni-list-item title="法人联系电话："
								   :note="companyData.FRLXDH">
						<template slot="footer">
							<!-- <image class="slot-image" :src="jcs" mode="widthFix"></image> -->
							<button class="slot-image"
									@click="callPerson(companyData.FRLXDH)">呼叫</button>
						</template>
					</uni-list-item>
					<uni-list-item title="经营场所地址："
								   :note="companyData.WRYDZ">
						<template slot="footer">
							<button class="slot-image"
									@click="navigation(companyData)">导航</button>
						</template>
					</uni-list-item>
					</uni-list>
				</PCard>

		<section class="pd-main">
			<div class="pd-inner" style="padding-top: 0;">
				<ul class="pd-ulbx4" style="padding: 0;margin: 0;margin-top: 10px;" v-if="isH5">
					<li v-for="(cate, index) in categories" :key="index" @click="goPollutionDetail(cate)" v-if="cate.name !== '标签管理'">
						<!-- <i v-show="item.SL">{{item.SL || ''}}</i> -->
						<image :src="resolveCategoryIcon(cate.tag)" alt="" class="pollution-image">
						<p>{{cate.name}}</p>
					</li>
				</ul>
				<ul class="pd-ulbx4" style="padding: 0;margin: 0;margin-top: 10px;" v-else>
					<li v-for="(cate, index) in categories" :key="index" @click="goPollutionDetail(cate)">
						<!-- <i v-show="item.SL">{{item.SL || ''}}</i> -->
						<image :src="resolveCategoryIcon(cate.tag)" alt="" class="pollution-image">
						<p>{{cate.name}}</p>
					</li>
				</ul>
			</div>
		</section>
				</view>
		</scroll-view>
	</Page>
</template>

<script>
// ../../static/login/images/ulic1.png
	import { getPollutionMenu } from '@/api/pollution.js'
	import { postQueryWryjcTzForm, queryIndustryCategories } from '@/api/book.js'
	import codeService from '@/api/code-service.js'

	import PCard from '@/pages/component/PCard.vue';
	import Page from '@/pages/component/Page.vue';

	import iconJbxx from '@/static/img/wry/wry_jbxx.png'
	import iconTags from '@/static/img/wry/wry_tags.png'
	import iconDischargePermit from '@/static/img/wry/wry_discharge_permit.png'
	import iconDangousWaster from '@/static/img/wry/wry_dangous_waster.png'
	import iconInspectRecord from '@/static/img/wry/wry_inspect_record.png'
	import iconAdminPenalty from '@/static/img/wry/wry_admin_penalty.png'
	import iconBuildProject from '@/static/img/wry/wry_build_project.png'
	import iconPollutionFacility from '@/static/img/wry/wry_pollution_facility.png'
	import iconOnlineMonitoring from '@/static/img/wry/wry_online_monitoring.png'
	import iconWarning from '@/static/img/wry/wry_warning.png'
	import iconHealth from '@/static/img/wry/wry_health.png'
  import iconVibration from '@/static/login/images/zdjk.png'
	import navition from '@/api/map-navigation.js';
	import uriUtil from '@/common/net/uri.js'
	import bookService from '@/api/standing-book-service.js'
	import { isH5 } from '@/common/config.js';
	//选项卡图片的对应映射
	const TEMPLATE_FACE_ICONS = new Map([
		['WRY_JBXX', iconJbxx],//基本信息
		['WRY_TAGS', iconTags],//标签管理
		['WRY_DISCHARGE_PERMIT', iconDischargePermit],//建设项目
		['WRY_DANGOUS_WASTER', iconDangousWaster],//排污许可
		['WRY_INSPECT_RECORD', iconInspectRecord],//查询污染源危废管理
		['WRY_ADMIN_PENALTY', iconAdminPenalty],//治污设施
		['WRY_BUILD_PROJECT', iconBuildProject],//排污许可
		['WRY_POLLUTION_FACILITY', iconPollutionFacility],//行政处罚
		['WRY_ONLINE_MONITORING', iconOnlineMonitoring],//在线监测
		['WRY_WARNING', iconWarning],
		['WRY_HEALTH', iconHealth],
    ['WRY_VIBRATION', iconVibration],
	])
	export default {
		data(){
			return {
				isH5:isH5,
				companyData: {},
				menuArr: [], // 菜单数组
				pageHeight: 60,
				uniteId: '', // 统一社会信用代码
				title: '企业信息',
				produceStatus: '',
				categories: [],
        vibrationType:false
			}
		},

		computed: {
		    formLayoutHeight: function() {
				return this.pageHeight - 40
			},

			pageListStyle: function() {
				return {
					height: 'calc(100vh - 80rpx)',
				}
			},

			industryCategory: function() {
				return this.companyData.HYMC
			}
		},

		components: {
			Page,
			PCard
		},

		onLoad(options){
			this.recordId = options.recordId;
			this.getCompanyData();
		},

		mounted() {
			bookService.queryBusinessCategory()
				.then(resp => {
					this.categories = resp
          let userInfo = uni.getStorageSync('userInfo')
          if(userInfo.orgid.substring(0,4) === '3706' || userInfo.name === '系统管理员'){
            this.filterVibration()
          }
				})
		},

		methods: {
			onFixedHeight(layout) {
				this.pageHeight = layout.height;
			},

      filterVibration(){
        // this.categories.push()
        this.vibrationType = true
      },

			resolveCategoryIcon(categoryTag) {
				return TEMPLATE_FACE_ICONS.get(categoryTag)
			},

			//获取污染源业务信息菜单
			getMenu(){
				getPollutionMenu({
					recordId: this.recordId
				}).then(res => {
					this.menuArr = res.data_json;
					this.menuArr.forEach(menu => {

					});
				})
			},

			/**
			 * 查询企业基本信息
			 */
			getCompanyData(){
				let self = this
				postQueryWryjcTzForm({
					service:  'QUERY_DYNAMICFORM_DATA',
					recordId: this.recordId,
					mbbh: '202102240924456a2f18c745934bab83c1e818600a2aca'
				}).then(res => {
					let wryInfo = res.data
					this.companyData = wryInfo
					this.uniteId = wryInfo.TYSHXYDM; // 统一社会信号用代码
					let produceStatusCode = wryInfo.SCZT
					//生态状态状态码不为空且是英文跟数字组合，请求相应的公共代码值，否则认为是中文，直接赋值
					if(produceStatusCode && /[a-zA-Z_0-9]+/gi.test(produceStatusCode)) {
						self.resolveProduceStatusName(produceStatusCode)
					} else {
						this.produceStatus = produceStatusCode
					}
					// 获取菜单
					// this.getMenu()
				})
			},

			resolveProduceStatusName(code) {
				codeService.getCode('WRY_SCZT', code)
					.then(resp => {
						this.produceStatus = resp.name
					})
			},

			// 跳转到污染源明细
			goPollutionDetail(category){
				let recordId = this.recordId;
				let title = category.name
				let templateId = category.templateId || ''

				if(category.name === '标签管理') {
					this.showPollutionTags()
					return
				}

				if(templateId) {
					this.goTemplateDetail(category.tag, category.name, recordId, templateId);
					return
				}

				let serverPath = category.server_path
				if(serverPath === ''){
					uni.showToast({
						icon: 'none',
					    title: '暂未开发',
					    duration: 2000
					});
					return
				}
				let uniteId = this.uniteId;
				this.goDetailList(category)
			},

			// 获取模板详情页面
			goTemplateDetail(tag, title, recordId, templateId) {
				if('WRY_DISCHARGE_PERMIT' === tag) {
					bookService.queryWryBusinessList('1619593281508000671744', recordId)
						.then(resp => {
							let permits = resp.list || []
							let permit = permits.length > 0 ? permits[0] : null
							if(permit) {
								this.showWryCategoryDetail(title, permit.XH, templateId)
							}else{
								uni.showToast({
								title: '暂无信息',
								duration: 2000,
								icon: 'none'
							});
							}
						})
				} else {
					this.showWryCategoryDetail(title, recordId, templateId)
				}
			},

			showWryCategoryDetail(title, recordId, templateId) {
				let params = {
					title: title,
					recordId: recordId,
					templateId: templateId
				}
				uni.navigateTo({
					url: `/pages/main/TemplateDetail?${uriUtil.transformObjectToUrlParams(params)}`
				})
			},

			// 模板详情列表
			goDetailList(category) {
				let params = {
					title: category.name,
					tag: category.tag,
					recordId: this.recordId,
					service: category.server_path,
				}

				let listDestination = `/pages/main/PollutionDetailList?${uriUtil.transformObjectToUrlParams(params)}`
				uni.navigateTo({
					url: listDestination
				})
			},

			//呼叫用户
			callPerson(value) {
				if(value){
					uni.makePhoneCall({
					phoneNumber: value //仅为示例
				});
				}else{
					uni.showToast({
						title: '暂无号码',
						duration: 2000,
						icon: 'none'
					});
				}
			},

			//导航
			navigation(data) {
				navition.getMapNavigation(data.WRYDZ,data.JD,data.WD)
			},

			showPollutionTags() {
				uni.navigateTo({
					url: `/pages/wry/wry-tags?pollutionId=${this.recordId}`
				})
			},

      routerVibration(){
         uni.navigateTo({
						url: `/pages/web/web-page?page=vibrationDetail&TYSHXYDM=${this.companyData.TYSHXYDM}`
					})
      }
		}
	}
</script>

<style scoped>
	.pd-ulbx4 li {
	    width: 25%;
	    padding: 26px 0;
	}
	.company-item{
		line-height: 80rpx;
		font-size: 30rpx;
	}

	.pd-ulbx4 li{
		position: relative;
	}
	.pd-ulbx4 li i{
		position: absolute; left: 50%; top: 13%; width: 28.985475rpx; height: 28.985475rpx; border-radius: 50%; background: #e86767; font-size: 21.7391249rpx; color: #fff; text-align: center; line-height: 28.985475rpx; margin-left: 36.2319rpx;
	}

	.pollution-image{
		width: 60rpx;
		height: 60rpx;
	}

		.slot-image {
		width: 80rpx;
		height: 60rpx;
		display: flex;
		align-self: center;
		font-size: 24rpx;
		justify-content: center;
	}
</style>
