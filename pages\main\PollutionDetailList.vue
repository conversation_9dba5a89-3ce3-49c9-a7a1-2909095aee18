<template>
	<Page class="power-page" :title="title" @layoutAware="onFixedContentHeight">
		<section class="pd-main">
			<div class="pd-inner" style="padding-top: 0px;">
				<scroll-view class="record-task-list" scroll-y="true"  style="height: calc(100vh - 180rpx);"  @scrolltolower="onReachBottomEvent" v-if="listData.length>0">
					<ul class="pd-ullst5">
						<li
							v-if="'WRY_DANGOUS_WASTER' === tag"
							@click="showDetail(item)"
							v-for="item in listData">
							<h2>转移危废名称：{{item.WASTENAME}}  </h2>
							<p>计划年度：{{item.PLANYEAR}}</p>
							<p>计划转移量（{{item.UNIT}}）：{{item.THISYEAR}}</p>
						</li>
						
						<li
							v-if="'WRY_INSPECT_RECORD' === tag"
							@click="showDetail(item)"
							v-for="item in listData">
							<h2>{{item.WRYMC}}  </h2>
							<p>检查时间：{{item.JSSJ}}</p>
							<p>是否发现涉嫌环境违法行为：{{item.SFFXSXHJWFXWMC}}</p>
						</li>
						
						<li
							v-if="'WRY_ADMIN_PENALTY' === tag"
							@click="showDetail(item)"
							v-for="item in listData">
							<h2>决定书文号：{{item.JDSWH}}  </h2>
							<p>立案日期：{{item.LARQ}}</p>
							<p>结案情况：{{item.JAQK}}</p>
						</li>
						
						<li 
							v-if="'WRY_BUILD_PROJECT' === tag"
							@click="showDetail(item)"
							v-for="item in listData">
							<h2>{{item.XMMC}}  </h2>
							<P>环境影响评价管理类别：{{item.HJYXPJGLLBMC}}</P>
							<p>预计开工日期：{{item.YJKGRQ}}</p>
							<p>预计投产日期：{{item.YJTCRQ}}</p>
						</li>
						
						<li
							v-if="'WRY_POLLUTION_FACILITY' === tag"
							@click="showDetail(item)"
							v-for="item in listData">
							<h2>设施名称：{{item.SSMC}}  </h2>
							<P>对应排污口：{{item.DYPWK}}</P>
							<p>主要去除物质：{{item.ZYQCWZ}}</p>
						</li>
						
						<li
							v-if="'WRY_ONLINE_MONITORING' === tag"
							@click="showDetail(item)"
							v-for="item in listData">
							<h2>排口名称：{{item.PKMC}}  </h2>
							<P>排口类型：{{item.PKLX}}</P>
						</li>
						
						<li
							v-if="'WRY_WASTER_PLAN_TRANSFER' === tag"
							@click="showDetail(item)"
							v-for="item in listData">
							<h2>转移单位：{{item.transEnterpriseName}}  </h2>
							<P>运输单位：{{item.transportEnterpriseName}}</P>
							<p>运输量（{{item.unit}}）：{{item.transferQuantity}}</p>
							<p>转移时间：{{item.transDate}}</p>
						</li>
						
						<li
							v-if="tag == 'QUERY_WRY_PWXK'"
							@click="showDetail(item)"
							v-for="item in listData">
							<h2>排污许可证编码：{{item.CREDITCODE}}  </h2>
							<h2>所属行业：{{item.HYNAME}}</h2>
							<p>发证日期：{{item.CJSJ}}</p>
						</li>
						
						<li
							v-if="service == 'QUERY_WRY_ZWSS'"
							@click="showDetail(item)"
							v-for="item in listData">
							<h2>设施名称：{{item.SSMC}}  </h2>
							<h2>对应排污口：{{item.DYPWK}}</h2>
						</li>
						
						<li
							v-if="service == 'QUERY_WRY_HJXF'"
							@click="showDetail(item)"
							v-for="item in listData">
							<h2>被投诉单位名称：{{item.WRYMC}}  </h2>
							<h2>被投诉单位地址：{{item.WRYDZ}}</h2>
						</li>
						
						<li
							v-if="service == 'QUERY_WRY_XZCF'"
							@click="showDetail(item)"
							v-for="item in listData">
							<h2>污染源名称：{{item.WRYMC}}  </h2>
							<h2>法定代表人：{{item.FDDBR}}</h2>
							<p>调查日期：{{item.DCRQ}}</p>
						</li>
						
						
						<li
							v-if="service == 'QUERY_WRY_ZXJC'"
							@click="showDetail(item)"
							v-for="item in listData">
							<h2>企业名称：{{item.QYMC}}  </h2>
							<h2>企业地址：{{item.QYDZ}}</h2>
							<h2>行业名称：{{item.HYMC}}</h2>
							<h2>组织机构代码：{{item.ZZJGDM}}</h2>
						</li>
						
						
						<li
							v-if="service == 'QUERY_WRY_JDXJC'"
							@click="showDetail(item)"
							v-for="item in listData">
							<h2>污染源名称：{{item.WRYMC}}</h2>
							<p>污染源地址：{{item.WRYDZ}}</p>
							<p>排污许可证编号：{{item.PWXKZBH}}</p>
							<p>组织机构代码：{{item.ZZJGDM}}</p>
							<p>营业执照注册号：{{item.YYZZZCH}}</p>
							<p>法定代表人：{{item.FDDBR}}</p>
							<p>法人联系电话：{{item.FRLXDH}}</p>
							<p>环保联系人：{{item.HBLXR || ''}}</p>
							<p>环保联系人电话: {{item.HBLXRDH}}</p>
						</li>
						
						<li
							v-if="service == 'QUERY_WRY_ZDJC'"
							@click="showDetail(item)"
							v-for="item in listData">
							<h2>单位名称：{{item.DWMC}}</h2>
							<p>地址：{{item.DZ}}</p>
							<p>排污许可证编号：{{item.PWXKZBH}}</p>
							<p>统一社会信用代码：{{item.TYSHXYD}}</p>
							<p>营业执照注册号：{{item.YYZZZCH}}</p>
							<p>环保联系人：{{item.HBLXR}}</p>
							<p>环保联系人电话：{{item.HBLXDH}}</p>
						</li>
						
					</ul>
				</scroll-view>
				<noData v-if="listData.length==0"></noData>
				
				
				 <!-- <web-view src="permit.mee.gov.cn/perxxgkinfo/xkgkAction!xkgk.action?xkgk=getxxgkContent&dataid=0086f7a94abf443c8e1e98b6553cedbf"></web-view> -->
			</div>
		</section>
    </Page>
</template>

<script>
	import { getPollutionList } from '@/api/pollution.js'
	import noData from '@/components/no-data.vue'
	import Page from '@/pages/component/Page.vue'
	
	import uriUtil from '@/common/net/uri.js'
	import bookService from '@/api/standing-book-service.js'
	
	export default {
		components: {
			Page,
			noData
		},
		data(){
			return {
				type: '', //CB DB TH
				pageNum: 1,
				pageSize: 10,
				listData: [],
				title: '',
				tag: '',
				recordId: '',
				service: '',
				templateId: '',
				pageHeight: 600,
				total: 0
			}
		},
		
		onLoad(options){
			this.title = options.title;  // 标题
			this.tag = options.tag
			this.recordId = options.recordId;  // 记录id
			this.service = options.service; // 模板id
			this.templateId = options.templateId;
			// this.getData();
			this.loadBusinessList()
		},
		
		methods:{
			changeType(type){
				this.type = type;
				this.listData = [];
				this.pageNum = 1;
				this.getData();
			},
			
			onReachBottomEvent (){
				this.pageNum++;
				this.getData();
			},
			
			loadBusinessList() {
				let categoryId = ''
				if(this.service) {
					let lastSlashIndex = this.service.lastIndexOf('/')
					categoryId = this.service.substring(lastSlashIndex + 1)
				}
				uni.showLoading({
					title: '正在加载数据',
				})
				bookService.queryWryBusinessList(categoryId, this.recordId)
					.then(resp => {
						uni.hideLoading()
						this.listData = resp.list
					})
			},
			
			getData(){
				let pageNum = this.pageNum;
				let pageSize = this.pageSize;
				let self = this;
				
				if(this.listData.length < this.total || this.listData.length == 0){
					let queryListParams = {
						service: self.service,
						pageNum,
						pageSize
					}
					
					switch(this.service){
						case 'QUERY_WRY_PWXK':  // 排污许可证
							queryListParams.TYSHXYDM = self.uniteId
							break;
						case 'QUERY_WRY_ZXJC':  //  在线监测
						default:
							queryListParams.recordId = self.recordId
							break;
					}		
					
					getPollutionList(queryListParams)
						.then(res=>{
							this.listData.push(...res.data_json.list);
							this.total = res.data_json.total;
						})
				}
			},
			
			showDetail(item){   
				this.log(item, '列表项')
				if(item.templateId) {
					this.goToDetail(item)
					return
				}
				
				if(item.server_path) {
					this.goBusinessList(item)
				}
			},
			
			// 模板详情列表
			goBusinessList(category) {
				let params = {
					title: category.name,
					tag: category.tag || '列表',
					recordId: category.recordId,
					service: category.server_path,
				}
				
				let listDestination = `/pages/main/PollutionDetailList?${uriUtil.transformObjectToUrlParams(params)}`
				uni.navigateTo({
					url: listDestination
				})
			},
			
			goToDetail(item) {
				let urlParams = {
					title: `${this.title}详情`,
					templateId: item.templateId,
					recordId: item.XH || item.recordId || item.WRYBH
				}
				uni.navigateTo({
					url: `/pages/main/TemplateDetail?${uriUtil.transformObjectToUrlParams(urlParams)}`
				})
			},
			
			onFixedContentHeight(layout) {
				this.pageHeight = layout.height;
			}
		}
	}
</script>

<style>
	.record-task-list {
		margin-top: 10px;
		background-color: #fff;
	}
	
	.record-task-item {
		padding: 10px 10px;
		width: calc(100% - 20px);
	}
	
	.record-task-item:active {
		background-color: #007AFF;
	}
	
	.record-task-type-icon {
		width: 36px;
		height: 36px;
	}
	
	.task-content-layout {
		flex: 1;
		margin: 0 10px;
	}
	
	.task-type {
		margin-left: auto;
		border-radius: 50px;
		padding: 2px 10px;
		color: #fff;
		background-color: #0FAEFF;
	}
</style>
