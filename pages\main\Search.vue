<template>
	<Page title="污染源">
		<view>
			<uni-search-bar
				placeholder="请输入污染源关键字"
				cancelButton="none"
				cancelText=""
				clearButton="always"
				bgColor="#F7F7F7"
				:radius="50"
				@input="handleKeyUp"
			/>
		</view>
		<ul v-if="showType" class="pd-ultbs1">
			<!-- <li class="on"><i>全部</i></li> -->
			<li :class="{on : type == 'WRY'}" @click="changeType('WRY')"><i>污染源</i></li>
			<!-- <li :class="{on : type == 'task'}" @click="changeType('task')"><i>任务</i></li> -->
			<!-- <li :class="{on : type == 'YJXX'}" @click="changeType('YJXX')"><i>预警</i></li> -->
			<!-- <li :class="{on : type == 'TZ'}" @click="changeType('TZ')"><i>台账</i></li> -->
			<li :class="{on : type == 'LXR'}" @click="changeType('LXR')"><i>联系人</i></li>
		</ul>
		<section class="pd-main">
			<div class="pd-inner pt2" style="padding:20rpx 0 100rpx 0">
				
				<!-- <div class="pd-tips">
					查询到 <i>5</i>条污染源记录
				</div> -->
				<dl class="pd-dlbx1" v-show="type=='WRY'" v-if="list.length>0">
					<dt><span>污染源<i>（{{total}}）</i></span></dt>
				</dl>
				<dl class="pd-dlbx1" v-show="type=='TZ'" v-if="list.length>0">
					<dt><span>台账<i>（{{total}}）</i></span></dt>
				</dl>
				<dl class="pd-dlbx1" v-show="type=='YJXX'" v-if="list.length>0">
					<dt><span>预警<i>（{{total}}）</i></span></dt>
				</dl>
				<dl class="pd-dlbx1" v-show="type=='LXR'" v-if="list.length>0">
					<dt><span>联系人<i>（{{total}}）</i></span></dt>
				</dl>
				<scroll-view class="record-task-list" scroll-y="true"  style="height: calc(100vh - 400rpx);"  @scrolltolower="onReachBottomEvent" v-if="list.length>0">
				<dl class="pd-dlbx1" v-for="item in list" :key="item.id">
					<div >
						<dd @click="showDetail(item)"  v-if="type=='WRY'">
							<image src="../../static/app/images/lstic1a.png" class="imgic" />
							<h2 v-html="item.title">{{item.title}}</h2>
							<p><em></em><span v-html="item.summary ? item.summary:'暂无详情'">
								</span></p>
							<!-- <small></small> -->
						</dd>
						<!-- <dd>
							<ul class="pd-ulbx2">
								<li><i class="lsts1">导航</i></li>
								<li><i class="lsts2">执法检查</i></li>
							</ul>
						</dd> -->
					</div>
				</dl>
			
				<dl class="pd-dlbx1" v-for="item in list" :key="item.ids">
					<dd  v-if="type=='TZ'">
						<image src="../../static/app/images/lstic2a.png" class="imgic" />
						<h2 v-html="item.title">{{item.title}}</h2>
						<p><em>检查人：</em><span v-html="item.summary ? item.summary:'暂无详情'"></span></p>
						<!-- <small></small> -->
					</dd>
				</dl>
				
				<dl class="pd-dlbx1" v-for="item in list" :key="item.ids">
					<dd  @click="showDetail(item)" v-if="type=='YJXX'" >
						<image src="../../static/app/images/lstic4a.png" class="imgic" />
						
						<h2>{{item.summary}}</h2>
						<p><em>任务期限：</em>{{item.createdate}}</p>
						<!-- <small></small> -->
					</dd>
				</dl>
				<!-- <dl class="pd-dlbx1" v-show="type=='task'" v-for="item in list">
					<dt><span>任务<i>（2）</i></span></dt>
					<dd>
						<img src="../../static/app/images/lstic3a.png" class="imgic" />
						<h2>广州市光电科技有限公司</h2>
						<p><em>任务期限：</em>2020.4.21</p>
						<small></small>
					</dd>
				</dl> -->
				
				<dl class="pd-dlbx1"  v-for="item in list" :key="item.ids">
					<dd  @click="showDetail(item)" v-if="type=='LXR'">
						<image src="../../static/app/images/lstic3a.png" class="imgic" />
						<h2 v-html="item.title">{{item.title}}</h2>
						<p><em></em><span v-html="item.summary ? item.summary:'暂无详情'">
							{{}}
							</span></p>
						<small></small>
					</dd>
				</dl>
				
				</scroll-view>
				<noData v-if="noDataType"></noData>
			</div>
		</section>
	</Page>
</template>

<script>
	import Page from '@/pages/component/Page.vue';
	import { getAllSearch } from '@/api/letter.js'
	import noData from '@/components/no-data.vue'
	import loginService from '@/api/login-service.js'
	
	export default {
		components: {noData,Page},
		data() {
			return {
				showType: false,
				search: '',
				type: 'WRY',
				pageSize: 10,
				pageNum: 1, 
				list: [],
				noDataType:false,
				total: 0,
				searchDebounceTimer: null
			}
		},
		
		mounted(){
			this.searchData();
		},
		
		methods:{
			showDetail(item){
				let id= item.id.split('_')[2];
				let title = '';
				let type = this.type;
				switch(this.type){
					case 'WRY':
						title = '污染源详情';
						break;
					case 'YJXX':
						title = '预警详情';
						break;
					case 'TZ':
						title = '台账详情';
						break;
					case 'LXR':
						title = '联系人详情';
						break;
				}
				
				
				if(this.type === 'WRY'){
					uni.navigateTo({
						url: `./PollutionDetail?title=${title}&recordId=${id}`
					})
				} else {
					uni.navigateTo({
						url: `./SearchDetail?title=${title}&recordId=${id}&type=${type}`
					})
				}
			},
			
			back(){
				uni.navigateBack()
			},
			
			handleKeyUp(params){
				let self = this
				if(this.searchDebounceTimer) {
					clearTimeout(self.searchDebounceTimer)
				}
				self.searchDebounceTimer = self.startKeywordSearch(params)
			},
			
			/**
			 * 触发关键字过滤搜索
			 */
			startKeywordSearch(params) {
				let self = this
				return setTimeout(() => {
					self.searchDebounceTimer = null
					self.search = params
					self.pageNum = 1;
					self.list = [];
					self.searchData();
				}, 800)
			},
			
			onReachBottomEvent (){
				this.pageNum++;
				this.searchData();
			},
			
			changeType(type){
				this.pageNum = 1;
				this.type = type;
				this.list = [];
				this.searchData();
			},
			
			searchData(){
				this.noDataType = false
				let userId = loginService.getAuthUserId()
				getAllSearch({
					text: this.search,
					dataType: this.type,
					userId: userId,
					pageIndex: this.pageNum,
					pageSize: this.pageSize
				}).then(res => {
					this.list.push(...res.data_json.datas);
					this.total = res.data_json.totalRecord;
					if(this.list.length === 0){
						this.noDataType = true
					}
				})
			}
		}
	}
</script>

<style>
.pd-ultbs1{
	top:0rpx;
	position: relative;
}

/* .pd-inner .pt2{
	padding-top: 40rpx;
} */

/* .pd-main{
	width: 100vw;
} */
.pd-dlbx1 + .pd-dlbx1{
	margin-top:14rpx;
}
</style>
