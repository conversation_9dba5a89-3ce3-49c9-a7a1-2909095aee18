<template>
	<Page class="power-page" :title="title" @layoutAware="onFixedContentHeight">
		<view class="template-form-layout">
			<template-form
			:form-data="formData"
			:template="template"
			:editable="editable"
			:parentHeight="formLayoutHeight"
			>
			</template-form>
		</view>
	</Page>
</template>

<script>
	import formUtil from '@/pages/form/Form.js';
	import Page from '@/pages/component/Page.vue'
	import templateForm from '@/pages/form/template-form.vue'
	import {
	    postQueryWryjcTzForm,
	    postQueryWryjcTzInfo
	} from '@/api/book.js'
	export default {
		data(){
			return {
				title: '',
				recordId: '',
				templateId: '',
				formData: {},
				template: {},
				editable: false,
				pageHeight: 600
			}
		},
		
		computed: {
		    formLayoutHeight: function() {
					return this.pageHeight - 40
				}
		},
		
		components:{
			Page,
			templateForm
		},
			
		onLoad(options){
			this.title = options.title;
			this.recordId = options.recordId;
			this.type = options.type;
			
			switch(this.type){
				case 'WRY':
					this.templateId = '20201218165503e92b3a0b9ad94f2db10d84af575224ff';
					break;
				case 'YJXX':
					this.templateId = '2020111914363163927d890beb4e70be99f561276f63c2';
					break;
				case 'TZ':
					this.templateId = '2020111014083832fdc394d7734dd4b0bdb87c1287253f';
					break;
				case 'LXR':
					this.templateId = '202011101012035018c1d2cf09460ca74869062d9d538a';
					break;
			}
			
			this.getData();
		},
		
		methods:{
			onFixedContentHeight(layout) {
				this.pageHeight = layout.height;
			},
			
			getData(){
				postQueryWryjcTzForm({
					service:  'QUERY_DYNAMICFORM_DATA',
					recordId: this.recordId,
					mbbh: this.templateId
				}).then((res)=>{
					this.formData = res.data;
					this.getTemplate();
				})
			},
			
			getTemplate(){
				
				
				let userInfo = uni.getStorageSync('userInfo');
				let userId = '';
				if (userInfo) {
					userId = userInfo.id;
				}
				postQueryWryjcTzForm({
					service:  'GET_DYNAMICFORM_MODEL',
					bbxh:  this.templateId,
					userId: userId || 'SDSZFJ'
				}).then((res)=>{
					this.template = formUtil.packPeerElementIntoGroup(res.datas_json)
				
					// 加载附件
				this.$templateUtils.loadAttachRecord(this.templateId, this.recordId, this.formData, this)
				})
			}
		}
		
	}
</script>

<style>
</style>
