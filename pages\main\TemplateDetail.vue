<!--
 * @Author: your name
 * @Date: 2021-03-08 22:42:57
 * @LastEditTime: 2021-03-23 15:14:28
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /SmartFormWeb/pages/main/TemplateDetail.vue
-->
<template>
	<Page :padding="false" class="power-page" :title="title" @layoutAware="onFixedContentHeight">
		<view class="template-form-layout">
			<template-form style="height: calc(100vh - 120rpx); overflow: auto;" :form-data="formData"
				:template="template" :editable="editable" :parentHeight="formLayoutHeight" />
		</view>
	</Page>
</template>

<script>
	import TemplateUtils from '@/common/template.utils.js';
	import formUtil from '@/pages/form/Form.js';
	import Page from '@/pages/component/Page.vue'
	// import templateForm from '@/pages/form/template-form.vue'
	import {
		queryRecordData
	} from '@/api/record.js'
	import {
		postQueryWryjcTzForm
	} from '@/api/book.js'

	export default {
		data() {
			return {
				title: '',
				recordId: '',
				templateId: '',
				formData: {},
				template: {},
				editable: false,
				pageHeight: 600
			}
		},

		computed: {
			formLayoutHeight: function() {
				return this.pageHeight - 120
			}
		},

		components: {
			Page
		},

		onLoad(options) {
			this.title = options.title; // 标题
			this.recordId = options.recordId; // 记录id
			this.templateId = options.templateId; // 模板id
			this.getData();
		},

		methods: {
			onFixedContentHeight(layout) {
				this.pageHeight = layout.height;
			},

			/**
			 * 加载表单数据
			 */
			getData() {
				uni.showLoading({
					title: '正在加载数据'
				})
				queryRecordData(this.templateId, this.recordId)
					.then(formData => {
						uni.hideLoading()
						this.formData = formData || {}
						this.getTemplate()
					})
			},

			getTemplate() {
				let self = this
				let userInfo = uni.getStorageSync('userInfo');
				let userId = '';
				if (userInfo) {
					userId = userInfo.id;
				}
				postQueryWryjcTzForm({
					service: 'GET_DYNAMICFORM_MODEL',
					bbxh: this.templateId,
					userId: userId || 'SDSZFJ'
				}).then((res) => {
					uni.hideLoading()
					formUtil.alignTemplateMajorAttachGroup(res.datas_json)
						.then(template => {
							self.template = formUtil.packPeerElementIntoGroup(template)
						})
				}).catch(e => {
					uni.hideLoading()
				})
			}
		}

	}
</script>

<style>
</style>