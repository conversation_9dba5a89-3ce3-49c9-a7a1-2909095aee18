<!--
 * @Author: 姚进玺 <EMAIL>
 * @Date: 2021-03-23 12:22:37
 * @LastEditors: 姚进玺 <EMAIL>
 * @LastEditTime: 2022-10-28 14:40:51
 * @FilePath: /UNI_APP_ShanDong/pages/main/portal-menu-item.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
    <view class="flex-column-layout portal-menu-item" @click="naviMenu">
        <uni-badge
            :text="menu.badgeNum"
            type="primary"
            size="small"
            class="badge"
            v-if="menu.badgeNum > 0"
        ></uni-badge>
        <image class="menu-icon" mode="aspectFit" :src="menu.icon" />
        <text class="menu-name">
            {{ menu.name }}
        </text>
    </view>
</template>

<script>
export default {
    props: {
        menu: {
            type: Object,
            default: () => {
                return {
                    id: 'menuId',
                    name: '菜单名称',
                    icon: '',
                    url: '',
                    badgeNum: 0
                };
            }
        }
    },

    methods: {
        naviMenu() {
            if (this.menu.url) {
                console.log(`导航到：${this.menu.url}`);
                uni.navigateTo({
                    url: this.menu.url
                });
            } else {
                alert('未定义菜单地址');
            }
        }
    }
};
</script>

<style scoped>
.portal-menu-item {
    background-color: #fff;
    margin-top: 1px;
    margin-left: 1px;
}

.portal-menu-item:active {
    filter: hue-rotate(180deg);
}

.portal-menu-item:nth-child(3n + 1) {
    margin-left: 0;
}

.portal-menu-item:nth-child(-n + 3) {
    margin-top: 0;
}

.menu-icon {
    width: 48px;
    height: 48px;
}

.menu-name {
    margin-top: 10px;
    font-size: 16px;
    color: #999;
}

.badge {
    float: right;
    position: relative;
    right: -50rpx;
}
</style>
