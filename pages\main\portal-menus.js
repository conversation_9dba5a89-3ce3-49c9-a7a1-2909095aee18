/*
 * @Author: 姚进玺 <EMAIL>
 * @Date: 2022-11-14 18:06:57
 * @LastEditors: 姚进玺 <EMAIL>
 * @LastEditTime: 2022-11-15 11:10:18
 * @FilePath: /UNI_APP_ShanDong/pages/main/portal-menus.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import zfrwIcon from '@/static/login/images/ulic2.png'; //执法任务
import jdbfIcon from '@/static/login/images/ulic1.png'; //监督帮扶
import yjtxIcon from '@/static/login/images/ulic3.png'; //预警提醒
import wryIcon from '@/static/img/portal/menu_icon_wry.png'; //污染源
import zgdbIcon from '@/static/img/portal/menu_zgdb.png'; //整改督办
import zdjkcon from '@/static/login/images/zdjk.png'; //振动监控
import zftzIcon from '@/static/login/images/ulic6.png'; //执法台账
import ydjkIcon from '@/static/login/images/ulic5.png'; //用电监控
import zyclIcon from '@/static/login/images/ulic7.png'; //裁量计算器
import hbzkIcon from '@/static/login/images/ulic8.png'; //环保知库
import xxksIcon from '@/static/img/portal/menu_learning.png'; //执法云学堂
import xfyqIcon from '@/static/login/images/ulic4.png'; //信访舆情
import wtfxIcon from '@/static/login/images/lsic1.png'; //问题发现

import { queryXcjcRwsl, getUserInfoPermissions } from '@/api/predit.js';
const H5_APP_SCHEME = 'h5://';

//图片的映射
const Portal_ICON_MENUS = new Map([
  ['ZFRW', zfrwIcon],//执法任务
  ['JDBF', jdbfIcon],//监督帮扶
  ['YJTX', yjtxIcon],//预警提醒
  ['WRY', wryIcon],//污染源
  ['ZGDB', zgdbIcon],//整改督办
  ['ZGJK', zdjkcon],//振动监控
  ['ZFTZ', zftzIcon],//执法台账
  ['YDJK', ydjkIcon],//用电监控
  ['ZYCL', zyclIcon],//裁量计算器
  ['HBZK', hbzkIcon],//环保智库
  ['XXKS', xxksIcon],//学习考试
  ['XFYQ', xfyqIcon],//信访舆情
  ['WTFX', wtfxIcon],//问题发现
])

//路径的跳转映射
const Portal_URL_MENUS = new Map([
  ['ZFRW', '/pages/task/task-list'],//执法任务
  ['JDBF', '/pages/support/layout'],//监督帮扶
  ['YJTX', '/pages/predit/Layout'],//预警提醒
  ['WRY', './Search'],//污染源
  ['ZGDB', '/pages/rectify/rectify-list'],//整改督办
  ['ZGJK', 'vibration'],//振动监控
  ['ZFTZ', '/pages/book/book-list'],//执法台账
  ['YDJK', `${H5_APP_SCHEME}power-consume`],//用电监控
  ['ZYCL', '/pages/freedom/freedom'],//裁量计算器
  ['HBZK', 'lawsBook'],//环保智库
  ['XXKS', `/pages/study/study`],//学习考试
  ['XFYQ', '/pages/task/dispatch-task'],//信访舆情
  ['WTFX', '/pages/task/wtfx/wtfx-list'],//问题发现
])


export default {
  getMenus(){
    return new Promise((resolve, reject) => {
      getUserInfoPermissions({
        CDLX:'SYCDLX',
        YHID:uni.getStorageSync('userInfo').id
      }).then((res)=>{
        let data = res.data_json
        data.forEach(element => {
            element.icon = Portal_ICON_MENUS.get(element.CDWYBZ)
            element.url = Portal_URL_MENUS.get(element.CDWYBZ)
        });
        resolve(data)
      })
    })

  }
}
