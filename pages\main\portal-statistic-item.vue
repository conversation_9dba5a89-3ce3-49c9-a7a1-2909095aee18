<template>
	<view class="flex-column-layout statistic-item">
		<text class="statistic-value">{{value}}</text>
		<text class="statistic-name">{{name}}</text>
	</view>
</template>

<script>
	export default {
		props: {
			name: {
				type: String,
				default: '统计项'
			},
			
			value: {
				type: [String, Number],
				default: '-'
			},
			
			color: {
				type: String,
				default: '#03a1fa'
			}
		}
	}
</script>

<style scoped>
	.statistic-item {
		flex: 1;
	}
	
	.statistic-name {
		font-size: 16px;
		color: #999;
		margin-top: 10px;
		text-align: center;
	}
	
	.statistic-value {
		font-size: 22px;
		text-align: center;
	}
</style>
