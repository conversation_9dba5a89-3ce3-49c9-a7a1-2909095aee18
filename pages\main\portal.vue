<template>
	<view style="width: 100%">
		<header class="pd-header idx">
			<div class="pd-name" @click="openMenu('/pages/mine/my')">
				{{ userName || "SDSZFJ" }}
			</div>
			<image src="../../static/img/logo_water_mark.png" class="pd-logo1" @click="show_vconsole" />
			<div class="gap"></div>
			<div class="gap"></div>
			<div class="gap"></div>
			<input type="text" :disabled="true" class="pd-inpsrh1" placeholder="请输入污染源" @click="openMenu('./Search')" />

			<ul class="pd-ulbx3">
				<li v-for="(item, index) in statistics" :key="item.id" @click="goStatistics(index)">
					<h2>{{ item.name }}</h2>
					<p v-show="index != 0">{{ item.value }}</p>
					<p v-show="index == 0" style="color: #e76565">
						{{ item.value }}
					</p>
				</li>
			</ul>
		</header>

		<section class="pd-main">
			<show-modal></show-modal>
			<div class="pd-inner idx">
				<scroll-view :scroll-y="true" :style="pageListStyle">
					<ul class="pd-ulbx4" v-if="isH5">
						<li @click="openMenu(item.url)" v-for="item in protalMenus" :key="item.id" v-if="
                item.CDMC !== '环保知库'
              ">
							<i v-if="item.badgeNum > 0">{{ item.badgeNum }}</i>
							<image :src="item.icon" alt="" />
							<p>{{ item.CDMC || "--" }}</p>
						</li>
					</ul>
					<ul class="pd-ulbx4" v-else>
						<li @click="openMenu(item.url)" v-for="item in protalMenus" :key="item.id">
							<i v-if="item.badgeNum > 0">{{ item.badgeNum }}</i>
							<image :src="item.icon" alt="" />
							<p>{{ item.CDMC || "--" }}</p>
						</li>
					</ul>
				</scroll-view>
			</div>
			<u-modal
				title="提示"
				confirm-text="修改密码"
				:title-style="{
				color: 'red'
				}"
				v-model="changePassword.show"
				:content="changePassword.content"
				@confirm="toChangePassword"
			></u-modal>
		</section>
	</view>
</template>

<script type="text/javascript" src="https://unpkg.com/@dcloudio/uni-webview-js@0.0.3/index.js"></script>
<script>
	import portStatisticItem from "./portal-statistic-item.vue";
	import portalMenuItem from "./portal-menu-item.vue";
	import {
		postQueryGgdmj,
		checkCsmm
	} from "@/api/record.js";
	import styleUtil from "@/common/style.js";
	import portal from "./portal-menus.js";
	import {
		queryXcjcRwsl
	} from "@/api/predit.js";
	import {
		LOGIN_ULR_BASE,
		isH5
	} from "@/common/config.js";
	import refreshLocation from "@/api/refresh-location.js";
	// #ifdef H5
	import vconsole from "vconsole";
	// #endif

	// #ifdef APP-PLUS
	// import '@/common/push-center.js'
	// #endif

	const H5_APP_SCHEME = "h5://";

	export default {
		components: {
			portStatisticItem,
			portalMenuItem
		},

		data() {
			return {
				changePassword: {
					show: false,
					content: "检测到您的密码是初始密码，请重新修改密码后再使用！"
				},
				lastClickTime: 0,
				vconsoleCount: 0, //vonsole调试工具计数展示
				isH5: isH5,
				protalMenus: [],
				areaCode: "", //地区行政码
				h5AppScheme: H5_APP_SCHEME,
				userName: "", //用户名
				screenWidth: -1,
				taskTypeOptions: [], //任务类型集合
				statistics: [{
						name: "超期任务",
						value: "0"
					},
					{
						name: "累计执法",
						value: "0"
					},
					{
						name: "发现问题数",
						value: "0"
					},
					{
						name: "检查企业",
						value: "0"
					}
				]
			};
		},

		computed: {
			pageListStyle() {
				return {
					height: "calc(100vh - 580rpx)"
				};
			}
		},

		onShow() {
			//烟台特供情况，每次回到首页都会情况该缓存
			uni.removeStorageSync("YANTAI_TEMPLATE");

			// 检查是否是初始密码
			this.checkCsmm()

			// 刷新角标
			queryXcjcRwsl().then((res) => {
				this.statistics[0].value = res.data_json.rwcqsl;
				this.statistics[1].value = res.data_json.ljzf;
				this.statistics[2].value = res.data_json.fxwt;
				this.statistics[3].value = res.data_json.jcqy;
				this.protalMenus[0].badgeNum = res.data_json.zfrw;


				// TODO 此处有问题，先写死
				if (this.protalMenus[2].CDMC == '预警提醒') {
					this.protalMenus[2].badgeNum = res.data_json.yjtx;
				}

				if (this.protalMenus[1].CDMC == '预警提醒') {
					this.protalMenus[1].badgeNum = res.data_json.yjtx;
				}

				if (this.protalMenus[9].CDMC == '监督帮扶') {
					this.protalMenus[9].badgeNum = res.data_json.jdbfnum;
				}

				if (this.protalMenus[1].CDMC == '监督帮扶') {
					console.log(res.data_json.jdbfnum)
					this.protalMenus[1].badgeNum = res.data_json.jdbfnum;
				}
			});
		},

		mounted() {
			this.getUserInfoPermissions();
			this.getTaskType(); //获取公共代码集--任务类型
			let userInfo = uni.getStorageSync("userInfo");
			if (userInfo) {
				this.userName = userInfo.name;
				this.areaCode = userInfo.orgid.substring(0, 4);
			}
			let _self = this;
			styleUtil.getScreenLayout().then((layout) => {
				_self.screenWidth = layout.width;
			});

			// #ifdef APP-PLUS
			this.checkAppUpgrade();
			refreshLocation.startFixLocationInterval();
			//每次进入应用，清除角标
			// plus.runtime.setBadgeNumber(0)
			// #endif
		},

		methods: {
			getUserInfoPermissions() {
				portal.getMenus().then((res) => {
					this.protalMenus = res;
				});
			},

			checkCsmm() {
				checkCsmm({
					pageNum:1,
					pageSize:20,
					params: {
						APPYHID: uni.getStorageSync('user_info').id || uni.getStorageSync('userInfo').xtzh,

					},
				}).then((res) => {
					if (res.data_json.success) {
						this.changePassword.show = true;
					}
				});
			},

			toChangePassword() {
				uni.navigateTo({
					url: `/pages/mine/change-password`
				});
			},


			//调用原生代码编写的环保知库
			openEnviormentLawsBook() {
				let Intent = plus.android.importClass("android.content.Intent");
				let book = new Intent(Intent.ACTION_VIEW);
				let Uri = plus.android.importClass("android.net.Uri");
				let bookUri = Uri.parse("boandahbzk:///main");
				book.setData(bookUri);
				book.setPackage(plus.android.runtimeMainActivity().getPackageName());
				plus.android.runtimeMainActivity().startActivity(book);
			},

			//跳转菜单相应页面
			openMenu(url) {
				if ("undefined" === url || url === null) {
					return;
				}

				if (url === "vibration") {
					uni.navigateTo({
						url: `/pages/web/web-page?page=${url}`
					});
				}

				if (url === "lawsBook") {
					this.openEnviormentLawsBook();
				}

				if (url.endsWith("placeholder")) {
					this.showUndevelopedMenuTip();
					return;
				}

				if (url.startsWith(H5_APP_SCHEME)) {
					let pageName = url.substring(H5_APP_SCHEME.length);
					uni.navigateTo({
						url: `/pages/web/web-page?page=${pageName}`
					});
					return;
				}
				uni.navigateTo({
					url: url
				});
			},

			launchThirdPartyApp(appUrl) {
				// #ifdef APP-PLUS
				let appStarter = plus.android.importClass(
					"com.bovosz.webapp.app.AppStarter"
				);
				let AppPlugin = plus.android.importClass(
					"com.bovosz.webapp.app.AppPlugin"
				);

				let context = plus.android.runtimeMainActivity();
				let appPlugin = new AppPlugin();
				appPlugin.setName("用电监控");
				appPlugin.setPackageName("android.UNIB9738F2");
				appPlugin.setSymbol("YDJK");
				appPlugin.setDownloadUrl(`${LOGIN_ULR_BASE}/`);

				let params = {
					username: "liujiqiang",
					password: "88a16a28e9d99f29@"
				};
				appStarter.startPluginApp(context, appPlugin, JSON.stringify(params));
				// #endif
			},

			//未开发菜单提示
			showUndevelopedMenuTip() {
				this.$showModal({
					title: "菜单提醒",
					content: "功能正在开发中，敬请期待"
				});
			},

			getTaskType() {
				//获取公共代码集--任务类型-并将其保存后用于其他页面
				postQueryGgdmj({
					DMJBH: "YDZF_RWLX"
				}).then((res) => {
					res.data_array.forEach((element, index) => {
						let list = {};
						list.value = element.DM;
						list.text = element.DMNR;
						this.taskTypeOptions.push(list);
					});
					//单独处理一个全部的情况
					let all = {};
					all.value = "";
					all.text = "全部任务类型";
					this.taskTypeOptions.unshift(all);
					uni.setStorage({
						key: "YDZF_RWLX_DATA",
						data: this.taskTypeOptions
					});
				});
			},

			//页面跳转
			goStatistics(index) {
				let url;
				switch (index) {
					case 0:
						url = `/pages/task/task-list?sfcq=0`;
						break;
					case 1:
						url = `/pages/book/book-list?JCR=0&TITLE=累计执法`;
						break;
					case 2:
						url = `/pages/book/book-list?JCR=0&SFFXWT=1&TITLE=发现问题数`;
						break;
					case 3:
						url = `/pages/book/book-list?JCWRY=1&JCR=0&TITLE=检查企业`;
						break;
				}
				uni.navigateTo({
					url: url
				});
			},

			// #ifdef APP-PLUS
			checkAppUpgrade() {
				let upgradeManagerClassName = "com.bovosz.webapp.app.AppUpgradeManager";
				let AppUpgradeManager = plus.android.importClass(upgradeManagerClassName);
				if (AppUpgradeManager) {
					let upgradeManager = new AppUpgradeManager(
						plus.android.runtimeMainActivity()
					);
					upgradeManager.checkUpgrade(`${LOGIN_ULR_BASE}/`);
				}
			},
			// #endif
			//控制显示vconsole
			show_vconsole() {
				// #ifdef H5
				const nowTime = new Date().getTime();
				if (nowTime - this.lastClickTime < 3000) {
					this.vconsoleCount++;
				} else {
					this.vconsoleCount = 0;
				}
				this.lastClickTime = nowTime;
				if (this.vconsoleCount >= 10) {
					this.$vconsole = new vconsole();
					this.vconsoleCount = 0;
				}
				// #endif
			}
		}
	};
</script>

<style scoped>
	.show {
		display: block !important;
	}

	.upfont {
		font-size: 2rem;
	}

	.pd-ulbx4 li {
		position: relative;
	}

	.pd-ulbx4 li i {
		position: absolute;
		left: 50%;
		top: 13%;
		min-width: 15px;
		width: auto;
		height: 15px;
		border-radius: 50px;
		background: #e86767;
		font-size: 21.7391249rpx;
		color: #fff;
		text-align: center;
		line-height: 28.985475rpx;
		margin-left: 36.2319rpx;
	}

	.pd-ulbx4 li:active {
		/* background-color: #0FAEFF; */
		color: #fff;
	}

	.uni-input-placeholder {
		color: white;
	}

	.portal {
		width: 100%;
		height: 100%;
		position: relative;
	}

	.portal-scene-layout {
		position: relative;
		justify-content: flex-start;
		padding: 10px;
		width: calc(100% - 20px);
		height: 40%;
		background: linear-gradient(#0084ff, #86e5f7);
		opacity: 0.7;
		z-index: 99;
	}

	.portal-menu-layout {
		position: absolute;
		display: flex;
		flex-flow: column nowrap;
		padding: 0 10px 10px 10px;
		width: calc(100% - 20px);
		height: 60%;
		bottom: 0;
		background-color: #f4f4f4;
	}

	.portal-statistic-layout {
		height: 80px;
		border-radius: 5px;
		background-color: #fff;
		z-index: 999;
	}

	.portal-menu-grid {
		display: flex;
		flex-flow: row wrap;
		width: 100%;
		height: 80%;
		margin-top: 10px;
	}

	.portal-user-avatar {
		width: 32px;
		height: 32px;
	}

	.portal-user-name {
		font-size: 16px;
		color: #fff;
	}

	.portal-logo {
		width: 64px;
		height: 64px;
		margin-top: 10px;
	}

	.portal-app-name {
		margin-top: 10px;
		font-size: 28px;
		color: #fff;
	}

	.portal-search-input {
		position: absolute;
		bottom: 52px;
		width: 90%;
		height: 42px;
		line-height: 42px;
		border-radius: 50px;
		font-size: 16px;
		color: #fff;
		background-color: rgba(255, 255, 255, 0.3);
		text-align: center;
	}
</style>