<template>
	<Page class="power-page" :title="title">
		<button type="primary" @click="fixLocation">定位</button>
		<template-form
			style="width: 100%;"
			:template="templateConfig"
			:editable="true"
		/>
	</Page>
</template>

<script>
	import Page from '@/pages/component/Page.vue';
	// import AMap from 'vue-amap';
	import templateConfig from '@/pages/form/config.json';
	import templateForm from '@/pages/form/template-form.vue';
	import elementGroup from '@/pages/form/element-group.vue';
	
	export default {
		components: {
			Page, elementGroup, templateForm
		},
		
		data() {
			return {
				title: '测试定位',
				templateConfig: templateConfig,
				template: {
					type: 'GROUP_ELEMENT',
					label: '基本信息',
					child: [
						{"label":"被检查单位","isshow":"","readonly":"","must":"1","dbcolum":"BJCDW","defaultvalue":"京口区卫生监督所","type":"TEXT_ELEMENT"},
						{"label":"地址","must":"1","dbcolum":"DZ","type":"TEXT_ELEMENT"},
						{"label":"检查场所","isshow":"","readonly":"","must":"1","dbcolum":"JCCS","type":"TEXT_ELEMENT"},
						{"label":"假类","isshow":"","readonly":"","must":"1","dbcolum":"DXX","type":"RADIO_ELEMENT"},
					]
				}
			}
		},
		
		methods: {
			fixLocation(){
				uni.showLoading({
					title: '正在定位'
				})
				let _self = this;
				try{
					if(window.AMap){
						alert(`存在`)
					} else {
						alert('不存在')
					}
					AMap.plugin('AMap.Geolocation', () => {
						let geolocation = new AMap.Geolocation({
						    // 是否使用高精度定位，默认：true
						    enableHighAccuracy: true,
						    // 设置定位超时时间，默认：无穷大
						    timeout: 10000,
						    // 定位按钮的停靠位置的偏移量，默认：Pixel(10, 20)
						    buttonOffset: new AMap.Pixel(10, 20),
						    //  定位成功后调整地图视野范围使定位位置及精度范围视野内可见，默认：false
						    zoomToAccuracy: true,     
						    //  定位按钮的排放位置,  RB表示右下
						    buttonPosition: 'RB'
						  })
						
						  geolocation.getCurrentPosition()
						  AMap.event.addListener(geolocation, 'complete', (data) => {
							  uni.hideLoading();
							  alert(`定位成功：${data.formattedAddress}`);
						  })
						  AMap.event.addListener(geolocation, 'error', (error) => {
							  uni.hideLoading();
							  alert(`定位失败：${JSON.stringify(error, null, 4)}`);
						  })
					});
				}catch(e){
					alert(`定位出错：${JSON.stringify(e, null, 4)}`);
				}

			}
		}
	}
</script>

<style scoped>

</style>
