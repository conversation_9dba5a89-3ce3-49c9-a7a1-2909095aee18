<!--
 * @Author: your name
 * @Date: 2021-05-07 17:44:23
 * @LastEditTime: 2021-05-22 18:03:21
 * @LastEditors: your name
 * @Description: In User Settings Edit
 * @FilePath: /UNI_APP_ShanDong/pages/media/video-player.vue
-->
<template>
	<video 
		id="videoPlayer" 
		:style="{ height: windowHeight + 'px' }" style="width: 100%;"
		:src="videoUrl"
		:autoplay ='true'
		@error="videoErrorCallback" 
	/>
</template>

<script>
	export default {
		data() {
			return {
				windowHeight: 0,
				videoUrl: ''
			}
		},
		
		onLoad(options) {
			
			this.videoUrl = options.videoUrl;
		},
		
		mounted: function (res) {
			var that = this;
			uni.getSystemInfo({
			    success: function (res) {
					that.windowHeight = res.windowHeight;
			    }
			});
		},
		methods: {
			videoErrorCallback: function(e) {
				console.log(e)
			},
		}
	}
</script>