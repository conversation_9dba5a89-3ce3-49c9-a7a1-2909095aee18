<template>
	<view class="demo-page">
		<view class="header">
			<text class="page-title">内网迁移通知演示</text>
		</view>
		
		<view class="demo-content">
			<view class="info-card">
				<text class="info-title">组件功能</text>
				<text class="info-text">• 显示内网迁移重要通知</text>
				<text class="info-text">• 支持查看附件操作说明图片</text>
				<text class="info-text">• 用户确认后可记录查看状态</text>
			</view>
			
			<view class="button-group">
				<button class="demo-button primary" @click="showNotice">
					显示迁移通知
				</button>
				
				<button class="demo-button" @click="resetViewStatus">
					重置查看状态
				</button>
				
				<button class="demo-button" @click="checkViewStatus">
					检查查看状态
				</button>
			</view>
			
			<view class="status-info">
				<text class="status-label">当前状态：</text>
				<text class="status-value" :class="hasViewed ? 'viewed' : 'not-viewed'">
					{{ hasViewed ? '已查看' : '未查看' }}
				</text>
			</view>
		</view>
		
		<!-- 内网迁移通知组件 -->
		<migration-notice 
			:show.sync="noticeVisible" 
			:attachment-image="attachmentImagePath"
			:mask-closable="false"
			@confirm="handleNoticeConfirm"
			@close="handleNoticeClose"
		></migration-notice>
	</view>
</template>

<script>
import MigrationNotice from '@/components/migration-notice/migration-notice.vue'

export default {
	name: 'migration-notice-demo',
	components: {
		MigrationNotice
	},
	data() {
		return {
			noticeVisible: false,
			attachmentImagePath: '/static/images/migration-guide.png',
			hasViewed: false
		}
	},
	
	onLoad() {
		// 页面加载时检查用户是否已查看通知
		this.checkViewedStatus();
		
		// 如果用户未查看，自动显示通知（可选）
		// if (!this.hasViewed) {
		//   setTimeout(() => {
		//     this.noticeVisible = true;
		//   }, 1000);
		// }
	},
	
	methods: {
		// 显示通知
		showNotice() {
			this.noticeVisible = true;
		},
		
		// 处理通知确认
		handleNoticeConfirm() {
			console.log('用户确认了内网迁移通知');
			
			// 记录用户已查看状态
			this.recordViewedStatus();
			
			uni.showToast({
				title: '已确认通知',
				icon: 'success',
				duration: 2000
			});
		},
		
		// 处理通知关闭
		handleNoticeClose() {
			console.log('用户关闭了内网迁移通知');
		},
		
		// 记录已查看状态
		recordViewedStatus() {
			try {
				uni.setStorageSync('migration_notice_viewed', true);
				uni.setStorageSync('migration_notice_view_time', new Date().getTime());
				this.hasViewed = true;
				
				// 这里也可以调用后端API记录状态
				// this.callApiToRecordStatus();
			} catch (e) {
				console.error('记录查看状态失败:', e);
			}
		},
		
		// 检查查看状态
		checkViewedStatus() {
			try {
				this.hasViewed = uni.getStorageSync('migration_notice_viewed') || false;
			} catch (e) {
				console.error('检查查看状态失败:', e);
				this.hasViewed = false;
			}
		},
		
		// 重置查看状态
		resetViewStatus() {
			try {
				uni.removeStorageSync('migration_notice_viewed');
				uni.removeStorageSync('migration_notice_view_time');
				this.hasViewed = false;
				
				uni.showToast({
					title: '状态已重置',
					icon: 'success'
				});
			} catch (e) {
				console.error('重置状态失败:', e);
			}
		},
		
		// 检查查看状态（按钮触发）
		checkViewStatus() {
			this.checkViewedStatus();
			
			const viewTime = uni.getStorageSync('migration_notice_view_time');
			let message = this.hasViewed ? '用户已查看通知' : '用户未查看通知';
			
			if (viewTime) {
				const date = new Date(viewTime);
				message += `\n查看时间：${date.toLocaleString()}`;
			}
			
			uni.showModal({
				title: '查看状态',
				content: message,
				showCancel: false
			});
		}
	}
}
</script>

<style lang="scss" scoped>
.demo-page {
	min-height: 100vh;
	background: #f5f5f5;
	padding: 40rpx;
	
	.header {
		text-align: center;
		margin-bottom: 60rpx;
		
		.page-title {
			font-size: 40rpx;
			font-weight: bold;
			color: #333333;
		}
	}
	
	.demo-content {
		.info-card {
			background: #ffffff;
			border-radius: 20rpx;
			padding: 40rpx;
			margin-bottom: 40rpx;
			box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
			
			.info-title {
				display: block;
				font-size: 32rpx;
				font-weight: bold;
				color: #333333;
				margin-bottom: 20rpx;
			}
			
			.info-text {
				display: block;
				font-size: 28rpx;
				color: #666666;
				line-height: 40rpx;
				margin-bottom: 10rpx;
			}
		}
		
		.button-group {
			margin-bottom: 40rpx;
			
			.demo-button {
				width: 100%;
				height: 88rpx;
				border: none;
				border-radius: 16rpx;
				font-size: 32rpx;
				margin-bottom: 20rpx;
				background: #ffffff;
				color: #333333;
				box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
				
				&.primary {
					background: #009BFF;
					color: #ffffff;
				}
				
				&::after {
					border: none;
				}
			}
		}
		
		.status-info {
			background: #ffffff;
			border-radius: 20rpx;
			padding: 30rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			
			.status-label {
				font-size: 28rpx;
				color: #666666;
				margin-right: 20rpx;
			}
			
			.status-value {
				font-size: 28rpx;
				font-weight: bold;
				
				&.viewed {
					color: #52c41a;
				}
				
				&.not-viewed {
					color: #ff4d4f;
				}
			}
		}
	}
}
</style>
