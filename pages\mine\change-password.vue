<template>
	<Page
		title="修改密码"
		:mainStyle="mainStyle">
		<view 
			class="flex-column-layout"
			style="padding: 0px 10%; width: 80%;">
			<input  
				ref="old"
				class="change-password-input"
				placeholder="原密码"
				v-model="oldPass"
				:password="!showPassword"
				:style="oldInputStyle"
				@focus="onInputFocus('old')"
			/>
			<text
				class="change-password-valid"
				v-show="!isOldTyped">
				请输入原密码
			</text>
			
			<input  
				ref="new"
				class="change-password-input"
				placeholder="请至少包含字母、数字、特殊字符中的至少3种且长度为8-16个字符"
				v-model="newPass"
				:password="!showPassword"
				:style="newInputStyle"
				@focus="onInputFocus('new')"
			/>
			<text 
				class="change-password-valid"
				v-show="isPasswordInvalid">
				请至少包含字母、数字、特殊字符中的至少3种且长度为8-16个字符
			</text>
			
			<input
				ref="confirm"
				class="change-password-input"
				placeholder="确认新密码"
				v-model="confirmPass"
				:password="!showPassword"
				:style="confirmInputStyle"
				@focus="onInputFocus('confirm')"
			/>
			<text 
				class="change-password-valid"
				v-show="!passwordConfirmed">
				密码不一致
			</text>
			
			<button
				class="change-password-confirm"
				type="primary"
				@click="commitChange">
				提交修改
			</button>
			<checkbox 
				style="width: 100%; zoom: 80%; margin-top: 10px;"
				:checked="showPassword"
				@click="togglePasswordVisibility">
				显示密码
			</checkbox>

			<show-modal></show-modal>
		</view>
	</Page>
</template>

<script>
	import Page from '@/pages/component/Page.vue';
	import securityService from '@/api/security-service.js';
	import {
	    postPassword
	} from '@/api/book.js'
	export default {
		components: {
			Page
		},
		
		data() {
			return {
				mainStyle: {
					'background-color': '#fff',
					padding: '0px'
				},
				oldPass: '',
				newPass: '',
				confirmPass: '',
				showPassword: false,
				focusRef: ''
			}
		},
		
		computed: {
			oldInputStyle: function(){
				let color = this.focusRef === 'old' ? '#1E8EEF' : '#C0C0C0';
				return {
					'border-bottom': `1px solid ${color}`
				}
			},
			
			newInputStyle: function(){
				let color = this.focusRef === 'new' ? '#1E8EEF' : '#C0C0C0';
				return {
					'border-bottom': `1px solid ${color}`
				}
			},
			
			confirmInputStyle: function(){
				let color = this.focusRef === 'confirm' ? '#1E8EEF' : '#C0C0C0';
				return {
					'border-bottom': `1px solid ${color}`
				}
			},
			
			inputType: function(){
				return this.showPassword ? 'text' : 'password';
			},
			
			/**
			 * 原密码未输入
			 */
			isOldTyped: function(){
				return this.focusRef ==='' || this.focusRef === 'old' || this.oldPass !== '';
			},
			
			isPasswordInvalid: function(){
				return this.focusRef !== 'new' && this.newPass !== '' 
					&& !securityService.checkPasswordFormat(this.newPass);
			},
			
			passwordConfirmed: function(){
				return this.confirmPass === '' 
					|| this.newPass === this.confirmPass;
			}
		},
		
		methods: {
			onInputFocus(which){
				this.focusRef = which;
			},
			
			togglePasswordVisibility(){
				this.showPassword = !this.showPassword;
			},
			
			commitChange(){
				this.focusRef = 'commit';
				if(this.isOldTyped === false){
					return;
				}
				
				if(securityService.checkPasswordFormat(this.confirmPass) === false){
					return;
				}
				
				if(this.passwordConfirmed === false){
					return;
				}
				
				let userInfo = uni.getStorageSync('userInfo');
				if(userInfo){
					let userId = userInfo.id;
					postPassword({
						yhid: userId,
						oldpass: this.oldPass,
						newpass: this.newPass,
						service:  'MODIFY_PASS'
					}).then((res)=>{
						console.log(res);
						this.showSuccessTip();
					})
					// uni.showLoading({
					// 	title: '数据提交中...'
					// })
					// let userId = userInfo.YHID;
					// let _self = this;
					// new Promise((resolve, reject) => {
					// 	resolve({
					// 		userId,
					// 		oldPass: this.oldPass,
					// 		newPass: this.newPass
					// 	})
					// }).then(securityService.checkOldPassword)
					// 	.then(securityService.changePassword)
					// 	.then(resp => {
					// 		if(resp.success){
					// 			this.showSuccessTip();
					// 		} else {
					// 			this.showChangeFailTip(resp.msg);
					// 		}
					// 	})
					// 	.catch(error => {
					// 		this.showChangeFailTip(error.msg);
					// 	})
					// 	.finally(() => {
					// 		uni.hideLoading();
					// 	});

				}
			},
			
			showSuccessTip(){
				this.$showModal({
					title: '修改成功',
					content: '下次登录请使用新密码',
					showCancel: false,
					success(res) {
						uni.navigateBack({
							delta: 1
						})
					}
				})
			},
			
			showChangeFailTip(failMessage){
				this.$showModal({
					title: '提示',
					content: failMessage,
					showCancel: false
				})
			}
		}
	}
</script>

<style scoped>
	.change-password-input {
		width: 80%;
		margin-top: 40rpx;
		padding-bottom: 5rpx;
		width: 100%;
		border-bottom: 1px solid #C0C0C0;
	}
	
	.change-password-confirm {
		width: 100%;
		line-height: 2rem;
		margin-top: 40px; 
		border-radius: 50px; 
		font-size: 0.9rem;
		box-shadow: 0px 0px 5px #1E8EEF;
	}
	
	.change-password-valid {
		width: 100%;
		margin-top: 3px;
		font-size: 30rpx;
		color: #f00;
	}
</style>
