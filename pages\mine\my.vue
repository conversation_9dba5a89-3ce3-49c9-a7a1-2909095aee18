<template>
	<Page class="power-page"
	      :title="title"
	      @layoutAware="onFixedHeight">
		<text class="mine-title">我的</text>
		<view class="flex-row-layout mine-header"
		      style="width: 100%; height: 200px;">
			<image class="head-image"
			       :src="iconAvatar" />
			<view class="mine-user-info">
				<text class="mine-user-name">{{ name }}</text>
				<text class="mine-user-department">{{ department }}</text>
			</view>
		</view>
		<view class="item-menu"
		      v-for="(action, index) in actions"
		      :key="index"
		      @click="doAction(action.method)">
			<image class="item-menu-image"
			       :src="action.icon" />
			<text style="margin-left: 10px;">{{action.label}}</text>
			<image class="item-menu-image"
			       style="margin-left: auto;"
			       :src="iconNext" />
		</view>
	</Page>
</template>

<script>
	import NaviBar from '@/pages/component/NaviBar.vue'
	import searchIcon from '@/static/img/icon_search_white.png';
	import iconAvatar from '@/static/img/mine/icon_head.png'
	import mineBg from '@/static/img/mine/mine_bg.png';
	import { postVersionInfo } from '@/api/version.js';
	import { DOWNLOAD_URL } from '@/common/config.js'
	import iconAbout from '@/static/img/mine/icon_about.png';
	import iconChangePassword from '@/static/img/mine/icon_lock.png';
	import iconFontSize from '@/static/img/mine/icon_font_size.png';
	import iconClear from '@/static/img/mine/icon_clear.png';
	import iconNext from '@/static/img/icon_right.png';
	import inonBack from '@/static/img/icon_back_blue.png';
	import Page from '@/pages/component/Page.vue';
	
	import appService from '@/api/app-service.js'

	export default {
		components: {
			NaviBar,
			Page
		},

		data() {
			return {
				title: '个人中心',
				mineBg,
				iconAvatar,
				CBNUM: '', //催办的数量
				FBNUM: '', //分办的数量
				iconNext,
				name: '-',
				department: '',
				actions: [
					// {label: '字体大小', icon: iconFontSize, method: 'modifyFontSize'},
					{
						label: '清除缓存',
						icon: iconClear,
						method: 'clearCahce'
					},
					{
						label: '版本更新',
						icon: iconAbout,
						method: 'about'
					},
					{
						label: '修改密码',
						icon: iconChangePassword,
						method: 'changePassword'
					}
					// {
					// 	label: '退出登录',
					// 	icon: inonBack,
					// 	method: 'getBack'
					// }
				]
			};
		},

		mounted() {
			// this.getPersonAll();
			let userInfo = uni.getStorageSync('userInfo');
			if (userInfo) {
				this.name = userInfo.name;
				this.department = userInfo.department;
			}
		},

		methods: {
			onFixedHeight(layout) {
				this.pageHeight = layout.height;
			},

			getPersonAll() {
				// service.getPersonAll().then((res) => {
				//   this.FBNUM = res.result.FBNUM
				//   this.CBNUM = res.result.DBNUM
				// })
			},

			back() {
				uni.navigateBack({
					delta: 1
				});
			},

			presseRouter(index) {
				if (index > 0) {
					uni.navigateTo({
						url: '/pages/overview/list/OverviewList?type=4&tabs=3'
					});
				}

			},

			dispatchRouter(index) {
				if (index > 0) {
					uni.navigateTo({
						url: '/pages/overview/list/OverviewList?type=6&tabs=3'
					});
				}
			},

			message() {
				uni.navigateTo({
					url: route.news
				});
			},

			doAction(method) {
				if (method) {
					this[method]();
				} else {
					console.error(`该操作未定义执行方法`)
				}
			},

			changePassword() {
				console.log('changePassword');
				// uni.navigateTo({
				// 	url: url
				// })
				uni.navigateTo({
					url: './change-password'
				});
			},

			modifyFontSize() {
				// uni.navigateTo({
				// 	url: `./settings`
				// })
				let clientWidth = document.documentElement.clientWidth;
				let roots = document.getElementsByTagName('html');
				if (roots) {
					console.log(`设置字体大小`)
					roots[0].style.fontSize = `${clientWidth / 30}px`;
					uni.reLaunch({
						url: '/pages/mine/my'
					})
				}
			},

			clearCahce() {
				try {
					let userInfoCacheKey = 'userInfo';
					let keyOfUsername = 'username';
					let keyOfPassword = 'password';
					let userInfo = uni.getStorageSync(userInfoCacheKey);
					let username = uni.getStorageSync(keyOfUsername);
					let password = uni.getStorageSync(keyOfPassword);
					uni.clearStorageSync();
					uni.showToast({
						duration: 1500,
						title: '清除成功'
					})
					uni.setStorageSync(userInfoCacheKey, userInfo)
					uni.setStorageSync(keyOfUsername, username);
					uni.setStorageSync(keyOfPassword, password);
					setTimeout(()=>{
						uni.reLaunch({
						url: '/pages/login/login'
					})
					},1000)
					
				} catch (error) {
					uni.showToast({
						duration: 1500,
						title: `清除出错：${JSON.stringify(error, null, 4)}`
					})
				}
			},

			getBack() {
				uni.reLaunch({
					url: '/pages/login/login'
				})
			},

			//检测版本是否可以更新
			about() {
				appService.checkAppUpgrade()
				// let self = this;
				// let versionNumber = uni.getStorageSync('versionNumber');
				// console.log(versionNumber);
				// postVersionInfo({
				// 	os: 'WECHAT',
				// 	service: 'SEARCH_BBXX_DETAIL',
				// }).then((res) => {
				// 	if (versionNumber !== res.data_json.BBH) {
				// 		uni.showModal({
				// 			title: '提示',
				// 			content: '当前有新的版本可以更新，是否更新最新版本',
				// 			success: function(r) {
				// 				if (r.confirm) {
				// 					self.upDataVersion(res.data_json.WJID)
				// 				} else if (r.cancel) {

				// 				}
				// 			}
				// 		});
				// 	}
				// }).catch(()=>{
				// 	uni.showToast({
				// 		title: '暂无更新',
				// 		duration: 2000,
				// 		icon: 'none'
				// 	});
				// })
			},

			//版本更新--下载
			upDataVersion(id) {
				let url = DOWNLOAD_URL + '?wdbh=' + id
				plus.nativeUI.showWaiting("下载升级包...");
				plus.downloader.createDownload(url, {
					filename: "_doc/update/"
				}, function(d, status) {
					if (status == 200) {
						//console.log("下载wgt成功："+d.filename);
						this.installWgt(d.filename); // 安装wgt包
					} else {
						// console.log("下载wgt失败！");
						plus.nativeUI.alert("下载升级包失败！");
					}
					plus.nativeUI.closeWaiting();
				}).setRequestHeader('Token', uni.getStorageSync('authToken')).start();
			},

			//版本更新--安装
			installWgt(path){
				plus.nativeUI.showWaiting("安装升级包...");
				plus.runtime.install(path,{},function(){
					plus.nativeUI.closeWaiting();
					//console.log("安装wgt文件成功！");
					plus.nativeUI.alert("应用资源更新完成！",function(){
						plus.runtime.restart();
					});
				},function(e){
					plus.nativeUI.closeWaiting();
					//console.log("安装wgt文件失败["+e.code+"]："+e.message);
					plus.nativeUI.alert("安装升级包失败["+e.code+"]："+e.message);
				});
			}
		}
	};
</script>

<style scoped>
	.mine-title {
		position: fixed;
		width: 100%;
		line-height: 48px;
		text-align: center;
		color: #fff;
		font-size: 18px;
		background: transparent;
	}

	.mine-header {
		background-image: url(../../static/img/mine/mine_bg.png);
		background-size: cover;
	}

	.head {
		height: 400upx;
		display: flex;
		justify-content: center;
		align-items: center;
	}

	.head-image {
		margin-left: 12px;
		width: 160upx;
		height: 160upx;
	}

	.mine-user-info {
		margin-left: 20px;
		display: flex;
		flex-flow: column;
		justify-content: center;
		color: #fff;
	}

	.mine-user-name {
		font-size: 36upx;
		padding-bottom: 20px;
	}

	.mine-user-department {
		font-size: 28upx;
	}

	.item-menu {
		padding: 0px 10px;
		width: calc(100% - 20px);
		line-height: 48px;
		display: flex;
		flex-direction: row;
		align-items: center;
		font-size: 30upx;
		color: #666666;
		border-bottom-color: #e0e0e0;
		border-bottom-width: 1upx;
		border-bottom-style: solid;
	}

	.item-menu-image {
		width: 22px;
		height: 22px;
	}

	.head-text-count {
		width: 90vw;
		margin: 0 auto;
		margin-top: -70upx;
		padding: 20upx 0upx 20upx 0upx;
		z-index: 999;
		flex: 1;
		background-color: #fff;
		color: #b2b2b2;
		display: flex;
		border-radius: 10upx;
		box-shadow: 0px 0px 5px #aaa;
		flex-direction: row;
		align-items: center;
		justify-content: space-evenly;
	}

	.head-text-column {
		display: flex;
		flex-direction: column;
		align-items: center;
		font-size: 28upx;
	}

	.head-split {
		color: #f7f7f7;
	}
</style>
