<template>
	<Page class="power-page" style="width:100%" :title="title">
		<section class="main">
			<div class="" style="padding-bottom: 0;">
		    <div style="padding: 0.3rem 0.402576rem; background: #fff;">
		      <ul class="ul-8">
		        <li>
		          <p>
		            <img src="../../static/online/images/img_g1.png" />
		            <span>所属行政区</span>
		          </p>
		          <p>{{companyDetail.ssxzq}}</p>
		        </li>
		        <li>
		          <p>
		            <img src="../../static/online/images/img_g2.png" />
		            <span>监管级别</span>
		          </p>
		          <p>{{companyDetail.jgjb}}</p>
		        </li>
		        <li>
		          <p>
		            <img src="../../static/online/images/img_g3.png" />
		            <span>单位地址</span>
		          </p>
		          <p>{{companyDetail.dwdz}}</p>
		        </li>
		      </ul>
		      <!-- <button class="btn-spjk"><em>视频监控</em></button> -->
		    </div>
		    <div class="tab-wrap-2">
		      <ul class="menu-tab menu-tab-3">
		        <li :class="{on: item.pkdm == pointId}" v-for="item in companyDetail.pk" @click="changePointId(item.pkdm)">{{item.pkmc}}<em class="normal" :class="{pass: item.pkzt != '正常' }">{{item.pkzt}}</em></li>
		        <!-- <li>排口二<em class="pass">超标</em></li> -->
		      </ul>
		    </div>
		    <div class="dwxx-wrap">
		      <h4 class="htit">{{pollutionList.jcxmc}}排放口</h4>
		      <div class="dwpanel">
		        <span class="dwtit">最新监测数据</span>
		        <em>{{pollutionList.jcsj}}</em>
		      </div>
		      <div style="margin: 0 0.25rem;">
		        <table class="table-1">
		          <thead>
		            <tr>
		              <td>因子</td>
		              <td>排放值</td>
		              <td>标准值</td>
		              <td>状态</td>
		            </tr>
		          </thead>
		          <tbody>
		            <tr v-for="item in pollutionList.sj">
		              <td>{{item.yz}}</td>
		              <td>{{item.pfz}}</td>
		              <td>{{item.bzz}}mg/L</td>
		              <td v-if="item.status == 0"><span  style="color: #d84b4b;">超标</span></td>
					  <td v-if="item.status != 0">正常</td>
		            </tr>
		          </tbody>
		        </table>
		      </div>
		      <div class="dwpanel">
		        <span class="dwtit">最新监测数据</span>
		      </div>
		      <div class="tab-wrap-2" style="margin: 0 0.4rem;">
		        <ul class="menu-tab menu-tab-4">
		          <li :class="{on: pltId=='zl'}" @click="changePlt('zl')">
		            <p>总磷</p>
		            <p>mg/l</p>
		          </li>
		          <li :class="{on: pltId=='hxxyl'}" @click="changePlt('hxxyl')">
		            <p>化学需氧量</p>
		            <p>mg/l</p>
		          </li>
		          <li :class="{on: pltId=='phz'}" @click="changePlt('phz')">
		            <p>PH值</p>
		            <p></p>
		          </li>
		          <li :class="{on: pltId=='ad'}" @click="changePlt('ad')">
		            <p>氨氮</p>
		            <p>mg/l</p>
		          </li>
				  <li :class="{on: pltId=='zd'}" @click="changePlt('zd')">
				    <p>总氮</p>
				    <p>mg/l</p>
				  </li>
		        </ul>
		      </div>
		      <div class="chart-wrap">
				  <!--#ifdef MP-ALIPAY -->
					<canvas canvas-id="canvasColumn" id="canvasColumn" class="charts" :style="{'width':cWidth*pixelRatio+'px','height':cHeight*pixelRatio+'px', 'transform': 'scale('+(1/pixelRatio)+')','margin-left':-cWidth*(pixelRatio-1)/2+'px','margin-top':-cHeight*(pixelRatio-1)/2+'px'}"></canvas>
					<!--#endif-->
					<!--#ifndef MP-ALIPAY -->
					<canvas canvas-id="canvasColumn" id="canvasColumn" class="charts"></canvas>
					<!--#endif-->
		      
		      </div>
		    </div>
		  </div>
		</section>
		
		
		
	</Page>
</template>

<script>
	import {gsjbxxpk, pkjcsj, queryGyzzjjcsj} from '@/api/online.js';
	import uCharts from '@/js_sdk/u-charts/u-charts/u-charts.js';
	var _self;
	var canvaColumn=null;
	import Page from '@/pages/component/Page.vue';
	export default {
		components: {Page},
		data(){
			return {
				pltId: 'zl',//总磷zl, 化学需氧量hxxyl, ph值phz, 氨氮ad, 总氮zd
				title: '企业明细',
				cWidth:'',
				cHeight:'',
				pixelRatio:1,
				serverData:'',
				pollutionList: {}, // 污染物列表
				pointId: '', // 排口id
				companyDetail: {
					pk: []
				}
			}
		},
		onLoad(options) {
			this.companyId = options.companyId;
			this.title = options.companyName;
			
			this.getData();
			
			_self = this;
			//#ifdef MP-ALIPAY
			uni.getSystemInfo({
				success: function (res) {
					if(res.pixelRatio>1){
						//正常这里给2就行，如果pixelRatio=3性能会降低一点
						//_self.pixelRatio =res.pixelRatio;
						_self.pixelRatio =2;
					}
				}
			});
			//#endif
			this.cWidth=uni.upx2px(750);
			this.cHeight=uni.upx2px(300);
			this.getPltLine();
		},
		methods:{
			changePointId(id){
				this.pointId = id;
				this.getPointList();
			},
			changePlt(type){
				this.pltId = type;
				this.getPltLine();
			},
			getPltLine(){
				queryGyzzjjcsj().then(res=>{
					let Column={categories:[],series:[{
						name: '最新检测值',
						data: []
					}]};
					res.data_json.forEach(item=>{
						Column.categories.push(item.sj)
						Column.series[0].data.push(item.sl)
					})
					
					
					_self.showColumn("canvasColumn",Column);
				})
			},
			getPointList(){
				pkjcsj({
					kssj: '',
					jssj: '',
					gsdm: this.companyId,
					pkdm: this.pointId
				}).then(res=>{
					this.pollutionList = res.data_json;
				})
			},
			getData(){
				gsjbxxpk({
					gsdm: this.companyId,
					kssj: '',
					jssj: ''
				}).then(res=>{
					if(res.data_json.pk.length>0){
						this.pointId = res.data_json.pk[0].pkdm;
						this.getPointList()
					}
					this.companyDetail = res.data_json;
				})
			},
			getServerData(){
				let Column={categories:[],series:[]};
				//这里我后台返回的是数组，所以用等于，如果您后台返回的是单条数据，需要push进去
				Column.categories=['2012', '2013', '2014', '2015', '2016', '2017'];
				//这里的series数据是后台做好的，如果您的数据没有和前面我注释掉的格式一样，请自行拼接数据
				Column.series= [{
						name: '成交量A',
						data: [35, 20, 25, 37, 4, 20],
						color: '#000000'
					}];
				_self.showColumn("canvasColumn",Column);
			},
			showColumn(canvasId,chartData){
				canvaColumn=new uCharts({
					$this:_self,
					canvasId: canvasId,
					type: 'line',
					legend:true,
					fontSize:11,
					background:'#FFFFFF',
					pixelRatio:_self.pixelRatio,
					animation: true,
					categories: chartData.categories,
					series: chartData.series,
					xAxis: {
						disableGrid:true,
					},
					yAxis: {
					},
					enableMarkLine: true,
					dataLabel: true,
					width: _self.cWidth*_self.pixelRatio,
					height: _self.cHeight*_self.pixelRatio,
					extra: {
						markLine: {
							type: 'dash',
						data: [{
							value: 10
						}]},
						column: {
						  width: _self.cWidth*_self.pixelRatio*0.45/chartData.categories.length
						}
					  }
				});
			},
			changeData(){
				canvaColumn.updateData({
					series: _self.serverData.ColumnB.series,
					categories: _self.serverData.ColumnB.categories
				});
			}
		}
	}
</script>

<style scoped>
	.menu-tab-4 li {
	    height: 42px;
	    min-width: 20%;
	}
</style>
