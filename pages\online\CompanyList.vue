<template>
	<Page class="power-page" style="width:100%" :title="title">
		<section class="main">
			<div class="">
		   <ul class="ul-2">
		      <li :class="{on: type == 'cbqy'}" @click="changeType('cbqy')">超标企业</li>
		      <li :class="{on: type == 'qbqy'}" @click="changeType('qbqy')">全部企业</li>
		    </ul>
			<scroll-view scroll-y="true" @scrolltolower="onReachBottomEvent" style="height: calc(100vh - 220rpx);" v-if="listData.length>0">
				<ul class="ul-4">
				  <li v-for="item in listData" @click="showCompanyDetail(item)" style="position: relative;">
					<div>
					  <h4>{{item.gsmc}}</h4>
					  <image
						style="position: absolute;right: 10rpx;top:32rpx;"
						class="navi-next"
					  />
					</div>
				  </li>
				</ul>
			</scroll-view>
			</div>
		</section>
	</page>
</template>

<script>
	import {queryQyxi} from '@/api/online.js';
	import Page from '@/pages/component/Page.vue';
	export default {
		components: {Page},
		data(){
			return {
				listData: [], 
				title: '在线监控',
				cityId: '',
				pageNum:1,
				pageSize:10,
				type: 'cbqy',//超标企业 cbqy,全部企业 qbqy
				total: 0
			}
		},
		onLoad(options){
			this.cityId = options.cityId;
			
			this.getData();
		},
		methods:{
			changeType(type){
				this.type = type;
				this.listData = [];
				this.pageNum = 0;
				this.getData();
			},
			showCompanyDetail(item){
				uni.navigateTo({
					url: `/pages/online/CompanyDetail?companyId=`+item.gsdm + '&companyName=' + item.gsmc
				})
			},
			getData(){
				let pms = {
					kssj: '',
					jssj: '',
					pageNum:this.pageNum,
					pageSize:this.pageSize,
					sjxzqhdm: this.cityId,
					sfqb: this.type,
					qymc: ''
				}

				if(this.listData.length < this.total || this.listData.length == 0){
					queryQyxi(pms).then(res=>{
						this.listData.push(...res.data_json);
						this.total = res.data_json.total;
					})
				}
			},
			onReachBottomEvent (){
				this.pageNum++;
				this.getData();
			}
		}
	}
</script>

<style scoped>
	.ul-4 li > div:first-child{
		line-height: inherit;
		color: #000000;
	}
	.ul-4 li > div:nth-child(2) p span{
		display: inherit;
		width: auto;
		font-size: inherit;
		color: inherit;
		position: relative;
		top: 35rpx;
	}
	.power-page-main{
		width: 100%!important;
	}
	.ul-4{
		padding: 0px;
	}
	.ul-2 li {
	    width: 48%;
	    height: 30px;
	    line-height: 30px;
	    border-radius: 15px;
	    text-align: center;
	    font-size: 13px;
	}
	.ul-4 li > div h4{
		padding-top: 40rpx;
		text-align: left;
		padding-left: 40rpx;
	}
	.navi-next{
		position:relative;
		top: 32rpx;
	}
</style>

