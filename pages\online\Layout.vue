<template>
	<Page class="power-page" style="width:100%" :title="title">
		<section class="main">
			<div class="">
		   <!-- <ul class="ul-2">
		      <li class="on">全部</li>
		      <li>COD</li>
		      <li>氨氮</li>
		      <li>总磷</li>
		    </ul> -->
		    <div class="ppanel">
		      <span class="tit tit-1">今日超标企业</span>
		      <span class="rili-slt">{{todayData.sj}}</span>
		    </div>
		    <div class="qstj-wrap">
		      <ul class="ul-3">
		        <li>
		          <!-- <img src="./images/img_u3_1.png"> -->
		          <h4>总数</h4>
		          <p>{{todayData.zs}}</p>
		        </li>
		        <li>
		          <!-- <img src="./images/img_u3_2.png"> -->
		          <h4>废水</h4>
		          <p>{{todayData.fs}}</p>
		        </li>
		        <li>
		         <!-- <img src="./images/img_u3_3.png"> -->
		          <h4>废气</h4>
		          <p style="color: #ffaa4f;">{{todayData.fq}}</p>
		        </li>
		      </ul>
		    </div>
		    <ul class="ul-4">
		      <li v-for="item in tableList" @click="showCompanyList(item)">
		        <div style="width:45%">
		          <h4>{{item.dsmc}}</h4>
		          <p><span>监控企业：{{item.jkqysl}}</span></p>
		        </div>
				<div>
				  <h4> </h4>
				  <p><span>超标企业：{{item.cbqysl}}</span></p>
				</div>
				<image
					class="navi-next"
				/>
		      </li>
		    </ul>
			</div>
		</section>
	</Page>
</template>

<script>
	import {queryJrcbqy, queryGdsjkqyhcbqy} from '@/api/online.js';
	import Page from '@/pages/component/Page.vue';
	export default {
		components: {Page},
		data(){
			return {
				todayData: {},
				title: '在线监控',
				tableList: []
			}
		},
		methods:{
			showCompanyList(item){
				uni.navigateTo({
					url: `/pages/online/CompanyList?cityId=` + item.dsdm
				})
			}
		},
		mounted(){
			queryJrcbqy({
				kssj: '',
				jssj: ''
			}).then(res=>{
				this.todayData = res.data_json;
			})
			
			queryGdsjkqyhcbqy({
				kssj: '',
				jssj: ''
			}).then(res=>{
				debugger
				this.tableList = res.data_json;
			})
		}
	}
</script>

<style scoped>
	.ul-4 li > div:first-child{
		line-height: inherit;
		color: #000000;
	}
	.ul-4 li > div:nth-child(2) p span{
		display: inherit;
		width: auto;
		font-size: inherit;
		color: inherit;
		position: relative;
		top: 35rpx;
	}
	.power-page-main{
		width: 100%!important;
	}
	.ul-4{
		padding: 0px;
	}
	.navi-next{
		position:relative;
		top: 32rpx;
	}
</style>
