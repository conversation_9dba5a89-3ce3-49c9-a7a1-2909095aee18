<template>
	<Page class="power-page" :title="title" @layoutAware="onFixedHeight">
		<ul class="pd-ultbs1" style="overflow-x: scroll;white-space: nowrap;width: 100%;display: flex;">
			<li :class="{on: type=='LATX'}" @click="onClickItem('LATX')"><i>立案提醒{{`(${latxNum})`}}</i></li>
			<li :class="{on: type=='yellowlhdyh'}" @click="onClickItem('yellowlhdyh')"><i>七天内到期{{`(${yellowlhdyhNum})`}}</i></li>
			<li :class="{on: type=='redlhdyh'}" @click="onClickItem('redlhdyh')"><i>超期未办理{{`(${redlhdyhNum})`}}</i></li>
			<li :class="{on: type=='videoWarming'}" @click="onClickItem('videoWarming')"><i>执法音视频上传提醒{{`(${videoWarmingNum})`}}</i></li>
			<li :class="{on: type=='sydzfyj'}" @click="onClickItem('sydzfyj')"><i>水源地执法预警{{`(${sydzfyjNum})`}}</i></li>
			
		</ul>
		<section class="pd-main" style="padding-top: 50rpx">
			<scroll-view scroll-y="true" :style="listStyle" @scrolltolower="onReachBottomEvent">
				<div class="pd-inner pt2"  v-for="item in dataArr" :key="item.id" >
					<dl class="pd-dlbx2"  v-if="dataArr.length > 0 && type !== 'videoWarming'">
						<dt v-if="item.CJSJ && type !== 'LATX'"><em>创建时间：{{item.CJSJ}}</em></dt>
						<dt><em>任务名称：{{item.RWMC || item.WRYMC}}</em></dt>
						<dt v-if="type == 'LATX'"><em>检查时间：{{item.KSSJ || '-'}}</em></dt>
						<dt v-if="type == 'LATX'"><em>请尽快立案！</em></dt>

						<dd>
							<p>{{item.WRAN_CONTENT}}</p>
						</dd>

						<!-- <dd><button type="button" >确认</button></dd> -->
						<view class="preditBTN" v-if="type !== 'sydzfyj' && type !== 'LATX'">
							<button class="preditShow" @click="confimMessage(item)" type="primary">确认</button>
						</view>
						
					</dl>
					<dl class="pd-dlbx2"  v-if="dataArr.length > 0 && type === 'videoWarming'">
						<dt><em>{{item.TXNR}}</em></dt>			
					</dl>
				</div>
				<noData type="message" v-if="dataArr.length === 0"></noData>
			</scroll-view>
			
		</section>
		<!-- <uni-segmented-control :current="current" :values="items" @clickItem="onClickItem" style-type="button" active-color="#1E8EEF"></uni-segmented-control>
		<scroll-view scroll-y="true" :style="listStyle">
			<uni-card
				v-for="item in dataArr"
				:title="item.YWLXMC"
				:extra="item.CJSJ"
				note="Tips"
			>
					{{item.YJMC}}
					<template v-slot:footer>
						<view class="footer-box">
							<view style="text-align: center;" @click="confimMessage(item)">确认</view>
						</view>
					</template>
			</uni-card>
			
			<div v-if="dataArr.length == 0" style="width: 100%;height: 40%;text-align: center;line-height: 200rpx;">暂无预警提醒</div>
		</scroll-view> -->
	</Page>
</template>

<script>
import codeService from '@/api/code-service.js';
import {queryYjxx, queryYjxxQr, queryFltj,querySyd} from '@/api/predit.js'
import { NOT_UPLOAD_VIDEO, GET_XZCF_LATX } from '@/api/record.js'
import uniSegmentedControl from "@/components/uni-segmented-control/uni-segmented-control.vue";
import uniCard from '@/components/uni-card/uni-card.vue'
import noData from '@/components/no-data.vue'
import Page from '@/pages/component/Page.vue';
export default {
	 components: {uniSegmentedControl, Page, uniCard, noData},
	 data() {
		 return {
			 videoWarmingNum:'',//执法音视频上传提醒
			 redlhdyhNum:'',//已超期的数据
			 yellowlhdyhNum:'',//7天内到期的数据
			 latxNum:'',//立案数据
			 sydzfyjNum:'',//水源地执法预警数据
			 pageNum: 1,
			 pageSize: 20,
			 pageHeight: 600,
			 dataArr: [],
			 type: 'LATX', //AJBL,JKCB
			 title: '预警提醒',
			 items: ['全部','案件办理','监控超标'],
			 current: 0
		 }
	 },
	 computed: {
	 	listStyle: function() {
	 		return {
				height: 'calc(100vh - 180rpx)',
			};
	 	}
	 },
	 mounted(){
		this.getData();
		
		// todo 下拉框
		// codeService.getCodeSet('YWYJ_YJLX').then(codes => {
		// 	debugger
		// })
	 },
	 methods: {
		 onReachBottomEvent (){
		 	this.pageNum++;
			if(this.type == 'sydzfyj'){
				this.getSyd();
			}else if(this.type == 'LATX'){
				this.getXczfLatx();
			}else if (this.type == 'videoWarming') {
				this.getVideoWarming();
			}else {
				this.getData();
			}
		 },
		 
		 confimMessage(item){
			 let number = item.XH;
			 queryYjxxQr({
				 'XH': number
			 }).then(res=>{
				 this.pageNum = 1;
				 this.dataArr = [];
				 this.getData();
			 })
		 },

		 onFixedHeight(layout) {
		 	this.pageHeight = layout.height;
		 },

		 getData(){
			 let pageNum = this.pageNum;
			 let pageSize = this.pageSize;
			 let type = this.type;
			 if(this.dataArr.length < this.total || this.dataArr.length == 0){
				if(type !== 'LATX') {
					queryYjxx({
					'pageNum':pageNum,
					'pageSize':pageSize,
					'params':{
					'searchText':'',
					'YJQX_KSSJ':'',
					'YJQX_JSSJ':'',
					'YJLX':type,  // 公共代码值
					'YJZT':'',
					'YWLX':'',
					'JCR': uni.getStorageSync('userInfo').id || ''
				 }}).then(res=>{
					 this.dataArr.push(...res.data_json.list);
					 this.total = res.data_json.total;
				 })
				} else if (type == 'LATX') {
					GET_XZCF_LATX({
					'pageNum':pageNum,
					'pageSize':pageSize,
					ORGID: uni.getStorageSync('userInfo').department || ''
					}).then(res=>{
						this.dataArr.push(...res.data_json.list);
						this.total = res.data_json.total;
				 })
				}
				
			 }
			queryFltj({}).then((r)=>{
				 this.redlhdyhNum = r.data_json.redlhdyh || 0
				 this.yellowlhdyhNum = r.data_json.yellowlhdyh || 0
				//  this.latxNum = r.data_json.latxNum || 0
			 })

			 querySyd({
					'pageNum':pageNum,
					'pageSize':pageSize,
					}).then(res=>{
					 this.sydzfyjNum = res.data_json.total;
				 })

			NOT_UPLOAD_VIDEO({
					'pageNum':pageNum,
					'pageSize':pageSize,
				}).then(res=>{
					 this.videoWarmingNum = res.data_json.total;
			})

			GET_XZCF_LATX({
					'pageNum':pageNum,
					'pageSize':pageSize,
					ORGID: uni.getStorageSync('userInfo').department || ''
					}).then(res=>{
					this.latxNum = res.data_json.total;
				 })
			 
		 },
		 getSyd() {
			 let pageNum = this.pageNum;
			 let pageSize = this.pageSize;
			 if(this.dataArr.length < this.total || this.dataArr.length == 0){
				querySyd({
					'pageNum':pageNum,
					'pageSize':pageSize,
					}).then(res=>{
					 this.dataArr.push(...res.data_json.list);
					 this.total = res.data_json.total;
				 })
			 }
		 },
		 getVideoWarming(){
			 let pageNum = this.pageNum;
			 let pageSize = this.pageSize;
			 if(this.dataArr.length < this.total || this.dataArr.length == 0){
				NOT_UPLOAD_VIDEO({
					'pageNum':pageNum,
					'pageSize':pageSize,
					}).then(res=>{
					 this.dataArr.push(...res.data_json.list);
					 this.total = res.data_json.total;
				 })
			 }
		 },
		 getXczfLatx(){
			 let pageNum = this.pageNum;
			 let pageSize = this.pageSize;
			 if(this.dataArr.length < this.total || this.dataArr.length == 0){
				GET_XZCF_LATX({
					'pageNum':pageNum,
					'pageSize':pageSize,
					ORGID: uni.getStorageSync('userInfo').department || ''
					}).then(res=>{
					 this.dataArr.push(...res.data_json.list);
					 this.total = res.data_json.total;
				 })
			 }
		 },
		 onClickItem(type) {
			  this.pageNum = 1;
			  this.type = type;
			  this.dataArr = [];
			  if(this.type == 'sydzfyj'){
				this.getSyd();
			  } else if(this.type == 'videoWarming'){
				this.getVideoWarming();
			  }else if(this.type == 'LATX'){
				this.getXczfLatx();
			  }else {
				this.getData();
			  }
			 
		 }
	 }
}
</script>

<style scoped>
	.pd-dlbx2 dd + dd{
		border-top: none;
	}
	.pd-dlbx2{
		padding-bottom: 20rpx;
	}

	.pt2{
		padding: 10rpx 0;
	}

	.preditBTN{
		width: 100%;
		display: flex;
		justify-content: center;
	}

	.preditShow{
		background: rgb(0, 155, 255);
		width: 80%;
		border-radius: 34rpx;
	}

	.pd-ultbs1 li{
		margin: 0 20rpx;
	}
</style>
