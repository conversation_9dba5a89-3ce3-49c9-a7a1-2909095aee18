<template>
	<Page :title="pollutionName" :padding="false">
		<!-- <record-step-indicator
			ref="indicator"
			style="width: 100%; padding: 8px 0;"
			:steps="steps"
			:activeIndexs="currentStepIndex"
			@activeStep="onStepActived" 
		/> -->
		<view style="width: 100%; height: 1px; background-color: #eee;" />
		<view
			id="fragment"
			style="height: calc(100% - 67px); background-color: #fff;">
			<record-pollution-health
				v-if="currentStepIndex === 0"
				:height="fragHeight"
				:pollution ="pollution"
				:stepIndex="0"
				@nextStep="doNextStep" 
			/>
			<record-data-online
				v-if="currentStepIndex === 1"
				:height="fragHeight"
				:pollution ="pollution"
				:stepIndex="1"
				@nextStep="doNextStep" 
			/>
			<record-power-consume
				v-if="currentStepIndex === 2"
				:height="fragHeight"
				:pollution ="pollution"
				:stepIndex="2"
				@nextStep="doNextStep" 
			/>
			<record-absent-form
				v-if="currentStepIndex === 3"
				:height="fragHeight"
				:taskInfo="taskInfo"
				:pollution ="pollution"
				:stepIndex="3"
				@nextStep="doNextStep" 
			/>
			<show-modal></show-modal>
		</view>
	</Page>
</template>

<script>
	import Page from '@/pages/component/Page.vue'
	import recordStepIndicator from '../record-step-indicator.vue'
	import recordPollutionHealth from './record-pollution-health.vue'
	import recordDataOnline from './record-data-online.vue'
	import recordPowerConsume from './record-power-consume.vue'
	import recordAbsentForm from './record-absent-form.vue'
	
	import styleUtil from '@/common/style.js'
	
	export default {
		components: {
			Page, recordStepIndicator, 
			recordPollutionHealth, 
			recordDataOnline, 
			recordPowerConsume, 
			recordAbsentForm
		},
		
		data() {
			return {
				steps: [
					{id: '1', name: '企业信息', order: 1},
					{id: '2', name: '在线监控', order: 2},
					{id: '3', name: '用电监控', order: 3},
					{id: '4', name: '非现场执法', order: 4},
				],
				fragHeight: 600,
				currentStepIndex: 3,
				taskInfo: null,
				pollution: {},
				foreignId: ''
			}
		},
		
		computed: {
			pollutionName: function() {
				return this.pollution.name || '企业名称'
			}
		},
		
		onLoad(options) {
			this.taskInfo = JSON.parse(options.taskInfo)
			this.pollution = JSON.parse(options.pollution)
			this.foreignId = options.foreignId
		},
		
		mounted() {
			styleUtil.getNodeLayout(this, '#fragment')
				.then(layout => {
					this.fragHeight = layout.height
				})
		},
		
		methods: {
			doNextStep(index) {
				this.$refs['indicator'].activeStep(index)
				this.currentStepIndex = index
			},
			
			//跳转,并记录每一次跳转的次数
			onStepActived(index) {
				this.currentStepIndex = index;
				// this.currenArr.push(this.currentStepIndex)
			},
		}
	}
</script>

<style>

</style>
