<template>
	<view class="flex-row-layout">
		<image class="image-count-item__icon" :src="icon"/>
		<text class="image-count-item__label">{{ label }}</text>
		<text class="image-count-item__count" :style="countStyle">{{ count }}</text>
	</view>
</template>

<script>
	export default {
		name: 'ImageCountItem',
		props: {
			icon: {
				type: String,
				default: null
			},
			
			label: {
				type: String,
				default: '统计项'
			},
			
			count:  {
				type: Number,
				default: 0
			},
			
			countColor: {
				type: String,
				default: '#2084EA'
			}
		},
		
		computed: {
			countStyle: function() {
				return {
					color: this.countColor
				}
			}
		}
	}
</script>

<style scoped>
	.image-count-item__icon {
		width: 42rpx;
		height: 42rpx;
	}
	
	.image-count-item__label {
		padding: 0 20rpx;
		color: #333;
		font-size: 16px;
	}
	
	.image-count-item__count {
		width: 32rpx;
		font-size: 16px;
	}
</style>
