<template>
	<view class="flex-row-layout pollution-health-code">
		<image 
			mode="aspectFit"
			class="pollution-health__icon"
			:src="icon"
		/>
		<view class="flex-column-layout" style="margin-left: 20rpx; align-items: flex-start;">
			<text style="font-size: 16px;">{{name}}</text>
			<view class="flex-row-layout" style="font-size: 15px; margin-top: 20rpx;">
				<text>健康体检得分：</text>
				<text :style="scoreStyle">{{ score }}</text>
				<text :style="scoreStyle">分</text>
			</view>
		</view>
	</view>
</template>

<script>
	import iconHealthGood from '@/static/img/wry/wry_health_good.png'
	
	export default {
		name: 'PollutionHealthCode',
		props: {
			name: {
				type: String,
				default: '企业名称'
			},
			
			score: {
				type: Number,
				default: 60
			}
		},
		
		computed: {
			icon: function() {
				return iconHealthGood
			},
			
			scoreStyle: function() {
				let style = {}
				if(this.score >= 80) {
					style.color = '#16D675'
				} else if(this.score >= 60) {
					style.color = '#FFA100'
				} else {
					style.color = '#FF0000'
				}
				return style
			}
		}
	}
</script>

<style>
	.pollution-health-code {
		width: calc(100% - 40rpx);
		padding: 20rpx;
		background-color: #fff;
	}
	
	.pollution-health__icon {
		width: 64px;
		height: 64px;
	}
</style>
