<template>
    <view id="formParent" style="height: 100%">
        <template-form
            ref="form"
            :height="formHeight"
            :editable="true"
            :record-id="recordId"
            :template="template"
            :form-data="formData"
            @templateLoaded="onTemplateLoaded"
            @onFormDataUpdate="listenFormValueChange"
        />
        <p-button name="结束任务" @click.native="finishAbsentInspect()" />
    </view>
</template>

<script>
import TemplateForm from 'bowo-form';
import PButton from '@/components/p-button';

import { mapState, mapMutations } from 'vuex';
import dayjs from 'dayjs';

import styleUtil from '@/common/style.js';
import formUtil from '@/pages/form/Form.js';
import { guid } from '@/common/uuid.js';
import { deepCopyObject } from '@/common/merge.js';

import onform from '@/api/onsaveForm.js';
import {
    MODE_ADD,
    MODE_MODIFY,
    EVENT_EDITOR_EXIT,
    queryTaskTemplates,
    queryFormDefaultValues,
    queryHistoryRecord,
    dynamicformsave,
    queryElementConstraints,
    getRecordData,
    queryRecordData,
    queryTaskFormTemplate,
    getTemplateUpdataTime,
    queryTaskDispatchTemplates,
    saveRecordToService
} from '@/api/record.js';

import { EVENT_FROM_VALUE_CHANGE } from '@/pages/form/Form.js';

import loginService from '@/api/login-service.js';
import recordFrag from '../record-fragment.js';
const mixins = [recordFrag];

//本地缓存表单数据时间戳存储键
const LOCAL_CACHE_TIMESTAMP_KEY = '_timestamp';

//非现场执法表单
const ABSENT_TEMPLATE_ID = '202201161657129047e43d72b64f54a7137ecc2bee37ec';

const DEAD_CODE_DEFAULT_VALUE = {
    XZXDRXZ: '02',
    XZXDRZJLX: '001',
    JCLB: '02',
    QYLX: '1'
};

export default {
    name: 'RecordAbsentForm',
    // #ifdef MP-ALIPAY
    mixins: mixins.map(item => ({
        ...item,
        props: {}
    })),
    // #endif
    // #ifndef MP-ALIPAY
    mixins: mixins,
    // #endif
    components: {
        TemplateForm,
        PButton
    },

    data() {
        return {
            templateId: ABSENT_TEMPLATE_ID,
            recordId: '',
            template: {},
            constraints: [],
            formData: {},
            editable: true,
            defaultValues: {},
            formValueChangeListener: null
        };
    },

    computed: {
        // #ifdef MP
        ...mapState({
            mpFormData: state => state.formData
        }),
        // #endif
        formLayoutHeight: function () {
            return this.height - 42;
        },

        localCacheKey: function () {
            return `form-data:${this.recordId}`;
        },

        formHeight: function () {
            // let finishBtnHeight = uni.upx2px(140);
            return this.height;
        }
    },

    mounted() {
        this.renderForm();
    },

    destroyed() {
        // #ifdef MP
        this.resetFormData(null);
        // #endif

        if (this.formValueChangeListener) {
            uni.$off(EVENT_FROM_VALUE_CHANGE, this.formValueChangeListener);
        }
    },

    methods: {
        // #ifdef MP
        ...mapMutations(['resetFormData', 'updateFormData']),
        //#endif

        listenFormValueChange(formData) {
            formData[LOCAL_CACHE_TIMESTAMP_KEY] = new Date().getTime();
            uni.setStorage({
                key: this.localCacheKey,
                data: formData
            });
        },

        onTemplateLoaded(template) {
            this.template = template;
            // this.$emit('returnTemplate', this.template);
            // if (this.exteriorRecordId) {
            //     this.recordId = this.exteriorRecordId;
            //     this.getFormServiceData();
            // } else {
            //     this.getRecordId();
            // }
        },

        renderForm() {
            let self = this;
            queryTaskFormTemplate( this.taskInfo.YWXTBH,
                ABSENT_TEMPLATE_ID,).then((res)=>{
                  console.log(res,'123');
                  let reorganizeTemplate =
                    formUtil.packPeerElementIntoGroup(res);
                this.template = reorganizeTemplate;

            })
            // queryTaskTemplates(
            //     this.taskInfo.YWXTBH,
            //     ABSENT_TEMPLATE_ID,
            //     this.pollutionId
            // ).then(resp => {
            //     let templateInfo = resp.datas_json;

            //     //从模板中提取默认值
            //     let elementsWithDefaultValue = deepCopyObject(
            //         templateInfo.child
            //     );
            //     this.defaultValues = this.extractDefaultValues(
            //         elementsWithDefaultValue
            //     );

            //     let reorganizeTemplate =
            //         formUtil.packPeerElementIntoGroup(templateInfo);
            //     this.template = reorganizeTemplate;

            //     uni.setStorageSync(ABSENT_TEMPLATE_ID, templateInfo);
            //     this.initRecordInfo();
            // });
        },

        //从模板配置中抽取默认值
        extractDefaultValues(elementValues) {
            let defaultValues = {};
            let self = this;
            elementValues.forEach(element => {
                let field = element.dbcolum;
                let value = element.MRZ;
                if (value) {
                    defaultValues[field] = value;
                }
            });
            return defaultValues;
        },

        /**
         * 初始化笔录信息
         */
        initRecordInfo() {
            let masterRecordsKey = `record-master:${this.foreignId}`;
            let records = uni.getStorageSync(masterRecordsKey) || [];
            let matchRecords = records.filter(r => {
                return (
                    r.templateId === ABSENT_TEMPLATE_ID &&
                    r.pollution.id === this.pollution.id
                );
            });
            let isAddNew = false;
            if (matchRecords.length > 0) {
                this.recordId = matchRecords[0].recordId;
                this.formData = uni.getStorageSync(this.localCacheKey);
            } else {
                isAddNew = true;
                this.recordId = guid();
                let record = {
                    recordId: this.recordId,
                    templateId: ABSENT_TEMPLATE_ID,
                    pollution: Object.assign({}, this.pollution)
                };
                records.push(record);
                uni.setStorageSync(masterRecordsKey, records);
            }

            if (isAddNew) {
                let fixDefaultValues = Object.assign(
                    {},
                    DEAD_CODE_DEFAULT_VALUE
                );
                this.formData = Object.assign(
                    fixDefaultValues,
                    this.defaultValues
                );
                console.log(
                    `表单默认值：${JSON.stringify(this.formData, null, 4)}`
                );
            } else {
                console.log(
                    `本地缓存：${JSON.stringify(this.formData, null, 4)}`
                );
                this.loadFormDataFromServer(this.recordId, this.formData);
            }
        },

        /**
         * 从服务端加载笔录数据
         */
        loadFormDataFromServer(recordId, cache) {
            let _self = this;
            queryRecordData(this.templateId, recordId).then(recordData => {
                let safeRecord = recordData || {};

                if (cache) {
                    let modifyTimeStr = safeRecord.XGSJ;
                    if (modifyTimeStr) {
                        let latestModifyTime = dayjs(
                            modifyTimeStr,
                            'YYYY-MM-DD HH:mm:ss'
                        ).valueOf();
                        let cacheTimestamp =
                            cache[LOCAL_CACHE_TIMESTAMP_KEY] || -1;

                        //服务端数据修改时间大于本地时间才更新服务器时间
                        if (latestModifyTime > cacheTimestamp) {
                            _self.formData = safeRecord;
                        }
                    }
                } else {
                    _self.formData = safeRecord;
                }
            });
        },

        finishAbsentInspect() {
            this.$refs.form
                .checkFormData()
                .then(this.showSaveConfirmTip)
                .then(formData => {
                    this.submitRecordForm(formData);
                })
                .catch(error => {
                    console.log(error, '数据不合规则或用户取消');
                });
        },

        /**
         * 提交笔录表单
         */
        submitRecordForm() {
            uni.showLoading({
                title: '提交表单'
            });

            let loadingHided = false;
            let originalFormData = this.$store.state.formData || this.formData;
            saveRecordToService(
                this.taskInfo,
                this.template,
                this.recordId,
                originalFormData
            )
                .then(res => {
                    this.refreshCacheTimestamp();
                    uni.hideLoading();
                    loadingHided = true;
                    this.doTaskFinish();
                })
                .catch(e => {
                    let errorMsg = e || '';
                    if (typeof errorMsg === 'object') {
                        errorMsg = JSON.stringify(errorMsg);
                    }
                    let invokeParams = {
                        recordId: this.recordId,
                        taskInfo: this.taskInfo,
                        formData: originalFormData
                    };
                    logService
                        .submitExceptionLog(
                            'DYNAMICFORM_SAVE',
                            invokeParams,
                            `提交笔录异常：${errorMsg}`
                        )
                        .then(resp => {
                            console.log(`提交异常响应`);
                        })
                        .catch(error => {
                            let em = error || '';
                            if (typeof error === 'object') {
                                em = JSON.stringify(em);
                            }
                            console.log(`提交异常出错：${em}`);
                        });
                    uni.showToast({
                        title: '保存失败',
                        duration: 2000,
                        icon: 'none'
                    });
                })
                .finally(() => {
                    if (!loadingHided) {
                        uni.hideLoading();
                    }
                });
        },

        /**
         * 刷新本地表单数据缓存时间戳
         */
        refreshCacheTimestamp() {
            let cacheRecord = uni.getStorageSync(this.localCacheKey);
            cacheRecord[LOCAL_CACHE_TIMESTAMP_KEY] = new Date().getTime();
            uni.getStorageSync(this.localCacheKey, cacheRecord);
        }
    }
};
</script>

<style scoped></style>
