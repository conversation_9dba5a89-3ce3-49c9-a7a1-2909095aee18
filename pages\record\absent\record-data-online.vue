<template>
	<view class="flex-column-layout record-data-online">
		<tab-indicator :options="ports" @onTabSelected="onPortChange">
			<template v-slot:default="{ option }">
				<view class="flex-row-layout data-online__port-tab">
					<text>{{ option.name }}</text>
					<text class="data-online__port-status" :style="statusStyle(option)">{{ option.status }}</text>
				</view>
			</template>
		</tab-indicator>
		<scroll-view scroll-y="true" :style="scrollStyle()">
			<view>
				<p-card title="最新监测数据" :round="false" :content-style="contentStyle">
					<template v-slot:option>
						<text>最近一小时</text>
					</template>
					<table-list 
						:headers="tableHeaders"
						:cellRender="factorCellRender"
						:list-data="latestData"
					/>
					
				</p-card>
				
				<p-card 
					title="各因子最近24小时监测数据" 
					:round="false" 
					style="margin-top: 20rpx;"
					:content-style="contentStyle">
					<view class="flex-column-layout">
						<tag-group
							:tags="factors"
							@check="onFactorChange">
						</tag-group>
						<fence-option 
							:options="timeTypes"
							@check="onTimeTypeChange"
						/>
						<view class="data-online__chart">
							<qiun-data-charts type="area" :opts="chartOptions" :chartData="chartData"/>
						</view>
					</view>
				</p-card>
			</view>
		</scroll-view>
		<p-button
			name="下一步" 
			@click.native="doNext(stepIndex)"
		/>
	</view>
</template>

<script>
	import TabIndicator from '@/pages/component/tab-indicator.vue'
	import PButton from '@/components/p-button';
	import PCard from '@/pages/component/PCard.vue'
	import TableList from '@/pages/component/list/table-list.vue'
	import TagGroup from '@/pages/component/tag-group.vue'
	import FenceOption from '@/pages/component/fence-option.vue'
	
	import { deepCopyObject } from '@/common/merge.js'
	import { queryDischargePorts, queryLatestOnlineData, queryFactorHistoryData } from '@/api/online-service.js'
	import recordFrag from '../record-fragment.js'
	const mixins = [recordFrag]
	
	const TABLE_HEADERS = [
		{key: 'factor', name: '因子'},
		{key: 'discharge', name: '排放值'},
		{key: 'standard', name: '标准值'},
		{key: 'status', name: '状态'}
	]
	
	const TIME_TYPES = [
		{name: '近24小时', code: 'hour'},
		{name: '近72小时', code: 'day'}
	]
	
	export default {
		name: 'RecordDataOnline',
		// #ifdef MP-ALIPAY
		mixins: mixins.map(item => ({
			...item,
			props: {}
		})),
		// #endif
		// #ifndef MP-ALIPAY
		mixins: mixins,
		// #endif
		components: {
			TabIndicator, PButton, PCard, TableList, TagGroup, FenceOption
		},
		
		data() {
			return {
				contentStyle: {
					width: '100%',
					padding: 0
				},
				ports: [
					{id: 0, label: '废水排口', staus: '正常'},
					{id: 1, label: '废气排口一', status: '正常'},
					{id: 2, label: '废气排口二', status: '异常'},
				],
				tableHeaders: TABLE_HEADERS,
				timeTypes: TIME_TYPES,
				currentPort: null,
				latestData: [],
				currentFactor: null,
				timeType: TIME_TYPES[0],
				chartOptions: {
					padding: [15, 20, 30, 20],
					dataLabel: false,
					dataPointShape: true,
					enableMarkLine: true,
					enableScroll: false,
					legend: {
						show: false
					},
					xAxis: {
						labelCount: 8,
						itemCount: 8,
						rotateLabel: true
					},
					extra: {
						area: {
							type: "curve",
							width: 2,
							gradient: true
						},
						markLine: {
							type: 'dash',
							dashLength: 4,
							data: [
								{
									value: 35,
									label: '标准值',
									lineColor: '#DE4A42',
									showLabel: true,
									labelFontColor: '#666',
									labelBgColor: '#DFE8FF',
									labelBgOpacity: 0.8,
									yAxisIndex: 0
								}
							]
						}
					}
				},
				chartData: {
				  categories: ["2016", "2017", "2018", "2019", "2020", "2021"],
				  series: [{
				    name: "排放量",
				    data: [35, 36, 31, 33, 13, 34]
				  }]
				}
			}
		},
		
		computed: {
			factors: function() {
				let factors = [
					{id: 'TP', name: '总磷'},
					{id: 'COD', name: '化学需氧量'},
					{id: 'PH', name: 'PH值'},
					{id: 'NH', name: '氨氮'},
					{id: 'TN', name: '总氮'},
				]
				if(this.currentPort) {
					factors = this.currentPort.factors
				}
				return factors
			}
		},
		
		mounted() {
			queryDischargePorts(this.pollution.id)
				.then(ports => {
					this.ports = ports
					this.currentPort = this.ports[0]
				})
		},
		
		watch: {
			currentPort: function(port) {
				this.currentFactor = port.factors[0]
				this.loadLatestOnlineData(port)
			},
			
			currentFactor: function(factor) {
				this.loadFactorLatestData(this.currentPort, factor)
			}
		},
		
		methods: {
			scrollStyle() {
				return {
					'margin-top': '10px',
					height: `${this.height - 94}px`
				}
			},
			
			statusStyle(port) {
				let style = {}
				let color = '#35CB78'

				if('异常' === port.status) {
					color = '#FF0000'
				}
				style['background-color'] = color
				return style
			},
			
			//因子单元格样式回调
			factorCellRender() {
				return function(row, column) {
					let style = {}
					let key = column.key
					let value = row[key]
					if('status' === key) {
						if('超标' === value) {
							style.color = '#ED5959'
						}
						if('正常' === value) {
							style.color = '#35CD78'
						}
					}
					return style
				}
			},
			
			/**
			 * 排口切换回调
			 * @param {Object} index
			 */
			onPortChange(index) {
				this.currentPort = this.ports[index]
			},
			
			/**
			 * 排口监测因子切换
			 * @param {Object} index
			 * @param {Object} checked
			 * @param {Object} factor
			 */
			onFactorChange(index, checked, factor) {
				this.loadFactorLatestData(this.currentPort, factor)
			},
			
			onTimeTypeChange(index, type) {
				if(this.timeType.code === type.code) {
					return
				}
				this.timeType = type
				this.loadFactorLatestData(this.currentPort, this.currentFactor)
			},
			
			loadLatestOnlineData(port) {
				queryLatestOnlineData(this.pollution.id, port.id)
					.then(latestData => {
						
						this.latestData = latestData
					})
			},
			
			loadFactorLatestData(port, factor, timeType) {
				let type = timeType || this.timeType
				queryFactorHistoryData(this.pollution.id, port.id, factor.id, type.code)
					.then(resp => {
						// console.log(`排口因子数据：${JSON.stringify(resp, null, 4)}`)
						let factorData = resp.data || {}
						let standard = factorData.bz || '0'
						if(standard.indexOf('.') === -1) {
							standard = parseInt(standard)
						} else {
							standard = parseFloat(standard)
						}
						let options = deepCopyObject(this.chartOptions)
						options.extra.markLine.data[0].value = standard
						this.chartOptions = options
						
						if(factorData.Xdata) {
							let chartData = {
								categories: factorData.Xdata,
								series: []
							}
							let yData = (factorData.data || []).map(y => {
								if(y) {
									return parseFloat(y)
								} else {
									return '0'
								}
							})
							chartData.series.push({
								name: factor.name,
								data: yData
							})
							this.chartData = chartData
						}
					})
			},
			
			doNextStep() {
				this.doNext(this.stepIndex)
			}
		}
	}
</script>

<style scoped>
	.record-data-online {
		background-color: #f4f4f4;
	}
	
	.data-online__port-tab {
		width: auto;
		padding: 20rpx 0;
		font-size: 14px;
	}
	
	.data-online__port-status {
		border-radius: 50px;
		margin-left: 10rpx;
		padding: 4rpx 16rpx;
		font-size: 12px;
		color: #fff;
	}
	
	.data-online__chart {
		width: 100%;
		height: 300px;
	}
</style>
