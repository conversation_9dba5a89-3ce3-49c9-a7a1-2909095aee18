<template>
	<view class="flex-column-layout">
		<scroll-view scroll-y="y" :style="scrollStyle">
			<pollution-health-code
				:name="pollution.name"
				:score="healthScore"
			/>
			<view class="flex-column-layout pollution-health__cards">
				<banner-card title="基本信息">
					<text 
						class="pollution-health__attr" 
						v-for="(item, index) in baseInfoConfig"
						:key="index">
						{{ `${item.label}：${pollutionBaseInfo[item.key] || '--'}` }}
					</text>
					<view class="flex-row-layout">
						<text class="pollution-health__attr">监控级别：{{pollutionBaseInfo.HBJGJBMC}}</text>
						<text class="pollution-health__attr" style="margin-left: auto;">生产状态：{{pollutionBaseInfo.SCZTMC}}</text>
					</view>
				</banner-card>
				
				<banner-card title="排污许可证" class="pollution-health__card">
					<text
						class="pollution-health__attr" 
						v-for="(item, index) in dischargeLicenseConfig"
						:key="index">
						{{ `${item.label}：${dischargeLicense[item.key] || '--'}` }}
					</text>
				</banner-card>
				
				<banner-card title="执法记录" class="pollution-health__card">
					<view class="flex-row-layout">
						<image-count-item 
							:icon="iconInspectCount"
							label="执法次数"
							:count="inspectRecord.ALLNUM || 0"
						/>
						<image-count-item 
							style="justify-content: flex-end;"
							:icon="iconRiskCount"
							label="发现总题数"
							countColor="#F49127"
							:count="inspectRecord.FXWFNUM || 0"
						/>
					</view>
					<view class="pollution-health__card-divider"></view>
					<view v-if="illegalIssues.length > 0" class="flex-row-layout" style="margin-bottom: 5px;">
						<image class="pollution-health__warn-icon" :src="iconRiskWarn"/>
						<text class="pollution-health__warn">发现问题</text>
					</view>
					<text class="pollution-health__attr"
						v-for="(issue, index) in illegalIssues"
						:key="index">
						{{ issue }}
					</text>
<!-- 					<text class="pollution-health__attr" style="margin-top: 20rpx;">重污染天气应急问题</text>
					<text class="pollution-health__attr">超标或者超总量排放污染物</text> -->
				</banner-card>
				
				<banner-card title="处罚记录" class="pollution-health__card">
					<view class="flex-row-layout">
						<image-count-item 
							:icon="iconPunishCount"
							label="处罚次数"
							:count="punishRecord.ALLNUM || 0"
						/>
						<image-count-item 
							style="justify-content: flex-end;"
							:icon="iconMoneyAmount"
							label="处罚金额"
							countColor="#F49127"
							:count="punishRecord.CFJE || 0"
						/>
					</view>
					<view class="pollution-health__card-divider"></view>
					<view v-if="illegalActions.length > 0" class="flex-row-layout" style="margin-bottom: 5px;">
						<image class="pollution-health__warn-icon" :src="iconRiskWarn"/>
						<text class="pollution-health__warn">违法行为</text>
					</view>
					<text class="pollution-health__attr"
						v-for="(action, index) in illegalActions"
						:key="index">
						{{ action }}
					</text>
<!-- 					<text class="pollution-health__attr">重污染天气应急问题</text>
					<text class="pollution-health__attr">超标或者超总量排放污染物</text> -->
				</banner-card>
			</view>
		</scroll-view>
		<p-button 
			name="下一步" 
			@click.native="doNextStep"
		/>
	</view>
</template>

<script>
	
	import pollutionHealthCode from './pollution-health-code.vue'
	import bannerCard from '@/pages/component/banner-card.vue'
	import imageCountItem from './image-count-item.vue'
	import PButton from '@/components/p-button';
	
	import iconInspectCount from '@/static/img/record/icon_inspect_count.png'
	import iconRiskCount from '@/static/img/record/icon_risk_count.png'
	import iconPunishCount from '@/static/img/record/icon_punish_count.png'
	import iconMoneyAmount from '@/static/img/record/icon_money_amount.png'
	import iconRiskWarn from '@/static/img/record/icon_risk_warn.png'
	
	import dayjs from 'dayjs'
	import recordFrag from '../record-fragment.js';
	import { 
		queryWryJbxx,
		queryPollutionBaseInfo,
		queryDischargeLicense,
		queryPollutionHealthStatus 
	} from '@/api/wry-service.js'
	import { queryPunishAndInspectRecord } from '@/api/record.js'
	
	const mixins = [recordFrag];
	
	export default {
		name: 'RecordPollutionHealth',
		// #ifdef MP-ALIPAY
		mixins: mixins.map(item => ({
			...item,
			props: {}
		})),
		// #endif
		// #ifndef MP-ALIPAY
		mixins: mixins,
		// #endif
		components: {
			pollutionHealthCode, bannerCard, imageCountItem, PButton
		},
		
		data() {
			return {
				iconInspectCount,
				iconRiskCount,
				iconPunishCount,
				iconMoneyAmount,
				iconRiskWarn,
				healthInfo: {},
				pollutionBaseInfo: {},
				baseInfoConfig: [
					{label: '统一社会信用代码', key: 'TYSHXYDM'},
					{label: '经营场所地址', key: 'WRYDZ'},
					{label: '行业类型', key: 'INDUSTRY'},
					{label: '法定代表人', key: 'FDDBR'},
					{label: '行政区域', key: 'DISTRICT'}
				],
				dischargeLicense: {},
				dischargeLicenseConfig: [
					{label: '证书编号', key: 'PWXKZHM'},
					{label: '是否过期', key: 'EXPIRED'},
					{label: '管理类别', key: 'PWXKGLLBMC'},
					{label: '主要污染物种类', key: 'ZYWRWLBMC'},
					{label: '有效期限', key: 'YXQ'},
					{label: '发证机关', key: 'FZJG'},
					{label: '发证日期', key: 'FZRQ'}
				],
				inspectRecord: {},
				punishRecord: {}
			}
		},
		
		computed: {
			scrollStyle: function() {
				return {
					height: `${this.height - 42}px`
				}
			},
			
			healthScore: function() {
				return this.healthInfo.FXZS || 80
			},
			
			illegalIssues: function() {
				return this.inspectRecord.wflx || []
			},
			
			illegalActions: function() {
				return this.punishRecord.wfxw || []
			} 
		},
		
		mounted() {
			queryPollutionBaseInfo(this.pollution.id)
				.then(resp => {
					let baseInfo = Object.assign({}, resp.data_json)
					if(baseInfo) {
						baseInfo.INDUSTRY = `${baseInfo.HYDM || ''}-${baseInfo.HYMC || ''}`
						baseInfo.DISTRICT = `${baseInfo.SSDSMC || ''}${baseInfo.SSQXMC || ''}`
					}
					this.pollutionBaseInfo = baseInfo
				})
				
			queryDischargeLicense(this.pollution.id)
				.then(resp => {
					let dischargeLicenseList = resp.data_json
					if(Array.isArray(dischargeLicenseList) && dischargeLicenseList.length > 0) {
						let firstLicense = dischargeLicenseList[0]
						let deadLine = firstLicense.YXQ
						if(deadLine && deadLine.indexOf('至') !== -1) {
							let dates = deadLine.split('至')
							if(dates.length === 2) {
								let limitDate = dayjs(dates[1])
								firstLicense.EXPIRED = limitDate.isAfter(dayjs()) ? '否' : '是'
							}
						}
						this.dischargeLicense = firstLicense
					}
				})
				
			queryPollutionHealthStatus(this.pollution.id)
				.then(resp => {
					let healthData = resp.data_json || []
					if(healthData.length > 0) {
						this.healthInfo = healthData[0]
					}
				})
				
			queryPunishAndInspectRecord(this.taskInfo.YWXTBH, this.pollution.id)
				.then(resp => {
					let inspectAndPunishRecord = resp.data_json || {}
					this.inspectRecord = inspectAndPunishRecord.zfjl
					this.punishRecord = inspectAndPunishRecord.cfjl
				})
		},
		
		methods: {
			doNextStep() {
				this.doNext(this.stepIndex)
			},
			
		}
	}
</script>

<style scoped>	
	.pollution-health__cards {
		width: calc(100% - 40rpx);
		padding: 20rpx;
		background-color: #f4f4f4;
	}
	
	.pollution-health__attr {
		padding: 10rpx 0;
		color: #333;
	}
	
	.pollution-health__card {
		margin-top: 30rpx;
	}
	
	.pollution-health__card-divider {
		width: 100%;
		height: 1px;
		margin: 20rpx 0;
		background-color: #ccc;
	}
	
	.pollution-health__warn-icon {
		width: 42rpx;
		height: 42rpx;
		margin-right: 20rpx;
	}
</style>
