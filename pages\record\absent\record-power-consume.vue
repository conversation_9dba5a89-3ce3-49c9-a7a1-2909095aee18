<template>
	<view class="flex-column-layout record-power-consume">
		<scroll-view scroll-y="true" :style="scrollStyle">
			<view style="background-color: #f4f4f4;">
				<p-card title="实时监测数据" :round="false" :content-style="contentStyle">
					<template v-slot:option>
						<text>最近一小时</text>
					</template>
					<table-list 
						:headers="tableHeaders"
						:columnWidth="columnWidth"
						:listData="devicePowerData"
					/>
				</p-card>
				<tab-indicator 
					:options="dataTypes" 
					style="margin-top: 20rpx;"
					@onTabSelected="changeDataType">
					<template v-slot="{ option }">
						<text style="padding: 20rpx 0;">{{ option.name }}</text>
					</template>
				</tab-indicator>
				<view style="width: 100%; height: 1px; background-color: #f4f4f4;"/>
				<view class="flex-row-layout" style="width: calc(100% - 40rpx); height: 42px; background-color: #fff; padding: 0 20rpx">
					<text>生产线：</text>
					<easy-select 
						style="width: 120px; height: 46rpx; border: 1px solid #ccc; border-radius: 50px;"
						size="small"
						:value="selectedLineName"
						:options="produceLineOptions"
						@selectOne="chooseLine"
					/>
					<p-segmented-button 
						style="width: 140px; margin-left: auto;"
						fillColor="#FFF"
						textColor="#666" 
						:text-style="timeTypeTextStyle"
						:options="timeTypes"
						@checkChange="onTimeTypeChange"/>
				</view>
				<view class="flex-column-layout" style="margin-top: 20rpx; background-color: #fff;">
					<text style="padding: 10rpx 0;">生产设施</text>
					<view class="power-consume__chart">
						<qiun-data-charts 
							type="line"
							:opts="chartOptions"
							:chartData="produceFacilitiesChartData"
						/>
					</view>
				</view>
				<view class="flex-column-layout" style="margin-top: 20rpx; background-color: #fff;">
					<text style="padding: 10rpx 0;">治污设施</text>
					<view class="power-consume__chart">
						<qiun-data-charts 
							type="line"
							:opts="chartOptions"
							:chartData="cleanFacilitiesChartData"
						/>
					</view>
				</view>
			</view>
		</scroll-view>
		<p-button
			name="下一步" 
			@click.native="doNext(stepIndex)"
		/>
	</view>
</template>

<script>
	import TabIndicator from '@/pages/component/tab-indicator.vue'
	import PButton from '@/components/p-button';
	import PCard from '@/pages/component/PCard.vue'
	import TableList from '@/pages/component/list/table-list.vue'
	import PSegmentedButton from '@/pages/component/PSegmentedButton.vue'
	
	import {queryLatestPowerConsume, queryPollutionProduceLines, queryProduceLineData} from '@/api/power-consume-service.js'
	
	import recordFrag from '../record-fragment.js'
	const mixins = [recordFrag]
	
	//表格列表表头配置
	const TABLE_HEADERS = [
		{key: 'device', name: '设备名称'},
		{key: 'phaseA', name: 'A相电压(Ua)'},
		{key: 'phaseB', name: 'B相电压(Ub)'},
		{key: 'phaseC', name: 'C相电压(Uc)'}
	]
	
	//设施数据类型配置
	const DATA_TYPES = [
		{id: 1, type: 'consume', name: '用电数据'},
		{id: 2, type: 'power', name: '功率数据'},
		{id: 3, type: 'current', name: '电流数据'}
	]
	
	//查看设施时间段类型
	const TIME_TYPES = [
		{text: '近24小时', code: 'hour'},
		{text: '近72小时', code: 'day'}
	]
	
	export default {
		name: 'RecordPowerConsume',
		// #ifdef MP-ALIPAY
		mixins: mixins.map(item => ({
			...item,
			props: {}
		})),
		// #endif
		// #ifndef MP-ALIPAY
		mixins: mixins,
		// #endif
		components: {
			TabIndicator, PButton, PCard, TableList, PSegmentedButton
		},
		
		data() {
			return {
				contentStyle: {
					width: '100%',
					padding: 0
				},
				timeTypeTextStyle: {
					'font-size': '14px',
					padding: '2px 5px'
				},
				tableHeaders: TABLE_HEADERS,
				columnWidth: [34, 22, 22, 22],
				devicePowerData: [
					{device: '等离子吸附设备', phaseA: '238.6', phaseB: '0.0', phaseC: '0.0'},
					{device: '西注塑机', phaseA: '238.6', phaseB: '0.0', phaseC: '0.0'},
					{device: '东注塑机', phaseA: '238.6', phaseB: '0.0', phaseC: '0.0'},
				],
				
				dataTypes:  DATA_TYPES,
				dataType: DATA_TYPES[0],
				timeTypes: TIME_TYPES,
				timeType: TIME_TYPES[0],
				produceLines: [],
				currentLine: null,
				selectedLineName: '',
				chartOptions:  {
					padding: [15, 20, 30, 20],
					dataLabel: false,
					dataPointShape: true,
					enableScroll: false,
					legend: {
						show: true,
						position: 'top'
					},
					xAxis: {
						labelCount: 8,
						rotateLabel: true
					},
					extra: {
						area: {
							type: "curve",
							width: 2,
							gradient: false
						}
					}
				},
				produceLineData: {}
			}
		},
		
		computed: {
			scrollStyle: function() {
				return {
					height: `${this.height - 42}px`
				}
			},
			
			produceLineOptions: function() {
				return this.produceLines.map(line => {
					return {
						id: line.id,
						label: line.name
					}
				})
			},
			
			produceFacilitiesChartData: function() {
				let chartData = {
					categories: this.produceLineData.time,
					series: this.produceLineData.produceFacilityData || []
				}
				// console.log(`生产设施图表数据：${JSON.stringify(chartData)}`)
				return chartData
			},
			
			cleanFacilitiesChartData: function() {
				let chartData = {
					categories: this.produceLineData.time,
					series: this.produceLineData.cleanupFacilityData || []
				}
				// console.log(`治污设施图表数据：${JSON.stringify(chartData)}`)
				return chartData
			}
		},
		
		mounted() {
			queryLatestPowerConsume()
				.then(latestData => {
					this.devicePowerData = latestData
				})
				
			queryPollutionProduceLines()
				.then(lines => {
					this.produceLines = lines
					if(lines.length > 0) {
						this.currentLine = lines[0]
						this.selectedLineName = this.currentLine.name
					}
				})
		},
		
		watch: {
			currentLine: function(line) {
				this.loadProduceLineData(line)
			}
		},
		
		methods: {
			/**
			 * 切换生产线选项回调
			 * @param {Object} line
			 */
			chooseLine(line) {
				this.currentLine = line
				this.selectedLineName = line.label
				this.loadProduceLineData(line)
			},
			
			/**
			 * 切换用电、功率、电流数据类型回调
			 * @param {Object} index
			 * @param {Object} dataType
			 */
			changeDataType(index, dataType) {
				this.dataType = dataType
				this.loadProduceLineData(this.currentLine)
			},
			
			/**
			 * 近24小时、3天回调
			 * @param {Object} timeType
			 */
			onTimeTypeChange(timeType) {
				if(timeType.code === this.timeType.code) {
					return
				}
				this.timeType = timeType
				this.loadProduceLineData(this.currentLine)
			},
			
			loadProduceLineData(line) {
				if(line) {
					let timeType = this.timeType.code
					queryProduceLineData(this.pollution.id, line.id, this.dataType.id, timeType)
						.then(produceLineData => {
							this.produceLineData = produceLineData
						})
				}
			}
		}
	}
</script>

<style scoped>
	.record-power-consume {
		width: 100%;
	}
	
	.power-consume__chart {
		width: 100%;
		height: 300px;
	}
</style>
