<template>
	<view class="flex-column-layout form-operate-menu-item">
		<image 
			class="form-menu-icon"
			mode="aspectFit"
			:src="menu.icon"
		/>
		<text class="form-menu-text">{{menu.name}}</text>
	</view>
</template>

<script>
	export default {
		name: 'FormOperateMenuItem',
		props: {
			menu: {
				type: Object,
				default: () => {
					return {
						code: 'submit',
						name: '提交',
						icon: null
					}
				}
			}
		},
		
		data() {
			return {
				
			}
		},
		methods: {
			
		}
	}
</script>

<style scoped>
	.form-operate-menu-item {
		flex: 1;
		padding: 5px 0;
	}
	
	.form-operate-menu-item:active {
		background-color: #ccc;
	}
	
	.form-menu-icon {
		width: 20px;
		height: 20px;
	}
	
	.form-menu-text {
		margin-top: 5px;
		font-size: 14px;
		color: #333;
	}
</style>
