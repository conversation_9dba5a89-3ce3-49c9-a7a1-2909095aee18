<template>
    <Page title="现场照片"
	      :padding="false">
          <view v-if="pictrueList.length > 0" class="photo-list">
              <!-- <image v-for="item in this.pictrueList" :key="item.id" :src ="item.url">  -->
              <liuyunoTabs :tabData="pictrueList" :defaultIndex="defaultIndex" @tabClick='tabClick'> 
              </liuyunoTabs>
               <image :src="pictrueUrl"  mode='aspectFit' class="printUrl"/>
               <view v-for="(item,index) in pictrueList" :key="item.id">
                   <view class="photo-form" v-if="defaultIndex === index">
                    <uni-forms :value="item.formData" ref="form">
                    <uni-forms-item label="拍摄时间" name="name">
                        <uni-easyinput  type="text" v-model="item.formData.ZFRYMC" placeholder="请输入姓名" />
                    </uni-forms-item>
                    <uni-forms-item label="签字时间" name="age">
                        <uni-easyinput type="text" v-model="item.formData.ZFRY" placeholder="请输入年龄" />
                    </uni-forms-item>
                    
                    </uni-forms>
                </view>
               </view>
               <button @click="submitForm">Submit</button>
              
          </view>
    </Page>
</template>

<script>
import liuyunoTabs from "@/components/liuyuno-tabs/liuyuno-tabs.vue";
import NaviBar from '@/pages/component/NaviBar.vue'
import Page from '@/pages/component/Page.vue'
import fileUtil from '@/common/file.js';
import http from '@/common/net/http.js';
import { deepCopyObject } from '@/common/merge.js';
export default {
    components:{
        NaviBar,Page,liuyunoTabs
    },
    data() {
        return {
            formData:{"PSSJ":"2021-02-08 15:53",
                      "QZRQ":"2021-02-28 15:53",
                      "ZFRYMC":"张楠",
                      "ZFRY":"zhangnan",
                      "PSDD":"深圳",
                      "ZFZH":"2142134"
                      },
            hobby: [{
                text: '足球',
                value: 0
            }, {
                text: '篮球',
                value: 1
            }, {
                text: '游泳',
                value: 2
            }],
            defaultIndex:0,
            pictrueUrl:'',//预览图片使用的url
            tabs:['1','2'],
            pictrueList:[]//图片相册列表
        };
    },

    onLoad(options){
        this.pictrueList = JSON.parse(options.pictrueList)
        this.pictrueUrl = this.pictrueList[0].url
        
        this.pictrueList.forEach(element => {
            let form = deepCopyObject(this.formData)
            element.formData = form
        });
        console.log(this.pictrueList,'123');
        // this.getPictrueForm()
    },

    methods: {
        //  根据传过来的图片信息，去挂载最新的图片列表信息
        getPictrueForm(){
            this.pictrueList.forEach(element => {
                uni.uploadFile({
					url: `${http.loginUrl}/webapp/uploadFile`,
					filePath: element.url,
					name: 'file',
					formData: {
						// LXDM: _self.attachMajorCate,
						// ZLXDM: _self.attachMinorCate,
						// YWSJID: attach.recordId, 
						WJMC: element.WDMC,
						WJLX: fileUtil.parseFileSuffix(element.WDMC)
					},

					success(uploadFileRes) {
						let resp = JSON.parse(uploadFileRes.data);
						let attachs = JSON.parse(resp.wd_data);
						let successAttach = attachs[0];

						let attachId = successAttach.WDBH;
						element.WDBH = attachId;

						// #ifdef MP
						// let attachElements = _self.findAttachElements();
						// attachElements.forEach(e => {
						// 	e.resetAttachId(attach.refId, attachId);
						// 	// #ifdef MP-ALIPAY
						// 	e.updateProgress(attach.refId, 100);
						// 	// #endif
						// })
						// #endif

					}
				});
            });
        },

        submitForm(form) {
            console.log(this.pictrueList);
            // 手动提交表单
            this.$refs.form.submit().then((res)=>{
                console.log('表单返回得值：', res)
            })
        },

        tabClick(e){
            this.defaultIndex = e
            this.pictrueUrl = this.pictrueList[e].url
        },

        isFocus(){}
    },
};
</script>

<style scoped>
.printUrl{
    width: 90%;
    display: flex;
    justify-content: center;
    max-height: 1200rpx;
}

.photo-list{
    background-color: #fff;
    width: 100%;  
}

.photo-form{
    padding: 0rpx 40rpx;
}
</style>
