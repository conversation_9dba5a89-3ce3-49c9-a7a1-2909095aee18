<template>
	<Page title="测试" ref="page" style="width: 100%;">
		<button type="primary" @click="select">选择</button>
		<view style="width: calc(100% - 40rpx); padding: 0 20rpx; margin-top: 20rpx;">
			<cascade-picker
				ref="cascade"
				:column="2"
				:value="indexs"
			/>
		</view>
		<button style="margin-top: 32rpx;" @click="showMenu">菜单</button>
		<button @click="signature">签名</button>
		<district-cascade-picker ref="district"/>
		<year-month-picker fields="year" ref="picker"></year-month-picker>
		<show-modal></show-modal>
		<operate-menu-dialog 
			ref="menu" 
			nameKey="text"
			:menus="menus"
			@menuClick="onMenuClick"
		/>
	</Page>
</template>

<script>
	import Page from '@/pages/component/Page.vue';
	import yearMonthPicker from '@/pages/component/date/year-month-picker.vue';
	
	import cascadePicker from '@/pages/component/cascade/cascade-picker.vue';
	import districtCascadePicker from '@/pages/component/cascade/district-cascade-picker.vue';
	import operateMenuDialog from '@/pages/component/dialog/operate-menu-dialog.vue'
	
	let _self = null;
	export default {
		components: {
			Page, yearMonthPicker, cascadePicker, districtCascadePicker, operateMenuDialog
		},
		
		data() {
			return {
				range: [
					['广东', '湖南', '湖北', '广西'],
					['广州', '深圳', '东莞', '惠州']
				],
				tree: {
					'广东': ['广州', '深圳', '东莞', '惠州'],
					'湖南': ['长沙', '株洲', '湘潭'],
					'湖北': ['武汉', '十堰', '孝感'],
					'广西': ['南宁', '桂林', '柳州'],
				},
				indexs: [1, 2],
				
				menus: [{id: 0, text: '重命名'}, {id: 1, text: '删除'}]
			}
		},
		
		mounted() {
			_self = this;
			// #ifdef MP-ALIPAY
			this.$refs['page'].$refs['cascade'].setLoader(parent => {
				return new Promise((resolve, reject) => {
					if(parent){
						resolve(_self.tree[parent])
					} else {
						resolve(['广东', '湖南', '湖北', '广西']);
					}
				});
			})
			// #endif
			
			// #ifndef MP-ALIPAY
			this.$refs['cascade'].setLoader(parent => {
				return new Promise((resolve, reject) => {
					if(parent){
						resolve(_self.tree[parent])
					} else {
						resolve(['广东', '湖南', '湖北', '广西']);
					}
				});
			})
			// #endif
		},
		
		methods: {
			select() {
				// #ifdef MP-ALIPAY
				this.$refs['page'].$refs['district'].show()
				// #endif
				
				// #ifndef MP-ALIPAY
				this.$refs['district'].show()
				// #endif
			},
			
			showMenu() {
				this.$refs.menu.show();
			},
			
			onMenuClick(option) {
				this.$showModal({
					title: '提示',
					content: option.text
				})
			},
			
			signature() {
				console.log(`点击了`)
				uni.navigateTo({
					url: 'pages/component/signature/signature-page'
				})
			}
		}
	}
</script>

<style>

</style>
