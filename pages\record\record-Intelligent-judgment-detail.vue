<template>
	<Page :title="title" @layoutAware="onFixedContentHeight">
		<scroll-view scroll-x="true" >
			<ul class="pd-ultbs1" style="position: inherit;">
				<li :class="{on: type =='1'}" @click="changeType('1')"><i>违法行为名称</i></li>
				<li :class="{on: type =='2'}" @click="changeType('2')"><i>勘察笔录模板</i></li>
				<li :class="{on: type =='3'}" @click="changeType('3')"><i>询问笔录模板</i></li>
				<li :class="{on: type =='4'}" @click="changeType('4')"><i>法律依据</i></li>
				<li :class="{on: type =='5'}" @click="changeType('5')"><i>典型案例</i></li>
				<li :class="{on: type =='6'}" @click="changeType('6')"><i>自由裁量说明</i></li>
			</ul>	
		</scroll-view>
		
		
		<FormTemplateDetail v-if="type =='1' || type =='4' || type=='6'" :form="formData" :templateId="templateId" :formLayoutHeight="pageHeight"></FormTemplateDetail>
		
	
		
		<section class="pd-main" v-if="type =='2' || type == '3' || type== '5'">
			<div class="pd-inner" style="padding-top: 0px;">
				<scroll-view class="record-task-list" scroll-y="true"  style="height: calc(100vh - 230rpx);"  @scrolltolower="onReachBottomEvent" v-if="listData.length>0">
					<ul class="pd-ullst5">
						<li class="none-icon" v-for="item in listData" v-show="type== '2'">
							<h2>模板语句:{{item.MBYJ}}  </h2>
							
						</li>
						
						<li class="none-icon" v-for="item in listData" v-show="type== '3'">
							<h2>问题：{{item.WT}}  </h2>
							<h2>回答：{{item.HD}}</h2>
						</li>
						
						<li v-for="item in listData" v-show="type== '5'" @click="showDetail(item)">
							<h2>案例名称：{{item.ALMC}}  </h2>
							<h2>案例级别：{{item.ALJB}}</h2>
							<h2>查处过程：{{item.CCGC}}</h2>
						</li>
					</ul>
				</scroll-view>
				<noData v-if="listData.length==0"></noData>
			</div>
		</section>
	</Page>
</template>

<script>
import { postIntelligentList } from '@/api/record.js';
import noData from '@/components/no-data.vue';
import Page from '@/pages/component/Page.vue';

import FormTemplateDetail from '@/pages/component/FormTemplateDetail.vue'
export default {
	components:{
		Page,
		noData,
		FormTemplateDetail
	},
	
	data() {
		return {
			itemData: {},
			formData: {},
			recordId: '',
			templateId: '2021022218001962c726956d4a4abf82fa91847f7cf604',
			listData: [],
			title: '智能判案',
			type: '1',
			pageNum: 1,
			pageHeight: 600
		};
	},
	computed: {
		formLayoutHeight: function() {
			return this.pageHeight - 40
		}
	},
	watch: {},

	created() {
		this.itemData = uni.getStorageSync('record-from-data')
		this.formData = JSON.parse(JSON.stringify(this.itemData));
	},

	methods: {
		onFixedContentHeight(layout) {
			this.pageHeight = layout.height;
		},
		
		onReachBottomEvent (){
			this.pageNum++;
			this.getData();
		},
		
		showDetail(item){
			let title = '典型案例详情';
			let templateId = '20210223092629a456f4271ecd49ec9a09bd851cc7d590';
			let recordId = item.XH;
			uni.navigateTo({
				url: `/pages/main/TemplateDetail?title=${title}&recordId=${recordId}&templateId=${templateId}`
			})
		},
		
		getData(){
			if(this.listData.length < this.total || this.listData.length == 0){
				let type = '';
				switch(this.type){
					case '2':
						type = 'kcblList'
						break;
					case '3':
						type = 'xwblList'
						break;
					case '5':
						type = 'dxalList'
						break;	
				}
				
				postIntelligentList({
					type: type,
					WFXWXH: this.itemData.XH,
					pageSize: 100,
					pageNum: this.pageNum
				}).then(res => {
					this.listData.push(...res.data_json.list);
					this.total = res.data_json.total;
				});
			}else{
				uni.showToast({
					icon: 'none',
				    title: '已经没有数据了',
				    duration: 2000
				});
			
			}
		},
		
		
		changeType(type){
			this.formData = {};
			switch(type){
				case '1':
					this.templateId = '2021022218001962c726956d4a4abf82fa91847f7cf604'
					this.formData = JSON.parse(JSON.stringify(this.itemData));
					this.type = type;
				break;
				case '2':
				case '3':
				case '5':
					this.pageNum = 1;
					this.type = type;
					this.listData = [];
					this.getData()
				break;
				case '4':
					postIntelligentList({
						"type":"flyj",
						"WFXWXH": this.itemData.XH
					}).then(res=>{
						this.formData = res.data_json;
						this.templateId = '202102221807197f735f2498824e728344e95c6f054c61'
						this.type = type;
					})
				break;
				
				case '6':
					postIntelligentList({
						"type":"zyclsm",
						"WFXWXH": this.itemData.XH
					}).then(res=>{
						this.formData = res.data_json || {};
						this.templateId = '20210223094147d22c9d5c440c413690ff93e8b3348dfa'
						this.type = type;
					})
				break;
			}
		}
	}
};
</script>

<style scoped>
	.pd-ullst5 .none-icon:after{
		background: none;
	}
.pd-ultbs1 li{
	width: 200rpx;
}
.record-task-list {
	margin-top: 10px;
	background-color: #fff;
}

.record-task-item {
	padding: 10px 10px;
	width: calc(100% - 20px);
}

.record-task-item:active {
	background-color: #007AFF;
}

.record-task-type-icon {
	width: 36px;
	height: 36px;
}

.task-content-layout {
	flex: 1;
	margin: 0 10px;
}

.task-type {
	margin-left: auto;
	border-radius: 50px;
	padding: 2px 10px;
	color: #fff;
	background-color: #0FAEFF;
}
</style>
