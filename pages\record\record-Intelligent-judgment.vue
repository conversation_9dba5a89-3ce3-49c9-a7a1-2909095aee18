<template>
	<Page :title="title" @layoutAware="onFixedContentHeight">
		<scroll-view scroll-x="true">
			<ul class="pd-ultbs1" style="position: inherit;width: 1200rpx;">
				<li :class="{on: type =='1'}" @click="changeType('1')"><i>大气</i></li>
				<li :class="{on: type =='2'}" @click="changeType('2')"><i>水</i></li>
				<li :class="{on: type =='3'}" @click="changeType('3')"><i>土壤</i></li>
				<li :class="{on: type =='4'}" @click="changeType('4')"><i>噪声</i></li>
				<li :class="{on: type =='5'}" @click="changeType('5')"><i>海洋</i></li>
				<li :class="{on: type =='6'}" @click="changeType('6')"><i>核辐射</i></li>
				<li :class="{on: type =='7'}" @click="changeType('7')"><i>固体废物</i></li>
				<li :class="{on: type =='8'}" @click="changeType('8')"><i>建设项目</i></li>
				<li :class="{on: type =='9'}" @click="changeType('9')"><i>环评</i></li>
				<li :class="{on: type =='10'}" @click="changeType('10')"><i>其他</i></li>		
			</ul>	
		</scroll-view>
		
		<section class="pd-main" >
			<div class="pd-inner" style="padding-top: 0px;">
				<scroll-view class="record-task-list" scroll-y="true"  style="height: calc(100vh - 230rpx);"  @scrolltolower="onReachBottomEvent" v-if="listData.length>0">
					<ul class="pd-ullst5">
						<li @click="showDetail(item)"
							:key="item.id"
							v-for="item in listData">
							<h2>违法行为类别：{{selectType(type)}}  </h2>
							<h2>违法行为名称：{{item.WFXWMC}}</h2>
						</li>
					</ul>
				</scroll-view>
				<noData v-if="listData.length==0"></noData>
			</div>
		</section>
	</Page>
</template>

<script>
import { postIntelligentList } from '@/api/record.js';
import noData from '@/components/no-data.vue';
import Page from '@/pages/component/Page.vue';
export default {
	components:{
		Page,
		noData
	},
	
	data() {
		return {
			selectArr: ['', '大气', '水', '土壤', '噪声', '海洋', '核辐射', '固体废物', '建设项目', '环评', '其他'],
			pageNum: 1,
			listData: [],
			title: '智能判案',
			type: '1',
			pageHeight: 600,
			total: 0
		};
	},
	computed: {
		
	},
	watch: {},

	mounted() {
		this.getIntelligentList();
	},

	methods: {
		selectType(type){
			return this.selectArr[type];
		},
		
		onFixedContentHeight(layout) {
			this.pageHeight = layout.height;
		},
		
		onReachBottomEvent (){
			this.pageNum++;
			this.getIntelligentList();
		},
		
		showDetail(item){
			uni.setStorageSync('record-from-data', item)
			
			uni.navigateTo({
				url: './record-Intelligent-judgment-detail'
			})
		},
		
		changeType(type){
			this.type = type;
			this.pageNum = 1;
			this.listData = [];
			this.getIntelligentList();
		},
		
		getIntelligentList() {
			
			if(this.listData.length < this.total || this.listData.length == 0){
				postIntelligentList({
					type: 'wfxwList',
					WFXWLB: this.type,
					pageSize: 10,
					pageNum: this.pageNum
					// {
					//    wfxwList:{
					//        WFXWLB:''
					//    },
					// },
				}).then(res => {
					this.listData.push(...res.data_json.list);
					this.total = res.data_json.total;
				});
			}else{
				uni.showToast({
					icon: 'none',
				    title: '已经没有数据了',
				    duration: 2000
				});

			}
		}
	}
};
</script>

<style scoped>
.record-task-list {
	margin-top: 10px;
	background-color: #fff;
}
.pd-ultbs1{
	display: block;
}
.pd-ultbs1 li{
	float: left;
	padding: 0 23rpx;
}

.record-task-item {
	padding: 10px 10px;
	width: calc(100% - 20px);
}

.record-task-item:active {
	background-color: #007AFF;
}

.record-task-type-icon {
	width: 36px;
	height: 36px;
}

.task-content-layout {
	flex: 1;
	margin: 0 10px;
}

.task-type {
	margin-left: auto;
	border-radius: 50px;
	padding: 2px 10px;
	color: #fff;
	background-color: #0FAEFF;
}
</style>
