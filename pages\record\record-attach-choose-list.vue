<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-04-23 11:05:04
 * @LastEditTime: 2022-10-28 14:33:46
 * @LastEditors: 姚进玺 <EMAIL>
 * @Description: In User Settings Edit
 * @FilePath: /SmartFormWeb/pages/record/record-attach-choose-list.vue
-->

<template>
    <Page :padding="false">
        <template v-slot:bar>
            <NaviBar title="执法证据">
                <!-- <template v-slot:option>
					<view @click="allchange" v-if="!allChecked">全选</view>
                    <view @click="allchange" v-else>取消全选</view>
				</template> -->
            </NaviBar>
        </template>
        <view>
            <uni-notice-bar
                class="notic-box"
                showIcon="true"
                text="注：该列表是您在证据收集部分上传的内容，您可以将其带入到笔录当中去，建议不超过两份！"
            ></uni-notice-bar>
            <view class="attach-chooselength" v-if="attachData.length > 0"
                >共{{ attachData.length }}条，选择了{{ chooseLength }}条</view
            >
            <scroll-view :scroll-y="true" :style="pageListStyle">
                <checkbox-group
                    class="from-checkbox-group"
                    @change.stop="changePreview"
                >
                    <view
                        v-for="(item, index) in attachData"
                        :key="index"
                        class="attach-list"
                    >
                        <view class="attach-main">
                            <view class="attach-check">
                                <checkbox
                                    class="attach-checkbox"
                                    :value="item.WDBH"
                                    :checked="allChecked ? true : false"
                                    style="transform: scale(0.7)"
                                />
                            </view>
                            <view class="attach-image">
                                <image
                                    @click="lookAttach(item)"
                                    mode="aspectFit"
                                    class="attach-images"
                                    :src="iconShow(item)"
                                />
                            </view>
                            <view class="attach-info">
                                <view class="attach-info-name">{{
                                    item.WDMC
                                }}</view>
                                <view
                                    >上传时间：{{
                                        item.SCSJ || item.CJSJ
                                    }}</view
                                >
                                <view>文件大小：{{ item.WDDX }}</view>
                            </view>
                        </view>
                        <view v-if="item.REMARKS" class="attach-caption"
                            >照片描述：{{ item.REMARKS }}</view
                        >
                    </view>
                </checkbox-group>
                <NoData v-if="attachData.length == 0" type="data" />
            </scroll-view>
        </view>
        <button @click="choosecheckedArr">选择</button>
    </Page>
</template>

<script>
import NoData from '@/components/no-data.vue';
import iconDoc from '@/static/img/file/word.png';
import iconExcel from '@/static/img/file/excel.png';
import iconPpt from '@/static/img/file/ppt.png';
import iconPdf from '@/static/img/file/pdf.png';
import iconFile from '@/static/img/file/file.png';
import NaviBar from '@/pages/component/NaviBar.vue';
import Page from '@/pages/component/Page.vue';
import fileService from '@/api/file-service.js';
import { queryEvidenceTemplate, postGetBasicData } from '@/api/record.js';
export default {
    components: {
        NaviBar,
        Page,
        NoData
    },
    data() {
        return {
            checkedArr: [], //存放选中的值
            //复选框选中的值
            allChecked: false, //全选操作时用的参数，用于判断是否全选或者全不选
            data: null,
            taskId: '',
            templateId: '',
            chooseLength: 0, //选择的条数
            attachData: [] //第四部附件数据
        };
    },

    computed: {
        pageListStyle() {
            return {
                height: 'calc(100vh - 360rpx)'
            };
        }
    },

    mounted() {
        this.data = uni.getStorageSync('record-data');
        this.taskId = this.data.YWXTBH;
        this.loadAttachPickerTemplate();
    },

    methods: {
        loadAttachPickerTemplate() {
            queryEvidenceTemplate(this.taskId)
                .then(template => {
                    this.templateId = template.templateId;
                    this.loadTaskRecords();
                })
                .catch(error => {
                    // console.log(`获取证据收集表单模板出错：${ error }`)
                });
        },

        loadTaskRecords() {
            let cacheKeyOfRecordId = `recordID:${this.templateId}${this.taskId}`;
            let id = uni.getStorageSync(cacheKeyOfRecordId);
            let form = {
                recordId: id,
                mbbh: this.templateId
            };
            postGetBasicData(form).then(res => {
                this.attachData = res.data_fjxx;
                this.attachData = this.getFilterPhoto(this.attachData);
                this.attachData.forEach(element => {
                    element.checked = false;
                });
            });
        },

        iconShow(item) {
            switch (true) {
                case /doc|docx/g.test(item.WDHZ):
                    return iconDoc;
                case /xls|xlsx/g.test(item.WDHZ):
                    return iconExcel;
                case /ppt/g.test(item.WDHZ):
                    return iconPpt;
                case /pdf/g.test(item.WDHZ):
                    return iconPdf;
                case /jpg|jpeg|png/g.test(item.WDHZ):
                    if (item.url) {
                        return item.url;
                    } else if (item.WDBH) {
                        return fileService.getFileUrl(item.WDBH);
                    }
                default:
                    return iconFile;
            }
        },

        getFilterPhoto(data) {
            let list = [];
            data.forEach(element => {
                if (/.jpg|.jpeg|.png|jpg|png|jpeg/g.test(element.WDHZ)) {
                    list.push(element);
                }
            });
            return list;
        },

        changePreview(e) {
            var items = this.attachData,
                values = e.detail.value;
            for (var i = 0, lenI = items.length; i < lenI; ++i) {
                const item = items[i];
                if (values.includes(item.WDBH)) {
                    this.$set(item, 'checked', true);
                } else {
                    this.$set(item, 'checked', false);
                }
            }
            this.attachData = items;
            this.getChooseLength();
        },

        getChooseLength() {
            let data = [];
            this.attachData.forEach(element => {
                if (element.checked) {
                    data.push(element);
                }
            });
            this.chooseLength = data.length;
            return data;
        },

        allchange() {
            this.attachData.forEach(element => {
                element.checked = !this.allChecked;
            });
            if (this.allChecked) {
                this.allChecked = false;
                for (let item of this.attachData) {
                    let itemVal = String(item.value);
                    if (!this.checkedArr.includes(itemVal)) {
                        this.checkedArr.push(itemVal);
                    }
                }
            } else {
                this.allChecked = true;
                this.checkedArr = [];
            }
            this.getChooseLength();
        },

        choosecheckedArr() {
            let data = this.getChooseLength();
            uni.$emit('chooseEvidenceData', data);
            uni.navigateBack({
                delta: 1
            });
        },

        lookAttach(item) {
            let attach = [];
            this.attachData.forEach(element => {
                attach.push(fileService.getFileUrl(element.WDBH));
            });
            uni.previewImage({
                urls: attach,
                indicator: 'number',
                current: fileService.getFileUrl(item.WDBH)
            });
        }
    }
};
</script>

<style scoped>
.attach-list {
    border-bottom: 1rpx solid #eee;
    padding: 16rpx 0;
}

.attach-main {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    margin-bottom: 10rpx;
}

.attach-check {
    display: flex;
    align-items: center;
    justify-content: center;
    /* width: 5%; */
}

.attach-checkbox {
    display: flex;
    align-items: center;
    justify-content: center;
}

.attach-image {
    height: 260rpx;
    width: 260rpx;
    background-color: #ccc;
    border-radius: 10rpx;
}

.attach-images {
    height: 260rpx;
    width: 260rpx;
}

.attach-info {
    width: 50%;
    display: flex;
    height: 100%;
    /* align-items: center; */
    flex-direction: column;
    justify-content: flex-start;
    padding: 20rpx 0;
    color: #888;
    font-size: 26rpx;
}

.attach-info view {
    padding: 14rpx;
}

.attach-info-name {
    font-size: 30rpx;
    color: #333;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

.attach-caption {
    font-size: 28rpx;
    color: #aaa;
    padding: 10rpx 40rpx;
    display: flex;
    justify-content: center;
}

.attach-chooselength {
    padding: 0 10rpx;
    color: #111;
}
</style>
