<template>
    <view class="record-attach-picker">
        <view class="flex-column-layout form-content-layout">
            <view
                id="formParent"
                style="
                    width: calc(100% - 16px);
                    height: calc(100% -16px);
                    padding-top: 8px;
                    padding-bottom: 100rpx;
                    flex: 1;
                "
            >
                <template-form
                    ref="templateForm"
                    :parentHeight="formLayoutHeight"
                    :constraints="constraints"
                    :editable="editable"
                    :template="template"
                    :record-id="recordId"
                    :form-data="formData"
                    style="position: relative;"
                ><p class="tips">需使用附带时间、地点信息的水印照片，推荐使用“今日水印相机”、“元道经纬相机”等软件拍摄。</p></template-form>
            </view>
            
            <PButton
                @click.native="fixedAttachs"
                class="height:20px"
                :name="nextStepText"
            />
        </view>
    </view>
</template>

<script>
import PButton from '@/components/p-button';
import iconAdd from '@/static/img/record/icon_add_attach.png';
import templateForm from 'bowo-form';
import { mapState } from 'vuex';
import {
    queryTaskRecords,
    queryRecordData,
    submitRecord,
    queryEvidenceTemplate
} from '@/api/record.js';
import { guid } from '@/common/uuid.js';
import styleUtil from '@/common/style.js';
import PCard from '@/pages/component/PCard.vue';
import attachIcon from '@/pages/component/attach-icon.vue';
import recordFrag from './record-fragment.js';
import formService from '@/node_modules/bowo-form/components/service/form-service.js';
//证据收集表单模板ID
const mixins = [recordFrag];

/**
 * 执法任务的证据收集使用指定的动态表单来实现，代替手动绘制的表单，该表单对应每条执法任务，仅保存一份
 */
export default {
    name: 'RecordAttachPicker',
    components: {
        PCard,
        attachIcon,
        PButton,
        templateForm
    },

    // #ifdef MP-ALIPAY
    mixins: mixins.map(item => ({ ...item, props: {} })),
    // #endif
    // #ifndef MP-ALIPAY
    mixins: mixins,
    // #endif

    props: {
        // #ifdef MP-ALIPAY
        ...mixins.reduce(
            (prev, curr) => ({ ...prev, ...(curr.props || {}) }),
            {}
        ),
        // #endif

        data: {
            type: Object
        },

        attchType: {
            type: Boolean
        }
    },

    data() {
        return {
            formLayoutHeight: -1, //表单的高度
            templateId: '',
            template: {}, //基础照片表单
            iconAdd,
            constraints: [], //表单联动规则数组集合
            editable: true, //表单是否可编辑
            taskId: '',
            recordId: '',
            formData: {} //基础数据
        };
    },

    onReady() {
        // #ifdef APP-PLUS
        this.resetHeight();
        // #endif
    },

    beforeDestroy() {
        // let id  = uni.getStorageSync('form_data_key');
        submitRecord(this.data, this.template, this.recordId, this.formData);
        // console.log(this.formData,this.data,this.template,this.recordId);
    },

    // // #ifdef MP
    // computed: mapState({
    // 	mpFormData: state => state.formData
    // }),
    // // #endif

    mounted() {
        // #ifdef APP-PLUS
        this.resetHeight();
        // #endif
        this.listenFormValueChange();

        //初始化任务编号
        this.taskId = this.data.YWXTBH;
        this.loadAttachPickerTemplate();
    },

    methods: {
        /**
         * 在手机上需要重置表单容器高度
         */
        resetHeight() {
            this.formLayoutHeight = 600;
            styleUtil.getNodeLayout(this, '#formParent').then(layout => {
                this.formLayoutHeight = layout.height - 100;
            });
        },

        /**
         * 监听表单字段值改变，缓存本地数据
         */
        listenFormValueChange() {
            let _self = this;
            uni.$on('onFormValueChange', () => {
                // #ifdef MP
                let latestFormData = this.mpFormData;
                // #endif

                // #ifndef MP
                let latestFormData = this.formData;
                // #endif
                uni.setStorage({
                    key: 'form-Data:' + this.recordId,
                    data: latestFormData
                });
            });
        },

        /**
         * 加载证据收据表单模板
         */
        loadAttachPickerTemplate() {
            queryEvidenceTemplate(this.taskId)
                .then(template => {
                    this.templateId = template.templateId;
                    this.template = template;
                    this.loadTaskRecords();
                })
                .catch(error => {
                    // console.log(`获取证据收集表单模板出错：${ error }`)
                });
        },

        /**
         * 加载任务所有笔录数据，用于判断服务器上是否存在其他设备填写的表单数据
         * 表单加载完成后调用
         */
        loadTaskRecords() {
            let _self = this;
            queryTaskRecords(this.taskId).then(records => {
                let remoteRecord = null;
                //过滤出任务下该表单已提交的记录，用于初始化表记录ID
                for (let record of records) {
                    if (_self.templateId === record.mbbh) {
                        remoteRecord = record;
                        break;
                    }
                }
                // console.log(remoteRecord);
                _self.initRecordId(remoteRecord);
            });
        },

        /**
         * 初始化证据收集记录ID，进入该页面初始化对应该表单唯一的记录ID，
         * 总是先检查是否有缓存ID，否则生成一个，并保存
         * @param {Object} existRecord 从服务端查询到的记录
         */
        initRecordId(existRecord) {
            let cacheKeyOfRecordId = `recordID:${this.templateId}${this.taskId}`;
            if (existRecord) {
                this.recordId = existRecord.recordId;
            } else {
                let cachedRecordId = uni.getStorageSync(cacheKeyOfRecordId);
                this.recordId = cachedRecordId || guid();
            }
            uni.setStorageSync(cacheKeyOfRecordId, this.recordId);
            this.loadAttachRecord(this.recordId);
        },

        /**
         * 加载附件表单数据
         */
        loadAttachRecord(recordId) {
            let _self = this;
            // let serverRecord = queryRecordData(this.templateId, recordId);

            // Promise.all([serverRecord, _self.loadCacheFromLocal()]).then(
            //     eitherRecords => {
            //         // debugger
            //         let submittedRecord = eitherRecords[0];
            //         //服务端未查询到数据的情况下，总是提交一次
            //         if (submittedRecord === null) {
            //             // submitRecord(_self.data, _self.template, _self.recordId, {})
            //             // 	.then(resp => {
            //             // 		_self.log(resp, '提交证据收集笔录响应')
            //             // 	})
            //         } else {
            //             // _self.log(submittedRecord, '已提交笔录')
            //         }
            //         let record = submittedRecord || eitherRecords[1];
            //         if (record) {
            //             _self.$nextTick(function () {
            //                 _self.formData = record;
            //             });
            //         }
            //     }
            // );

            formService
                    .getRecordDetailById(this.templateId, recordId)
                    .then(recordData => {
                       console.log(recordData);
                       this.formData = recordData;
                    })
                    .catch(error => reject(error));
        },

         /**
         * 等待表单元素挂载完成
         */
         waitFormMounted() {
            return new Promise((resolve, reject) => {
                setTimeout(() => {
                    resolve();
                }, 1600);
            });
        },

        /**
         * 从本地加载附件表单笔录缓存
         */
        loadCacheFromLocal() {
            return new Promise(resolve => {
                let keyOfCache = `form-Data:${this.recordId}`;
                let cacheRecord = uni.getStorageSync(keyOfCache) || null;
                resolve(cacheRecord);
            });
        },

        fixedAttachs() {
            let rwlx = uni.getStorageSync("record-data").RWLX;
            if(rwlx == 'WXYGJC' || rwlx == 'SYDJC' || rwlx == 'ZRBHQ') {
                if (
                (this.formData.ZJSJFJ_PICTURE &&
                this.formData.ZJSJFJ_PICTURE.length > 0) || (this.formData.ZJSJFJ_VIDEO &&
                this.formData.ZJSJFJ_VIDEO.length > 0)|| (this.formData.ZJSJFJ_DOCUMENT &&
                this.formData.ZJSJFJ_DOCUMENT.length > 0)
                ) {
                    this.doNext(this.stepIndex);
                } else {
                    uni.showToast({
                        title: '需上传图片或视频或文件后再进行下一步',
                        duration: 2000,
                        icon: 'none'
                    });
                }
            } else {
                 this.doNext(this.stepIndex);
            }
            
        }
    }
};
</script>

<style scoped>
.record-attach-picker {
    height: 100%;
    /* padding: 10px; */
    background-color: #f4f4f4;
}

.attach-add-button {
    width: 42px;
    height: 42px;
    margin-left: 16px;
}

.form-content-layout {
    width: 100%;
    height: 100%;
}

.record-title {
    padding-top: 140rpx;
    /* position: relative; */
}

.tips{
    /* position: absolute; */
    padding: 20rpx;
    bottom: 0rpx;
    font-size: 28rpx;
}

.tips::before{
    content: '*';
    color: red;
}
</style>
