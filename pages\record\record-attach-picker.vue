<template>
  <view class="record-attach-picker">
    <view class="flex-column-layout form-content-layout">
      <view
        id="formParent"
        style="
          width: calc(100% - 16px);
          height: calc(100% -16px);
          padding-top: 8px;
          padding-bottom: 100rpx;
          flex: 1;
        "
      >
        <template-form
          ref="form"
          :height="formHeight"
          :editable="true"
          :template="template"
          :record-id="recordId"
          :form-data="formData"
          @onFormMounted="onFormMounted"
          @onFormDataUpdate="listenFormValueChange"
          ><p class="tips">
            需使用附带时间、地点信息的水印照片，推荐使用“今日水印相机”、“元道经纬相机”等软件拍摄。
          </p></template-form
        >
      </view>

      <PButton
        @click.native="fixedAttachs"
        class="height:20px"
        :name="nextStepText"
      />
    </view>
    <!-- show-cancel-button -->
    <u-modal
      :content-style="{
        color: 'red',
      }"
      @confirm="show = false"
      v-model="show"
      :content="'严禁在本互联网非涉密平台处理、传输国家秘密，请确认扫描、传输的文件资料不涉及国家秘密'"
    ></u-modal>
  </view>
</template>

<script>
import TemplateForm from "bowo-form";
import PButton from "@/components/p-button";
import { guid } from "bowo-sdk/util/uuid.js";
import {
  queryTaskRecords,
  queryRecordData,
  submitRecord,
  queryEvidenceTemplate,
} from "@/api/record.js";
import formService from "bowo-form/components/service/form-service.js";

import recordFrag from "./record-fragment.js";

//证据收集表单模板ID
const mixins = [recordFrag];

const CHOOSE_TYPE_ALBUM = "album";
const CHOOSE_TYPE_CAMERA = "camera";

const MENU_ID_ALBUM = "album";
const MENU_ID_TAKE_PHOTO = "take_photo";
const MENU_ID_ALBUM_VIDEO = "album_video";
const MENU_ID_SHOOT_VIDEO = "shoot_video";
const MENU_ID_DOCUMENT = "document";

const ATTACH_MENU_DICT = {
  PICTURE: [
    { id: MENU_ID_ALBUM, name: "相册", sourceType: [CHOOSE_TYPE_ALBUM] },
    { id: MENU_ID_TAKE_PHOTO, name: "拍照", sourceType: [CHOOSE_TYPE_CAMERA] },
  ],
  VIDEO: [
    {
      id: MENU_ID_ALBUM_VIDEO,
      name: "上传视频",
      sourceType: [CHOOSE_TYPE_ALBUM],
    },
    {
      id: MENU_ID_SHOOT_VIDEO,
      name: "拍摄视频",
      sourceType: [CHOOSE_TYPE_CAMERA],
    },
  ],
  DOCUMENT: [{ id: MENU_ID_DOCUMENT, name: "文件" }],
};

/**
 * 执法任务的证据收集使用指定的动态表单来实现，代替手动绘制的表单，该表单对应每条执法任务，仅保存一份
 */
export default {
  name: "RecordAttachPicker",
  components: {
    TemplateForm,
    PButton,
  },

  // #ifdef MP-ALIPAY
  mixins: mixins.map((item) => ({
    ...item,
    props: {},
  })),
  // #endif
  // #ifndef MP-ALIPAY
  mixins: mixins,
  // #endif

  props: {
    // #ifdef MP-ALIPAY
    ...mixins.reduce(
      (prev, curr) => ({
        ...prev,
        ...(curr.props || {}),
      }),
      {}
    ),
    // #endif

    data: {
      type: Object,
    },

    attchType: {
      type: Boolean,
    },
  },

  data() {
    return {
      templateId: "",
      template: {}, //基础照片表单
      constraints: [], //表单联动规则数组集合
      taskId: "",
      recordId: "",
      formData: {}, //基础数据
      show: true,
    };
  },

  beforeDestroy() {
    submitRecord(this.data, this.template, this.recordId, this.formData);
  },

  computed: {
    formHeight() {
      let nextStepBtnHeight = uni.upx2px(160);
      return this.height - nextStepBtnHeight;
    },
  },

  mounted() {
    this.listenFormValueChange();

    //初始化任务编号
    this.taskId = this.data.YWXTBH;
    this.loadAttachPickerTemplate();
  },

  methods: {
    /**
     * 监听表单字段值改变，缓存本地数据
     */
    listenFormValueChange(formData) {
      uni.setStorage({
        key: "form-Data:" + this.recordId,
        data: formData,
      });
    },

    /**
     * 加载证据收据表单模板
     */
    loadAttachPickerTemplate() {
      queryEvidenceTemplate(this.taskId).then((template) => {
        this.templateId = template.templateId;
        this.template = template;
        this.loadTaskRecords();
      });
    },

    /**
     * 加载任务所有笔录数据，用于判断服务器上是否存在其他设备填写的表单数据
     * 表单加载完成后调用
     */
    loadTaskRecords() {
      queryTaskRecords(this.taskId).then((records) => {
        let remoteRecord = null;
        //过滤出任务下该表单已提交的记录，用于初始化表记录ID
        for (let record of records) {
          if (this.templateId === record.mbbh) {
            remoteRecord = record;
            break;
          }
        }
        this.initRecordId(remoteRecord);
      });
    },

    /**
     * 初始化证据收集记录ID，进入该页面初始化对应该表单唯一的记录ID，
     * 总是先检查是否有缓存ID，否则生成一个，并保存
     * @param {Object} existRecord 从服务端查询到的记录
     */
    initRecordId(existRecord) {
      let cacheKeyOfRecordId = `recordID:${this.templateId}${this.taskId}`;
      if (existRecord) {
        this.recordId = existRecord.recordId;
      } else {
        let cachedRecordId = uni.getStorageSync(cacheKeyOfRecordId);
        this.recordId = cachedRecordId || guid();
      }
      uni.setStorageSync(cacheKeyOfRecordId, this.recordId);
      this.loadAttachRecord(this.recordId);
    },

    /**
     * 加载附件表单数据
     */
    loadAttachRecord(recordId) {
      let serverRecord = formService.getRecordDetailById(
        this.templateId,
        recordId
      );
      Promise.all([serverRecord, this.loadCacheFromLocal()]).then(
        (eitherRecords) => {
          // debugger
          let submittedRecord = eitherRecords[0];
          let record = submittedRecord || eitherRecords[1];
          if (record) {
            this.formData = record;
          }
        }
      );
    },

    /**
     * 从本地加载附件表单笔录缓存
     */
    loadCacheFromLocal() {
      return new Promise((resolve) => {
        let keyOfCache = `form-Data:${this.recordId}`;
        let cacheRecord = uni.getStorageSync(keyOfCache) || null;
        resolve(cacheRecord);
      });
    },

    fixedAttachs() {
      let rwlx = uni.getStorageSync("record-data").RWLX;
      let formData = this.$refs.form.getFormData();
      if (rwlx == "WXYGJC" || rwlx == "SYDJC" || rwlx == "ZRBHQ") {
        if (
          (formData.T_ATTACH_ZJSJFJ_PICTURE &&
            formData.T_ATTACH_ZJSJFJ_PICTURE.length > 0) ||
          (formData.T_ATTACH_ZJSJFJ_VIDEO &&
            formData.T_ATTACH_ZJSJFJ_VIDEO.length > 0) ||
          (formData.T_ATTACH_ZJSJFJ_DOCUMENT &&
            formData.T_ATTACH_ZJSJFJ_DOCUMENT.length > 0)
        ) {
          this.doNext(this.stepIndex);
        } else {
          uni.showToast({
            title: "需上传图片或视频或文件后再进行下一步",
            duration: 2000,
            icon: "none",
          });
        }
      } else {
        this.doNext(this.stepIndex);
      }
    },

    onFormMounted() {
      this.resetAttachElementsMenus();
    },

    resetAttachElementsMenus() {
      for (let classCode in ATTACH_MENU_DICT) {
        let targetAttachElement = this.findAttachElementByClass(classCode);
        if (targetAttachElement === null) {
          continue;
        }
        targetAttachElement.resetPickAttachMenus(ATTACH_MENU_DICT[classCode]);
      }
    },

    findAttachElementByClass(classCode) {
      let evidencPickElementMatch = (elementInstance) => {
        let elementConfig = elementInstance.template;
        return (
          elementConfig.type === "ATTACH_ELEMENT" &&
          elementConfig.uploadFileClass === classCode
        );
      };
      let elements = this.$refs.form.findMatchedElements(
        evidencPickElementMatch
      );
      if (elements.length === 0) {
        return null;
      }

      return elements[0];
    },
  },
};
</script>

<style scoped>
.record-attach-picker {
  height: 100%;
  /* padding: 10px; */
  background-color: #f4f4f4;
}

.attach-add-button {
  width: 42px;
  height: 42px;
  margin-left: 16px;
}

.form-content-layout {
  width: 100%;
  height: 100%;
}

.record-title {
  padding-top: 140rpx;
  /* position: relative; */
}

.tips {
  /* position: absolute; */
  padding: 20rpx;
  bottom: 0rpx;
  font-size: 28rpx;
}

.tips::before {
  content: "*";
  color: red;
}
</style>
