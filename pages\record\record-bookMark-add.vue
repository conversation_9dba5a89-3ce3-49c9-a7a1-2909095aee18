<!--
 * @Author: <PERSON><PERSON>inXi
 * @Date: 2021-03-11 17:28:52
 * @LastEditTime: 2021-04-15 16:26:19
 * @LastEditors: Please set LastEditors
 * @Description: 便签的新增服务
 * @FilePath: /SmartFormWeb/pages/record/record-bookMark-add.vue
-->

<template>
    <Page title="便签新增">
        <template v-slot:bar>
			<NaviBar title="便签新增"
			         >
				<template v-slot:option>
					<view class="record-list" @click="onSaveForm">
                        保存
					</view>
				</template>
			</NaviBar>
		</template>
        <template-form  :editable="editable"
                        :form-data="formData"
			            :template="template"/>
    </Page>
</template>

<script>
import Page from '@/pages/component/Page.vue'
import NaviBar from '@/pages/component/NaviBar.vue'
import templateForm from '@/pages/form/template-form.vue'
import {
    dynamicformsave,
    queryTaskFormTemplate,
    queryRecordData
} from '@/api/record.js'
import {
    guid
} from '@/common/uuid.js';
const TEMPLATE_ID = '20210311151707eeab57f4bf794b648593d033ad2d6cda'
export default {
    components:{
        Page,
        templateForm,
        NaviBar
    },

    data() {
        return {
            recordId:'',//表单唯一编号，新增随机生成，编辑用之前的
            editable: true, //表单是否可编辑
            template: {}, //模板的基础
			formData: {}, //表单的数据
            bookMarkType:'add'//便签的状态，默认是新增，add:新增，edit:编辑
        };
    },

    onLoad(options){
        if(options.recordId){
            this.recordId = options.recordId
            this.bookMarkType = 'edit'
        }else{
            this.recordId =  guid()
        }
    },

    mounted(){
        this.getFormTemplate()
    },

    methods: {
        getFormTemplate(){
            queryTaskFormTemplate('',TEMPLATE_ID).then((res)=>{
                this.template = res
                if(this.bookMarkType === 'edit'){
                    this.getFormInfo()
                }
            })
        },

        //获取便签详情
        getFormInfo(){
            let self = this
            queryRecordData(TEMPLATE_ID,self.recordId).then((res)=>{
                self.formData = res
            })
        },

        //便签的保存
        onSaveForm(){
            let self = this
            let data = self.formData
            let tableName = self.template['templateTable'];
            let form = {
                client_type: 'mobile_web',
                service: 'DYNAMICFORM_SAVE',
                record_id: self.recordId,
                template_id: TEMPLATE_ID,
                user_id: uni.getStorageSync('user_info').yhid || 'SDSZFJ',
            }
            form[tableName] = data
            dynamicformsave(form).then((res)=>{
                uni.showToast({
                    title: '保存成功！',
                    duration: 2000,
                    icon: 'success'
                });
                setTimeout(() => {
                    uni.navigateBack({
                        delta: 1,
                    });
                }, 1000)
            })
        }

    },
};
</script>

<style scoped>

</style>
