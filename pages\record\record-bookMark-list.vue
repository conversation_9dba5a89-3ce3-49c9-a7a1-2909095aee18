<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-03-11 16:58:15
 * @LastEditTime: 2021-04-21 04:15:55
 * @LastEditors: Please set LastEditors
 * @Description: 便签的管理
 * @FilePath: /SmartFormWeb/pages/record/record-bookMark.vue
-->

<template>
   <Page :padding="false">
        <template v-slot:bar>
			<NaviBar title="便签">
				<template v-slot:option>
					<view class="record-list" @click="addBookMark">
                        新增
					</view>
				</template>
			</NaviBar>
		</template>
        <scroll-view :scroll-y="true" :style="pageListStyle">
        <uni-list-item v-for="(item,index) in bookMarkList" :key="index" :title="item.CJSJ" >
            <template slot="footer">
                 <view @click="editBookMark(item)" >编辑</view>
                 <view @click="deleteBookMark(item)" style="margin-left:10rpx;">删除</view>
            </template>
        </uni-list-item>
        <NoData :type="bookMarkList.length < 1 ? 'data' : ''" />
        </scroll-view>
   </Page>
</template>

<script>
import Page from '@/pages/component/Page.vue'
import NaviBar from '@/pages/component/NaviBar.vue'
	import {
		postBookMarkList,
        deleteBookMarkList
	} from '@/api/record.js'
    import NoData from '@/components/no-data.vue';
const TABLE_NAME = 'T_COMMON_BQ'

export default {
    components:{
        Page,
        NaviBar,
        NoData
    },

    data() {
        return {
            bookMarkList:[]
        };
    },

    computed: {
		pageListStyle() {
			return {
				height: 'calc(100vh - 160rpx)',
				backgroundColor: '#fff'
			};
		}
	},

    onShow(){
        this.getBookMarkList()
    },

    methods: {
        //获取便签列表
        getBookMarkList(){
            postBookMarkList({}).then((res)=>{
                this.bookMarkList = res.data_json.list
            })
        },

        editBookMark(item){
            uni.navigateTo({
                url: `/pages/record/record-bookMark-add?recordId=${item.XH}`
            })
        },

        deleteBookMark(item){
            deleteBookMarkList({
                table:TABLE_NAME,
                primaryKey:'XH',
                XH:item.XH
            }).then((res)=>{
                this.getBookMarkList()
                uni.showToast({
                    title: '删除成功',
                    duration: 2000,
                    icon: 'success'
                });
            })
        },

        addBookMark(){
            uni.navigateTo({
                url: `/pages/record/record-bookMark-add`
            })
        }
    },
};
</script>

<style scoped>

</style>
