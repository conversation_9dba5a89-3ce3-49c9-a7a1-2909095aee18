<!--
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2020-11-23 18:38:02
 * @LastEditTime: 2021-05-13 15:36:59
 * @LastEditors: Please set LastEditors
 * @Description: 移动执法第二步，亮证告知
 * @FilePath: /SmartFormWeb/pages/record/record-cert-confirm.vue
-->
<template>
	<view>
		<view class="flex-row-layout"
		      >
			<text class="cert-prop"
			      style="margin-left: 10px;">执法人员</text>
			<!-- <text class="cert-prop cert-prop-value">{{names || otherNmaes}}</text> -->
			<view class="conton">
			<view class="container" v-for="(item,index) in inspectors" :key="index" >
		        <image src="/static/img/record/icon_delete_corner.png"
					@click.stop="deletePerson(index)"
					class="imgic"/>
		        <view class="content">{{item.YHMC}}</view>
			</view>
		</view>
			<image class="navi-next" :src="person_choose_icon" @click="selectPerson"/>
		</view>
		<view class="list-divider" />

		
		<view style="width: calc(100% - 20px); padding: 10px;">
			<view class="confirm-info">
				<text style="color: #666">
				我们是{{company ||  personsForm.bmmc}}的行政执法人员，这是我们的执法证件{{nameIDs || otherNameID}}，请过目确认。
				
				</text>
			</view>
			<view class="confirm-info">
				<text style="color: #666">
				今天我们依法对你单位进行调查并了解有关情况，请配合，如实回答询问和提供材料，不得拒绝、阻碍、隐瞒或者提供虚假情况。如果你认为我们与本案有利害关系，可能影响公正办案，可以申请我们回避，并说明理由 。
				</text>
			</view>
			<!-- <template-form
                    ref="TemplateForm"
                    :style="{height: '600rpx '}"
                    :editable="editable"
                    :template="template"
                    :record-id="recordId"
                    :form-data="formData"
                /> -->
			<!-- <view class="cert-speaker" /> -->
		</view>
		<PButton @click.native="fixedPersons"
		         name="确认" />
	</view>
</template>

<script>
	import templateService from '@/node_modules/bowo-form/components/service/template-service.js';
	import templateUtil from '@/node_modules/bowo-form/components/template.js';
	import formService from '@/node_modules/bowo-form/components/service/form-service.js';
	import onform from "@/api/onsaveForm.js";
	import templateForm from '@/node_modules/bowo-form';
	import PButton from '@/components/p-button'
	import {
		EVENT_SELECT_CONFIRM,
	} from '@/pages/department/department.js';
	import iconSpeaker from '@/static/img/record/icon_speaker.png'
	import person_choose_icon from '@/static/img/record/person_choose.png'
	import {
		postQueryZfryxx,
		postPersonEnter,
		postUpDataPerson
	} from '@/api/record.js'
	import recordFrag from './record-fragment.js'
	const mixins = [recordFrag]

	export default {
		name: 'RecordCertConfirm',
		// #ifdef MP-ALIPAY
		mixins: mixins.map(item => ({ ...item,
			props: {}
		})),
		// #endif
		// #ifndef MP-ALIPAY
		mixins: mixins,
		// #endif

		components: {
			PButton,
			templateForm
		},

		props: {
			// #ifdef MP-ALIPAY
			...mixins.reduce((prev, curr) => ({ ...prev,
				...(curr.props || {})
			}), {}),
			// #endif
		},

		data() {
			return {
				formLayoutHeight: 1000, //表单的高度
				templateId: '202405071150502372eeaf9da5401fa39f4286df38bf06',
				recordId: '',
				template: {}, //基础照片表单
				editable: true, //表单是否可编辑
				formData: {}, //基础数据
				person_choose_icon,
				personsForm: {},//人员信息
				iconSpeaker,
				otherNmaes: '',
				otherNameID: '',
				inspectors: [],//选择的人员信息
				//是否注册选择人员监听
				registered: false,
				onlyID: null, //单独部门的ID
				bmID: null //最外层部门的ID
			}
		},

		computed: {
			//姓名的渲染
			names: function() {
				return this.inspectors.map(p => p.YHMC).join(',')
			},

			//姓名加ID的选择
			nameIDs() {
				return this.inspectors.map(p => p.YHMC + `(${p.ZFZHSD || p.ZFZHHBB})`).join(',')
			},

			//最外层部门的渲染
			company() {
				let data = this.inspectors.map(p => p.BMMC)
				data = [...new Set(data)]
				return data.join(',')
			},

			//执法证号
			zfzh() {
				return this.inspectors.map(p => p.ZFZHSD || p.ZFZHHBB).join(',')
			}
		},

		watch: {
			nameIDs(val) {
				let that = this
				this.onlyID = this.inspectors.map(p => p.YHID).join(',')
				this.bmID = this.inspectors.map(p => p.FBMBH).join(',')
				let data = {
					names: this.names,
					nameIDs: this.nameIDs,
					onlyID: this.onlyID,
					bmmc: this.company,
					bmid: this.bmID,
					zfzh: this.zfzh
				}
				this.personsForm = data
				uni.setStorage({
					key: 'ZFRY_NAME' + that.data.YWXTBH,
					data: that.personsForm
				})
			}
		},

		mounted() {
			this.data = uni.getStorageSync('record-data')
			this.recordId = this.data.YWXTBH;
			this.getPerson()
			this.loadAttachPickerTemplate();
		},

		destroyed() {
			if (this.registered) {
				uni.$off(EVENT_SELECT_CONFIRM)
			}
		},

		methods: {
			getFormRef() {
            	return this.$refs.TemplateForm;
        	},
			loadAttachPickerTemplate(){
				templateService.getTemplateById(this.templateId).then((templateJson) => {
					let groupedChildTemplate =
						templateUtil.packPeerElementIntoGroup(templateJson);
					this.template = groupedChildTemplate;
					formService
						.getRecordDetailById(this.templateId, this.recordId)
						.then((detail) => {
							this.formData = detail;
						});
				});
			},

			/**
			* @description: 跳转到部门人员选择，并监听返回的人员信息
			*/
			selectPerson() {
				let _self = this
				this.registered = true
				uni.$once(EVENT_SELECT_CONFIRM, (persons) => {
					_self.registered = true
					_self.inspectors = persons
				})
				uni.navigateTo({
					url: '/pages/department/department?multi=' + true + '&checked=' + _self.personsForm.onlyID
				})
			},


			/**
			* @description: 获取人员信息
			* @param {Array} personData 人员的缓存数据
			*/
			getPerson() {
				let that = this
				uni.getStorage({
					key: 'ZFRY_NAME' + this.data.YWXTBH,
					success: function(r) {
						that.otherNmaes = r.data.names
						that.otherNameID = r.data.nameIDs
						that.personsForm = r.data
						postQueryZfryxx({
							service: "QUERY_ZFYH_INFO",
							isDelDept: true
						}).then((res) => {
							let personData = r.data.names.split(',')
							res.data_array.forEach(element => {
								personData.forEach((e) => {
									if (e === element.YHMC) {
										that.inspectors.push(element)
										if(!that.inspectors.includes(element)){
											that.inspectors.push(element)
										}	
									}
								})
							});
						let obj = {};
						let peon = that.inspectors.reduce((cur,next) => {
							obj[next.YHMC] ? "" : obj[next.YHMC] = true && cur.push(next);
							return cur;
						},[]) //设置cur默认类型为数组，并且初始值为空的数组
						that.inspectors = peon
						})	
					},
					fail(res) {
						postQueryZfryxx({
							service: "QUERY_ZFYH_INFO",
							isDelDept: true
						}).then((res) => {
							let personData = that.data.JCR.split(',')
							res.data_array.forEach(element => {
								personData.forEach((e) => {
									if (e === element.YHID ) {
										that.inspectors.push(element)
									}
								})
							});
						})
					}
				})
			},

			/**
			* @description: 人员更新确认；并且缓存人员信息
			*/
			fixedPersons() {
				//获取表单的格式校验的内容
				// let requiredFields = this.getFormRef().checkRequireFields();
				// if (requiredFields.length > 0) {
				// 	uni.showToast({
				// 		title: '请上传执法依据',
				// 		icon: 'none',
				// 		duration: 2000
				// 	});
				// 	return;
				// }
					let self = this
					uni.setStorage({
						key: 'ZFRY_NAME' + this.data.YWXTBH,
						data: this.personsForm
					})
					let form = {}
					let data = []
					data.push({
						XH: this.data.YWXTBH,
						JCR: this.personsForm.onlyID,
						JCRMC: this.personsForm.names,
					})
					form.isUpdateFlow = true
					form.service = "BATCH_SAVE_BEAN",
					form.primaryKey = 'XH',
					form.table = "T_YDZF_XCZF_JBXX"
					form.bean = data
					if (this.personsForm.names && this.personsForm.names.indexOf(",") != -1) {
						
						postPersonEnter(
							form).then((res) => {
							postUpDataPerson({
								YWXTBH: self.data.YWXTBH,
								JCR: self.personsForm.onlyID,
								JCRMC: self.personsForm.names,
								ZFZHSD: self.personsForm.zfzh
							}).then((res) => {
								this.submitFormData();

							})
						})
					} else {
						uni.showToast({
							title: '请至少选择两名或两名以上的执法人员',
							duration: 2000,
							icon: 'none'
						});
					}
			},

			submitFormData() {
					this.doNext(this.stepIndex);
						uni.showLoading({
						title: '正在提交数据'
					});
					// let formData = Object.assign({}, this.getFormRef().getFormData());
					// formService
					// 	.submitFormData(this.template, this.recordId, formData)
					// 	.then((resp) => {
					// 		uni.showModal({
					// 			title: '提交成功',
					// 			content: resp
					// 		});
					// 	})

					// 	.catch((error) => {
					// 		uni.showModal({
					// 			title: '提交出错',
					// 			content: error
					// 		});
					// 	})
					// 	.finally(() => {
					// 		uni.hideLoading();
					// 	});
        	},

			deletePerson(index){
				this.inspectors.splice(index,1)
			}
		}
	}
</script>

<style scoped>
	.record-cert-confirm {
		width: 100%;
		height: 100%;
	}

	.cert-prop {
		height: 42px;
		line-height: 42px;
		font-size: 16px;
		color: #666
	}

	.cert-prop-value {
		flex: 1;
		margin: 0 10px;
		text-align: right;
		color: #666;
	}

	.cert-speaker {
		width: 36px;
		height: 36px;
		background-size: 22px 22px;
		background: url(~@/static/img/record/icon_speaker.png) no-repeat center;
	}

	.conton{
		display: flex;
		flex-wrap: wrap;
		padding: 10rpx 10rpx 0 10rpx;
		width: 68%;
	}

	.container{
		position:relative;
		margin-right: 16rpx;
		margin-bottom: 14rpx;
	}

	.content{
		padding: 12rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		background-color: #EEEEEE;
		color: #333333;
		font-size: 28rpx;
		border-radius: 10rpx;
	}

	.navi-next{
		width: 50rpx;
		height: 50rpx;
	}

	.flex-row-layout{
		display: flex;
		/* justify-content: s; */
		align-items: center;
	}

	.imgic {
		position:absolute;
		top:-13rpx;
		right:-13rpx;
		width: 32rpx;
		height: 32rpx;
		z-index: 10;
	}

	.confirm-info{
		text-indent:66rpx;
	}

</style>
