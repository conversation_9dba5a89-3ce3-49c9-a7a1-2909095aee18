<template>
	<view id="fragment" style="height: calc(100% - 30rpx);">
		<PCard title="企业基本信息">
			<template #option>
				<button style="background:#009BFF" type="primary" size="mini" v-show="!editable" @click="editable = true">修改</button>
				<button style="background:#009BFF" type="primary" size="mini" v-show="editable" @click="saveEvent()">保存</button>
			</template>
		</PCard>
		<view class="template-form-layout" id="formParent">
			<template-form ref="templateForm" :form-data="formData" :template="template" :editable="editable" :parentHeight="formLayoutHeight"></template-form>
		</view>
		<p-button @click.native="onMenuClick" name="确认"></p-button>
	</view>
</template>

<script>
import PCard from '@/pages/component/PCard.vue';
import iconHistory from '@/static/img/record/icon_form_history.png';
import iconSave from '@/static/img/record/icon_form_save.png';
import iconChange from '@/static/img/record/icon_form_change.png';
import iconNext from '@/static/img/record/icon_form_next.png';
import NoData from '@/components/no-data.vue';
import Page from '@/pages/component/Page.vue';
import NaviBar from '@/pages/component/NaviBar.vue';
import templateForm from '@/pages/form/template-form.vue';
import formOperateMenuItem from '@/pages/record/form-operate-menu-item.vue';
import styleUtil from '@/common/style.js';
import formUtil from '@/pages/form/Form.js';
import onform from '@/api/onsaveForm.js';

import { guid } from '@/common/uuid.js';
import { deepCopyObject } from '@/common/merge.js';

import { postQueryWryjcTzForm, postQueryWryjcTzInfo } from '@/api/book.js';

import { dynamicformsave } from '@/api/record.js';
import loginService from '@/api/login-service.js'

import recordFrag from './record-fragment.js';
const mixins = [recordFrag];

import PButton from '@/components/p-button';
export default {
	components: {
		Page,
		NaviBar,
		NoData,
		templateForm,
		formOperateMenuItem,
		PButton,
		PCard
	},

	// #ifdef MP-ALIPAY
	mixins: mixins.map(item => ({ ...item, props: {} })),
	// #endif
	// #ifndef MP-ALIPAY
	mixins: mixins,
	// #endif

	props: {
		// #ifdef MP-ALIPAY
		...mixins.reduce((prev, curr) => ({ ...prev, ...(curr.props || {}) }), {}),
		// #endif

		data: {
			type: Object
		}
	},

	data() {
		return {
			title: '公司名称',
			recordId: '',
			templateId: '',
			formData: {},
			template: {},
			editable: false,
			formLayoutHeight: -1, //表单的高度
			pageHeight: 600,
			menus: [
				{
					name: '保存',
					icon: iconSave
				}
			]
		};
	},

	watch: {
		//每次改变表单内容都要缓存数据--以templateId（表单ID）作为标识符来缓存
		// formData: {
		// 	handler(newVal, oldVal) {
		// 		uni.setStorage({
		// 			key: 'form-Data' + this.recordData.YWXTBH + this.template.templateId,
		// 			data: newVal
		// 		})
		// 		this.saveFormType = false
		// 		this.formData.KSSJ = dayjs(new Date()).format('YYYY-MM-DD HH:mm');
		// 		this.formData.JSSJ = dayjs(new Date()).subtract(-0.5, 'hour').format('YYYY-MM-DD HH:mm');
		// 	},
		// 	deep: true
		// }
	},

	// computed: {
	// 	formLayoutHeight: function() {
	// 		return this.pageHeight - 40;
	// 	}
	// },

	mounted() {
		this.$store.state.verifyList = [];
		this.templateId = '202102240924456a2f18c745934bab83c1e818600a2aca';
		this.recordId = uni.getStorageSync('record-data').WRYBH;
		this.getData();
		this.getHeight();
	},

	methods: {
			//获取动态表单高度

			getHeight() {
				// #ifdef APP-PLUS
				this.formLayoutHeight = 600
				styleUtil.getNodeLayout(this, '#formParent')
					.then(layout => {
						this.formLayoutHeight = layout.height
					})
				// #endif
			},

		getData() {
			postQueryWryjcTzForm({
				service: 'QUERY_DYNAMICFORM_DATA',
				recordId: this.recordId,
				mbbh: this.templateId
			}).then(res => {
				this.formData = res.data;
				this.getTemplate();
			});
		},

		onFixedContentHeight(layout) {
			this.pageHeight = layout.height;
		},

		getTemplate() {
			let userInfo = uni.getStorageSync('userInfo');
			let userId = '';
			if (userInfo) {
				userId = userInfo.id;
			}
			postQueryWryjcTzForm({
				service: 'GET_DYNAMICFORM_MODEL',
				bbxh: this.templateId,
				userId: userId || 'SDSZFJ'
			}).then(res => {
				this.template = formUtil.packPeerElementIntoGroup(res.datas_json);
			});
		},

		// 保存事件
		saveEvent() {
			let self = this;
			styleUtil.getNodeLayout(this, '#formParent').then(layout => {
				let dataType = onform.saveTemplateForm(self.formData, self, layout.top);
				if (dataType === true) {
					this.postFormAxios();
				}
			});
		},

		onMenuClick() {
			if (this.editable) {
				uni.showToast({
					title: '请先保存修改信息',
					duration: 2000,
					icon: 'none'
				});
			} else {
				this.doNext(this.stepIndex);
			}
		},

		postFormAxios() {
			let self = this;
			let from = {
				client_type: 'mobile_web',
				record_id: self.recordId,
				service: 'DYNAMICFORM_SAVE',
				template_id: self.templateId,
				user_id: loginService.getAuthUserId() || 'SDSZFJ'
			};
			let tableName = self.template['templateTable'];
			let data = this.$store.state.formData || this.formData;
			data = deepCopyObject(data);
			from[tableName] = data;
			dynamicformsave(from)
				.then(res => {
					self.editable = false;
					uni.showToast({
						title: '保存成功',
						duration: 2000,
						icon: 'none'
					});
				})
				.catch(() => {
					uni.showToast({
						title: '保存失败',
						duration: 2000,
						icon: 'none'
					});
				});
		}
	}
};
</script>

<style scoped>
.template-form-layout {
	/* border-radius: 5rpx; */
	/* padding: 10rpx; */
	width: calc(100%);
	background-color: #fff;
	margin-top: 0rpx;
	height: calc(100% - 140rpx);
}

.template-form-tabsList {
	margin-top: 60upx;
}

.pd-ultbs1 {
	white-space: nowrap;
	overflow-x: scroll;
	overflow-y: hidden;
	-webkit-overflow-scrolling: touch;
}

.pd-ultbs1 li {
	font-size: 20px;
	margin: 10rpx 12rpx;
	display: inline-block;
	vertical-align: top;
}

/* .template-form-layout {
		border-radius: 5px;
		padding: 10px;
		width: calc(100% - 20px);
		background-color: #f4f4f4;
	} */

.form-menu-layout {
	margin-top: 70rpx;
	height: 70rpx;
	z-index: 10;
	background-color: white;
}

.form-operate-menu-item {
	flex: 1;
	padding: 22rpx 0;
}

.form-operate-menu-item:active {
	background-color: #ccc;
}

.form-menu-icon {
	width: 40rpx;
	height: 40rpx;
}

.form-menu-text {
	margin-top: 2px;
	font-size: 26rpx;
	color: white;
}

.form-content-layout {
	width: 100%;
	height: 100%;
}

.form-menu-layout {
	height: 50px;
	background-color: #fff;
	/* box-shadow: 0 -1px 1px 0 #ccc; */
}

.form-list-tab {
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 20rpx 0;
	color: #fff;
}
</style>
