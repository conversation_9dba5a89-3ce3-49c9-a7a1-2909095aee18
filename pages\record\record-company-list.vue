<!--
 * @Author: YaoJinXi
 * @Date: 2021-03-05 11:38:18
 * @LastEditTime: 2021-05-17 11:42:59
 * @LastEditors: Please set LastEditors
 * @Description: 用于展示最近一次的执法信息和近两年的执法记录
 * @FilePath: /SmartFormWeb/pages/record/record-company-list.vue
-->

<template>
	<Page title="现场执法检查"
		  :padding="false">
		<scroll-view :scroll-y="true"
					 :style="pageListStyle">
			<view>
				<PCard title="最后一次执法详情">
					<view class="dialog-main">
						<uni-list-item title="检查时间"
									   :rightText="lastInfo.KSSJ || '--'"></uni-list-item>
						<uni-list-item title="是否发现涉嫌环境违法行为"
									   :rightText="lastInfo.SFFXSXHJWFXWMC || '无'"></uni-list-item>
						<uni-list-item title="涉嫌环境违法行为类型"
									   :note="lastInfo.SXHJWFXWLXMC || '无'"></uni-list-item>
						<uni-list-item title="现场检查情况"
									   :note="lastInfo.XCJCQK || '暂无结论'"></uni-list-item>
						<uni-list-item title="现场负责人对笔录的审阅意见"
									   :note="lastInfo.XCFZRDBLSYYJ || '暂无意见或要求'"></uni-list-item>
					</view>
				</PCard>
				<PCard title="两年内执法情况"
					   v-if="twoYearInfo.jcs">
					<uni-list-item link
								   v-for="(item,index) in mensList"
								   :key="index"
								   @click="mensClicK(twoYearInfo[item.type],item.name)"
								   :rightText="twoYearInfo[item.type].length + ''">
						<template slot="header">
							<image class="slot-image"
								   :src="item.icon"
								   mode="widthFix"></image>
							<view class="slot-text">{{item.name}}</view>
						</template>
					</uni-list-item>
				</PCard>
			</view>
		</scroll-view>
	</Page>
</template>

<script>
	import {
		postQueryJcbl,
		postTwoYearInfo
	} from '@/api/record.js'
	import NaviBar from '@/pages/component/NaviBar.vue'
	import Page from '@/pages/component/Page.vue'
	import PCard from '@/pages/component/PCard.vue';
	import jcs from '@/static/img/record/jcs.png';
	import xzcfsl from '@/static/img/record/xzcfsl.png';
	import tssl from '@/static/img/record/tssl.png';
	import wfhjwtsl from '@/static/img/record/wfhjwtsl.png';
	export default {
		components: {
			Page,
			NaviBar,
			PCard
		},

		data() {
			return {
				mensList: [{
						type: 'jcs',
						name: '检查数',
						icon: jcs
					},
					// {
					// 	type: 'tssl',
					// 	name: '投诉数量',
					// 	icon: tssl
					// },
					{
						type: 'wfhjwtsl',
						name: '违法环境问题数量',
						icon: wfhjwtsl
					},
					{
						type: 'xzcfsl',
						name: '行政处罚数量',
						icon: xzcfsl
					}
				], //两年执法的情况
				xzcfsl,
				jcs,
				tssl,
				wfhjwtsl,
				WRYMC: '', //污染源编号
				WRYBH: '', //污染源名称
				lastInfo: {}, //最后一次检查的信息
				twoYearInfo: {} //两年内的执法情况
			};
		},

		onLoad(options) {
			this.WRYBH = options.wrybh
			this.WRYMC = options.wrymc
			this.getRecordInfo()
		},

		computed: {
			pageListStyle() {
				return {
					height: 'calc(100vh - 80rpx)',
				}
			}
		},

		methods: {
			/**
			 * @description:用于获取服务数据 postQueryJcbl获取上一次检查的信息；postTwoYearInfo获取最近两年的检查信息
			 * @param {*}
			 * @return {*}
			 */
			getRecordInfo() {
				uni.showLoading({
					title: '加载中'
				});

				postQueryJcbl({
					params: {
						WRYMC: this.WRYMC,
						WRYBH: this.WRYBH,
					},
					service: 'QUERY_XCJC_KCBL',
				}).then((res) => {
					uni.hideLoading()
					this.lastInfo = res.data_json
				});

				postTwoYearInfo({
					WRYMC: this.WRYMC,
					WRYBH: this.WRYBH,
				}).then((res) => {
					this.twoYearInfo = res.data_json
				})
			},

			/**
			 * @description: 跳转方法
			 * @param {*}
			 * @return {*}
			 */
			mensClicK(data, name) {
				uni.navigateTo({
					url: `/pages/record/record-company-twoYearList?name=${name}&data=${encodeURIComponent(JSON.stringify(data))}`
				});
			}
		},
	};
</script>

<style scoped>
	.slot-image {
		width: 50rpx;
		height: 50rpx;
	}

	.slot-text {
		font-size: 30rpx;
		padding-left: 10rpx;
		display: flex;
		align-items: center;
	}
</style>
