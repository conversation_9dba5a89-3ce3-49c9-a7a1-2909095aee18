<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-04-09 11:27:54
 * @LastEditTime: 2021-04-21 03:31:21
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /SmartFormWeb/pages/record/record-company-twoYearList.vue
-->

<template>
	<Page :title="titleName">
        <scroll-view :scroll-y="true"
                    :style="pageListStyle">
            <uni-collapse accordion>
                <uni-collapse-item :title="getPerson(item)" v-for="(item,index) in twoList" :key="index" :open="false" showAnimation>
                    <uni-list v-if="titleName === '检查数'||titleName === '违法环境问题数量'">
                        <uni-list-item title="检查人"
                                            :rightText="item.JCRMC || '--'"></uni-list-item>
                        <uni-list-item title="检查时间"
                                            :rightText="item.KSSJ || '--'"></uni-list-item>
                        <uni-list-item title="是否发现涉嫌环境违法行为"
                                        :rightText="item.SFFXSXHJWFXWMC || '无'"></uni-list-item>
                        <uni-list-item title="涉嫌环境违法行为类型"
                                        :note="item.SXHJWFXWLXMC || '无'"></uni-list-item>
                        <uni-list-item title="现场检查情况"
                                        :note="item.XCJCQK || '暂无结论'"></uni-list-item>
                        <uni-list-item title="现场负责人对笔录的审阅意见"
                                        :note="item.XCFZRDBLSYYJ || '暂无意见或要求'"></uni-list-item>
                    </uni-list>
                    <uni-list v-if="titleName === '投诉数量'">
                        <uni-list-item title="投诉人"
                                            :rightText="item.TSRXM || '--'"></uni-list-item>
                        <uni-list-item title="投诉时间"
                                            :rightText="item.TSSJ || '--'"></uni-list-item>
                        <uni-list-item title="投诉人电话"
                                            :rightText="item.TSRDH || '--'"></uni-list-item>
                        <uni-list-item title="投诉人地址"
                                        :rightText="item.TSRDZ || '无'"></uni-list-item>
                        <uni-list-item title="投诉内容"
                                        :note="item.TSNR || '无'"></uni-list-item>
                    </uni-list>
                    <uni-list v-if="titleName === '行政处罚数量'">
                        <uni-list-item title="受理人"
                                            :rightText="item.SLR || '--'"></uni-list-item>
                        <uni-list-item title="案件来源日期"
                                            :rightText="item.AJLYRQ || '--'"></uni-list-item>
                        <uni-list-item title="案件来源"
                                            :rightText="item.AJLYMC || '--'"></uni-list-item>
                        <uni-list-item title="案件程序类型"
                                            :rightText="item.AJCXLXMC || '无'"></uni-list-item>
                        <uni-list-item title="案由名称"
                                        :rightText="item.AYMC || '无'"></uni-list-item>
                        <uni-list-item title="案情简介及立案"
                                        :note="item.AQJJJLALY || '无'"></uni-list-item>
                    </uni-list>
                </uni-collapse-item>
            </uni-collapse>
		</scroll-view>
	</Page>
</template>

<script>
	import Page from '@/pages/component/Page.vue'
	export default {
		components: {
			Page
		},
		data() {
			return {
				twoList: [],
				titleName: '列表' //最近两年的列表
			};
		},

        computed: {
			pageListStyle() {
				return {
					height: 'calc(100vh - 130rpx)',
				}
			}
		},

		onLoad(options) {
			this.twoList = JSON.parse(decodeURIComponent(options.data))
			this.titleName = options.name
		},

		methods: {
            getPerson(item){
                let time = item.CJSJ|| '--'
                let name = item.JCRMC || item.SLR || '--' 
                return time + `(${name})`
            }
		},
	};
</script>

<style scoped>

</style>
