//笔录间共享的字段
const SHARE_FIELDS = [
	'XZXDR', 'XZXDRBM', 'XZXDRXZ', 'XZXDRZJLX', 'JCXS', 'JCLB', 'RWLX', 'JGSX', 'JCXWMC', 'SFFXSXHJWFXW'
]

//同一任务下笔录共享数据前缀
const SHARE_DATA_KEY_PREFIX = 'RecordShare@'

const getTaskShareDataKey = (taskId) => {
	return `${SHARE_DATA_KEY_PREFIX}${taskId}`
}

/**
 * 从表单数据中提取共享字段值
 */
const extractShareData = (formData) => {
	let shareData = {}
	SHARE_FIELDS.forEach(field => {
		let fieldValue = formData[field]
		if(fieldValue) {
			shareData[field] = fieldValue
		}
	})
	return shareData
}

/**
 * 保存任务笔录共享数据
 */
export const saveShareData = (taskId, formData) => {
	let shareDataKey = getTaskShareDataKey(taskId)
	let existShareData = uni.getStorageSync(shareDataKey) || {}
	let shareData = extractShareData(formData)
	let updatedShareData = Object.assign(existShareData, shareData)
	if(Object.keys(updatedShareData).length === 0) {
		return
	}

	uni.setStorageSync(shareDataKey, updatedShareData)
}

/**
 * 获取任务笔录共享数据
 */
export const getShareData = (taskId) => {
	return uni.getStorageSync(getTaskShareDataKey(taskId)) || {}
}

const INSPECTOR_STORAGE_KEY_PREFIX = 'task_processors@'

const createInspectorCacheKey = (taskId) => {
	return `${INSPECTOR_STORAGE_KEY_PREFIX}${taskId}`
}

/**
 * 记录检查人
 * @param {Object} params
 */
const recordInspectors = (taskId, formRef) => {
	let match = (elementInstance) => {
		return elementInstance.template.SFJCR === '1'
	}
	let inspectorElements = formRef.findMatchedElements(match)
	if(inspectorElements.length === 0) {
		return
	}

	let inspectors = []
	for(let elementInstance of inspectorElements) {
		let inpsectorIds = (elementInstance.value || '').split(',')
		if(inpsectorIds.length === 0) {
			continue
		}
		inspectors.push(...inpsectorIds)
	}
	shareInspectors(taskId, inspectors)
}

export const shareInspectors = (taskId, inspectors) => {
	if(!Array.isArray(inspectors) || inspectors.length === 0) {
		return
	}
	let inspectorIds = inspectors.map(inspector => {
		//当inspectors元素是对象时，取YHID属性，否则认为是检查人账号ID
		return inspector.YHID || inspector
	})

	let cacheKey = createInspectorCacheKey(taskId)
	let existInpsectorIds = uni.getStorageSync(cacheKey) || null
	if(existInpsectorIds === null) {
		uni.setStorageSync(cacheKey, inspectorIds.join(','))
		return
	}
	let distinctInspectorIds = new Set(inspectorIds)
	existInpsectorIds.split(',').forEach(id => {
		distinctInspectorIds.add(id)
	})
	uni.setStorageSync(cacheKey, Array.from(distinctInspectorIds).join(','))
}

export const getSharedInspectorIds = (taskId) => {
	return uni.getStorageSync(createInspectorCacheKey(taskId)) || ''
}

export default {
	saveShareData,
	getShareData,
	shareInspectors,
	recordInspectors,
	getSharedInspectorIds
}
