<template>
    <view class="dialog">
        <view>
            <image src="/static/img/icon_delete_corner.png" @click="closeDialog" class="imgic" ></image>
            <view class="dialog-title">提示</view>
            <view class="dialog-main">
                <scroll-view :style="pageMainStyle"
			                 :scroll-y="true">
                <view>该企业最后一次检查中发现如下问题</view>
                <view class="dialog-main-type">检查时间</view>
                <view class="dialog-main-info">{{lastInfo.KSSJ || KSSJ}}</view>
                <view class="dialog-main-type">是否发现涉嫌环境违法行为</view>
                <view class="dialog-main-info">{{lastInfo.SFFXSXHJWFXW === '1' ? '是' : '否'}}</view>
                <view class="dialog-main-type">涉嫌环境违法行为类型</view>
                <view class="dialog-main-info">{{lastInfo.SXHJWFXWLX || '无'}}</view>
                <view class="dialog-main-type">现场检查结论</view>
                <view class="dialog-main-info">{{lastInfo.XCJCQK || '暂无结论'}}</view>
                <view class="dialog-main-type">处理意见或要求</view>
                <view class="dialog-main-info">{{lastInfo.XCFZRDBLSYYJ || "暂无意见或要求"}}</view>
                </scroll-view>
            </view>
        </view>
    </view>
</template>

<script>
import dayjs from 'dayjs';
export default {
    props:{
        //最后一次的信息
        lastInfo:{
            type:Object
        }
    },

    data() {
        return {
            KSSJ:''
        };
    },

    mounted(){
        this.KSSJ = dayjs(new Date()).format('YYYY-MM-DD HH:mm');
    },

    computed: {
        pageMainStyle(){
            return {
					height: 'calc(100vh - 750rpx)',
				}
        }
    },
    watch: {

    },
    methods: {
        closeDialog(){
            this.$emit('closeDialog')
        }
    },
};
</script>

<style scoped>
.dialog{
    background-color: #ffffff;
    border-radius: 16rpx;
    max-width:550rpx;
    position: relative;
    z-index: 1000;
}

.dialog-title{
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    font-size: 40rpx;
}

.dialog-main{
    padding: 22rpx;
}

.dialog-main view{
    padding: 20rpx 0;
}

.dialog-main-type{
    color:#52b4ff
}

.dialog-main-info{
    background-color: #eee;
}

.imgic{
    float: right;
    width: 40rpx;
    height: 40rpx;
}
</style>
