<template>
	<Page 
		:title="title"
		:padding="false"
		@layoutAware="onPageLayoutAware">
		<show-modal></show-modal>
		<scroll-view 
			scroll-y="true"
			:style="scrollStyle">
			<view class="flex-column-layout">
				<view class="flex-row-layout record-archive__gallery">
					<view 
						ref="attachItem"
						v-for="(item, index) in attachs"
						class="attach-list-item"
						:key="item.id">
						<image 
							class="attach-thumbnail"
							:mode="isImageAttach(item) ? 'aspectFit' : 'center'"
							:src="getAttachThumbnail(item)"
							@click.stop="previewImage(item, index)" 
						/>
						<image 
							class="attach-close-button" 
							:src="closeIcon"
							@click.stop="deleteAttach(item)" 
						/>
						
						<view 
							v-show="maskVisibilityStatus[index]"
							class="flex-column-layout attach-progress-mask">
							<circle-progress
								ref="circleProgress"
								:width="80"
								:ringWidth="5"
								:progress="0"
							/>
						</view>
					</view>
					
					<image class="attach-list-item attach-thumbnail attach-add-button"
						mode="aspectFit"
						:src="iconAdd"
						@click="showArchiveTypeDialog" 
					/>
				</view>
			</view>
		</scroll-view>
		<operate-menu-dialog
			ref="chooseMenuDialog"
			:menus="chooseFileMenus"
			@menuClick="onChooseMenuClick"
		/>
	</Page>
</template>

<script>
    import closeIcon from '@/static/img/icon_delete_corner.png'
	import iconAdd from '@/static/img/record/template_add.png'
	import iconVideoThumbnail from '@/static/img/record/icon_video_thumbnail.png'
	
	import NaviBar from '@/pages/component/NaviBar.vue'
	import Page from '@/pages/component/Page.vue'
	import circleProgress from '@/pages/component/circle-progress.vue';
	import operateMenuDialog from '@/pages/component/dialog/operate-menu-dialog.vue'
	
	import { guid } from '@/common/uuid.js';
	import dayjs from 'dayjs';
	import mediaUtil from '@/common/media.js'
	import styleUtil from '@/common/style.js'
	import http from '@/common/net/http.js';
	import fileUtil from '@/common/file.js';
    import fileService from '@/api/file-service.js';
    import { deleteElectronicFile, postElectronicList, sortFormAattachs } from '@/api/record.js'
	
	const MENU_ID_IMAGE_ALBUM = 'album_image'
	const MENU_ID_IMAGE_CAMERA = 'camera_image'
	const MENU_ID_VIDEO_ALBUM = 'album_video'
	const MENU_ID_VIDEO_CAMERA = 'camera_video'
	
	//选择错误码
	const CHOOSE_ERROR_CODE_CANCEL = 12
	
	export default {
		components: {
			Page,
			NaviBar,
			circleProgress,
			operateMenuDialog
		},
		
		data() {
			return {
                closeIcon,
				iconAdd,
				iconVideoThumbnail,
				scrollHeight: 600,
                title:"",
                recordId: '',
                attachMajorCate:'',//父类参数
                attachMinorCate:'',//子类参数
                attachs: [],//照片数据
				attachCountChanged: false, //是否有上传、删除档案文件
				chooseFileMenus: [
					{id: MENU_ID_IMAGE_ALBUM, name: '相册'},
					{id: MENU_ID_IMAGE_CAMERA, name: '拍照'},
					{id: MENU_ID_VIDEO_ALBUM, name: '相册视频(180M以内)'},
					{id: MENU_ID_VIDEO_CAMERA, name: '视频拍摄(180M以内)'}
				],
				maskVisibilityStatus: [] //进度条遮罩显示状态
			};
        },
		
		computed: {
			scrollStyle: function() {
				let style = {
					'height': `${this.scrollHeight}px`
				}
				return styleUtil.wrapStyleObject(style)
			}
		},
        
        onLoad(options){
            this.title = options.title
            this.attachMajorCate = options.FL
            this.attachMinorCate = options.ZL
            this.getElectronicList()
        },

		mounted() {
			this.recordId = guid()
		},
		
		destroyed() {
			uni.$emit('onArchiveUploadPageDestroyed', this.attachCountChanged)
		},

		methods: {
			onPageLayoutAware(layout) {
				this.scrollHeight = layout.height
			},
			
            //获取电子归档中每种照片的列表
            getElectronicList(){
                let data = uni.getStorageSync('record-data').YWXTBH
                postElectronicList({
                    YWSJID: data,
                    LXDM: this.attachMajorCate,
                    ZLXDM: this.attachMinorCate
                }).then(res => {
					let attachs = res.data_json || []
					attachs.forEach(attach => {
						let fileId = attach.WJID
						attach.WDBH = fileId
						// #ifdef APP-PLUS
						let localUrl = uni.getStorageSync(fileId)
						if(localUrl) {
							attach.url = localUrl
						}
						// #endif
					})
                    this.attachs = attachs
					this.resetMaskStatus()
                })
            },
			
			resetMaskStatus() {
				let status = [this.attachs.length]
				status.fill(false)
				this.maskVisibilityStatus.forEach((sta, index) => {
					if(index < status.length) {
						status[index] = sta
					}
				})
				this.maskVisibilityStatus = status
			},

			previewImage(item, index) {
				if(this.isVideoAttach(item)) {
					// #ifdef H5
					this.playVideo(url)
					// #endif
					// #ifdef APP-PLUS
					let fileId = item.WDBH
					let localUrl = uni.getStorageSync(fileId)
					if(localUrl) {
						this.playVideo(localUrl)
					} else {
						this.downloadVideoAttach(item, index)
					}
					// #endif
				} else {
					let attach = []
					let index = 0
					this.attachs.forEach((element,i) => {
						if(item.WDBH === element.WDBH){
							index = i
						}
					attach.push(fileService.getFileUrl(element.WDBH))
					});
					uni.navigateTo({
						url: `/pages/component/attach-preview?list=${encodeURIComponent(JSON.stringify(attach))}&index=${index}`
					});
					// let url = this.getAttachUrl(item)
					// uni.previewImage({
					// 	urls: [url]
					// })
				}
            },

            deleteAttach(item){
                let self = this
				let attachRemoteFileId = item.WJID || item.WDBH
                this.$showModal({
					title: '提示',
					content: '确定是否删除吗？',
					success: function (res) {
						if (res.confirm) {
							deleteElectronicFile({
								WDBH: attachRemoteFileId
							}).then((res)=>{
								// #ifdef APP-PLUS
								if(item.url && item.url.startsWith('file://')) {
									uni.removeStorageSync(attachRemoteFileId)
								}
								// #endif
								self.attachCountChanged = true
								self.getElectronicList()
							})
						} else if (res.cancel) {
							('用户点击取消');
						}
					}
				});
            },
			
			showArchiveTypeDialog() {
				this.$refs.chooseMenuDialog.show()
			},
			
			onChooseMenuClick(menu) {
				this.chooseFileType = menu.id
				if(menu.id === MENU_ID_IMAGE_ALBUM || menu.id === MENU_ID_IMAGE_CAMERA) {
					this.chooseImages()
				}
				
				if(menu.id === MENU_ID_VIDEO_ALBUM || menu.id === MENU_ID_VIDEO_CAMERA) {
					this.chooseVideos()
				}
			},

			//上传已经拍摄好的笔录图片
			chooseImages() {
				console.log(this.chooseFileType);
				let _self = this;
				uni.chooseImage({
					sizeType: ['compressed'], //可以指定是原图还是压缩图，默认二者都有
    				sourceType:this.chooseFileType === MENU_ID_IMAGE_ALBUM ? ['album'] : ['camera'], //从相册选择
					success(chooseInfo) {
						let now = dayjs();
						let time = now.format('YYYY-MM-DD');
						let timestamp = now.format('YYYY-MM-DD_HHmmss');
						chooseInfo.tempFilePaths.forEach((fileUrl, index) => {
							let id = fileUrl.substring(fileUrl.lastIndexOf("/") + 1);
							// #ifndef MP-ALIPAY
							let file = chooseInfo.tempFiles[index];
							// #endif

							// #ifdef MP-WEIXIN
							let name = `${_self.attachMajorCate}_${timestamp}_${index}.${fileUtil.parseFileSuffix(file.path)}`;
							// #endif
							// #ifdef MP-ALIPAY
							//钉钉小程序读取文件信息字段使用files
							let file = chooseInfo.files[index];
							let name = `${_self.attachMajorCate}_${timestamp}_${index}.${file.fileType}`;
							// #endif
							// #ifndef MP
							let name = file.name;
							// #endif
							let size = file.size;
							let attach = {
								refId: guid(),
								recordId: _self.recordId,
								url: fileUrl,
								progress: 0,
								WDMC: name || id,
								SCSJ: time,
								WDDX: fileUtil.bitSizeToElegantSize(size)
							}
							// #ifdef APP-PLUS
							// uni.saveImageToPhotosAlbum({
							// 	filePath: chooseInfo.tempFilePaths[0],
							// 	success: function() {}
							// });
							// #endif

							_self.updateFormAttachData(attach);
							_self.uploadAttach(attach, _self.attachs.length - 1);
						})
					},
					fail(error) {
						// _self.processChooseError(error)
					}
				})
			},
			
			//上传已经拍摄好的笔录图片
			chooseVideos() {
				let _self = this;
				uni.chooseVideo({
					sizeType: ['compressed'], //可以指定是原图还是压缩图，默认二者都有
    				sourceType:this.chooseFileType === MENU_ID_VIDEO_ALBUM ? ['album'] : ['camera'], //从相册选择
					success(chooseInfo) {
						if(chooseInfo === 'undefined' || chooseInfo === null) {
							return
						}
						let now = dayjs();
						let time = now.format('YYYY-MM-DD');
						let timestamp = now.format('YYYY-MM-DD_HHmmss');
						let filePath = chooseInfo.tempFilePath
						
						// #ifdef APP-PLUS
						if(!filePath.startsWith('file://')) {
							filePath = `file://${filePath}`
						}
						// #endif
						
						let name = chooseInfo.name || filePath.substring(filePath.lastIndexOf("/") + 1);
		
						let size = chooseInfo.size;
						let attach = {
							refId: guid(),
							recordId: _self.recordId,
							url: chooseInfo.tempFilePath,
							progress: 0,
							WDMC: name,
							SCSJ: time,
							WDDX: fileUtil.bitSizeToElegantSize(size)
						}
		
						_self.updateFormAttachData(attach);
						let currentIndex = _self.attachs.length - 1
						_self.uploadAttach(attach, currentIndex);
					},
					fail(error) {
						// _self.processChooseError(error)
					}
				})
			},
			
			processChooseError(error) {
				let errorCode = error.code || -1
				if(errorCode !== CHOOSE_ERROR_CODE_CANCEL) {
					if(error.errMsg){
						this.$showModal({
							title: '选择文件出错',
							content: error.errMsg
						})
					}
				}
			},

			updateFormAttachData(attach) {
				this.attachs.push(attach);
			},
			
			/**
			 * 切换指定项的进度条遮罩显示、隐藏
			 * @param {Object} index
			 */
			toggleProgressVisibility(index, visibility) {
				this.$set(this.maskVisibilityStatus, index, visibility)
			},
			
			/**
			 * 上传附件
			 * @param {Object} attach
			 */
			uploadAttach(attach, index) {
				let _self = this;
				_self.toggleProgressVisibility(index, true)
				let uploadTask = uni.uploadFile({
					url: `${http.loginUrl}/webapp/uploadFile`,
					filePath: attach.url,
					name: 'file',
					formData: {
						LXDM: _self.attachMajorCate,
						ZLXDM: _self.attachMinorCate,
						YWSJID: uni.getStorageSync('record-data').YWXTBH,
						WJMC: attach.WDMC,
						WJLX: fileUtil.parseFileSuffix(attach.WDMC)
					},
				
					success(uploadFileRes) {
						
						_self.toggleProgressVisibility(index, false)
						let resp = JSON.parse(uploadFileRes.data);
						let attachs = JSON.parse(resp.wd_data);
						console.log(attachs,'123');
						let successAttach = attachs[0];
				
						attach.WDBH = successAttach.WDBH
						_self.attachCountChanged = true
						
						// #ifdef APP-PLUS
						if(attach.url && attach.url.startsWith('file://') && _self.isVideoAttach(attach)) {
							uni.setStorageSync(attach.WDBH, attach.url)
						}
						// #endif
					}
				});
				
				uploadTask.onProgressUpdate((resp) => {
					let progress = resp.progress
					_self.$refs['circleProgress'][index].updateProgress(progress)
				});
            },
			
			/**
			 * 是否图片附件
			 */
			isImageAttach(attach) {
				return mediaUtil.isPicture(attach.WDMC || attach.WJMC)
			},
			
			/**
			 * 是否视频附件
			 * @param {Object} attach
			 */
			isVideoAttach(attach) {
				return mediaUtil.isVideo(attach.WDMC || attach.WJMC)
			},
            
			/**
			 * 获取附件图标
			 * @param {Object} item
			 */
            getAttachThumbnail(item){
				if(this.isVideoAttach(item)) {
					return this.iconVideoThumbnail
				} else {
					return item.url || fileService.getFileUrl(item.WJID)
				}
            },
			
			/**
			 * 获取附件浏览地址
			 */
			getAttachUrl(attach) {
				let attachUrl = attach.url
				if(attachUrl) {
					if(attachUrl.startsWith('file://') || attachUrl.startsWith('/Storage')) {
						return fileService.getFileUrl(attach.WJID || attach.WDBH)
					} else {
						return attachUrl
					}
				} else {
					return fileService.getFileUrl(attach.WJID || attach.WDBH)
				}
			},
			
			downloadVideoAttach(videoAttach, index) {
				let self = this
				let fileId = videoAttach.WDBH
				this.toggleProgressVisibility(index, true)
				let listener = {
					updateProgress(percent) {
						self.$refs['circleProgress'][index].updateProgress(percent)
					},
					
					onDownloadFinish(path) {
						uni.setStorageSync(fileId, path)
						self.toggleProgressVisibility(index, false)
						self.playVideo(path)
					}
				}
				
				fileService.downloadFileProgress(fileId, videoAttach.WJLX, listener)
			},
			
			playVideo(url) {
				uni.navigateTo({
					url: `/pages/media/video-player?videoUrl=${url}`
				})
			}
		},
	};
</script>

<style scoped>
	.record-archive__gallery {
		flex-wrap: wrap;
		width: 608rpx;
	}
	
	.attach-thumbnail {
		width: 280rpx;
		height: 360rpx;
		border: 1rpx solid #ddd;
		box-shadow: 2px 2px 4px #ccc;
		border-radius: 4rpx;
		margin-top: 20px;
	}

    .attach-list-item {
        position: relative;
    }
	
	.attach-list-item:nth-child(2n) {
		margin-left: 40rpx;
	}

    .attach-close-button{
        position: absolute;
        z-index: 10;
        right: -10px;
        top: 10px;
        height: 40rpx;
        width: 40rpx;
    }
	
	.attach-add-button {}
	
	.attach-add-button:first-child {
		margin-left: 20px;
	}
	
	.attach-progress-mask {
		position: absolute;
		left: 0;
		top: 20px;
		width: 100%;
		height: calc(100% - 20px);
		z-index: 999;
		background-color: rgba(0, 0, 0, .3);
	}
</style>
