<template>
  <view id="folderParent">
    <scroll-view scroll-y="true" :style="scrollStyle">
      <view class="file">
        <view
          class="file-icon"
          v-for="item in templates"
          :key="item.id"
          @click="chooseFile(item)"
        >
          <view class="file-list">
            <view class="file-num">
              <text class="file-num-info">{{ item.SL }}</text>
            </view>
            <image :src="iconName" class="file-image" />
            <view class="file-filename">{{ item.ZLXMC }}</view>
          </view>
          <view class="file-bottom">
            <image :src="iconFile" class="file-downimage" />
            <view class="file-downname">上传</view>
          </view>
        </view>
      </view>
    </scroll-view>
    <view v-if="canFinish">
      <PButton
        style="height: 48px; line-height: 48px"
        :name="nextStepText"
        :disabled="isFinishingTask"
        @click.native="finishTask()"
      />
    </view>
    <!-- show-cancel-button -->
    <u-modal
      :content-style="{
        color: 'red',
      }"
      @confirm="show = false"
      v-model="show"
      :content="'严禁在本互联网非涉密平台处理、传输国家秘密，请确认扫描、传输的文件资料不涉及国家秘密'"
    ></u-modal>
  </view>
</template>

<script>
import iconFile from "@/static/img/record/upfile.png";
import iconName from "@/static/img/record/file_name.png";
import PButton from "@/components/p-button";
import recordFrag from "./record-fragment.js";

import styleUtil from "@/common/style.js";

import { postQueryBdmblx, queryArchiveTypes } from "@/api/record.js";

const mixins = [recordFrag];

export default {
  name: "RecordElectronicFile",
  // #ifdef MP-ALIPAY
  mixins: mixins.map((item) => ({ ...item, props: {} })),
  // #endif
  // #ifndef MP-ALIPAY
  mixins: mixins,
  // #endif

  components: {
    PButton,
  },
  props: {
    // #ifdef MP-ALIPAY
    ...mixins.reduce((prev, curr) => ({ ...prev, ...(curr.props || {}) }), {}),
    // #endif
  },

  data() {
    return {
      templates: [], //模块的内容
      iconName,
      iconFile,
	  show: true,
    };
  },

  computed: {
    scrollStyle: function () {
      let scrollHeight = this.height;
      if (this.canFinish) {
        scrollHeight -= 48;
      }
      let style = {
        height: `${scrollHeight}px`,
      };
      return styleUtil.wrapStyleObject(style);
    },
  },

  mounted() {
    this.loadArchiveTypes();
  },

  methods: {
    //获取表单基本结构
    loadArchiveTypes() {
      uni.showLoading({
        title: "正在加载档案类型",
      });
      let taskId = uni.getStorageSync("record-data").YWXTBH;
      queryArchiveTypes(taskId).then((res) => {
        this.templates = res.data_json;
        uni.hideLoading();
      });
    },

    //选择对应的文件类型上传
    chooseFile(item) {
      uni.navigateTo({
        url: `/pages/record/record-eleclist?FL=${item.LXDM}&ZL=${item.ZLXDM}&title=${item.ZLXMC}`,
      });
      this.listenArchiveFileUpload();
    },

    listenArchiveFileUpload() {
      let self = this;
      let event = "onArchiveUploadPageDestroyed";
      uni.$on(event, (hasUploadArchive) => {
        uni.$off(event);
        if (hasUploadArchive) {
          self.loadArchiveTypes();
        }
      });
    },
  },
};
</script>

<style scoped>
.file {
  width: 100%;
  display: flex;
  align-items: center;
  /* justify-content: space-around; */
  flex-wrap: wrap;
}

.file-icon {
  width: 44%;
  margin: 20rpx 20rpx;
  border: 1rpx solid #ebf0f4;
  border-radius: 12rpx;
}

.file-list {
  position: relative;
  padding: 10rpx 0;
  height: 80%;
  display: flex;
  align-items: center;
  flex-direction: column;
  border-bottom: 1rpx solid #ebf0f4;
}

.file-num {
  width: 40rpx;
  height: 40rpx;
  line-height: 40rpx;
  background: red;
  position: absolute;
  right: 10rpx;
  color: #fff;
  border-radius: 100rpx;
}

.file-num-info {
  display: flex;
  justify-content: center;
  font-size: 14px;
  height: 40rpx;
  line-height: 40rpx;
}

.file-bottom {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12rpx 0;
}

.file-image {
  width: 100rpx;
  height: 100rpx;
}

.file-downimage {
  height: 26rpx;
  width: 26rpx;
}

.file-downname {
  color: #2095ef;
  font-size: 26rpx;
}

.file-filename {
  width: 90%;
  text-align: center;
  font-family: bold;
  padding: 16rpx 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
