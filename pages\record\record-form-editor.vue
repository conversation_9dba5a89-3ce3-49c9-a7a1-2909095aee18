<template>
    <Page :padding="false" :naviBack="false">
        <template v-slot:bar>
            <NaviBar :title="template.templateName">
                <template v-slot:navi>
                    <image
                        @click.stop="naviBack()"
                        :src="backurl"
                        style="width: 39rpx; height: 39rpx; margin-left: 2rpx"
                    >
                    </image>
                </template>
            </NaviBar>
        </template>
        <mi-loading
            ref="Loading"
            title="加载表单中"
            :hasMask="true"
            id="loadingBtn"
        ></mi-loading>
        <!-- #ifdef H5 -->
        <view
            class="flex-column-layout"
            style="height: 100%; justify-content: flex-start"
        >
            <view
                id="formParent"
                style="width: 100%; height: calc(100% - 50px)"
            >
                <template-form
                    ref="form"
                    :height="formHeight"
                    :editable="editable"
                    :template="template"
                    :record-id="recordId"
                    :form-data="formData"
                    @onFormMounted="onFormMounted"
                    @onFormDataUpdate="listenFormValueChange"
                />
            </view>
            <view class="flex-row-layout form-menu-layout">
                <view
                    class="flex-row-layout form-list-tab"
                    v-for="menu in menus"
                    :key="menu.item"
                    @click="onMenuClick(menu)"
                    :style="{ backgroundColor: menu.bg }"
                >
                    {{ menu.name }}
                </view>
            </view>
        </view>
        <!-- #endif -->
        <show-modal></show-modal>

        <!-- #ifdef APP-PLUS -->
        <view
            id="formParent"
            class="flex-column-layout"
            style="height: 100%; justify-content: flex-start"
        >
            <template-form
                ref="form"
                :height="formHeight"
                :editable="editable"
                :template="template"
                :record-id="recordId"
                :form-data="formData"
                @onFormMounted="onFormMounted"
                @onFormDataUpdate="listenFormValueChange"
            />
            <view class="flex-row-layout form-menu-layout">
                <view
                    class="flex-row-layout form-list-tab"
                    v-for="menu in menus"
                    :key="menu.item"
                    @click="onMenuClick(menu)"
                    :style="{ backgroundColor: menu.bg }"
                >
                    {{ menu.name }}
                </view>
            </view>
        </view>
        <!-- #endif -->

        <!-- AI智能提醒弹窗 -->
        <Info ref="aiPop" :content="aiResult"></Info>
    </Page>
</template>

<script>
import backurl from '@/static/img/icon_back_white.png';
import Page from '@/pages/component/Page.vue';
import formUtil from '@/pages/form/Form.js';
import NaviBar from '@/pages/component/NaviBar.vue';
import styleUtil from 'bowo-sdk/util/style.js';
import templateUtil from 'bowo-form/components/template.js';
import miLoading from '../../components/mi-loading/mi-loading.vue';
import TemplateForm from 'bowo-form';
import dayjs from 'dayjs';
import { guid } from '@/common/uuid.js';
import { deepCopyObject, deepAssign } from 'bowo-sdk/util/merge.js';
import { mapState, mapMutations } from 'vuex';

import recordSharer from './record-data-share.js';
import loginService from '@/api/login-service.js';
import logService from '@/api/log-service.js';
import formService from '@/node_modules/bowo-form/components/service/form-service.js';
import { QUERY_YDZFAI_ZNTX } from '@/api/ai.js';
import storage from 'bowo-sdk/util/storage.js';
import Info from '@/views/ai/components/Info.vue';
import {
    MODE_ADD,
    MODE_MODIFY,
    EVENT_EDITOR_EXIT,
    queryFormDefaultValues,
    queryHistoryRecord,
    dynamicformsave,
    queryElementConstraints,
    getRecordData,
    queryRecordData,
    getTemplateUpdataTime,
    queryTaskDispatchTemplates,
    queryTaskTemplates,
    saveRecordToService,
    recordTableSave,
    verifyStandards,
    saveValidationResults
} from '@/api/record.js';

// import { queryTaskTemplates } from '@/api/rectify.js';

import { EVENT_FROM_VALUE_CHANGE } from '@/pages/form/Form.js';

// 需要进行规范化判断的表单
const CHECK_NORMATIVE_FORM_LIST = 
    [
        {
            id: "202201161657129047e43d72b64f54a7137ecc2bee37ec",
            XCJCJL: "XCJCJL", // 现场检查结论
            SFFXWT: "SFFXSXHJWFXW", // 是否发现问题
            ZFZH: "ZFZH", // 执法证号
            KSSJ: "KSSJ", // 开始时间
            JSSJ: "JSSJ", // 结束时间
        }, // 污染源非现场检查记录表

        {
            id: "2021031114351147e82f7094ec4108b0d6bc80ba958ab5",
            XCJCJL: "XCJCJL",
            SFFXWT: "SFFXSXHJWFXW",
            ZFZH: "ZFZH",
            KSSJ: "KSSJ",
            JSSJ: "JSSJ",
        }, // 建设项目监督检查记录表

        {
            id: "20210311180311e60401f38bab4db09ae8c75229c189e1",
            XCJCJL: "XCJCJL",
            SFFXWT: "SFFXSXHJWFXW",
            ZFZH: "ZFZH",
            KSSJ: "KSSJ",
            JSSJ: "JSSJ",
        }, // 油气污染防治设施现场执法记录（加油站）

        {
            id: "202011091556246e1df6bd052449c7b6a4ddbb57378ccf",
            XCJCJL: "XCJCJL",
            SFFXWT: "SFFXSXHJWFXW",
            ZFZH: "ZFZH",
            KSSJ: "KSSJ",
            JSSJ: "JSSJ",
        }, // 现场检查勘察笔录

        {
            id: "202011101029580d3374fb8ffa49e087200c27896ac348",
            XCJCJL: "XCJCJL",
            SFFXWT: "SFFXSXHJWFXW",
            ZFZH: "ZFZH",
            KSSJ: "KSSJ",
            JSSJ: "JSSJ",
        }, // 调查询问笔录
            
    ];

//本地缓存表单数据时间戳存储键
const LOCAL_CACHE_TIMESTAMP_KEY = '_timestamp';
const MENU_ID_HISTORY = 'history';
const MENU_ID_SAVE = 'save';

export default {
    name: 'RecordFormEditor',
    components: {
        Page,
        NaviBar,
        TemplateForm,
        miLoading,
        Info
    },

    data() {
        return {
            backurl,
            menus: [
                {
                    code: 'history',
                    name: '历史笔录',
                    bg: '#e4a54d'
                },
                {
                    code: 'save',
                    name: '保存',
                    bg: 'rgb(0, 155, 255)'
                }
            ],
            mrzList: [],
            historyList: [],
            recordData: {}, //缓存过的企业数据
            taskId: '',
            recordId: '', //表单唯一ID，用于缓存、纪录编辑和保存
            formLayoutHeight: -1, //表单的高度
            wrybh: '', //受执法的企业污染源编号
            editable: true, //表单是否可编辑
            constraints: [], //表单联动规则数组集合
            template: {}, //模板的基础
            formData: {}, //表单的数据
            editMode: MODE_MODIFY, //编辑模式，新增（MODE_ADD）或修改(MODE_MODIFY)
            hasSubmitForm: false, //是否有提交表单
            backStep: -1,
            pageHeight: null,
            remarks: [],
            hasClearSignature: false, //是否已清除过签名
            extraParams: {
                signatureUploadParams: {
                    SCR: '博沃测试',
                    SCRID: 'BOWO'
                }
            },
            aiResult: '', // AI智能提醒的结果
            checkResult: {}, // 规范化提醒的结果
        };
    },

    computed: {
        // #ifdef MP
        ...mapState({
            mpFormData: state => state.formData
        }),
        // #endif

        formHeight: function () {
            let naviBarHeight = uni.upx2px(96);
            let bottomMenuHeight = uni.upx2px(100);
            let paddingBetwewnFormAndBottomMenu = 4;
            return (
                this.pageHeight -
                naviBarHeight -
                bottomMenuHeight -
                paddingBetwewnFormAndBottomMenu
            );
        },

        defaultValues: function () {
            let self = this;
            let defautValues = {};
            return defautValues;
        },

        localCacheKey: function () {
            return `form-Data:${this.recordId}`;
        },

        //是否新增笔录模式
        isAddNewRecordMode: function () {
            return this.editMode === MODE_ADD;
        },

        //是否修改笔录模式
        isModifyRecordMode: function () {
            return this.editMode === MODE_MODIFY;
        }
    },

    onReady() {
        let self = this;
        uni.showLoading({
            title: '表单加载中'
        });
    },

    onLoad(options) {
        this.templateId = options.id;
        this.wrybh = options.wrybh;
        this.recordData = uni.getStorageSync('record-data');
        if (
            (this.recordData && this.recordData.hasOwnProperty('YWXTBH')) ||
            this.recordData.hasOwnProperty('XH')
        ) {
            this.taskId = this.recordData.YWXTBH || this.recordData.XH;
        }

        if (options.backStep) {
            this.backStep = parseInt(options.backStep);
        }

        this.editMode = options.type;
        if (this.isModifyRecordMode) {
            this.recordId = options.recordId;
        }

        if (this.isAddNewRecordMode) {
            this.initNewRecordId();
        }
    },

    mounted() {
        this.initPageHeight();
        this.renderTemplateForm();
    },

    /**
     * 页面退出，清除当前表单在store中的缓存
     */
    destroyed() {
        // #ifdef MP
        let latestFormData = this.mpFormData;
        // #endif
        // #ifndef MP
        let latestFormData = this.formData;
        // #endif
        recordSharer.saveShareData(this.taskId, latestFormData);

        // #ifdef MP
        this.resetFormData(null);
        // #endif

        //新增笔录模式，结束笔录编辑，发送退出事件，
        //在record-form-workshop.vue页面中监听以刷新页面
        if (this.isAddNewRecordMode) {
            uni.$emit(EVENT_EDITOR_EXIT, this.hasSubmitForm);
        }


        // 当规范化提醒结果弹窗打开时，用户手动退出也要调用一次 保存规范化提醒结果接口
        if(this.$modalStore.state.cancelText == '忽略' && this.$modalStore.state.show) {
            // 保存规范化提醒结果
            this.checkResult.TXJG = '1'
            this.saveValidationResults(this.checkResult);
        }
    },

    methods: {
        getFormRef() {
            return this.$refs.form;
        },

        /**
         * 当新增笔录时，需要从缓存读取缓存的笔录id或生成新的id
         */
        initNewRecordId() {
            let recordIdCacheKey = `recordID:${this.wrybh}${this.templateId}${this.taskId}`;
            let cachedRecordId = uni.getStorageSync(recordIdCacheKey);
            this.recordId = cachedRecordId || guid();
            uni.setStorageSync(recordIdCacheKey, this.recordId);
        },

        //初始化页面高度
        initPageHeight() {
            styleUtil.getPageLayout().then(layout => {
                this.pageHeight = layout.height;
            });
        },

        /**
         * 监听表单元素值改变
         */
        listenFormValueChange(formData, event) {
            formData[LOCAL_CACHE_TIMESTAMP_KEY] = new Date().getTime();
            uni.setStorage({
                key: this.localCacheKey,
                data: formData
            });

            let valueChangeElement = event.element;
            if (valueChangeElement.SFJCR === '1') {
                recordSharer.recordInspectors(this.taskId, this.getFormRef());
            }
        },

        /**
         * 渲染和加载表单数据
         */
        renderTemplateForm() {
            this.loadTemplateForm()
                .then(this.waitFormMounted)
                .then(this.loadFormDataCache)
                .then(cache => {
                    this.formData = cache;
                    //  if(this.formData.JCLB && this.formData.SFFXSXHJWFXW){
                    //     this.formData.JCLB = '01';
                    //     this.formData.SFFXSXHJWFXW = '0'
                    //  }
                })
                .catch(error => console.log(error, '加载表单异常'))
                .finally(() => {
                    setTimeout(() => {
                        uni.hideLoading();
                    }, 800);
                });
        },

        //获取表单的基础模板数据
        loadTemplateForm() {
            return new Promise((resolve, reject) => {
                try {
                    let params = {
                        bbxh: this.templateId,
                        businessId: this.taskId,
                        service: 'GET_DYNAMICFORM_MODEL',
                        userId: loginService.getAuthUserId()
                    };
                    if (this.wrybh) {
                        params.WRYBH = this.wrybh;
                    }
                    queryTaskTemplates(params)
                        .then(res => {
                            let templateInfo = res.datas_json;
                            let reorganizeTemplate =
                                templateUtil.packPeerElementIntoGroup(
                                    templateInfo
                                );
                            this.template = reorganizeTemplate;
                            uni.setStorageSync(this.templateId, templateInfo);
                            resolve();
                        })
                        .catch(requestError => reject(requestError));
                } catch (tryError) {
                    reject(tryError);
                }
            });
        },

        /**
         * 等待表单元素挂载完成
         */
        waitFormMounted() {
            return new Promise((resolve, reject) => {
                setTimeout(() => {
                    resolve();
                }, 1600);
            });
        },

        /**
         * 加载表单缓存
         */
        loadFormDataCache() {
            let localCacheRecord = this.loadCacheFromLocal();
            if (this.isModifyRecordMode) {
                return this.loadFormDataFromServer(
                    this.recordId,
                    localCacheRecord
                );
            } else {
                return new Promise((resolve, reject) => {
                    //本地存在缓存，总是加载缓存
                    if (localCacheRecord) {
                        resolve(localCacheRecord);
                        return;
                    }

                    //默认值附加表单共享字段数据
                    let recordShareData = recordSharer.getShareData(
                        this.taskId
                    );
                    let defaultValues = this.getFormRef().getFormData();
                    //追加共享数据时，需要合并已加载默认数据，否则会覆盖默认数据
                    deepAssign(defaultValues, recordShareData);
                    this.appendSharedInspectorIds(defaultValues);
                    resolve(defaultValues);
                });
            }
        },

        /**
         * 新增笔录时，读取第二步选择的人员数据添加到默认数据中
         * @param {Object} formData
         */
        appendSharedInspectorIds(formData) {
            let sharedInspectorIds = recordSharer.getSharedInspectorIds(
                this.taskId
            );
            if (sharedInspectorIds === '') {
                return;
            }
            let match = elementInstance => {
                return elementInstance.template.SFJCR === '1';
            };
            let appendData = {};
            let inspectorElements =
                this.getFormRef().findMatchedElements(match);
            for (let instance of inspectorElements) {
                let field = instance.template.dbcolum || null;
                if (field === null) {
                    continue;
                }
                appendData[field] = sharedInspectorIds;
            }
            Object.assign(formData, appendData);
        },

        /**
         * 从本地加载附件表单笔录缓存
         */
        loadCacheFromLocal() {
            return uni.getStorageSync(this.localCacheKey) || null;
        },

        /**
         * 从服务端加载笔录数据
         */
        loadFormDataFromServer(recordId, localCache) {
            return new Promise((resolve, reject) => {
                formService
                    .getRecordDetailById(this.templateId, recordId)
                    .then(recordData => {
                        if (recordData === null && localCache === null) {
                            resolve(null);
                            return;
                        }

                        if (recordData === null || localCache === null) {
                            resolve(recordData || localCache);
                            return;
                        }
                        let modifyTimeStr =
                            recordData.XGSJ || '1970-01-01 00:00:00';
                        let latestModifyTime = dayjs(
                            modifyTimeStr,
                            'YYYY-MM-DD HH:mm:ss'
                        ).valueOf();
                        let cacheTimestamp =
                            localCache[LOCAL_CACHE_TIMESTAMP_KEY] || -1;

                        //服务端数据修改时间大于本地时间才更新服务器时间
                        let fixedFormData =
                            latestModifyTime > cacheTimestamp
                                ? recordData
                                : localCache;
                        resolve(fixedFormData);
                    })
                    .catch(error => reject(error));
            });
        },

        //底部事件汇总
        onMenuClick(menu) {
            switch (menu.code) {
                case MENU_ID_HISTORY:
                    this.getHistoryList();
                    break;
                case MENU_ID_SAVE:
                    this.saveForm();
                    break;
            }
        },

        //获取历史笔录
        getHistoryList() {
            queryHistoryRecord(this.templateId, this.recordData.WRYBH).then(
                res => {
                    let historyRecords = res.data_json || [];
                    if (historyRecords.length === 0) {
                        uni.showToast({
                            title: '暂无历史笔录',
                            duration: 2000,
                            icon: 'none'
                        });
                        return;
                    }

                    //保存历史笔录列表到本地
                    let tempHistoryKey = 'historyRecords';
                    uni.setStorageSync(tempHistoryKey, historyRecords);

                    //监听列表页面返回事件
                    uni.$once('onHistoryRecordListDestoryed', record => {
                        //清除历史笔录列表本地缓存
                        uni.removeStorageSync(tempHistoryKey);
                        if (record) {
                            this.renderHistoryData(record);
                        }
                    });
                    uni.navigateTo({
                        url: `/pages/record/record-history-list?id=${this.templateId}`
                    });
                }
            );
        },

        //获取历史笔录的详情并覆盖
        renderHistoryData(data) {
            let cacheRecord = this.loadCacheFromLocal();
            this.loadFormDataFromServer(data.RECORDID, null).then(
                historyRecord => {
                    this.getFormRef().reuseHistoryData(historyRecord);
                }
            );
        },

        /**
         * 保单保存前做必填性校验
         */
        saveForm() {
            this.getFormRef()
                .checkFormData()
                .then(this.showPromiseConfirmTip)
                .then(this.checkPromiseNormative)
                .then(formData => {
                    this.submitFormData(formData);

                    // QUERY_YDZFAI_ZNTX({
                    //     YDZFXH: this.taskId,
                    //     SFFXSXWFWT: storage.getEffectiveStorage('code-set@SHF').filter(e=> e.code == formData.SFFXSXHJWFXW)[0].name,
                    //     JCJG: formData.JCJGMC,
                    //     LHJCXJ: formData.XCJCJL,
                    //     TBXH: this.recordId,
                    //     TBCODE: this.template['templateTable'],
                    // }).then(res => {
                    //     if(res.data_json.AIFXJG == '内容和结论一致') {
                    //         this.submitFormData(formData);
                    //     } else {
                    //         this.aiResult =  res.data_json.AIFXJG;
                    //         this.$refs.aiPop.showPop();
                    //     }
                    // })

                    
                    // let trueData = this.getFormRef().getFormData()
                    // console.log(trueData);
                    // console.log(trueData.JCLB == '02' && trueData.SFFXSXHJWFXW == '0');
                    //     if(trueData.JCLB == '02' && trueData.SFFXSXHJWFXW == '0'){
                    //        uni.showModal({
                    //         title: '提示',
                    //         content: '检查类别选择‘非现场检查’时，是否发现涉嫌环境违法行为必须选择‘发现涉嫌环境违法问题’或‘发现一般环境问题’',
                    //         success: function (res) {
                    //             if (res.confirm) {
                    //                 console.log('用户点击确定');
                    //             } else if (res.cancel) {
                    //                 console.log('用户点击取消');
                    //             }
                    //         }
                    //        });
                    //     }else{
                    //         this.submitFormData(formData);
                    //     }
                    
                })
                .catch(() => console.log(`数据不合规或用户取消`));
        },

        showPromiseConfirmTip(formData) {
            return new Promise((resolve, reject) => {
                this.$showModal({
                    title: '提示',
                    content: '您确定要保存吗？',
                    success: function (r) {
                        console.log(formData);
                        if (r.confirm) {
                            resolve(formData);
                        } else {
                            reject();
                        }
                    }
                });
            });
        },

        // 表单规范化判断
        checkPromiseNormative(formData) {
            const checkFormConfig = CHECK_NORMATIVE_FORM_LIST.filter(
					(item) => item.id === this.template.templateId
			)[0];
            
            // console.log(checkFormConfig);
            return new Promise((resolve, reject) => {
                // 不需要校验的表单直接返回
                if(!checkFormConfig) {
                    resolve(formData);
                } else {
                    const obj = {
						BLLX: checkFormConfig.id, // 模板id
						XCJCJL: this.formData[checkFormConfig.XCJCJL], // 现场检查结论
						SFFXWT: this.formData[checkFormConfig.SFFXWT], // 是否发现问题
                        RYID: this.formData.JCRBH, // 用户id
                        RYMC: this.formData.JCR, // 执法人员名称
                        KSSJ: this.formData[checkFormConfig.KSSJ], // 开始时间
                        JSSJ: this.formData[checkFormConfig.JSSJ], // 结束时间
                        WRYBH: this.recordData.WRYBH,
                        XCZFBH: this.recordData.YWXTBH,
					}

                    // console.log(obj);
                    verifyStandards(obj).then(res=> {
                        this.checkResult = res?.data_json || {};
                        // 如果有提示内容说明没有校验通过，弹窗提示
                        if(this.checkResult?.TXNR) {
                            this.$showModal({
                                title: "提示",
                                content: this.checkResult?.TXNR?.split('\n')
                                    .filter(line => line.trim() !== '')
                                    .map((line, index) => `${index + 1}. ${line}`)
                                    .join('<br>'),
                                confirmColor: "#3873ff",
                                cancelText: "忽略",
                                confirmText: "去修改",
                                clickMaskClose: false, // 是否点击遮罩层关闭弹窗，需在initModal.js中添加clickMaskClose参数
                                textAlign: 'left',
                                success: (modal)=> {
                                    // 点击去修改按钮不保存表单，并调用 保存规范化提醒结果 接口
                                    if (modal.confirm) {
                                        this.checkResult.TXJG = '1'
                                        reject()
                                    } else if (modal.cancel) { // 点击忽略按钮直接校验通过，并调用 保存规范化提醒结果 接口
                                        this.checkResult.TXJG = '0'
                                        resolve(formData);
                                    }

                                    // 保存规范化提醒结果
                                    this.saveValidationResults(this.checkResult);
                                },
                            });
                        } else {
                            resolve(formData);
                        }
                    }).catch(() => {
                        reject();
                    })
                }
            });
        },

        // 保存规范化提醒结果
        saveValidationResults(checkResult) {
            saveValidationResults({XH:checkResult.XH,TXJG:checkResult.TXJG}).then(res => {
                // console.log(res);
                this.checkResult = {}
            })
        },

        buildSubmitParams(formData) {
            let appendProps = {
                ORGID: uni.getStorageSync('user_info').orgid,
                WRYBH: this.recordData.WRYBH,
                YWXTBH: this.taskId
            };
            Object.assign(formData, appendProps);
            if (this.sfblType) {
                formData.SFBL = this.sfblType;
            }
            let submitParams = {
                service: 'DYNAMICFORM_SAVE',
                batch_id: this.taskId,
                client_type: 'mobile_web',
                record_id: this.recordId,
                ywlcdybh: this.recordData.LCBH,
                template_id: this.template.templateId,
                user_id: loginService.getAuthUserId() || 'SDSZFJ'
            };
            let tableName = this.template['templateTable'];
            submitParams[tableName] = formData;
            return submitParams;
        },

        /**
         * 提交笔录表单
         */
        submitFormData(formData) {
            let submitParams = this.buildSubmitParams(formData);
            dynamicformsave(submitParams)
                .then(resp => {
                    this.onSubmitSuccess(resp);
                })
                .catch(error => {
                    this.onSubmitFailure(error, submitParams);
                });
        },

        onSubmitSuccess() {
            //清空缓存，保证下次进入加载默认值
            uni.removeStorageSync(
                `recordID:${this.wrybh}${this.templateId}${this.taskId}`
            );
            this.$showModal({
                title: '提示',
                content: '保存成功',
                type: 'other',
                icon: 'success'
            });

            // 保存的如果是污染源现场检查记录表，则调用记录数据接口
            if(this.template.templateId == '2021030615473254ef0c271e6d4914be744e75ad11e22a') {
                this.recordTableSave();
            }

            this.refreshCacheTimestamp();
            let self = this;
            setTimeout(() => {
                self.hasSubmitForm = true;
                self.naviBack();
            }, 1500);
        },

        recordTableSave() {
            let obj = {
                tableName: this.template['templateTable'],
                ORGMC : this.formData.ORGMC,
                WRYMC : this.recordData.RWBT,
                WRYBH : this.recordData.WRYBH,
                JCR : this.recordData.JCRMC,
                JCRBH:  this.recordData.JCR,
                TBXH : this.recordId
            }
           
            recordTableSave(obj).then(res=>{
                console.log(obj,res);
            })
        },

        onSubmitFailure(error, params) {
            let errorMsg = error || '';
            if (typeof errorMsg === 'object') {
                errorMsg = JSON.stringify(errorMsg);
            }

            logService
                .submitExceptionLog(
                    'DYNAMICFORM_SAVE',
                    params,
                    `提交笔录异常：${errorMsg}`
                )
                .then(resp => {})
                .catch(logError => {});
            this.$showModal({
                title: '提示',
                content: '提交笔录异常',
                type: 'other',
                icon: 'error'
            });
        },

        /**
         * 刷新本地表单数据缓存时间戳
         */
        refreshCacheTimestamp() {
            let cacheRecord = this.loadCacheFromLocal();
            if (cacheRecord) {
                cacheRecord[LOCAL_CACHE_TIMESTAMP_KEY] = new Date().getTime();
                uni.getStorageSync(this.localCacheKey, cacheRecord);
            }
        },

        //返回上一页，并根据进入的最大步骤来缓存步骤
        naviBack() {
            let naviBackStepLength = this.isAddNewRecordMode ? 1 : 2;
            if (this.backStep !== -1) {
                naviBackStepLength = this.backStep;
            }
            uni.navigateBack({
                delta: naviBackStepLength
            });
        },

        showAssistMenuDialog() {
            this.$refs.assistMenuDialog.show = true;
        },

        onAssistMenuClick(menu) {
            if (menu.path) {
                uni.navigateTo({
                    url: menu.path
                });
            }
        },

        onFormMounted() {
            // this.resetPickAttachMenus();
        }
    }
};
</script>

<style scoped>
.form-content-layout {
    width: 100%;
    height: 100%;
}

.form-menu-layout {
    height: 50px;
    background-color: #fff;
}

.form-list-tab {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 50px;
    color: #fff;
}
</style>
