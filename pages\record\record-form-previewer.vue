<template>
	<Page :padding="false" :naviBack="true">
		<template v-slot:bar>
			<NaviBar :title="title">
				<template v-slot:option>
					<image :src="printIcon" style="width: 39rpx; height: 39rpx;margin-left:2rpx" @click="formPrint()">
					</image>
				</template>
			</NaviBar>
		</template>
		<show-modal></show-modal>
		<view class="pageimg">
			<ImagePrint :imageUrl="printUrlList" />
			<view class="flex-row-layout form-menu-layout" v-if="!isOnlyRead">>
				<view class="flex-row-layout form-list-tab" v-for="menu in menus" :key="menu.item"
					@click="onMenuClick(menu)" :style="{'background-color':menu.bg}">
					{{menu.name}}
				</view>
			</view>
		</view>
	</Page>
</template>

<script>
	import {
		DOWNLOAD_URL
	} from '@/common/config.js'
	import uriUtil from '@/common/net/uri.js'

	import Page from '@/pages/component/Page.vue'
	import NaviBar from '@/pages/component/NaviBar.vue'
	import printIcon from '@/static/img/record/print2.png'
	import Thumbnail from '@/static/img/test.png'
	import ImagePrint from '@/pages/component/image-print.vue'
	import printTemplate from '@/api/print-template.js';
	import {
		MODE_MODIFY,
		EVENT_RECORD_DELETE_EXIT,
		postDeleteFormList,
		postPrintForm
	} from '@/api/record.js'

	export default {
		components: {
			Page,
			NaviBar,
			ImagePrint
		},

		data() {
			return {
				isOnlyRead: false, // 是否只读
				Thumbnail,
				menus: [
					// {
					// 	code: 'delete',
					// 	name: '删除',
					// 	bg: '#666666'
					// },
					{
						code: 'edit',
						name: '修改',
						bg: '#e4a54d'
					},
				],
				printIcon,
				templateId: '', //模板ID
				wrybh: '', //污染源编号
				recordId: '', //笔录ID
				printUrlList: [], //预览图片所用的路径
				title: '', //笔录名称
				isDeleted: false
			};
		},

		onLoad(options) {
			this.printUrlList = uni.getStorageSync('printURL')
			this.templateId = options.id
			this.wrybh = options.wrybh
			this.recordId = options.recordId
			this.title = options.title

			this.isOnlyRead = options.isOnlyRead == 1 ? true : false;
		},

		destroyed() {
			uni.$emit(EVENT_RECORD_DELETE_EXIT, this.isDeleted)
		},

		methods: {
			onMenuClick(menu) {
				switch (menu.code) {
					case 'delete':
						this.deleteRecord()
						break
					case 'edit':
						this.editForm()
						break
				}
			},

			//编辑表单
			editForm() {
				this.$emit(EVENT_RECORD_DELETE_EXIT, this.isDeleted)
				uni.preloadPage({
					url: "/pages/record/record-form-editor"
				});
				let params = {
					type: MODE_MODIFY,
					id: this.templateId,
					wrybh: this.wrybh,
					recordId: this.recordId
				}
				uni.navigateTo({
					url: `/pages/record/record-form-editor?${uriUtil.transformObjectToUrlParams(params)}`
				})
			},

			//删除笔录
			deleteRecord() {
				let self = this
				this.$showModal({
					title: '提示',
					content: '您确定要删除吗？',
					success: function(r) {
						if (r.confirm) {
							postDeleteFormList({
								service: 'DELETE_RECORED_SERVICE',
								recordId: self.recordId,
								templateId: self.templateId
							}).then((res) => {
								if (res.status_code == '0') {
									uni.showToast({
										title: '已成功删除！',
										duration: 2000,
										icon: 'success'
									});
									self.isDeleted = true
									setTimeout(() => {
										uni.navigateBack({
											delta: 1
										});
									}, 600)
								}
							})
						}
					}
				});
			},

			//打印文件
			formPrint() {
				let self = this;
				if (this.printUrlList.length > 0) {
					let data = []

					data = [{
						record_id: self.recordId,
						template_id: self.templateId,
					}]
					printTemplate.getCallPrinter(data, true)
				} else {
					uni.showToast({
						icon: 'none',
						mask: true,
						title: '没有模板无法下载',
					});
				}
			}
		},
	};
</script>

<style scoped>
	.form-list-tab {
		width: 100%;
	}

	.form-menu-layout {
		position: fixed;
		bottom: 0;
	}

	.form-list-tab {
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 20rpx 0;
		color: #fff
	}
</style>