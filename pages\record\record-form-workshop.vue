<template>
    <view>
        <record-types
            style="padding: 16rpx 20rpx; width: calc(100% - 40rpx)"
            :current-type="recordType"
            :effectiveTypeCodes="effectiveTypes"
            @select="onRecordTypeSelect"
        />
        <view class="template-form-layout" v-if="isTemplateListLoaded">
           <!-- <view @click="tipsInfoClick()" class="template-form-tips" v-if="isTipsShow">
                <image class="template-form-tips-img" :src="wmzfIcon" />
            </view> -->
            <scroll-view :scroll-y="true" :style="pageListStyle">
                <view v-for="item in typedTemplates" :key="item.id">
                    <record-template-list
                        :template="item"
                        :wrybh="recordData.WRYBH"
                        @addNewRecord="registerAddNewEvent"
                        @previewRecord="registerPreviewEvent"
                    />
                </view>
            </scroll-view>
        </view>
        <PButton @click.native="fixedAttachs" name="下一步" />
    </view>
</template>

<script>
import PButton from '@/components/p-button';
import RecordTemplateList from './record-template-list';
import RecordTypes from './record-types.vue';
import wmzfIcon from '@/static/images/wmzf.png';
import {
    postQueryBdmblx,
    postBLFormList,
    queryTaskDispatchTemplates
} from '@/api/record.js';
import recordFrag from './record-fragment.js';
import { EVENT_EDITOR_EXIT, EVENT_RECORD_DELETE_EXIT,checkTableStatus } from '@/api/record.js';
import { getUserInfoPermissions } from '@/api/predit.js';
const RECORD_TYPES = ['ZFJC', 'ZFJC_CATEGORY', 'ZFJC_ILLEGAL'];

const mixins = [recordFrag];

export default {
    name: 'RecordFormWorkshop',
    // #ifdef MP-ALIPAY
    mixins: mixins.map(item => ({ ...item, props: {} })),
    // #endif
    // #ifndef MP-ALIPAY
    mixins: mixins,
    // #endif

    props: {
        // #ifdef MP-ALIPAY
        ...mixins.reduce(
            (prev, curr) => ({ ...prev, ...(curr.props || {}) }),
            {}
        ),
        // #endif

        changeNumber: {
            type: Number
        }
    },

    components: {
        PButton,
        RecordTemplateList,
        RecordTypes
    },

    data() {
        return {
            recordType: 'ZFJC',
            isTipsShow:false,//是否显示文明执法
            effectiveTypes: [],
            isTemplateListLoaded: false, //表单模板是否已加载
            templates: [],
            wmzfIcon,
            formData: {}, //表单的基础数据
            recordData: {}, //存放的数据--列表污染源数据
            locationData: {}, //存放的数据--坐标数据
            personData: {}, //存放的数据--执法人员缓存数据
            isRefreshTemplates: false
        };
    },

    computed: {
        pageListStyle: function () {
            return {
                height: 'calc(100vh - 428rpx)'
            };
        },

        //根据笔录类型过滤的表单
        typedTemplates: function () {
            return this.templates.filter(template => {
                return template.BLLX === this.recordType;
            });
        }
    },

    mounted() {
        this.initTemplates();
        // getUserInfoPermissions({
        //     CDLX: 'WMZFCDLX'
        // }).then(res => {
        //     if(res.data_json.length > 0){
        //       this.isTipsShow = true
        //     }
        // });
    },

    methods: {
        //新增笔录时，监听笔录编辑页面返回事件以刷新页面
        registerAddNewEvent(item) {
            let _self = this;
            uni.$once(EVENT_EDITOR_EXIT, hasSubmitForm => {
                if (hasSubmitForm) {
                    _self.refreshTemplateList();
                }
            });

            uni.navigateTo({
                url: `/pages/record/record-form-editor?type=1&id=${item.TEMPLATEID}&wrybh=${this.recordData.WRYBH}`
            });
        },

        //预览笔录时，监听笔录预览事件，在预览页面删除笔录退出时刷新页面
        registerPreviewEvent() {
            let _self = this;
            uni.$once(EVENT_RECORD_DELETE_EXIT, isDeleted => {
                if (isDeleted) {
                    _self.refreshTemplateList();
                }
            });
        },

        /**
         * 新增、预览删除表单返回后刷新表单模板
         */
        refreshTemplateList() {
            this.isTemplateListLoaded = false;
            this.isRefreshTemplates = true;
            this.initTemplates();
        },

        //获取表单基本结构
        initTemplates() {
            this.getData();
            uni.showLoading({
                title: '正在获取表单列表'
            });

            queryTaskDispatchTemplates(this.recordData.YWXTBH).then(resp => {
                uni.hideLoading();
                let groupTemplates = resp.data_json;
                if (groupTemplates) {
                    let templates = [];
                    for (let groupKey in groupTemplates) {
                        templates = templates.concat(
                            groupTemplates[groupKey] || []
                        );
                    }
                    this.templates = templates
                        .map(item => {
                            return {
                                TEMPLATEID: item.MBBH,
                                TEMPLATENAME: item.MBMC,
                                BLLX: item.BLLX,
                                PXH: item.PXH
                            };
                        })
                        .sort((first, sec) => {
                            let firstOrder = parseInt(first.PXH || '999');
                            let secOrder = parseInt(sec.PXH || '999');
                            return firstOrder - secOrder;
                        });

                    let groupTypes = new Set();
                    this.templates.forEach(t => {
                        groupTypes.add(t.BLLX);
                    });
                    let dispatchTypes = Array.from(groupTypes);
                    this.effectiveTypes = RECORD_TYPES.filter(t => {
                        return dispatchTypes.indexOf(t) !== -1;
                    });
                    if (!this.isRefreshTemplates) {
                        this.recordType = this.effectiveTypes[0];
                    }
                    this.isRefreshTemplates = false;
                    this.getRecordList();
                }
            });
        },

        getRecordList() {
            postBLFormList({
                service: 'QUERY_BL_LIST',
                xh: this.recordData.YWXTBH
            })
                .then(res => {
                    this.templates.forEach(element => {
                        element.formData = [];
                        if (res.data_json.length > 0) {
                            res.data_json.forEach((e, index) => {
                                if (e.mbbh === element.TEMPLATEID) {
                                    if (
                                        element.TEMPLATEID !==
                                        '202011161722220c3f488fabc7497d90931b5f904b9109'
                                    ) {
                                        element.formData.push(e);
                                    }
                                    // this.$set(element.formData,index,e)
                                }
                            });
                        }
                    });
                    // debugger
                    uni.hideLoading();
                    this.isTemplateListLoaded = true;
                })
                .catch(res => {});
        },

        //获取缓存过的数据，经纬度、用户信息等
        getData() {
            this.recordData = uni.getStorageSync('record-data');
            this.locationData = uni.getStorageSync('location-info');
            this.personData = uni.getStorageSync(
                'ZFRY_NAME' + this.recordData.YWXTBH
            );
        },

        //挂载其他数据，如经纬度、执法人员等
        getOhterFormData() {
            let self = this;
            // //针对污染源有值情况下的赋值
            // //只是暂时这么写，后续根据动态表单会有专门的渲染方式
            self.$set(self.formData, 'JD', self.locationData.JD);
            self.$set(self.formData, 'WD', self.locationData.WD);
            self.$set(self.formData, 'DD', self.locationData.ADDRESS);
            self.$set(self.formData, 'ZFRY', self.personData.onlyID);
            self.$set(self.formData, 'ZFRYBH', self.personData.onlyID);
            // self.$set(self.formData, "ZFBM", self.personData.bmid)
            // self.$set(self.formData, "ZFZH", self.personData.nemeIDs)
        },

        //跳转下一步
        fixedAttachs() {
            checkTableStatus({xh:this.recordData.YWXTBH}).then(res=>{
                console.log(res);
            })
            this.doNext(this.stepIndex);
        },

        onRecordTypeSelect(code) {
            this.recordType = code;
        },

        //跳转到精神文明界面
        tipsInfoClick() {
            uni.navigateTo({
                url: `/pages/record/record-template-form-tips`
            });
        }
    }
};
</script>

<style scoped>
.template-form-layout {
    padding: 0 10px 10px 10px;
    width: calc(100% - 20px);
    background-color: #f4f4f4;
}

.template-form-tips {
    position: absolute;
    z-index: 100;
    right: 60rpx;
	top: 50%;
}

.template-form-tips-img {
    width: 100rpx;
    height: 100rpx;
}
</style>
