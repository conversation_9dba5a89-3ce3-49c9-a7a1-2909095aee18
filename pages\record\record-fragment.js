/*
 * @Author: your name
 * @Date: 2021-05-07 17:44:23
 * @LastEditTime: 2021-05-24 10:10:02
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /UNI_APP_ShanDong/pages/record/record-fragment.js
 */
import taskService from '@/api/task-service.js'
import {getStepIndex } from '@/api/record.js'
export default {
	props: {
		height: {
			type: Number,
			default: 600
		},
		
		stepIndex: {
			type: Number,
			default: 0
		},
		
		isTargetAssigned: {
			type: Boolean,
			default: false
		},
		
		taskInfo: {
			type: Object,
			default: () => {
				return {}
			}
		},
		
		isLastStep: {
			type: Boolean,
			default: false
		},
		
		canFinish: {
			type: Boolean,
			default: false
		},
		
		pollution: Object
	},

	data(){
		return{
			recordData: {},
			isFinishingTask: false
		}
	},
	
	computed: {
		nextStepText: function () {
			return this.isLastStep === true ? '结束任务' : '下一步'
		},
	},
	
	methods: {
		doNext(index) {
			this.postStep(index)
			this.$emit('nextStep', index + 1)
		},

		postStep(index){
			let data = uni.getStorageSync('record-data')
			getStepIndex({
				method:'addRecord',
				xczfbh:data.YWXTBH,
				stepNum:index + 1
			}).then((res)=>{
			})
		},


		finishTask() {
			this.askBeforeFinish()
		},
		
		askBeforeFinish() {
			let _self = this
			uni.showModal({
				title: '结束任务',
				content: '确定结束当前执法任务？',
				cancelText:'确认',
				confirmText:'取消',
				cancelColor:"rgb(15, 174, 255)",
				confirmColor:'#666',
				success(resp) {
					if(resp.confirm) {
						
					}else if (resp.cancel) {
						_self.doTaskFinish()
					}
				}
			})
		},
		
		doTaskFinish() {
			let _self = this
			this.isFinishingTask = true
			taskService.finishWryjcTask(this.taskInfo.YWXTBH, this.taskInfo.BZBH)
				.then(resp => {
					if(resp.msg === 'success') {
						_self.showFinishSuccessTip()
					}
				})
				.finally(() => {
					_self.isFinishingTask = false
				})
		},
		
		/**
		 * 任务流转成功，1.5秒后退出
		 */
		showFinishSuccessTip() {
			uni.showToast({
				title: '结束任务成功',
			})
			
			setTimeout(() => {
				this.postStep(5)
				uni.navigateBack({
					delta: 2
				})
			}, 1500)
		}
	}
}