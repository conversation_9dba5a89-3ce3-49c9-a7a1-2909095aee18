<template>
    <Page title="历史笔录"
	      :padding="false">
          <section class="pd-main">
			  <show-modal></show-modal>
			<view>
				<div class="gap"></div>
				<scroll-view :scroll-y="true"
				             :style="pageListStyle">
					<dl class="pd-dlbx1"
					    v-for="item in historyList"
					    @click="clickRouter(item)"
					    :key="item.id">
						<dd>
							<image src="/static/app/images/lstic1a.png"
							       class="imgic"></image>
							<h2>{{item.XGSJ}}
								<span class="mk"
								      v-if="item.RWLXMC">
									{{item.RWLXMC}}
								</span>
							</h2>
							<p><em>检查人：</em>{{item.JCRMC}}</p>
							<text class="look" @click.stop="chooseClick(item)">选择</text>
						</dd>
					</dl>
				</scroll-view>
			</view>
		</section>
    </Page>
</template>

<script>
import Page from '@/pages/component/Page.vue'
import { queryHistoryRecord } from '@/api/record.js'
export default {
    components: {
        Page
    },
    data() {
        return {
			historyList: [],
			confirmRecord: null
        };
	},

	computed:{
		pageListStyle(){
			return {
				height: 'calc(100vh - 100upx)',
			}
		}
	},
	
	mounted() {
		this.historyList = uni.getStorageSync('historyRecords')
	},
	
	destroyed() {
		uni.$emit('onHistoryRecordListDestoryed', this.confirmRecord)
	},

    methods: {
		chooseClick(item){
			let self = this
			this.$showModal({
				title: '提示',
				content: '确定要用历史数据覆盖当前数据吗？（此操作不可撤销）',
				success: function(r) {
					if (r.confirm) {
						self.confirmRecord = item
						uni.navigateBack({
							delta: 1
						})
					}
				}
			})
		},

		//
		clickRouter(){
			console.log(123);
		},
    },
};
</script>

<style scoped>
.look{
	position: absolute;
    right: 10px;
    top: 28%;
	padding: 10rpx;
}
</style>
