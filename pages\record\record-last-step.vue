<template>
	<view>
		<view class="from-checkbox">
			<PCard title="笔录">
				<!-- <view class="from-checkbox-preview" @click="clickAllList">全选</view> -->
				<scroll-view :scroll-y="true"
				             :style="pageListStyle">
					<checkbox-group class="from-checkbox-group"
					                @change.stop="changePreview">
						<label class="from-checkbox-label"
						       v-for="item in printFormList"
						       :key="item.id">
							<view>
								<checkbox :value="item.recordId"
										  style="transform:scale(0.7)"
								          :checked="item.checked" />{{item.mbmc}}
							</view>

							<view class="from-checkbox-item">
								<view class="from-checkbox-preview"
								      @click.stop="recordPrint(item)">打印</view>
								<!-- <view class="from-checkbox-preview"
								      @click.stop="recordEdit(item)">修改</view> -->
								<view class="from-checkbox-share"
								      @click.stop="getShareDialog(item)">分享</view>
								<view class="from-checkbox-delete"
								      @click.stop="getDeleteForm(item)">删除</view>
							</view>
						</label>

					</checkbox-group>
					<!-- <NoData v-if="!pageShow "
				        :type="printFormList.length < 1 ? 'message':''" /> -->
				</scroll-view>
			</PCard>
			<show-modal></show-modal>
		</view>
		<view class="flex-row-layout form-menu-layout">
			<form-operate-menu-item v-for="menu in menus"
			                        :key="menu.code"
			                        :menu="menu"
			                        @click.native="onMenuClick(menu)" />
		</view>
	</view>
</template>

<script>
	import PCard from '@/pages/component/PCard.vue'
	import NoData from '@/components/no-data.vue'
	import iconShare from '@/static/img/record/icon_share.png'
	import iconOver from '@/static/img/record/icon_over.png'
	import recordFrag from './record-fragment.js'
	import formOperateMenuItem from './form-operate-menu-item.vue'
	import {
		DOWNLOAD_URL
	} from '@/common/config.js'
	import {
		postPrintForm,
		postBLFormList,
		postOverForm,
		postDeleteFormList
	} from '@/api/record.js'
	const mixins = [recordFrag]
	//菜单操作码
	const MENU_CODE_PRINT = 'print'
	const MENU_CODE_SUBMIT = 'submit'
	const MENU_CODE_SHARE = 'share'
	export default {
		name: 'RecordLastStep',
		// #ifdef MP-ALIPAY
		mixins: mixins.map(item => ({ ...item,
			props: {}
		})),
		// #endif
		// #ifndef MP-ALIPAY
		mixins: mixins,
        // #endif
    
		components: {
			formOperateMenuItem,
			NoData,
			PCard
		},

		props: {
			// #ifdef MP-ALIPAY
			...mixins.reduce((prev, curr) => ({ ...prev,
				...(curr.props || {})
			}), {}),
			// #endif

		},

		data() {
			return {
				data:{},
				menus: [
					// {
					// 	code: MENU_CODE_PRINT,
					// 	name: '打印',
					// 	icon: iconPrint
					// },

					// {
					// 	code: MENU_CODE_SHARE,
					// 	name: '分享',
					// 	icon: iconShare
					// },
					{
						code: MENU_CODE_SUBMIT,
						name: '结束任务',
						icon: iconOver
					},
				],
				printFormList: [], //预览的列表数据
				downLoadUrl: '', //下载的连接
				shareType: '', //分享的类型
				chooseRecordId: '',
				chooseItem: {}, //选择的笔录
				pageShow: true //页面显示的状态
			}
		},

		computed: {
			pageListStyle() {
				return {
					height: 'calc(100vh - 440rpx)',
				}
			},
		},

		mounted() {
			this.data = uni.getStorageSync('record-data')
			this.getFormList()
		},

		methods: {
			getFormList() {
				this.pageShow = true
				postBLFormList({
					service: 'QUERY_BL_LIST',
					xh: this.data.YWXTBH
				}).then((res) => {
					if (res.data_json.length > 0) {
						//过滤证据笔录
						res.data_json.forEach((element,index) => {
							if(element.mbbh === "202011161722220c3f488fabc7497d90931b5f904b9109"){
								res.data_json.splice(index,1);
									}
						});
						this.printFormList = res.data_json
						this.printFormList.forEach(element => {
							element.checked = true
						});
						if (this.companprintFormListyList.length < 1) {
							this.pageShow = false
						}
					}
					
				}).catch((res) => {
					this.pageShow = false
				})
			},

			//不同的功能方法//分别对应打印、提交、分享
			onMenuClick(menu) {
				switch (menu.code) {
					case MENU_CODE_PRINT:
						this.getPrintAll()
						break
					case MENU_CODE_SUBMIT:
						this.getAllSubmit()
						break
					case MENU_CODE_SHARE:
						this.getAllShare()
						break
				}
			},

			getAllSubmit() {
				let self = this
				if (self.printFormList.length < 1) {
					uni.showToast({
						title: '至少要保存一份笔录之后才可以提交',
						duration: 2000,
						icon: 'none'
					});
				} else {
					let data = this.getAllData()
					// if (data.length < 1) {
					// 	uni.showToast({
					// 		title: '请至少选择一份笔录',
					// 		duration: 2000,
					// 		icon: 'none'
					// 	});
					// } else {
						this.$showModal({
							title: '提示',
							content: '您确定要提交当前的表单并结束任务吗？',
							success: function(r) {
								if (r.confirm) {
									let req = []
									data.forEach(element => {
										req.push(postDeleteFormList({
											service: 'DELETE_RECORED_SERVICE',
											recordId: element.recordId,
											templateId: element.mbbh
										}))
									});
									Promise.all(req)
										.then((res) => {
											self.onSaveFrom(res)
											// idList.push(res[0].WJID)
										})
								}
							}
						})
					// }
				}
			},

			getAllShare(){

			},

			//统一将内容分享出去
			allShareList(idList) {

				console.log(idList);
			},

			//抛出选择了的数据
			getAllData() {
				let data = []
				this.printFormList.forEach(element => {
					if (element.checked === false) {
						data.push(element)
					}
				});
				return data
			},

			recordPrint(item) {
				this.shareType = 'print'
				this.chooseItem = item
				this.recordPreview(this.chooseItem)
			},

			//获取到返回的打印ID,并完成分享功能
			recordPreview(item) {
				let self = this
				postPrintForm({
					service: 'PRINT_FORM',
					record_id: item.recordId,
					template_id: item.mbbh
				}).then((res) => {
					self.downLoadUrl = DOWNLOAD_URL + '?wdbh=' + res.WJID
					if (self.shareType === 'share') {
						// #ifdef APP-PLUS
						uni.shareWithSystem({
							// imageUrl:testIcon,
							summary: self.chooseItem.mbmc + '下载连接：',
							href: self.downLoadUrl,
							success() {
								// 分享完成，请注意此时不一定是成功分享
							},
							fail() {
								// 分享失败
							}
						})
						// #endif
					} else {
						this.getUpdata(res.WJID)
					}
				}).catch(() => {})
			},

			//提交功能
			onSaveFrom() {
				let self = this;
				console.log(this.data);
				let userName
				uni.getStorage({
					key: 'user_id',
					success: function(res) {
						userName = res.data
						postOverForm({
							service: 'WRYJC_FINISH_SERVICE',
							bzbh: self.data.BZBH,
							bussinessId: self.data.YWXTBH,
							userId: userName
						}).then((res) => {
							uni.showToast({
								title: '已成功提交！',
								duration: 2000,
								icon: 'success'
							});
							setTimeout(() => {
								uni.$emit('overFinish')
								uni.redirectTo({
									url: '/pages/task/task-list'
								});
							}, 1000)

						}).catch((res) => {
							uni.showToast({
								title: '提交有误',
								duration: 2000,
								icon: 'none'
							});
						})
					}
				})

			},

			//打开分享页面的弹窗
			getShareDialog(item) {
				this.chooseItem = item
				this.shareType = 'share'
				this.recordPreview(item)
			},

			getUpdata(id) {
				this.downLoadUrl = DOWNLOAD_URL + '?wdbh=' + id
				uni.downloadFile({
					url: DOWNLOAD_URL + '?wdbh=' + id, //仅为示例，并非真实的资源
					header: {
						token: uni.getStorageSync('authToken'), // 这里是要添加的请求头
					},
					success: (res) => {
						let filePath = res.tempFilePath;

						uni.saveFile({
							tempFilePath: filePath,
							success: function(r) {
									let Printer = plus.android.importClass('com.bovosz.webapp.print.Printer')
                                    Printer.printAppPlusPDFFile(plus.android.runtimeMainActivity(), r.savedFilePath)
							}
						});
						// uni.openDocument({
						// 	filePath: filePath,
						// 	success: function(res) {
						// 		console.log('打开文档成功');
						// 	}
						// });
					}
				});
			},

			//编辑表单
			recordEdit(item) {
				uni.navigateTo({
					url: `/pages/record/record-form-list?type=2&id=${item.mbbh}&wrybh=${this.data.WRYBH}&recordId=${item.recordId}`
				})
			},

			//删除表单
			getDeleteForm(item) {
				let self = this
				this.$showModal({
					title: '提示',
					content: '您确定要删除吗？',
					success: function(r) {
						if (r.confirm) {
							postDeleteFormList({
								service: 'DELETE_RECORED_SERVICE',
								recordId: item.recordId,
								templateId: item.mbbh
							}).then((res) => {
								if (res.status_code == '0') {
									uni.showToast({
										title: '已成功删除！',
										duration: 2000,
										icon: 'success'
									});
								}
								self.$delete(self.printFormList,item)
								// self.printFormList = []
								self.getFormList()
							}).catch(() => {
								uni.showToast({
									title: '删除失败！',
									duration: 2000,
									icon: 'none'
								});
							})
						}
					}
				});

			},

			//全选
			clickAllList() {
				this.printFormList.forEach(element => {
					console.log(element);
					this.$set(element, 'checked', true)
				});
				console.log(this.printFormList);
			},

			lookValue(item) {
				this.chooseRecordId = item.recordId
			},

			changePreview(e) {
				var items = this.printFormList,
					values = e.detail.value;
				for (var i = 0, lenI = items.length; i < lenI; ++i) {
					const item = items[i]
					if (values.includes(item.recordId)) {
						this.$set(item, 'checked', true)
					} else {
						this.$set(item, 'checked', false)
					}
				}
				// console.log(this.printFormList);
			},
		}
	}
</script>

<style>
	.from-checkbox {
		padding: 10upx 20upx;
	}

	.from-checkbox-item {
		font-size: 28rpx;
		width: 35%;
		display: flex;
		align-items: center;
		justify-content: space-between;
	}

	.from-checkbox-list {
		padding: 18upx 0;
		display: flex;
		align-items: center;
		justify-content: space-between;
	}

	.from-checkbox-preview {
		color: #037FF7
	}

	.from-checkbox-share {
		color: #333
	}

	.from-checkbox-delete {
		color: #aaa
	}

	.from-checkbox-label {
		display: flex;
		align-items: center;
		width: 100%;
		justify-content: space-between;
		padding: 16rpx 0;
	}

	.from-checkbox-group {
		width: 100%;
	}

	.form-menu-layout {
		z-index: 9999;
		position: fixed;
		bottom: 0px;
	}
</style>
