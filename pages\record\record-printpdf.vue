<template>
        <Page
	      :padding="false">
          <template v-slot:bar>
			<NaviBar title="打印预览"
			         >
				<template v-slot:option>
					<image @click="onBackIndex"
					       :src="printUrl"
					       style="width: 39rpx; height: 39rpx;margin-left:2rpx"></image>
				</template>
			</NaviBar>
		</template>
        </Page>
</template>

<script>
import NaviBar from '@/pages/component/NaviBar.vue'
import Page from '@/pages/component/Page.vue'
import defaultNaviIcon from '@/static/img/record/print2.png'
export default {
    components:{
       Page,
       NaviBar
    },

    data() {
        return {
            printUrl:defaultNaviIcon
        };
    },

    computed: {

    },
    watch: {

    },
    methods: {

    },
};
</script>

<style scoped>

</style>
