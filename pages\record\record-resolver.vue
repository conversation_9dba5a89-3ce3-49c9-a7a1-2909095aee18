<template>
	<Page title="现场执法检查" :padding="false">
		<template v-slot:bar>
			<NaviBar title="现场执法检查" :naviBack="false">
				<template v-slot:navi>
					<image @click="onBackIndex" :src="backurl" style="width: 39rpx; height: 39rpx; margin-left: 2rpx">
					</image>
				</template>
				<template v-slot:option>
					<view class="record-list" v-show="currentStepIndex > 0">
						<image @click="recordListRouter()" :src="record_list" class="record-ilist-image" />
						<image :src="record_addIcon" @click="seeImg()" class="record-ilist-image" />
					</view>
				</template>
			</NaviBar>
		</template>

		<!-- <AiFab :isDock="true" :existTabBar="true" v-if="currentStepIndex > 0">
		</AiFab> -->

		<record-step-indicator ref="indicator" style="width: 100%; padding: 8px 0" :steps="steps"
			:activeIndexs="currentStepIndex" @activeStep="onStepActived" />
		<view style="width: 100%; height: 1px; background-color: #eee" />
		<view id="fragment" style="height: calc(100% - 67px); background-color: #fff">
			<record-sign-location class="record-fragment" v-show="currentStepIndex === 0" :height="fragHeight"
				:data="recordData" :stepIndex="0" :isTargetAssigned="isTargetAssigned" :relateTaskId="relateTaskId"
				@nextStep="doNextStep" @resetInspectInfo="resetInspectInfo" />
			<record-cert-confirm v-if="currentStepIndex === 1" :height="fragHeight" :data="recordData" :stepIndex="1"
				@nextStep="doNextStep" />
			<record-attach-picker v-if="currentStepIndex === 2" :height="fragHeight" :data="recordData" :stepIndex="2"
				@nextStep="doNextStep" />
			<record-form-workshop v-if="currentStepIndex === 3" :height="fragHeight" :changeNumber="currentStepIndex"
				:stepIndex="3" @nextStep="doNextStep" />
			<record-result-preview v-if="currentStepIndex === 4" :height="fragHeight" :task-info="recordData"
				:stepIndex="4" @nextStep="doNextStep" />
			<record-electronic-file v-if="currentStepIndex === 5" :height="fragHeight" :stepIndex="5" :isLastStep="true"
				:task-info="recordData" :canFinish="canFinish" @removeRecordCurren="removeCurren"
				@nextStep="doNextStep" />
		</view>
		<uni-popup ref="popup" v-show="dialogType" type="dialog">
			<record-dialog @closeDialog="close" :lastInfo="lastInfo" />
		</uni-popup>
		<navi-bar-option-menu ref="chooseMenuDialog" :menus="chooseFileMenus" @menuClick="onChooseMenuClick" />
	</Page>
</template>

<script>
	import naviBarOptionMenu from '@/pages/component/dialog/right-top-dialog.vue';
	import record_addIcon from '@/static/img/record/record_add.png';
	import record_list from '@/static/img/record/record_list.png';
	import NaviBar from '@/pages/component/NaviBar.vue';
	import Page from '@/pages/component/Page.vue';
	import recordStepIndicator from './record-step-indicator.vue';
	import recordSignLocation from './record-sign-location.vue'; //定位签到（1）
	import recordCertConfirm from './record-cert-confirm.vue'; //亮证告知（2）
	import recordComfirmPollution from './record-comfirm-pollution.vue'; //信息核实（3）
	import recordAttachPicker from './record-attach-picker.vue'; //检查拍照（4）
	import recordFormWorkshop from './record-form-workshop.vue'; //笔录制作（5）
	import recordResultPreview from './record-result-preview.vue'; //打印分享（6）
	import recordElectronicFile from './record-electronic-file.vue'; //电子归档（7）
	import recordLastStep from './record-last-step.vue'; //结束任务（8）
	import defaultNaviIcon from '@/static/img/icon_back_white.png';
	import seeIcon from '@/static/img/icon_see.png';
	import {
		postQueryJcbl,
		getStepIndex
	} from '@/api/record.js';
	import styleUtil from '@/common/style.js';
	// import uniPopup from '@/components/uni-popup/uni-popup.vue'
	// import uniPopupMessage from '@/components/uni-popup/uni-popup-message.vue'
	// import uniPopupDialog from '@/components/uni-popup/uni-popup-dialog.vue'
	import recordDialog from './record-dialog.vue';

	const PARAM_TASK_ID = 'taskId';
	//参数 - 检查对象是否已分配
	const PARAM_TARGET_ASSIGNED = 'targetAssigned';

	export default {
		components: {
			naviBarOptionMenu,
			Page,
			recordStepIndicator,
			NaviBar,
			recordLastStep,
			recordElectronicFile,
			recordSignLocation,
			recordComfirmPollution,
			recordCertConfirm,
			recordAttachPicker,
			recordFormWorkshop,
			recordDialog,
			recordResultPreview
			// uniPopup,
			// uniPopupMessage,
			// uniPopupDialog
		},

		data() {
			return {
				record_addIcon,
				record_list,
				//步骤片断容器高度
				fragHeight: 600,
				dialogType: false, //弹窗的状态
				maxCurren: 0, //每一次进入时最大的那一次
				currenArr: [],
				backurl: defaultNaviIcon,
				seeUrl: seeIcon,
				recordData: {}, //基本的信息
				lastInfo: {}, //最后一次获取的消息
				steps: [{
						id: '1',
						name: '定位签到',
						order: 1
					},
					{
						id: '2',
						name: '亮证告知',
						order: 2
					},
					{
						id: '3',
						name: '现场取证',
						order: 3
					},
					{
						id: '4',
						name: '笔录制作',
						order: 4
					},
					{
						id: '5',
						name: '打印分享',
						order: 5
					},
					{
						id: '6',
						name: '办结归档',
						order: 6
					}
				],
				chooseFileMenus: [{
						name: '智能判案',
						id: 'intelligentJudgment'
					},
					// {name:'录音',id:'music'},
					{
						name: '便签',
						id: 'bookMark'
					}
					// {name:'污染源详情',id:'enterpriseDetails'},
				],
				currentStepIndex: 0,
				//是否
				isTargetAssigned: false,
				//任务ID，即每一次执法对应一个任务，此执法可能对应用多条检查笔录
				relateTaskId: '',
				stepArr: [],
				canFinish: true
			};
		},

		watch: {
			currentStepIndex(val) {
				this.recordData = uni.getStorageSync('record-data');
				console.log(this.recordData);
				let maxIndex =
					this.currenArr.length > 0 ? Math.max(...this.currenArr) : 0;
				this.stepArr.push(val);
				// getStepIndex({
				// 	method:'addRecord',
				// 	xczfbh:this.recordData.YWXTBH,
				// 	stepNum:val
				// }).then((res)=>{
				// })
				uni.setStorage({
					key: this.recordData.YWXTBH,
					data: this.maxCurren > maxIndex ? this.maxCurren : maxIndex
				});
			}
		},

		created() {
			this.recordData = uni.getStorageSync('record-data');
		},

		onLoad(options) {
			if (options) {
				if (options.hasOwnProperty(PARAM_TARGET_ASSIGNED)) {
					this.isTargetAssigned = JSON.parse(
						options[PARAM_TARGET_ASSIGNED]
					);
				} else {
					this.isTargetAssigned = true;
				}
				this.taskId = options[PARAM_TASK_ID];
				this.relateTaskId = options.relateTaskId;
				if (options.canFinish) {
					this.canFinish = JSON.parse(options.canFinish);
				}
			}
		},

		mounted() {
			let self = this;
			styleUtil.getNodeLayout(this, '#fragment').then(layout => {
				this.fragHeight = layout.height;
			});
			//获取列表页面过来的缓存数据

			self.getCurrenIndex();
		},

		methods: {
			seeImg() {
				this.$refs.chooseMenuDialog.show = true;
			},

			onChooseMenuClick(menu) {
				if (menu.id === 'intelligentJudgment') {
					uni.navigateTo({
						url: `/pages/record/record-Intelligent-judgment`
					});
				}
				if (menu.id == 'enterpriseDetails') {
					uni.navigateTo({
						url: `/pages/main/PollutionDetail?title=污染源详情&recordId=${this.recordData.WRYBH}`
					});
				}
				if (menu.id === 'music') {
					uni.navigateTo({
						url: `/pages/record/record-sound-management-list`
					});
				}
				if (menu.id === 'bookMark') {
					uni.navigateTo({
						url: `/pages/record/record-bookMark-list`
					});
				}
			},

			//查看企业的检查情况
			recordListRouter() {
				uni.navigateTo({
					url: `/pages/main/PollutionDetail?title=污染源详情&recordId=${this.recordData.WRYBH}`
				});
				// uni.navigateTo({
				// 	url: `/pages/record/record-company-list?wrybh=${this.recordData.WRYBH}&wrymc=${this.recordData.RWBT || this.recordData.WRYMC}`
				// })
			},

			//获取退出时的缓存过的步骤
			getCurrenIndex() {
				let self = this;
				getStepIndex({
						method: 'getRecord',
						xczfbh: self.recordData.YWXTBH
					})
					.then(res => {
						let id = res.data_json.steps.length;
						self.currentStepIndex = id;
						if (id > self.maxCurren) {
							self.maxCurren = id;
						}
					})
					.catch(() => {
						uni.getStorage({
							key: self.recordData.YWXTBH,
							success: function(res) {
								self.currentStepIndex = res.data;
								if (res.data > self.maxCurren) {
									self.maxCurren = res.data;
								}
								console.log(res.data);
							}
						});
					});
			},

			/**
			 * 信访舆情任务发起任务后设置企业信息
			 * @param {Object} taskInfo
			 */
			resetInspectInfo(taskInfo) {
				console.log('重置污染源信息');
				this.recordData = taskInfo;
			},

			//获取最后一次的勘查信息
			getLastInfo() {
				postQueryJcbl({
					params: {
						WRYMC: this.recordData.RWBT,
						WRYBH: this.recordData.WRYBH || ''
					},
					service: 'QUERY_XCJC_KCBL'
				}).then(res => {
					this.lastInfo = res.data_json;
				});
			},

			doNextStep(index) {
				this.$refs['indicator'].activeStep(index);
				this.currentStepIndex = index;
			},

			//跳转,并记录每一次跳转的次数
			onStepActived(index) {
				this.currentStepIndex = index;
				this.currenArr.push(this.currentStepIndex);
			},

			//返回上一页，并根据进入的最大步骤来缓存步骤
			onBackIndex() {
				uni.navigateBack({
					delta: 1
				});
			},

			//移出记录的跳转步骤缓存，进入后不再跳转缓存至的步骤
			removeCurren() {
				let self = this;
				uni.removeStorage({
					key: self.recordData.BZBH,
					success: function(res) {
						uni.navigateBack({
							delta: 1
						});
					}
				});
			},

			close() {
				this.dialogType = false;
				this.$refs.popup.close();
			}
		}
	};
</script>

<style scoped>
	.record-fragment {
		height: 100%;
	}

	.record-list {
		width: 120rpx;
		display: flex;
		align-items: center;
		justify-content: space-around;
	}

	.record-ilist-image {
		height: 40rpx;
		width: 40rpx;
		margin-right: 6rpx;
	}
</style>