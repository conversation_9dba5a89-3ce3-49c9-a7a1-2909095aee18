<template>
    <view>
        <view class="from-checkbox">
			<view @click="tipsInfoClick()" class="template-form-tips" v-if="isTipsShow">
			    执法</br>评价
			</view>
            <PCard title="笔录">
                <template v-slot:option>
                    <image
                        :src="printImage"
                        @click="allPrintImage()"
                        class="printimage"
                        v-if="!isH5"
                    />
                </template>
                <scroll-view :scroll-y="true" :style="pageListStyle">
                    <checkbox-group
                        class="from-checkbox-group"
                        @change.stop="changePreview"
                    >
                        <label
                            class="from-checkbox-label"
                            v-for="item in printFormList"
                            :key="item.id"
                        >
                            <view>
                                <checkbox
                                    :value="item.recordId"
                                    style="transform: scale(0.7)"
                                    :checked="item.checked"
                                />{{ item.mbmc }}
                            </view>

                            <view class="from-checkbox-item">
                                <!-- <view class="from-checkbox-preview"
								      @click.stop="recordPrint(item)">打印</view> -->
                                <view
                                    class="from-checkbox-share"
                                    @click.stop="getShareDialog(item)"
                                    >分享</view
                                >
                                <!-- <view class="from-checkbox-delete"
								      @click.stop="getDeleteForm(item)">删除</view> -->
                            </view>
                        </label>
                    </checkbox-group>
                </scroll-view>
                <uni-popup ref="previewShareTypeShow" type="dialog">
                    <view class="sharedialogtype">
                        <view
                            class="sharedialogtype-main"
                            @click="qrcodeShareClick"
                        >
                            <image
                                :src="qrcodeShareIcon"
                                class="sharedialogtype-image"
                            />
                            <text>二维码分享</text>
                        </view>
                        <view
                            class="sharedialogtype-main"
                            @click="appShareClick"
                        >
                            <image
                                :src="appShareIcon"
                                class="sharedialogtype-image"
                            />
                            <text>APP分享</text>
                        </view>
                    </view>
                </uni-popup>
                <uni-popup ref="previewPopup" type="bottom">
                    <view class="qrcodes">
                        <view class="qrcodes-view">
                            <view class="qrcodes-name">{{ recordName }}</view>
                            <tki-qrcode
                                v-if="ifShow"
                                ref="qrcode"
                                :lv="lv"
                                :onval="onval"
                                :loadMake="loadMake"
                                :background="background"
                                :foreground="foreground"
                                :pdground="pdground"
                                :icon="icon"
                                :cid="cid"
                                :size="size"
                                :val="qrCodeVal"
                            />
                            <view class="qrcodes-info"
                                >扫一扫上面的二维码，查看笔录</view
                            >
                            <view class="qrcodes-btn" @click="saveQrcode"
                                >保存到相册</view
                            >
                        </view>
                    </view>
                </uni-popup>
				
				<!-- 文明执法二维码 -->
				<uni-popup ref="civilizationPopup" type="bottom">
				    <view class="qrcodes">
				        <view class="qrcodes-view">
				            <view class="qrcodes-name">文明执法调查问卷</view>
				            <tki-qrcode
				                v-if="1"
				                ref="qrcode"
				                :lv="lv"
				                :onval="onval"
				                :loadMake="loadMake"
				                :background="background"
				                :foreground="foreground"
				                :pdground="pdground"
				                :icon="icon"
				                :cid="cid"
				                :size="size"
				                :val="civilizationqrCode"
				            />
				            <view class="qrcodes-info"
				                >请用手机自带的浏览器扫描二维码，填写文明执法调查问卷</view
				            >
				        </view>
				    </view>
				</uni-popup>
            </PCard>
            <show-modal></show-modal>
        </view>
        <PButton @click.native="getAllSubmit" name="下一步" />
    </view>
</template>

<script>
import qrcodeShareIcon from '@/static/images/qrcode-share.png';
import appShareIcon from '@/static/images/app-share.png';
import printTemplate from '@/api/print-template.js';
import printImage from '@/static/img/record/print.png';
import PButton from '@/components/p-button';
import PCard from '@/pages/component/PCard.vue';
import NoData from '@/components/no-data.vue';
import iconOver from '@/static/img/record/icon_over.png';
import recordFrag from './record-fragment.js';
import formOperateMenuItem from './form-operate-menu-item.vue';
import tkiQrcode from '@/components/tki-qrcode/tki-qrcode.vue';
import { DOWNLOAD_URL,isH5 } from '@/common/config.js';
import qrLogo from '@/static/images/ic-login.png';
import {
    postPrintForm,
    postOverForm,
    postDeleteFormList,
    postBLFormList
} from '@/api/record.js';
import { getUserInfoPermissions } from '@/api/predit.js';

import { WMZF } from '@/common/config.js';

const mixins = [recordFrag];
//菜单操作码
const MENU_CODE_PRINT = 'print';
const MENU_CODE_SUBMIT = 'submit';
const MENU_CODE_SHARE = 'share';

//证据收集模板ID
const ARCHIVE_TEMPLATE_ID = '202011161722220c3f488fabc7497d90931b5f904b9109';

export default {
    name: 'RecordResultPreview',
    components: {
        formOperateMenuItem,
        NoData,
        PCard,
        PButton,
        tkiQrcode
    },

    // #ifdef MP-ALIPAY
    mixins: mixins.map(item => ({ ...item, props: {} })),
    // #endif
    // #ifndef MP-ALIPAY
    mixins: mixins,
    // #endif

    props: {
        // #ifdef MP-ALIPAY
        ...mixins.reduce(
            (prev, curr) => ({ ...prev, ...(curr.props || {}) }),
            {}
        ),
        // #endif

        //表单数据
        data: {
            type: Object
        }
    },

    data() {
        return {
            menus: [
                // {
                // 	code: MENU_CODE_PRINT,
                // 	name: '打印',
                // 	icon: iconPrint
                // },

                // {
                // 	code: MENU_CODE_SHARE,
                // 	name: '分享',
                // 	icon: iconShare
                // },
                {
                    code: MENU_CODE_SUBMIT,
                    name: '下一步',
                    icon: iconOver
                }
            ],
            isH5:isH5,
            icon: qrLogo,
            hint: false,
            ifShow: false,
            cid: 'tki-qrcode-canvas',
            qrCodeVal: '',
            lv: 3, // 二维码容错级别 ， 一般不用设置，默认就行
            onval: false, // val值变化时自动重新生成二维码
            loadMake: true, // 组件加载完成后自动生成二维码
            size: 500, // 二维码大小
            background: '#b4e9e2', // 背景色
            foreground: '#3873ff', // 前景色
            pdground: '#32dbc6', // 角标色
            qrcodeShareIcon,
            appShareIcon,
            recordName: '',
            printImage,
            printFormList: [], //预览的列表数据
            downLoadUrl: '', //下载的连接
            shareType: '', //分享的类型
            chooseRecordId: '',
            chooseItem: {}, //选择的笔录
            pageShow: true, //页面显示的状态
			isTipsShow:false,//是否显示文明执法
			civilizationqrCode:'',
			civilizationFlag:true
        };
    },

    computed: {
        pageListStyle() {
            return {
                height: 'calc(100vh - 440rpx)'
            };
        }
    },

    mounted() {
        this.getFormList();
		getUserInfoPermissions({
		    CDLX: 'WMZFCDLX'
		}).then(res => {
		    if(res.data_json.length > 0){
		      this.isTipsShow = true
			  this.civilizationFlag = false
		    }
		});
    },

    methods: {
        getFormList() {
            this.pageShow = true;
            let data = uni.getStorageSync('record-data');
            postBLFormList({
                service: 'QUERY_BL_LIST',
                bllx: 'ZFJC',
                xh: data.YWXTBH
            })
                .then(res => {
                    if (res.data_json.length > 0) {
                        //过滤证据笔录
                        res.data_json.forEach((element, index) => {
                            if (element.mbbh === ARCHIVE_TEMPLATE_ID) {
                                res.data_json.splice(index, 1);
                            }
                        });
                        this.printFormList = res.data_json;
                        this.printFormList.forEach(element => {
                            element.checked = true;
                        });
                        if (this.companprintFormListyList.length < 1) {
                            this.pageShow = false;
                        }
                    }
                })
                .catch(res => {
                    this.pageShow = false;
                });
        },

        //不同的功能方法//分别对应打印、提交、分享
        onMenuClick(menu) {
            switch (menu.code) {
                case MENU_CODE_PRINT:
                    this.getPrintAll();
                    break;
                case MENU_CODE_SUBMIT:
                    this.getAllSubmit();
                    break;
                case MENU_CODE_SHARE:
                    this.getAllShare();
                    break;
            }
        },

        getAllSubmit() {
			if(!this.civilizationFlag){
				uni.showToast({
					icon:'none',
					title:'请先点击执法评价，填写调查问卷'
				})
				return
			}
            this.doNext(this.stepIndex);
        },

        getAllShare() {},

        //统一将内容分享出去
        allShareList(idList) {
            console.log(idList);
        },

        //抛出选择了的数据
        getAllData() {
            let data = [];
            this.printFormList.forEach(element => {
                if (element.checked) {
                    data.push(element);
                }
            });
            return data;
        },

        recordPrint(item) {
            this.shareType = 'print';
            this.chooseItem = item;
            this.recordPreview(this.chooseItem);
        },

        //获取到返回的打印ID,并完成分享功能
        recordPreview(item) {
            let self = this;
            postPrintForm({
                service: 'PRINT_FORM',
                record_id: item.recordId,
                template_id: item.mbbh
            })
                .then(res => {
                    self.downLoadUrl = DOWNLOAD_URL + '?wdbh=' + res.WJID;
                    if (self.shareType === 'share') {
                        // #ifdef APP-PLUS
                        uni.shareWithSystem({
                            // imageUrl:testIcon,
                            summary: self.chooseItem.mbmc + '下载连接：',
                            href: self.downLoadUrl,
                            success() {
                                // 分享完成，请注意此时不一定是成功分享
                            },
                            fail() {
                                // 分享失败
                            }
                        });
                        // #endif
                    } else {
                        this.getUpdata(res.WJID);
                    }
                })
                .catch(() => {});
        },

        //提交功能
        onSaveFrom() {
            let self = this;

            let userName;
            uni.getStorage({
                key: 'user_id',
                success: function (res) {
                    userName = res.data;
                    postOverForm({
                        service: 'WRYJC_FINISH_SERVICE',
                        bzbh: self.data.BZBH,
                        bussinessId: self.data.YWXTBH,
                        userId: userName
                    })
                        .then(res => {
                            uni.showToast({
                                title: '已成功提交！',
                                duration: 2000,
                                icon: 'success'
                            });
                            setTimeout(() => {
                                uni.$emit('overFinish');
                                uni.navigateBack({
                                    url: '/pages/task/task-list'
                                });
                            }, 1000);
                        })
                        .catch(res => {
                            uni.showToast({
                                title: '提交有误',
                                duration: 2000,
                                icon: 'none'
                            });
                        });
                }
            });
        },

        //打开分享页面的弹窗
        getShareDialog(item) {
            if(this.isH5){
                uni.showToast({
                    title: '请到智慧监管网页端后台打印表单',
                    duration: 2000,
                    icon: 'none'
                });
            }else{
                this.chooseItem = item;
                this.shareData = [
                    {
                        record_id: item.recordId,
                        template_id: item.mbbh
                    }
                ];
                this.recordName = item.mbmc;
                this.qrcodeShareClick();
                // this.$refs.previewShareTypeShow.open();
            }
          
           
        },

        //APP的分享
        appShareClick() {
            printTemplate.getShareInfo(this.shareData, this.recordName);
            this.$refs.previewShareTypeShow.close();
        },

        //二维码的分享
        qrcodeShareClick() {
            this.$refs.previewShareTypeShow.close();
            printTemplate.getShareQrcode(this.shareData, true).then(res => {
                this.qrCodeVal = res;
                this.$refs.previewPopup.open();
                this.ifShow = true;
            });
        },

        //保存二维码到相册
        saveQrcode() {
            this.$refs.qrcode._saveCode();
        },

        getUpdata(id) {
            // let url = 'http://192.168.3.161:32180/plat/server/webapp/downloadFile?wdbh='
            // + id;
            // let name = (((1 + Math.random()) * 0x10000000000) | 0).toString(16).substring(1);//随机数
            // let fanalUrl = url + '&fullfilename=' + name + '.pdf';
            // fanalUrl = CryptoJS.enc.Utf8.parse(fanalUrl);
            // fanalUrl = CryptoJS.enc.Base64.stringify(fanalUrl);
            // let lastUrl = 'http://192.168.3.161:32008/onlinePreview?url=' + fanalUrl;
            // console.log(lastUrl);

            this.downLoadUrl = DOWNLOAD_URL + '?wdbh=' + id;
            uni.downloadFile({
                url: DOWNLOAD_URL + '?wdbh=' + id, //仅为示例，并非真实的资源
                header: {
					token: uni.getStorageSync('authToken'), // 这里是要添加的请求头
				},
                success: res => {
                    let filePath = res.tempFilePath;

                    uni.saveFile({
                        tempFilePath: filePath,
                        success: function (r) {
                            let Printer = plus.android.importClass(
                                'com.bovosz.webapp.print.Printer'
                            );
                            Printer.printAppPlusPDFFile(
                                plus.android.runtimeMainActivity(),
                                r.savedFilePath
                            );
                        }
                    });
                    // uni.openDocument({
                    // 	filePath: filePath,
                    // 	success: function(res) {
                    // 		console.log('打开文档成功');
                    // 	}
                    // });
                }
            });
        },

        //编辑表单
        recordEdit(item) {
            uni.navigateTo({
                url: `/pages/record/record-form-list?type=2&id=${item.mbbh}&wrybh=${this.taskInfo.WRYBH}&recordId=${item.recordId}`
            });
        },

        //删除表单
        getDeleteForm(item) {
            let self = this;
            this.$showModal({
                title: '提示',
                content: '您确定要删除吗？',
                success: function (r) {
                    if (r.confirm) {
                        postDeleteFormList({
                            service: 'DELETE_RECORED_SERVICE',
                            recordId: item.recordId,
                            templateId: item.mbbh
                        })
                            .then(res => {
                                if (res.status_code == '0') {
                                    uni.showToast({
                                        title: '已成功删除！',
                                        duration: 2000,
                                        icon: 'success'
                                    });
                                }
                                self.$delete(self.printFormList, item);
                                // self.printFormList = []
                                self.getFormList();
                            })
                            .catch(() => {
                                uni.showToast({
                                    title: '删除失败！',
                                    duration: 2000,
                                    icon: 'none'
                                });
                            });
                    }
                }
            });
        },

        //全选
        clickAllList() {
            this.printFormList.forEach(element => {
                console.log(element);
                this.$set(element, 'checked', true);
            });
            console.log(this.printFormList);
        },

        lookValue(item) {
            this.chooseRecordId = item.recordId;
        },

        changePreview(e) {
            var items = this.printFormList,
                values = e.detail.value;
            for (var i = 0, lenI = items.length; i < lenI; ++i) {
                const item = items[i];
                if (values.includes(item.recordId)) {
                    this.$set(item, 'checked', true);
                } else {
                    this.$set(item, 'checked', false);
                }
            }
            // console.log(this.printFormList);
        },

        //抛出选择了的数据
        getAllData() {
            let data = [];
            this.printFormList.forEach(element => {
                if (element.checked === true) {
                    data.push(element);
                }
            });
            return data;
        },

        //批量打印
        allPrintImage() {
            let datas = this.getAllData();
            if (datas.length > 0) {
                let data = [];
                datas.forEach(element => {
                    let list = {};
                    list.record_id = element.recordId;
                    list.template_id = element.mbbh;
                    data.push(list);
                });
                printTemplate.getCallPrinter(data, true);
            } else {
                uni.showToast({
                    title: '请先填写笔录',
                    icon: 'none'
                });
            }
        },
		//跳转到精神文明界面
		tipsInfoClick() {
			let _times = new Date().getTime()
			// let url = `${WMZF}?v=${_times}#/pages/record/record-template-form-tips`
			let url = `${WMZF}#/pages/record/record-template-form-tips`
			let ywxtbh = uni.getStorageSync('record-data').YWXTBH
			let token = uni.getStorageSync('token')
			let yhid = uni.getStorageSync('userInfo').yhid || ''
			let xtzh = uni.getStorageSync('userInfo').xtzh
			this.civilizationqrCode = `${url}?ywxtbh=${ywxtbh}&token=${token}&yhid=${yhid}&xtzh=${xtzh}`
			console.log('222',this.civilizationqrCode)
			this.$refs.civilizationPopup.open()
			
			//文明执法按钮是否点击过
			this.civilizationFlag = true
		}
    }
};
</script>

<style>
.from-checkbox {
    padding: 10upx 20upx;
}

.from-checkbox-item {
    font-size: 28rpx;
    width: 160rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.from-checkbox-list {
    padding: 18upx 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.from-checkbox-preview {
    color: #037ff7;
}

.from-checkbox-share {
    color: #333;
}

.from-checkbox-delete {
    color: #aaa;
}

.from-checkbox-label {
    display: flex;
    align-items: center;
    width: 100%;
    justify-content: space-between;
    padding: 16rpx 0;
}

.from-checkbox-group {
    width: 100%;
}

.form-menu-layout {
    z-index: 9999;
    position: fixed;
    bottom: 0px;
}

.printimage {
    width: 46rpx;
    height: 46rpx;
}

.sharedialogtype {
    display: flex;
    justify-content: center;
    width: 400rpx;
    height: 300rpx;
    background: #fff;
    border-radius: 8rpx;
    font-size: 32rpx;
    justify-content: center;
}

.sharedialogtype-image {
    width: 90rpx;
    height: 90rpx;
}

.sharedialogtype-main {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 100%;
}

.qrcodes {
    height: 60vh;
    background: #fff;
}

.qrcodes-view {
    width: 100%;
    padding-top: 50rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
}

.qrcodes-name {
    font-size: 32rpx;
    color: #333;
}

.qrcodes-info {
    font-size: 26rpx;
    color: #666;
    padding: 20rpx 0;
}

.qrcodes-btn {
    color: #fff;
    background: #3873ff;
    border-radius: 4rpx;
    padding: 8rpx 12rpx;
}
.template-form-tips {
    position: absolute;
    z-index: 99;
    right: 60rpx;
	top: 50%;
	height: 120rpx;
	width: 120rpx;
	text-align: center;
	border: 3px solid #ffa100;
	background-color: #009bff;
	color: #fff;
	border-radius: 50%;
	display: flex;
	justify-content: center;
	align-items: center;
}
</style>
