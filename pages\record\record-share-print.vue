<template>
	<Page title="现场执法检查"
	      :padding="false">
		<template v-slot:bar>
			<NaviBar title="现场执法检查1"
			         :naviBack="false">
			</NaviBar>
		</template>
		<view class="pageimg">
			<image mode="widthFix"
			       src="/static/img/test.png" />
			<view>下载链接:
				<text @click="clickRouter()" class="downurl">{{downurl}}</text>
			</view>
		</view>

	</Page>
</template>

<script>
	import NaviBar from '@/pages/component/NaviBar.vue'
	import Page from '@/pages/component/Page.vue'
	import {
		ULR_BASE,
		LOGIN_ULR_BASE
	} from '@/common/config.js'
	export default {
		components: {
			Page,
			NaviBar
		},
		data() {
			return {
                downurl:'http://*************:32180/plat/server/webapp/downloadFile?wdbh=1610965200900030314496'
			};
		},
		computed: {

		},
		watch: {

		},
		onload(options) {
            console.log(options.url);
        },
		methods: {
			clickRouter() {
				window.open(this.downurl, "_blank")
			}
		},
	};
</script>

<style scoped>
	.pageimg {
		width: 100%;
		height: 100%;
	}

	.pageimg image {
		width: 100%;
	}

    .downurl{
        color:cornflowerblue
    }
</style>
