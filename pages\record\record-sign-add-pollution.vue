<template>
	<Page :padding="false" @layoutAware="onFixedContentHeight">
		<template v-slot:bar>
			<NaviBar title="新增企业" />
		</template>

		<view class="template-form-layout" id="formParent">
			<template-form 
				ref="templateForm" 
				:height="formHeight"
				:editable="editable" 
				:template="template" 
				:form-data="formData" 
			/>
		</view>

		<show-modal></show-modal>
		<p-button @click.native="onMenuClick" name="保存"></p-button>
	</Page>
</template>

<script>
import iconHistory from '@/static/img/record/icon_form_history.png';
import iconSave from '@/static/img/record/icon_form_save.png';
import iconChange from '@/static/img/record/icon_form_change.png';
import iconNext from '@/static/img/record/icon_form_next.png';
import NoData from '@/components/no-data.vue';
import Page from '@/pages/component/Page.vue';
import NaviBar from '@/pages/component/NaviBar.vue';
// import templateForm from '@/pages/form/template-form.vue';
import formOperateMenuItem from '@/pages/record/form-operate-menu-item.vue';
import styleUtil from '@/common/style.js';
import formUtil from '@/pages/form/Form.js';
import onform from '@/api/onsaveForm.js';

import { guid } from '@/common/uuid.js';
import { deepCopyObject } from '@/common/merge.js';

import { postQueryWryjcTzForm, postQueryWryjcTzInfo } from '@/api/book.js';

import { dynamicformsave } from '@/api/record.js';
import loginService from '@/api/login-service.js'
import templateService from '@/node_modules/bowo-form/components/service/template-service.js';
import templateUtil from '@/node_modules/bowo-form/components/template.js';
import formService from '@/node_modules/bowo-form/components/service/form-service.js';
import templateForm from '@/node_modules/bowo-form';
import { queryWryJbxx } from '@/api/wry-service.js'

import PButton from '@/components/p-button'

export default {
	components: {
		Page,
		NaviBar,
		NoData,
		templateForm,
		formOperateMenuItem,
		PButton
	},

	data() {
		return {
			turnPage: 1,
			title: '公司名称',
			recordId: '',
			templateId: '',
			formData: {},
			template: {},
			editable: true,
			pageHeight: null,
			menus: [
				{
					name: '保存',
					icon: iconSave
				}
			]
		};
	},

	computed: {
		formLayoutHeight: function() {
			return this.pageHeight - 40;
		},
		formHeight: function () {
            let naviBarHeight = uni.upx2px(0);
            let bottomMenuHeight = uni.upx2px(100);
            let paddingBetwewnFormAndBottomMenu = 4;
            return (
                this.pageHeight -
                naviBarHeight -
                bottomMenuHeight -
                paddingBetwewnFormAndBottomMenu
            );
        },
	},

	onLoad(options) {
		if(options.hasOwnProperty('turnPage')){
			this.turnPage = options.turnPage;
		}
		
		this.$store.state.verifyList = []
		
		this.templateId = '202102240924456a2f18c745934bab83c1e818600a2aca';
		this.recordId = '';

		this.getData();
	},

	methods: {
		getData() {
			// postQueryWryjcTzForm({
			// 	service: 'QUERY_DYNAMICFORM_DATA',
			// 	recordId: this.recordId,
			// 	mbbh: this.templateId
			// }).then(res => {
			// 	// this.formData = res.data;
			// 	this.getTemplate();
			// });

			templateService.getTemplateById(this.templateId).then((templateJson) => {
				let groupedChildTemplate =
					templateUtil.packPeerElementIntoGroup(templateJson);
				this.template = groupedChildTemplate;

				formService
					.getRecordDetailById(this.templateId, this.recordId)
					.then((detail) => {
						this.formData = detail;
					
					});

				// this.resetAttachElementMenu();
        });
		},

		getFormRef() {
            return this.$refs.templateForm;
        },

		onFixedContentHeight(layout) {
			this.pageHeight = layout.height;
		},

		getTemplate() {
			let userInfo = uni.getStorageSync('userInfo');
			let userId = '';
			if (userInfo) {
				userId = userInfo.id;
			}
			postQueryWryjcTzForm({
				service: 'GET_DYNAMICFORM_MODEL',
				bbxh: this.templateId,
				userId: userId || 'SDSZFJ'
			}).then(res => {
				this.template = formUtil.packPeerElementIntoGroup(res.datas_json);
			});
		},

		onMenuClick() {
			// let self = this;
			// styleUtil.getNodeLayout(this, '#formParent').then(layout => {
			// 	let dataType = onform.saveTemplateForm(self.formData, self, layout.top);
			// 	if (dataType === true) {
			// 		this.postFormAxios();
			// 	}
			// });
			this.getFormRef().checkFormData().then(res=>{
				this.postFormAxios();
			});
		},

		postFormAxios() {
			let self = this;
			let form = {
				client_type: 'mobile_web',
				record_id: guid(),
				service: 'DYNAMICFORM_SAVE',
				template_id: self.templateId,
				user_id: loginService.getAuthUserId() || 'SDSZFJ'
			};
			let tableName = self.template['templateTable'];
			let data = this.getFormRef().getFormData();
			data = deepCopyObject(data);
			form[tableName] = data;
			dynamicformsave(form)
				.then(res => {
					uni.showToast({
						title: `新增企业成功`,
						duration: 1000,
						icon: 'none'
					});
					setTimeout(() => {
						self.onCreatedWry(res.data)
					},1000)	
				})
				.catch(error => {
					let errorMsg = error.error_msg
					if(errorMsg && errorMsg.indexOf('此企业系统中已存在，不能重复添加') !== -1) {
						// queryWryJbxx(data.TYSHXYDM, data.WRYMC)
						// 	.then(resp => {
						// 		self.onCreatedWry(resp.data_json)
						// 	})
						self.$showModal({
							title: '提示',
							content: '此企业系统中已存在，不能重复添加，请核实',
							success: function (res) {
								
							}
						});
						
					} else {
						uni.showToast({
							title: `新增企业出错：${errorMsg}`,
							duration: 6000,
							icon: 'none'
						});
					}
				});
		},
		
		onCreatedWry(createdWry) {
			let backStepNum = this.turnPage === 2 ? 2 : 1
			uni.navigateBack({
				delta: backStepNum
			})
			
			let wryPrimaryKey = 'record_id'
			if(createdWry && createdWry.hasOwnProperty(wryPrimaryKey)) {
				createdWry.WRYBH = createdWry[wryPrimaryKey]
			}
			
			setTimeout(() => {
				uni.$emit('onAddPollutionSelect', createdWry);
			}, 500);
		}
		
	}
};
</script>

<style scoped>
.template-form-layout {
	/* border-radius: 5rpx; */
	/* padding: 10rpx; */
	width: calc(100%);
	background-color: #fff;
	margin-top: 0rpx;
	height: calc(100%);
}

.template-form-tabsList {
	margin-top: 60upx;
}

.pd-ultbs1 {
	white-space: nowrap;
	overflow-x: scroll;
	overflow-y: hidden;
	-webkit-overflow-scrolling: touch;
}

.pd-ultbs1 li {
	font-size: 20px;
	margin: 10rpx 12rpx;
	display: inline-block;
	vertical-align: top;
}

/* .template-form-layout {
		border-radius: 5px;
		padding: 10px;
		width: calc(100% - 20px);
		background-color: #f4f4f4;
	} */

.form-menu-layout {
	margin-top: 70rpx;
	height: 70rpx;
	z-index: 10;
	background-color: white;
}

.form-operate-menu-item {
	flex: 1;
	padding: 22rpx 0;
}

.form-operate-menu-item:active {
	background-color: #ccc;
}

.form-menu-icon {
	width: 40rpx;
	height: 40rpx;
}

.form-menu-text {
	margin-top: 2px;
	font-size: 26rpx;
	color: white;
}


.form-content-layout {
	width: 100%;
	height: 100%;
}

.form-menu-layout {
	height: 50px;
	background-color: #fff;
	/* box-shadow: 0 -1px 1px 0 #ccc; */
}

	.form-list-tab {
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 20rpx 0;
	color:#fff
}
</style>
