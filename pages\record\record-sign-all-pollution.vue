<template>
	<view>
		<header class="pd-header">
			<i class="goback" @click="back()"></i>
			<div class="pd-srh1a">
				<input type="text" placeholder="请输入企业名称" v-model="search" />
			</div>
		</header>
		<section class="pd-main">

			<div class="pd-inner">

				<view class="layerBox" v-if="show">
					<view class="mask" @click="show = false"></view>
					<view class="dialog-container">
						<!-- 标题 -->
						<view class="title">提醒</view>

						<!-- 提示内容 -->
						<view class="content-box" @click="seeMoreBookList">
							今年企业已检查<i style="color: #E53E31;">{{times}}</i>次，是否继续检查
						</view>

						<!-- 输入框 -->
						<view class="input-box">
							<textarea v-model="reason" placeholder="继续原因" class="reason-input" />
						</view>

						<!-- 按钮组 -->
						<view class="btn-group">
							<button class="btn cancel" @click="show = false; reason = ''">取消</button>
							<button class="btn confirm" @click="handleConfirm">继续检查</button>
						</view>
					</view>
				</view>

				<scroll-view class="record-task-list" scroll-y="true" style="height: calc(100vh - 80rpx);"
					@scrolltolower="onReachBottomEvent" v-if="list.length>0">
					<dl class="pd-dlbx1" v-if="list.length>0">
						<dt><span>查询到 <i>（{{total}}）</i> 条记录</span></dt>
					</dl>
					<dl class="pd-dlbx1" v-for="(item,index) in list" :key="index">
						<div>
							<dd @click="showDetail(item)">
								<image src="../../static/app/images/lstic1a.png" class="imgic" />
								<h2 v-html="item.WRYMC">{{item.WRYMC}}</h2>
								<p><em>地址：</em><span v-html="item.WRYDZ"></span></p>
								<!-- <small></small> -->
							</dd>
							<dd>
								<ul class="pd-ulbx2">
									<!-- <li><i class="lsts1">导航</i></li> -->
									<!-- <li><i class="lsts2">执法检查</i></li> -->
								</ul>
							</dd>
						</div>
					</dl>
				</scroll-view>
				<noDataAdd v-if="list.length==0"></noDataAdd>
				<!-- <div v-if="list.length==0">新增企业</div> -->
			</div>
		</section>
	</view>
</template>

<script>
	import {
		postQueryWryjcTz,
		QUERY_HISTORY_CHECK_LIST,
		SAVE_CHECK_REASON
	} from '@/api/record.js'
	import noDataAdd from '@/components/no-data-add.vue'
	import loginService from '@/api/login-service.js'

	export default {
		components: {
			noDataAdd
		},
		data() {
			return {
				itemData:{},
				show: false,
				times: 0,
				reason: '',
				search: '',
				type: 'WRY',
				pageSize: 10,
				pageNum: 1,
				list: [],
				total: 0
			}
		},
		watch: {
			search: 'handleKeyUp'
		},

		mounted() {
			this.searchData();
		},
		methods: {
			seeMoreBookList(){
				// 跳转到这个页面 /#/pages/book/book-list?timeType=4&searchValue=威海
				uni.navigateTo({
					url: `/pages/book/book-list?timeType=4&searchValue=${this.itemData.WRYMC}`
				})
			},
			handleConfirm() {
				if(!this.reason){
					uni.showToast({
						title: '请填写继续检查原因',
						icon: 'none'
					});
					return;
				}
				// TODO 填写继续检查原因
				SAVE_CHECK_REASON({
					"WRYBH":this.itemData.WRYBH,//污染源编号
					"JCYY":this.reason,//继续检查原因
					"YDZFXH":"",//移动执法XH
				}).then(res=>{
					if(res.data_json.success){
						this.show = false;
						let item = this.itemData;
						uni.navigateBack({
							delta: 1
						});

						if (!item.hasOwnProperty('JD')) {
							item.JD = '';
							item.WD = '';
						}
						uni.setStorageSync('choose_company', item)
						uni.$emit('onAllPollutionSelect', item);
					}else{
						uni.showToast({
							title: res.data_json.msg,
							icon: 'none'
						});
					}
					
				})
			},

			showDetail(item) {
				// 判断是否有历史检查记录，如果有就弹出弹框，如果没有，就执行之前方法
				QUERY_HISTORY_CHECK_LIST({
					pageSize: 100,
					pageNum: 1,
					WRYBH: item.WRYBH
				}).then(res => {
					if (res.data_json.list.length > 0) {
						this.itemData = item;
						this.times = res.data_json.list.length;
						this.show = true;
					} else {
						uni.navigateBack({
							delta: 1
						});

						if (!item.hasOwnProperty('JD')) {
							item.JD = '';
							item.WD = '';
						}
						uni.setStorageSync('choose_company', item)
						uni.$emit('onAllPollutionSelect', item);
					}
				})


				
			},
			back() {
				uni.navigateBack();
			},
			handleKeyUp() {
				this.pageNum = 1;
				this.list = [];

				this.searchData();
			},
			onReachBottomEvent() {
				this.pageNum++;
				this.searchData();
			},
			changeType(type) {
				this.pageNum = 1;
				this.type = type;
				this.list = [];
				this.searchData();
			},
			searchData() {
				let userId = loginService.getAuthUserId()
				postQueryWryjcTz({
					searchText: this.search,
					service: 'QUERY_WRYXX_SERVICE',
					pageSize: 20,
					pageNum: this.pageNum
				}).then(res => {
					// console.log(this.list)
					// debugger
					this.list.push(...res.data_json.list);
					this.total = res.data_json.total;
				})
			}
		}
	}
</script>

<style>
	.pd-main {
		width: 100vw;
	}

	.pd-dlbx1+.pd-dlbx1 {
		margin-top: 0px;
	}



	.layerBox {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 999;
	}

	.mask {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: rgba(0, 0, 0, 0.5);
		z-index: 998;
		display: block;
	}

	.dialog-container {
		width: 80%;
		background: #fff;
		border-radius: 12rpx;
		padding: 30rpx;
		z-index: 999;
		/* 确保对话框在遮罩层之上 */
	}

	.title {
		font-size: 32rpx;
		font-weight: bold;
		text-align: center;
		color: #333;
		margin-bottom: 20rpx;
	}

	.content-box {
		font-size: 28rpx;
		color: #666;
		text-align: center;
		margin-bottom: 30rpx;
	}

	.input-box {
		margin-bottom: 30rpx;
	}

	.reason-input {
		width: 100%;
		height: 160rpx;
		border: 1rpx solid #ddd;
		border-radius: 8rpx;
		padding: 20rpx;
		font-size: 28rpx;
		box-sizing: border-box;
	}

	.btn-group {
		display: flex;
		justify-content: space-between;
	}

	.btn {
		width: 45%;
		height: 80rpx;
		line-height: 80rpx;
		text-align: center;
		border-radius: 40rpx;
		font-size: 28rpx;
	}

	.cancel {
		background: #f5f5f5;
		color: #666;
	}

	.confirm {
		background: #0FAEFF;
		color: #fff;
	}
</style>