<template>
	<view class="bg-white">
		
		<view
			v-show="statusSteps.length <= 6"
			id="indicator"
			class="flex-row-layout record-step-indicator">
			<step-axis-node 
				v-for="(step, index) in statusSteps"
				:key="index"
				:step="step"
				:active="step.active"
				:finish="step.finish"
				@click.native="onNodeClick(index)"
			/>
		</view>
		<show-modal></show-modal>
		<scroll-view v-show="statusSteps.length > 6" scroll-into-view="true" style="width: 100vw;" scroll-x="true" :scroll-left="scrollLeft">
			<view
				style="width: 150%;"
				id="indicator"
				class="flex-row-layout record-step-indicator">
				<step-axis-node 
					v-for="(step, index) in statusSteps"
					:key="index"
					:step="step"
					:active="step.active"
					:finish="step.finish"
					@click.native="onNodeClick(index)"
				/>
			</view>
		</scroll-view>
	</view>
</template>

<script>
	
	import stepAxisNode from './step-axis-node.vue'
	
	export default {
		name: 'RecordStepIndicator',
		components: {
			stepAxisNode
		},
		props: {
			steps: {
				type: Array,
				default: () => {
					return []
				}
			},

			activeIndexs:{
				type:Number,
				default:() => {
					return 0
				}
			}
		},
		
		data() {
			return {
				scrollLeft: 0,
				//包含激活、完成状态的步骤信息
				statusSteps: [],
				//当前激活步骤
				activeIndex: 0
			}
		},

		watch:{
			activeIndexs(){
				this.statusSteps.forEach((element,i) => {
					if(this.activeIndexs >= i){
						element.finish = true
					}if(this.activeIndexs == i){
						element.active = true
					}else{
						element.active = false
					}
				});
				
			}
		},
		
		mounted() {
			this.activeIndex = this.activeIndexs
			let backupSteps = this.steps.map(step => {
				return {...step, active: false, finish: false}
			})
			//默认设置第一个节点为激活节点
			if(backupSteps.length > 0) {
				backupSteps[this.activeIndex].active = true
			}
			this.statusSteps = backupSteps
			
			// 超过5 scroll-view滚动到最后
			setTimeout(()=>{
				if(this.activeIndexs >= 4){
					this.scrollLeft = 10000;
				}
			}, 200)
			
			
			// console.log(bd09togcj02);
			
		},
		
		methods: {
			onNodeClick(index) {
				let step = this.statusSteps[index]
				if(index > 0 ) {
					let preIndex = index;
					let preStep = this.statusSteps[preIndex]
					if(preStep.finish) {
						//只有前一步骤已完成才允许激活下一步骤
						this.activeStep(index)
					} else {
						this.showFinishTip()
					}
				} else {
					this.activeStep(index)
				}
			},
			
			activeStep(index) {
				let preActiveStep = this.statusSteps[this.activeIndex]
				preActiveStep.active = false
				if(index > this.activeIndex) {
					preActiveStep.finish = true
				}
				
				let step = this.statusSteps[index]
				this.activeIndex = index
				step.active = true
				this.$emit('activeStep', index)
			},
			
			showFinishTip() {
				uni.showToast({
					icon: 'none',
					title: '请完成前置步骤',
					duration: 2000
				});
			}
		}
	}
</script>

<style scoped>
	/* .flex-row-layout{
		width: 750rpx!important;
		overflow: scroll;
		display: block;
	} */
	.record-step-indicator {
		height: 50px;
		background-color: #fff;
	}
	
	.bg-white{
		background-color: white;
	}
</style>
