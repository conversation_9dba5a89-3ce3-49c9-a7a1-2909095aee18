<template>
	<PCard 
		:title="template.TEMPLATENAME">
		<view 
			class="flex-row-layout"
			style="flex-wrap: wrap; position: relative;">	
			<view
				style="position: relative;"
				class="flex-column-layout template-cover-button"
				v-for="recordData in template.formData"
				:key="recordData.id"
				@click="previewRecord(recordData)">
				<text		
					class="template-name-thumbnail">
					{{ templateName }}
				</text>
				<image 
					style="position: absolute; left: 0; top: 0; margin: 0;"
					class="template-cover-button"
					mode="aspectFit"
					:src="iconSubmitted"
				/>
			</view>
			
			<view 
				style="position: relative;"
				class="flex-column-layout template-cover-button">
				<text		
					class="template-name-thumbnail">
					{{ templateName }}
				</text>
				<image 
					style="position: absolute; left: 0; top: 0; margin: 0;"
					class="template-cover-button"
					mode="aspectFit"
					:src="iconAdd"
					@click="addForm"
				/>
			</view>
		</view>
	</PCard>
</template>

<script>
	import PCard from '@/pages/component/PCard.vue'
	import printTemplate from '@/api/print-template.js';
	
	import iconAdd from '@/static/img/record/template_add.png'
	import iconSubmitted from '@/static/img/record/form_submitted.png'
	
	import { transformObjectToUrlParams } from '@/common/net/uri.js'
	
	import { MODE_MODIFY, EVENT_PREVIEW_RECORD } from '@/api/record.js'
	import uriUtil from '@/common/net/uri.js'
	
	const TEMPLATE_PHONEID = '20201116170458751323485bf54c61b60cdb07311fe5e7'
	
	export default {
		name: 'RecordTemplateList',
		components: {
			PCard
		},
		
		props: {
			template: {
				type: Object
			},

			wrybh: {
				type: String
			}
		},

		data() {
			return {
				iconAdd,
				iconSubmitted,
				templateId: null,
			};
		},
		
		computed: {
			templateName: function() {
				return this.template.TEMPLATENAME
			}
		},
		
		mounted() {
			let templateId = this.template.TEMPLATEID
			this.templateId = templateId
		},

		methods: {
			//新增笔录
			addForm() {
				uni.setStorageSync('router-KCSHT', this.templateId)
				if(this.template.TEMPLATEID === TEMPLATE_PHONEID){
					uni.setStorageSync('TEMPLATE_PHONEID',TEMPLATE_PHONEID)
					this.$emit('addNewRecord', this.template)
					
				}else{
					// uni.setStorageSync('TEMPLATE_PHONEID','123')
					this.$emit('addNewRecord', this.template)
					}
				
			},

			/**
			 * 预览笔录
			 * @param {Object} item
			 */
			previewRecord(item) {
				let _self = this
				uni.setStorageSync('router-KCSHT', item.mbbh)
                uni.showLoading({
					title: '加载表单中'
				})
                let type = true
				let data = []
				let list = {}
				list.record_id = item.recordId
				list.template_id = item.mbbh
				data.push(list)
				printTemplate.getPrintUrl(data, type)
					.then(result => {
						uni.setStorageSync('printURL', result)
						let params = {
							type: MODE_MODIFY,
							id: item.mbbh,
							wrybh: this.wrybh,
							recordId: item.recordId,
							title: this.template.TEMPLATENAME
						}
						let urlParams = transformObjectToUrlParams(params)
						uni.hideLoading()
						_self.$emit(EVENT_PREVIEW_RECORD)
						uni.navigateTo({
							url: `/pages/record/record-form-previewer?${urlParams}`
						})
					})
					.catch(e => {
						if(e.error_msg === '没有找到模板') {
							let params = {
								type: MODE_MODIFY,
								id: item.mbbh,
								wrybh: _self.wrybh,
								recordId: item.recordId,
								backStep: 1
							}
							uni.navigateTo({
								url: `/pages/record/record-form-editor?${uriUtil.transformObjectToUrlParams(params)}`
							})
						}
					})
			}
		},
	};
</script>

<style scoped>
	.template-cover-button {
		width: 160rpx;
		height: 212rpx;
		margin: 16rpx 20rpx;
		border-radius: 4rpx;
		box-shadow: 2px 2px 4px #ccc;
		justify-content: flex-start;
	}
	
	/* .template-cover-button:not(:first-child) {
		margin-left: 32rpx;
	} */
	
	.template-add-thumbnail {
		width: 160rpx;
		height: 212rpx;
	}
	
	.template-name-thumbnail {
		z-index: 9;
		width: 180rpx;
		color: #999;
		font-size: 8px;
		text-align: center;
		transform: scale(0.6);
	}
</style>
