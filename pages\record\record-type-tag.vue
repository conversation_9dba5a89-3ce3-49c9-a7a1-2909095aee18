<template>
	<view :class="tagStyle" @click="onTypeClick">
		{{ type.text }}
	</view>
</template>

<script>
	export default {
		name: 'RecordTypeTag',
		props: {
			type: {
				type: Object,
				default: () => {
					return {
						code: 'type',
						text: '类型'
					}
				}
			},
			active: {
				type: Boolean,
				default: false
			}
		},
		
		computed: {
			tagStyle: function() {
				let tagClass = this.active === false ? 'record-type-tag' : 'record-type-tag record-type-tag__active'
				return tagClass
			}
		},
		
		methods: {
			onTypeClick() {
				this.$emit('click')
			}
		}
	}
</script>

<style>
	.record-type-tag {
		height: 72rpx;
		line-height: 72rpx;
		text-align: center;
		border-radius: 5px;
		background-color: white;
		border: 2rpx solid white;
		color: #898989;
		font-size: 16px;
	}
	
	.record-type-tag:hover {
		color: #228EE9;
		border: 2rpx solid #4AA8F0;
		background-color: #E9F9FF;
	}
	
	.record-type-tag__active {
		color: #228EE9;
		border: 2rpx solid #4AA8F0;
		background-color: #E9F9FF;
	}
	
</style>
