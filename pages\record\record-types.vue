<template>
	<view class="flex-row-layout record-types">
		<record-type-tag
			class="type-tag-item"
			v-for="(type, index) in effectiveTypes"
			:key="index"
			:type="type"
			:active="actives[index]"
			@click="onTypeTagClick(type, index)"
		/>
	</view>
</template>

<script>
	
	import RecordTypeTag from './record-type-tag.vue'
	
	export default {
		name: 'RecordTypes',
		components: {
			RecordTypeTag
		},
		
		props: {
			currentType: {
				type: String,
				default: 'ZFJC'
			},
			
			effectiveTypeCodes: {
				type: Array,
				default: () => {
					return ['ZFJC', 'ZFJC_CATEGORY', 'ZFJC_ILLEGAL']
				}
			}
		},
		
		data() {
			return {
				types: [
					{code: 'ZFJ<PERSON>', text: '常用'},
					{code: 'ZFJC_CATEGORY', text: '行业'},
					{code: 'ZFJC_ILLEGAL', text: '违法'}
				],
				
				actives: [true, false, false]
			}
		},
		
		computed: {
			effectiveTypes: function() {
				return this.types.filter(item => {
					return this.effectiveTypeCodes.indexOf(item.code) !== -1
				})
			}
		},
		
		methods: {
			onTypeTagClick(type, index) {
				this.actives = [false, false, false]
				this.actives[index] = true
				this.$emit('select', type.code);
			}
		}
	}
</script>

<style>
	.record-types {
		background-color: #f4f4f4;
	}
	
	.type-tag-item {
		flex: 1;
	}
	
	.type-tag-item:not(:first-child) {
		margin-left: 20rpx;
	}
	
</style>
