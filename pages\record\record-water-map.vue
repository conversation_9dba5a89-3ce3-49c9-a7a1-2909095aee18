<template>
  <view id="fragment">
    <view
      @click="amap.onClick"
      :anginType="anginType"
      :prop="option"
      :change:prop="amap.updateEcharts"
      :change:anginType="amap.receiveMsg"
      id="amap"
      class="locationmaps"
    ></view>
  </view>
</template>

<script>
const pointUrl = require("@/static/img/point.png");
import http from "@/common/net/http.js";
import { guid } from "@/common/uuid.js";
import miLoading from "../../components/mi-loading/mi-loading.vue";
import styleUtil from "@/common/style.js";
import recordFrag from "./record-fragment.js";
import { deepCopyObject } from "@/common/merge.js";
import {
  postQueryXcjcQd,
  getCompanyList,
  linkZhzfToXfyqTask,
  getWaterTaskList,
  getLandTaskList,
  getAreaTaskList
} from "@/api/record.js";

import PButton from "@/components/p-button";

const mixins = [recordFrag];

//执法类型
const INSPECT_TYPE_COMPLAINT = "complaint";
const INSPECT_TYPE_OTHER = "other";
const INSPECT_TYPE_WATER = "SYDJC";
const INSPECT_TYPE_LAND = "DKJC";
const INSPECT_TYPE_AREA = "ZRBHQ";
export default {
  components: {
    miLoading,
    PButton
  },
  name: "RecordSignLocation",

  data() {
    let self = this;
    return {
      newCompanyData: {}, // 新添加公司数据

      showNearCompany: false, // 附近企业显示
      option: {
        pointUrl: pointUrl,
        JD: "117.1713" || "", // 公司经度
        WD: "36.6696" || "", // 公司纬度
        lat: "", // 企业经度
        lng: "", //企业纬度
        taskType: uni.getStorageSync("task_type")
      },
      companyList: [],
      address: "山东省生态环境保护厅",
      anginType: false,
      latitude: "117.1713",
      longitude: "36.6696",
      adressType: false, //是否可以手动输入地址
      newTaskId: null
    };
  },
  onLoad(options) {
			this.option.lat = options.JD;
			this.option.lng = options.WD;
	},

  computed: {
    isAddNew: function () {
      return !this.isTargetAssigned;
    },

    isNoAddress() {
      return this.option.JD !== "" ? false : true;
    },

    isWaterType() {
      return uni.getStorageSync("task_type");
    },

    markers: function () {
      let points = [];
      if (this.latitude && this.longitude) {
        points.push({
          id: 0,
          latitude: this.latitude,
          longitude: this.longitude,
          title: "当前位置",
          width: 32,
          height: 64,
          iconPath: "../../static/img/record/icon_location_marker.png"
        });
      }
      return points;
    }
  },
  methods: {
   
  }
};
</script>

<script module="amap" lang="renderjs">
window._AMapSecurityConfig = {
	securityJsCode:'107bd01de399ea7dfe44cfcf0b891b43',
}
const selectedStart = require('static/img/record/companyIcon.png'); //选中的图片

const selectIco =
	'data:image/png;base64,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';

import {
	bd09togcj02
} from '@/common/coordTransform.js';

import
	waterJson
 from '@/static/water.geojson';
 import
	waterJson1
 from '@/static/water1.geojson';
 import
	waterJson2
 from '@/static/water2.geojson';
 import
	waterJson3
 from '@/static/water3.geojson';
export default {
	data() {
		return {
      JD:'',
      WD:'',
			companyMarker: null,
			circle: null,
			marker: null,
			map: null,
			ownerInstanceObj: null, //service层对象
		}
	},

	mounted() {
		// this.getMapData()
	},

	methods: {
    onClick(e) {
      // console.log(e.lnglat.getLng());
    },
		getMapData(){
			if (typeof window.AMap === 'function') {
			this.initAmap()
		  } else {

			// 动态引入较大类库避免影响页面展示
			const script = document.createElement('script')
			script.src = 'https://webapi.amap.com/maps?v=1.4.15&key=fb9b0e9bb1b2cfaeab2e17dc0f0003cc&plugin=AMap.Geocoder'
			script.onload = this.initAmap.bind(this)
			document.head.appendChild(script)


		}
		},

		geoAddress(lnglat) {
			var geocoder = new AMap.Geocoder({
				radius: 1000 //范围，默认：500
			});

			// console.log(lnglat);
			geocoder.getAddress(lnglat, function(status, result) {
				if (status === 'complete' && result.regeocode) {
					var address = result.regeocode.formattedAddress;
					try {
						document.getElementById('lng').setAttribute('data-address', address);
						document.getElementById('address').innerHTML = address;
					} catch (e) {
						console.log(e)
					}

				} else {
					console.log('根据经纬度查询地址失败')
				}

				try {
					document.getElementById('loadingBtn').style.display = 'none';
				} catch (e) {

				}

			});
		},

    getWaterJson(){
         // 1. 使用setTimeout分批次加载
        setTimeout(() => {
            this.loadSingleGeoJson(waterJson, 'red') // 地表水型饮用水水源保护区
        }, 0)
        
        setTimeout(() => {
            this.loadSingleGeoJson(waterJson1, 'yellow') // 山东省乡镇级水源地
        }, 300)
        
        setTimeout(() => {
            this.loadSingleGeoJson(waterJson2, 'darkblue') // 饮用水水源一级保护区
        }, 600)

         setTimeout(() => {
            this.loadSingleGeoJson(waterJson3, 'lightblue') // 饮用水水源二级保护区
        }, 600)
        let cAddress = bd09togcj02(this.JD, this.WD);
          this.companyMarker = new AMap.Marker({
            offset: new AMap.Pixel(-10, -10),
            icon: 'https://webapi.amap.com/theme/v1.3/markers/n/mark_r.png' // 添加 Icon 图标 URL
          });
          this.companyMarker.setLabel({
            content: "<div class='info'>签到地点</div>", //设置文本标注内容
            direction: 'bottom' //设置文本标注方位
        });
          this.map.add(this.companyMarker);
    },
    
    loadSingleGeoJson(data, fillColor) {
        // 2. 简化GeoJSON数据（如果数据量很大）
        const simplifiedData = this.simplifyGeoJson(data)
        
        const geoJson = new AMap.GeoJSON({
            geoJSON: simplifiedData,
            getPolygon: function(geojson, lnglats) {
                return new AMap.Polygon({
                    path: lnglats,
                    fillOpacity: .6,
                    strokeColor: 'white',
                    fillColor: fillColor,
                    strokeWeight: 1  // 3. 减少描边宽度
                })
            }
        })
        
        // 4. 设置合适的zIndex
        // this.map.setZoom(10)
        this.map.add(geoJson)
    },

    simplifyGeoJson(data) {
        // 这里可以添加数据简化逻辑
        // 例如使用Turf.js的simplify方法
        return data
    },
		updateEcharts(option, oldOpt) {
      console.log(option);
      this.JD = option.lat;
      this.WD = option.lng;
      this.getMapData();
			 
		},

		receiveMsg(newValue, oldValue){
			if(oldValue){
				this.getMapData()
			}
		},

		updateLocation(option) {
			if (this.circle) {
				this.map.remove(this.circle);
			}

			if (this.companyMarker) {
				this.map.remove(this.companyMarker);
			}
			this.circle = new AMap.Circle({
				center: new AMap.LngLat(cAddress[0], cAddress[1]), // 圆心位置
				radius: Number(5000), //半径
				strokeColor: '#3385FF', //线颜色
				strokeOpacity: 1, //线透明度
				strokeWeight: 1, //线粗细度
				fillColor: '#EBF3FF', //填充颜色
				fillOpacity: 0.35 //填充透明度
			});
			this.map.add(this.circle);

			// 计算是否在五公里之内，定位和企业的距离
			let center = new AMap.LngLat(cAddress[0], cAddress[1]);
			let dis = 5000;
			let pt = new AMap.LngLat(document.getElementById('lng').getAttribute('data-lng'), document.getElementById(
				'lng').getAttribute('data-lat'));
			let arr = new Array(); //经纬度坐标数组
			arr.push(center);
			arr.push(pt);
			let distance = Math.round(
				AMap.GeometryUtil.distanceOfLine(arr)
			);
			// 超过范围的时候，就提示距离偏移太多了
			if (document) {
				let noticeBox = document.getElementById('notice-box')
				if (noticeBox) {
					if (distance > dis) {
						noticeBox.style.display = 'block'
					} else {
						noticeBox.style.display = 'none'
					}
				}
			}
		},

		initAmap() {
			try {
				let loadingBtn = document.getElementById('loadingBtn')
				if (loadingBtn && loadingBtn !== null) {
					loadingBtn.style.display = 'block'
				}
			} catch (e) {
				console.log(e);
			}

			// uni.showLoading({
			// 	title: '定位中'
			// })

			let self = this;
			let map = new AMap.Map('amap', {
				resizeEnable: true,
				center: [this.JD, this.WD],
				layers: [ //使用多个图层
					// new AMap.TileLayer.Satellite() //使用卫星图
				],
				zooms: [4, 18], //设置地图级别范围
				zoom: 14
			})
      this.map = map;
      this.map.on('click', function(e) {
            console.log(e.lnglat.getLng(),e.lnglat.getLat())
            // document.getElementById("c-longitude").innerText = e.lnglat.getLng()
            // document.getElementById("c-latitude").innerText = e.lnglat.getLat()
        });
      // 如果是水源地类型任务，地图新增水源地矢量数据
      // if(this.option.taskType == 'water'){
      //     this.getWaterJson();
      // }
      this.getWaterJson();

			AMap.plugin('AMap.Geolocation', function() {
				var geolocation = new AMap.Geolocation({
					enableHighAccuracy: false, //是否使用高精度定位，默认:true
					timeout: 15 * 1000, //超过15秒后停止定位，默认：5s
					buttonPosition: 'RB', //定位按钮的停靠位置
					buttonOffset: new AMap.Pixel(10, 20), //定位按钮与设置的停靠位置的偏移量，默认：Pixel(10, 20)
					zoomToAccuracy: true, //定位成功后是否自动调整地图视野到定位点
				});
				// map.addControl(geolocation);
				geolocation.getCurrentPosition(function(status, result) {
					if (status == 'complete') {
						onComplete(result)
					} else {
						onError(result)
					}
				});
			});
			//解析定位结果
			function onComplete(data) {
				map.setCenter([data.position.lng, data.position.lat]);
				self.addMarker(data.position.lng, data.position.lat)
				self.geoAddress([data.position.lng, data.position.lat])
				try {
					document.getElementById('lng').setAttribute('data-lng', data.position.lng)
					document.getElementById('lng').setAttribute('data-lat', data.position.lat)
					document.getElementById('lng').innerHTML = data.position.lng;
					document.getElementById('lat').innerHTML = data.position.lat;

					try {

						// 不是新增的时候
						if (self.option.JD != '') {
							self.updateLocation(self.option, self.option)
						}
					} catch (e) {
						console.log(e);
					}

				} catch (e) {
					console.log(e)
				}
			}
			//解析定位错误信息
			function onError(data) {
				console.log(`定位出错：${JSON.stringify(data, null, 4)}`)
				document.getElementById('loadingBtn').style.display = 'none';
			}
            // console.log(this.option);
            // if(this.option.taskType == 'water'){
            //   this.getWaterJson();
            // }

		}
	}
}
</script>

<style scoped>
.add-com {
  width: 26rpx;
  height: 26rpx;
  margin-right: 8rpx;
  /* margin-top: 10rpx; */
  position: relative;
  top: 5rpx;
}

.locationmaps {
  height: 100vh;
  width: 100vw;
}

.ul-box .li-box {
  line-height: 70rpx;
  border-bottom: 1px solid #eeeeee;
  font-size: 28rpx;
  font-family: PingFang SC;
  font-weight: 500;
  color: #333333;
  line-height: 77rpx;
}

.scroll-view-box {
  height: 480rpx;
}

.position-data {
  position: absolute;
  right: 18rpx;
  top: 168rpx;
  color: #0077ff;
  font-size: 28rpx;
  font-family: PingFang SC;
  font-weight: 500;
}

.none-data {
  position: absolute;
  right: 18rpx;
  top: bottom;
  top: 30rpx;
  font-size: 29rpx;
  font-family: PingFang SC;
  font-weight: 500;
  color: #e39d16;
}

.sign-info-layout {
  position: relative;
  padding: 10px;
  width: calc(100% - 20px);
  height: auto;
  background-color: #fff;
  align-items: flex-start;
}

.sign-prop {
  font-size: 16px;
}

.sign-prop:first-child {
  margin-top: 0;
}

.sign-new-border {
  box-shadow: 0px 4rpx 18rpx 1rpx rgba(185, 185, 185, 0.32);
  border-radius: 36rpx 36rpx 0 0;
  top: -36rpx;
  /* padding: 28rpx; */
}

.sign-prop-label {
  color: #333;
}

.sign-prop-value {
  padding: 18rpx;
  height: auto;
  line-height: auto;
  border-radius: 3px;
  background-color: #f4f4f4;
  color: #999;
  font-size: 28rpx;
  width: 676rpx;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  white-space: nowrap;
  /*设置不换行*/
}

.choose-position {
  display: flex;
  justify-content: center;
}

#amap >>> .amap-copyright {
  opacity: 0 !important;
  display: none !important;
}

#amap >>> .amap-logo {
  opacity: 0 !important;
  display: none !important;
}

.notic-box {
  position: relative;
  margin-bottom: 20rpx;
}

.color-black {
  font-size: 30rpx;
  font-family: PingFang SC;
  font-weight: bold;
  color: #333333;
  line-height: 58rpx;
}
</style>
