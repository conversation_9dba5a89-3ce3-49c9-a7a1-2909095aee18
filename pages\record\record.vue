<template>
	<Page class="power-page" :title="title" @layoutAware="onFixedHeight">
		<scroll-view scroll-y="true" :style="listStyle" class="scroll-view_Line" :scroll-anchoring="true">
			<template v-for="(template, index) in templateList">
				<view class="list-divider" />
				<template-item 
					:key="index"
					:order="index"
					:template="template"
				/>
			</template>
		</scroll-view>
	</Page>
</template>

<script>
	import Page from '@/pages/component/Page.vue';
	import templateItem from './template-item.vue';
	
	import formService from '@/api/form-service.js';
	import standingBookService from '@/api/standing-book-service.js';
	
	import styleUtil from '@/common/style.js';
		
	export default {
		components: {
			Page, templateItem
		},
		
		data() {
			return {
				title: '动态表单',
				templateList: [],
				pageHeight: 600,
			}
		},
		
		computed: {
			listStyle: function() {
				return styleUtil.styleObjectToString({
					height: `${this.pageHeight - 16}px`
				});
			}
		},
		
		mounted() {
			formService.getTemplateList()
				.then(templateList => {
					this.templateList = templateList;
				})
		},
		
		methods: {
			onFixedHeight(layout) {
				this.pageHeight = layout.height;
			},
			
			loadStandingBookList(){
				standingBookService
			}
		}
	}
</script>

<style scoped>
.scroll-view_Line {
	white-space: nowrap;
}
</style>
