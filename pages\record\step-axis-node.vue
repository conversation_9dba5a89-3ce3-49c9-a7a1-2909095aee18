<template>
	<view class="flex-column-layout step-axis-node">
		<view class="flex-row-layout" style="width: 100%; justify-content: center;">
			<view class="axis-join-line join-line-left" :style="axisLineLeftStyle"/>
			<view :class="spotClass" :style="spotStyle">{{spotText}}</view>
			<view class="axis-join-line join-line-right" :style="axisLineRightStyle"/>
		</view>
		<text class="axis-node-name" :style="stepNameStyle">{{step.name}}</text>
	</view>
</template>

<script>
	export default {
		name: 'StepAxisNode',
		props: {
			step: {
				type: Object,
				default: () => {
					return {
						id: '步骤编号',
						name: '步骤名称',
						order: 1
					}
				}
			},
			
			active: Boolean,
			finish: Boolean,
			color: {
				type: String,
				default: '#999'
			},
			
			activeColor: {
				type: String,
				default: '#FFA100'
			},
			
			finishColor: {
				type: String,
				default: '#0077FF'
			}
		},
		
		data() {
			return {
				
			}
		},
		
		computed: {
			spotText: function() {
				//已完成且未处于激活状态节点不显示文本
				return this.finish && !this.active ? '' : this.step.order;
			},
			
			currentColor: function() {
				let spot = this.color
				let background = 'transparent'
				let spotText = this.active || this.finish ? '#fff' : this.color
				if(this.finish) {
					spot = this.finishColor
					background = this.finishColor
				}
					
				if(this.active) {
					spot = this.activeColor
					background = this.activeColor
				}
				return {
					spot,
					background,
					spotText
				}
			},
			
			spotClass: function() {
				//已完成且未处于激活状态节点需要加上完成背景
				return this.finish && !this.active ? 'step-node step-node-finish' : 'step-node'
			},
			
			spotStyle: function() {
				let currentColor = this.currentColor
				let style = {
					'border-color': currentColor.spot,
					color: currentColor.spotText,
				}
				//激活或者未完成节点必需要设置背景色
				if(this.active || !this.finish) {
					style['background-color'] = currentColor.background
				}
				return style
			},
			
			stepNameStyle: function() {
				return {
					color: this.currentColor.spot
				}
			},
			
			axisLineLeftStyle: function() {
				return {
					'background-color': this.finish || this.active ? this.finishColor : '#999'
				}
			},
			
			axisLineRightStyle: function() {
				return {
					'background-color': this.finish ? this.finishColor : '#999'
				}
			}
		},
		
		methods: {
			
		}
	}
</script>

<style scoped>

	/* .step-axis-node {
		width: 200rpx;
	} */
	
	.axis-join-line {
		flex: 1;
		height: 1px;
		background-color: #999;
	}
	
	.join-line-left {
		margin-right: 5px;
	}
	
	.join-line-right {
		margin-left: 5px;
	}
	
	/* 第一个节点，不显示左边连线 */
	.step-axis-node:first-child .join-line-left {
		visibility: hidden;
	}
	
	/* 最后一个节点，不显示右边连线 */
	.step-axis-node:last-child .join-line-right {
		visibility: hidden;
	}
	
	.step-node {
		border: 1px solid #999;
		border-radius: 50px;
		width: 28px;
		height: 28px;
		line-height: 28px;
		text-align: center;
	}
	
	.step-node-finish {
		background: url(~@/static/img/record/icon_step_finish.png) no-repeat center;
	}
	
	.axis-node-name {
		margin-top: 3px;
		font-size: 12px;
		color: #999;
	}
</style>
