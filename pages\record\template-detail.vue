<template>
	<Page
		class="power-page"
		id="templateDetail"
		title="表单详情"
		:mainStyle="mainStyle">
		
		<!-- #ifdef APP-PLUS || H5 -->
		<template v-slot:bar>
			<NaviBar
				style="position: fixed;"
				title="表单详情">
				<template v-slot:option>
					<image
						style="width: 32px; height: 32px;"
						:src="saveIcon"
						mode="aspectFit"
						@click="saveFormData"
					/>
				</template>
			</NaviBar>
		</template>
		
		<template-form
			class="template-form-detail"
			:editable="true"
			:record-id="recordId"
			:constraints="constraints"
			:template="template"
			:form-data="data"
			:parentHeight="formContentHeight"
		/>
		<!-- #endif -->
		
		<!-- #ifdef MP -->
		<view style="padding: 16rpx;">
			<template-form
				class="template-form-detail"
				:editable="true"
				:record-id="recordId"
				:constraints="constraints"
				:template="template"
				:form-data="data"
				:parentHeight="formContentHeight"
			/>
		</view>
		<view class="flex-row-layout form-operate-layout">
			<view class="power-button power-button-primary" @click="saveFormData">提交</view>
		</view>
		<!-- #endif -->
		
	</Page>
</template>

<script>
	import NaviBar from '@/pages/component/NaviBar.vue';
	import Page from '@/pages/component/Page.vue';
	import templateForm from '@/pages/form/template-form.vue';
	import formService from '@/api/form-service.js';
	import formUtil from '@/pages/form/Form.js';
	import saveIcon from '@/static/img/save_icon.png';
	
	import { guid } from '@/common/uuid.js';
	
	import styleUtil from '@/common/style.js';
	
	export default {
		components: {
			Page, NaviBar, templateForm
		},
		
		data() {
			// let defaultRecordId = null;
			let defaultRecordId = guid();
			// let defaultRecordId = '39a84f36-2eea-4d3d-923b-d3be94fc3d96';
			// let defaultRecordId = 'f9aff6f8-3396-427a-9b4b-7f4f68158ad0';
			
			return {
				// #ifdef MP
				mainStyle: {
					width: '100%',
					height: '100%',
					padding: '0'
				},
				// #endif
				// #ifndef MP
				mainStyle: {
					width: 'calc(100% - 32rpx)',
					height: '100%',
					padding: '16rpx'
				},
				// #endif
				saveIcon,
				formContentHeight: -1,
				templateDefineId: null,
				templateId: null,
				template: {},
				recordId: defaultRecordId,
				data: {
					XLKYS: '2'
				},
				constraints: [
					{
						active_id: '202011031501106b797a7c7a1041259bf1c78afb695255',
						follow_id: '20201103150731ad00bc08aa994cfcbc6c5996300173bd',
						rule: 'hide=0'
					}
				]
			}
		},
		
		onLoad(option) {
			let paramKeys = ['templateId', 'templateDefineId'];
			paramKeys.forEach(key => {
				this[key] = option[key];
			})
		},
		
		mounted() {
			this.loadTemplateJson();
			
			//非H5应用需要指定表单窗口高度，H5应用在template-form会自动计算
			// #ifndef H5
			//标题栏高度
			styleUtil.getScreenLayout()
				.then(layout => {
					if(layout){
						//Page组件的插槽内边上下内边距之和
						let pageSlotPadding = 16;
						let totalHeight = layout.height;
						let titleBarHeight = 48;
						this.formContentHeight = totalHeight - pageSlotPadding - titleBarHeight;
					}
				})
			// #endif
		},
		
		onReady() {
			console.log('template-detail on ready')
		},
		
		methods: {
			loadTemplateJson(){
				formService.getTemplateById(this.templateId)
					.then(templateJson => {
						let reorganizeTemplate = formUtil.packPeerElementIntoGroup(templateJson);
						this.template = reorganizeTemplate;
						// this.log(this.template, `表单配置`)
						this.loadFormData()
					})
					.catch(error => {
						console.log(`获取表单配置出错：${JSON.stringify(error, null, 4)}`)
					})
			},
			
			loadFormData(){
				if(this.recordId){
					formService.getRecordData(this.templateId, this.recordId)
						.then(formData => {
							this.log(formData, '表单数据')
							this.data = formData;
						})
						.catch(error => {
							this.error(error, '获取表单数据出错');
						})
				}
					
			},
			
			saveFormData(){
				this.log(this.$store.state.formData || this.data, '待提交的数据')
				let e = this.$refs['20201103115400ff01ceeb39e2490c946cb46092e2b2fb'];
				if(e) {
					console.log(`找到了`)
				} else {
					console.log(`未找到`)
				}
				// uni.showLoading({
				// 	title: '提交表单中...'
				// })
				// formService.submitFormData(this.template, this.$store.state.formData || this.data, this.recordId)
				// 	.then(resp => {
				// 		console.log(`响应数据：${JSON.stringify(resp, null, 4)}`);
				// 	})
				// 	.catch(error => {
				// 		console.log(`保存表单出错：${JSON.stringify(error, null, 4)}`);
				// 	})
				// 	.finally(() => {
				// 		uni.hideLoading();
				// 	})
			}
		}
	}
</script>

<style scoped>
	.template-form-detail {
		background-color: #f4f4f4;
	}
	
	.form-operate-layout {
		padding: 0 20rpx; 
		width: calc(100% - 40rpx);
		height: 96rpx; 
		background-color: #fff; 
		justify-content: flex-end;
	}
</style>
