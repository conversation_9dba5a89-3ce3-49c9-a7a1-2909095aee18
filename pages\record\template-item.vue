<template>
	<view 
		class="flex-row-layout template-list"
		@click="showTemplateDetail">
		<text style="max-width: 90%;">{{order + 1}}.&nbsp;&nbsp;{{template.MBMC}}</text>
		<view
			class="navi-next"
			style="margin-left: auto;"
		/>
	</view>
</template>

<script>
	export default {
		name: 'TemplateItem',
		props: {
			order: {
				type: Number,
				default: 1
			},
			
			template: {
				type: Object
			}
		},
		
		methods: {
			showTemplateDetail(){
				uni.navigateTo({
					url: `/pages/record/template-detail?templateId=${this.template.MBBH}&templateDefineId=${this.template.BBXH}`
				})
			}
		}
	}
</script>

<style scoped>
	.template-list {
		padding-left: 10px;
		width: calc(100% - 10px);
		height: 42px;
		background-color: #fff;
		font-size: 16px;
	}
</style>
