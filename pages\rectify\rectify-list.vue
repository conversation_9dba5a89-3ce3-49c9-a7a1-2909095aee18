<!--
 * @Author: your name
 * @Date: 2021-12-24 12:25:42
 * @LastEditTime: 2022-01-28 17:53:51
 * @LastEditors: Please set LastEditors
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: /UNI_APP_ShanDong/pages/rectify/rectify-list.vue
-->

<template>
    <Page :padding="false" title="整改通知">
        <!-- <ul class="pd-ulbx3 rectify" style="top: 10%">
            <li
                v-for="(item, index) in statistics"
                :key="item.id"
                @click="goStatistics(index)"
            >
                <h2>{{ item.name }}</h2>
                <p :style="{ color: item.color }">{{ item.value }}</p>
            </li>
        </ul> -->
        <view class="rectify-head">
            <view
                class="rectify-head-list"
                v-for="(item, index) in statistics"
                :key="index"
                @click="chooseType(item)"
            >
                <view class="rectify-head-title">{{ item.name }}</view>
                <view
                    class="rectify-head-value"
                    :style="{ color: item.color }"
                    >{{ item.value }}</view
                >
            </view>
        </view>
        <scroll-view
            :scroll-y="true"
            @scrolltolower="loadMore"
            :style="pageListStyle"
        >
            <view
                class="company-list"
                v-for="(task, index) in companyList"
                @click="doRecordTask(task)"
                :key="index"
            >
                <view class="flex-row-layout">
                    <text class="company-title company-title-text">{{
                        task.WRYMC || '--'
                    }}</text>
                    <image
                        v-if="task.SFZMQD === '1'"
                        style="width: 62px; height: 20px; margin-left: 5px"
                        :src="iconAbsentStamp"
                    />
                </view>
                <view class="company-footer">
                    <view class="company-time">
                        <image :src="timeIcon" />
                        <text v-if="task.JCSJ"
                            >检查时间：{{ task.JCSJ.substring(0, 10) || '--' }}</text
                        >
                    </view>
                    <view class="company-type">
                        <text v-if="task.ZFRYMC">{{ task.ZFRYMC }}</text>
                    </view>
                    <view class="company-router">
                        <image :src="nextIcon" />
                    </view>
                </view>
                <!-- <view class="flex-row-layout" style="margin-bottom: 8px">
                    <view class="flex-row-layout" style="width: auto">
                        <image
                            style="width: 27px; height: 22px"
                            mode="aspectFit"
                            :src="iconDistanceMark"
                        />
                        <view style="margin-left: 5px" class="company-distance">
                            {{ getDistance(task.ZXJL || '--') }}
                        </view>
                    </view>
                    <text v-if="task.cgqx" class="company-delay">
                        超期： {{ getDelay(task.cgqx) }}
                    </text>
                    <text v-else class="company-tips">
                        <text>{{ getShowTips(task) }}</text>
                    </text>
                </view> -->
            </view>
            <!-- <NoData
                v-if="!isListShown"
                :type="companyList.length < 1 ? 'data' : ''"
            /> -->
        </scroll-view>
        <p-button
            style="
                width: 100%;
                background: #00cf6f;
                height: 50px;
                line-height: 50px;
                font-size: 18px;
            "
            @click.native="checkAddRectify()"
            name="整改通知"
        />
    </Page>
</template>

<script>
import nextIcon from '@/static/img/navi_next_icon.png';
import timeIcon from '@/static/img/time_template.png';
import NoData from '@/components/no-data.vue';
import PButton from '@/components/p-button';
import Page from '@/pages/component/Page.vue';
import { getRectifyList, getRectifyCount } from '@/api/rectify.js';
export default {
    components: {
        Page,
        PButton,
        NoData
    },
    data() {
        return {
            statistics: [
                {
                    name: '企业总数',
                    value: '0',
                    color: '#e76565',
                    type: '3'
                },
                {
                    name: '问题数',
                    value: '0',
                    type: '0'
                },
                {
                    name: '已整改',
                    value: '0',
                    color: '#00cf6f',
                    type: '2'
                },
                {
                    name: '未整改',
                    value: '0',
                    color: '#e76565',
                    type: '1'
                }
            ],
            listType: '',
            nextIcon,
            timeIcon,
            count: {}, //统计数据
            companyList: [] //列表
        };
    },
    computed: {
        pageListStyle() {
            return {
                height: 'calc(100vh - 390rpx)',
                backgroundColor: '#fff'
            };
        }
    },
    watch: {},

    onShow() {
        this.getRectifyList();
    },

    mounted() {
        let list = {
            id: '202112270908216bfab732e8c941cf9b4b312e06044af6'
        };
        uni.setStorageSync('YANTAI_TEMPLATE', list);
    },

    methods: {
        getRectifyList() {
            getRectifyList({
                state: this.listType
            }).then(res => {
                this.companyList = res.data_json.list;
            });
            getRectifyCount({}).then(res => {
                this.count = res.data_json;
                this.statistics[0].value = this.count.QYWTNUM || 0;
                this.statistics[1].value = this.count.NUM || 0;
                this.statistics[2].value = this.count.YZGNUM || 0;
                this.statistics[3].value = this.count.WZGNUM || 0;
            });
        },

        doRecordTask(item) {
            uni.navigateTo({
                url: `/pages/rectify/rectify-template?id=${item.XH}`
            });
        },

        chooseType(item) {
            this.listType = item.type;
            this.getRectifyList();
        },

        loadMore() {},

        checkAddRectify() {
            uni.navigateTo({
                url: `/pages/rectify/rectify-template`
            });
        }
    }
};
</script>

<style scoped>
.record-task-list {
    margin-top: 10px;
    background-color: #fff;
}

.book {
    width: 100%;
    background-color: #f1f2f6;
}

.mk {
    background-color: #009bff;
    font-size: 22rpx;
    padding: 2rpx 2rpx;
    color: #fff;
    border-radius: 4rpx;
    margin-left: 2rpx;
}

.company-list {
    margin-left: 28rpx;
    background-color: #fff;
    border-bottom: 1rpx solid #eeeeee;
}

.company-title {
    font-weight: 500;
    color: #333333;
    font-size: 30rpx;
    padding: 16rpx 0 12rpx 0;
}

.company-footer {
    display: flex;
    align-items: center;
    width: 100%;
    padding-bottom: 12rpx;
}

.company-time {
    width: 46%;
    display: flex;
    align-items: center;
}

.company-time image {
    width: 24rpx;
    height: 24rpx;
}

.company-type {
    width: 46%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin-bottom: 10rpx;
}

.company-type text {
    background-color: #3f97dc;
    color: #fff;
    font-size: 24rpx;
    padding: 2rpx 20rpx;
    border-radius: 20rpx;
}

.company-time text {
    font-size: 26rpx;
    color: #666;
    padding-left: 12rpx;
}

.company-router image {
    width: 28rpx;
    height: 28rpx;
    position: relative;
    bottom: 30rpx;
    left: 16rpx;
}

.company-delay {
    margin-left: auto;
    margin-right: 10px;
    color: red;
}

.company-tips {
    margin-left: auto;
    margin-right: 10px;
    color: #ff9900;
}
.search-bar {
    width: 90%;
}
.more-search {
    width: 10%;
    background: url('~@/static/img/icon_search_blue.png') no-repeat center;
    background-color: #ffffff;
    background-size: 50rpx auto;
}
.search-content {
    display: flex;
}

.rectify {
    position: relative;
}

.rectify-head {
    display: flex;
    width: 100%;
    justify-content: center;
    align-items: center;
}

.rectify-head-list {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40rpx 0;
    background: #009bff;
}

.rectify-head-title {
    color: #fff;
    font-size: 34rpx;
}

.rectify-head-value {
    color: #ccc;
    font-size: 32rpx;
    padding-top: 10rpx;
}
</style>
