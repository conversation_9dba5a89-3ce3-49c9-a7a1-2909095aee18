<template>
    <Page :padding="false" @layoutAware="onFixedContentHeight">
        <template v-slot:bar>
            <NaviBar title="整改通知" />
        </template>

        <view class="template-form-layout" id="formParent">
            <template-form
                ref="templateForm"
                :parentHeight="formLayoutHeight"
                :editable="editable"
                :template="template"
                :form-data="formData"
                :record-id="recordId"
            />
        </view>
        <p-button
            @click.native="onMenuClick"
            v-if="editable"
            name="保存"
        ></p-button>
        <p-button
            @click.native="enterBack"
            v-if="!editable"
            name="确定"
        ></p-button>
    </Page>
</template>

<script>
import iconSave from '@/static/img/record/icon_form_save.png';
import NoData from '@/components/no-data.vue';
import Page from '@/pages/component/Page.vue';
import NaviBar from '@/pages/component/NaviBar.vue';
import templateForm from '@/pages/form/template-form.vue';
import formOperateMenuItem from '@/pages/record/form-operate-menu-item.vue';
import styleUtil from '@/common/style.js';
import formUtil from '@/pages/form/Form.js';
import onform from '@/api/onsaveForm.js';

import { guid } from '@/common/uuid.js';
import { deepCopyObject } from '@/common/merge.js';

// import { postQueryWryjcTzForm, postQueryWryjcTzInfo } from '@/api/book.js';

import {
    dynamicformsave,
    queryRecordData,
    postGetBasicData,
    queryTaskFormTemplate
} from '@/api/rectify.js';
import loginService from '@/api/login-service.js';
import { queryWryJbxx } from '@/api/wry-service.js';

import PButton from '@/components/p-button';

const LOCAL_CACHE_TIMESTAMP_KEY = '_timestamp';
import { EVENT_FROM_VALUE_CHANGE } from '@/pages/form/Form.js';

export default {
    components: {
        Page,
        NaviBar,
        NoData,
        templateForm,
        formOperateMenuItem,
        PButton
    },

    data() {
        return {
            turnPage: 1,
            title: '公司名称',
            recordId: '',
            templateId: '',
            formData: {},
            template: {},
            editable: true,
            pageHeight: 600,
            menus: [
                {
                    name: '保存',
                    icon: iconSave
                }
            ],
			cacheRecordId:'', //缓存的key
			formValueChangeListener: null, //表单值变量监听回调
        };
    },

    computed: {
        formLayoutHeight: function () {
            return this.pageHeight - 40;
        },
		
		localCacheKey: function () {
			return `form-Data:${this.recordId}`;
		},
    },

    onLoad(options) {
        this.$store.state.verifyList = [];
        this.templateId = '2022011816074686014f290cb4473f9a0f61e8175497d8';
		
		if(options.id){
			this.recordId = options.id
		}else{
			this.cacheRecordId = `recordID:${this.templateId}`;
			let id = uni.getStorageSync(this.cacheRecordId);
			if (id) {
			    this.recordId = id;
			} else {
			    this.recordId = guid();
			    uni.setStorageSync(this.cacheRecordId, this.recordId);
			}
		}

        // this.recordId = options.id || guid();
        if (options.id) {
            this.editable = false;
        }
        this.getData();
		this.listenFormValueChange();
    },

    methods: {
        getData() {
            queryTaskFormTemplate('', this.templateId).then(res => {
                this.template = res;
                if (!this.editable) {
                    queryRecordData(this.templateId, this.recordId).then(r => {
                        if (r) {
                            this.formData = r;
                        }
                    });
                }else{
					this.bindFormData();
				}
            });
        },

        onFixedContentHeight(layout) {
            this.pageHeight = layout.height;
        },

        getTemplate() {
            let userInfo = uni.getStorageSync('userInfo');
            let userId = '';
            if (userInfo) {
                userId = userInfo.id;
            }
            postQueryWryjcTzForm({
                service: 'GET_DYNAMICFORM_MODEL',
                bbxh: this.templateId,
                userId: userId || 'SDSZFJ'
            }).then(res => {
                this.template = formUtil.packPeerElementIntoGroup(
                    res.datas_json
                );
            });
        },

        onMenuClick() {
            let self = this;
            styleUtil.getNodeLayout(this, '#formParent').then(layout => {
                let dataType = onform.saveTemplateForm(
                    self.formData,
                    self,
                    layout.top
                );
                if (dataType === true) {
                    this.postFormAxios();
                }
            });
        },

        postFormAxios() {
            let self = this;
            let form = {
                client_type: 'mobile_web',
                record_id: this.recordId,
                service: 'DYNAMICFORM_SAVE',
                template_id: self.templateId,
                user_id: loginService.getAuthUserId() || 'SDSZFJ'
            };
            let tableName = self.template['templateTable'];
            let data = this.$store.state.formData || this.formData;
            data = deepCopyObject(data);
            form[tableName] = data;
            data.QYBH = data.WRYBH;
            dynamicformsave(form)
                .then(res => {
					uni.removeStorageSync(
                        'recordID:' + self.templateId
                    );
                    self.onCreatedWry(res.data);
                })
                .catch(error => {
                    let errorMsg = error.error_msg;
                    if (
                        errorMsg &&
                        errorMsg.indexOf('此企业系统中已存在，不能重复添加') !==
                            -1
                    ) {
                        queryWryJbxx(data.TYSHXYDM, data.WRYMC).then(resp => {
                            self.onCreatedWry(resp.data_json);
							uni.removeStorageSync(
							    'recordID:' + self.templateId
							);
                        });
                    } else {
                        uni.showToast({
                            title: `新增企业出错：${errorMsg}`,
                            duration: 6000,
                            icon: 'none'
                        });
                    }
                });
        },

        onCreatedWry(createdWry) {
            let backStepNum = this.turnPage === 2 ? 2 : 1;
            uni.navigateBack({
                delta: backStepNum
            });

            let wryPrimaryKey = 'record_id';
            if (createdWry && createdWry.hasOwnProperty(wryPrimaryKey)) {
                createdWry.WRYBH = createdWry[wryPrimaryKey];
            }

            setTimeout(() => {
                uni.$emit('onAddPollutionSelect', createdWry);
            }, 500);
        },

        enterBack() {
            uni.navigateBack({
                delta: 1
            });
        },
		
		/**
         * 绑定表单数据，编辑模式加载缓存数据，新增模式绑定默认值
         */
        bindFormData() {
            //本地存在缓存，总是加载缓存
            let localCacheRecord = this.loadCacheFromLocal();
            if (localCacheRecord) {
                this.formData = deepCopyObject(localCacheRecord);
                // if (this.isAddNewRecordMode) {
                //     return;
                // }
            }
        },

        /**
         * 从本地加载附件表单笔录缓存
         */
        loadCacheFromLocal() {
            return uni.getStorageSync(this.localCacheKey) || null;
        },

        //监听表单字段改变
        listenFormValueChange() {
            let _self = this;
            let valueChangeListener = params => {
                // #ifdef MP
                let latestFormData = this.mpFormData;
                // #endif

                // #ifndef MP
                let latestFormData = this.formData;
                // #endif
                latestFormData[LOCAL_CACHE_TIMESTAMP_KEY] =
                    new Date().getTime();
                uni.setStorage({
                    key: _self.localCacheKey,
                    data: latestFormData
                });
            };
            uni.$on(EVENT_FROM_VALUE_CHANGE, valueChangeListener);
            this.formValueChangeListener = valueChangeListener;
        }
    }
};
</script>

<style scoped>
.template-form-layout {
    /* border-radius: 5rpx; */
    /* padding: 10rpx; */
    width: calc(100%);
    background-color: #fff;
    margin-top: 0rpx;
    height: calc(100% - 140rpx);
}

.template-form-tabsList {
    margin-top: 60upx;
}

.pd-ultbs1 {
    white-space: nowrap;
    overflow-x: scroll;
    overflow-y: hidden;
    -webkit-overflow-scrolling: touch;
}

.pd-ultbs1 li {
    font-size: 20px;
    margin: 10rpx 12rpx;
    display: inline-block;
    vertical-align: top;
}

/* .template-form-layout {
		border-radius: 5px;
		padding: 10px;
		width: calc(100% - 20px);
		background-color: #f4f4f4;
	} */

.form-menu-layout {
    margin-top: 70rpx;
    height: 70rpx;
    z-index: 10;
    background-color: white;
}

.form-operate-menu-item {
    flex: 1;
    padding: 22rpx 0;
}

.form-operate-menu-item:active {
    background-color: #ccc;
}

.form-menu-icon {
    width: 40rpx;
    height: 40rpx;
}

.form-menu-text {
    margin-top: 2px;
    font-size: 26rpx;
    color: white;
}

.form-content-layout {
    width: 100%;
    height: 100%;
}

.form-menu-layout {
    height: 50px;
    background-color: #fff;
    /* box-shadow: 0 -1px 1px 0 #ccc; */
}

.form-list-tab {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20rpx 0;
    color: #fff;
}
</style>
