<template>
  <body style="overflow-y: scroll;">
    <div class="begin-exam" style="width:100%">
      <h1 class="h1-til">{{ jbxx.KSMC }}</h1>
      <p>共 <span>{{ jbxx.KTSL }}</span> 题，答题时间 <span> {{ jbxx.KSSC }} </span> 分钟</p>
      <p style="color: red;font-size: 14px;">注 : 考试中途不能退出，否则将自动交卷 </p>
      <div class="begin" @click="
          goClick('/pages/study/exam/exam')
        "></div>
    </div>
  </body>
</template>

<script>

  export default {
    data() {
      return {
        jbxx: {
          KSMC: '山东是5月知识竞赛', //考试标题
          KTSL: '12', //总共多少道题
          KSSC: "60", //多少分钟只能传分钟
          XH:'', // 这场考试序号
        }
      }
    },
    onLoad(option) {
      
    // 获取上个页面传过来的参数  本场考试的基本信息
    this.jbxx = JSON.parse(decodeURIComponent(option.obj));
  },
    methods: {
      // 跳转，传参到下一个页面
      goClick(url) {
      
        uni.navigateTo({
          url: url + '?obj=' + encodeURIComponent(JSON.stringify(this.jbxx)),
        })

        
      }
    }
  }
</script>

<style>

</style>
