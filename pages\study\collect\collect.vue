<template>
  <body style="background: #f7f7f7" id="box" >
    <header class="pd-header">
      <i class="goback" @click="goClick('/pages/study/study')"></i>
      <h1 class="pd-title">我的收藏</h1>
    </header>
    <section class="pd-main" style="width: 100%" >
      <div class="pd-inner" style="padding-bottom: 0">
		  <scroll-view :scroll-y="true"
				             @scrolltolower="loadMore"
							 style="height: 100%"
				             >
        <ul class="pd-ullst1b">
			
          <div v-for="(item, index) in data" :key="index">
            <!-- 没用视频且没有图片 -->
            <li
              v-show="item.video.length == '0' && item.picture.length == '0'"
              @click="wzClick(item.XH)"
            >
              <h1>{{ item.WZBT }}</h1>
              <p>{{ item.CJSJ }}</p>
            </li>
            <!-- 有视频且,只放第一个视频 -->
            <li v-show="item.video.length > 0" @click="wzClick(item.XH)">
              <h1>{{ item.WZBT }}</h1>

              <p>{{ item.CJSJ }}</p>
            </li>

            <!-- 没有视频 只放 一张图片 -->
            <li
              class="df"
              v-show="
                item.video.length == '0' &&
                item.picture.length <= 2 &&
                item.picture.length > 0
              "
              @click="wzClick(item.XH)"
            >
              <div class="fl">
                <h1>{{ item.WZBT }}</h1>
                <p>{{ item.CJSJ }}</p>
              </div>
              <div class="fr">
                <image :src="item.picture[0]" alt="" />
              </div>
            </li>

            <!-- 没有视频，放三张图片 -->
            <li
              v-show="item.video.length == '0' && item.picture.length >= 3"
              @click="wzClick(item.XH)"
            >
              <h1>{{ item.WZBT }}</h1>
              <p>{{ item.CJSJ }}</p>
              <ul class="pd-ulpic1">
                <li style="border: none">
                  <image :src="item.picture[0]" alt="" />
                </li>
                <li style="border: none">
                  <image :src="item.picture[1]" alt="" />
                </li>
                <li style="border: none">
                  <image :src="item.picture[2]" alt="" />
                </li>
              </ul>
            </li>
          </div>
		    
        </ul>
		</scroll-view>
      </div>
    </section>
  </body>
</template>

<script>
import { journalismQUERY_COLLECTION } from "@/api/study-test.js";
export default {
  data() {
    return {
      data: [],
      pageSize:10,
      pageNum:1,
    };
  },
  mounted(){
    this.journalismQUERY_COLLECTION()
  },
  methods: {
    journalismQUERY_COLLECTION(){
      let pam = {};
      pam.pageSize = this.pageSize
      pam.pageNum = this.pageNum
      journalismQUERY_COLLECTION(pam).then(res=>{
        this.data = res.data_json.list
      })
    },
	  loadMore(){
		  this.pageSize = this.pageSize + 10
      this.journalismQUERY_COLLECTION()

	  },
    // 文章详情跳转
    wzClick(XH) {
      uni.navigateTo({
        url: "/pages/study/knowledge/knowledgeDetails?XH=" + XH,
      });
    },
    // 跳转
    goClick(url) {
      uni.navigateTo({
        url: url,
      });
    },
  },
};
</script>

<style>
</style>
