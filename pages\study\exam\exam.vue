<template>
	<body class="dati-body">
		<header class="pd-header">
			<!-- <i class="goback"></i>
        <h1 class="pd-title">练习</h1> -->
		</header>
		<section class="pd-main page-datiku" style="width:100%">
			<div class="pd-inner">
				<h2 class="h2-til">{{ jbxx.KSMC }}</h2>
				<p class="til zy-til1">
					剩余时间
					<span><i v-show="fen < 10"> 0 </i> {{ fen }}:
						<i v-show="miao < 10"> 0 </i> {{ miao }}
					</span>
				</p>

				<div class="timu timu-num" v-for="(item, index) in data" :key="index" v-show="num == index + 1">
					<div class="xuhao" v-show="item.type == 'DXT'">单选题</div>
					<div class="xuhao" v-show="item.type == 'FXT'">多选题</div>
					<div class="xuhao" v-show="item.type == 'PDT'">判断题</div>
					<p class="ti">
						<em>第 {{ num }} 题</em> <i> · 共{{ jbxx.KTSL }}题</i>
					</p>
					<p class="zi">{{ item.name }}</p>
					<ul class="xuanxiang">
						<li :class="item.DA.indexOf(itemOne.xh) != '-1' ? 'correct' : ''"
							v-for="(itemOne, indexOne) in item.choice" :key="indexOne"
							@click="xzdaClick(item, itemOne)">
							{{ itemOne.text }}
						</li>
					</ul>
				</div>

				<div class="xiayiti shang" v-show="num > 1" @click="num--">
					<p>上一题</p>
				</div>
				<div class="xiayiti xia" v-if="num == data.length" @click="tjClick()">
					<p>提交答案</p>
				</div>

				<div class="xiayiti xia" v-else @click="num++">
					<p>下一题</p>
				</div>
			</div>
		</section>

		<div class="mask" style="display: block" v-show="jsbtShow || dfShow"></div>

		<div class="pd-dlg zy-alert-jieshu" v-show="jsbtShow">
			<h1>{{ jsbt }}</h1>
			<div class="btns">
				<span class="zy-cancel" @click="jsbtShow = false" v-show="!timeOut && !isLate"></span>
				<span class="zy-confirm" @click="subClick()" v-show="!timeOut && !isLate"></span>
			</div>
		</div>

		<div class="pd-dlg dlg1" style="display: block" v-show="dfShow">
			<h1>考试结束</h1>
			<h2>得分</h2>
			<h3>{{ score.score }}<sub>分</sub></h3>
			<p>共{{ score.allTopic }}题，答对<i>{{ score.rightTopic }}题</i></p>
			<small class="dlgcls" @click="goClick('/pages/study/study')"></small>
		</div>
	</body>
</template>

<script>
	import {
		getExaminationTopic,
		submitExamination
	} from '@/api/study-test.js'
	export default {
		data() {
			return {
				jbxx: {
					KSMC: '', //考试标题
					KTSL: '', //总共多少道题
					KSSC: "", //多少分钟只能传分钟
					XH: '', // 这场考试序号
				}, //本场考试得基本信息
				fen: 0, // 分
				miao: 59, // 秒
				jsbt: "是否结束考试?", // 结束标题
				jsbtShow: false, //结束弹框
				dfShow: 0, // 得分弹框
				num: 1, // 当前第几题
				data: [],
				score: {}, //考试得分
				isLate: 0, //考试是否已结束
				hideNum: 0,
				timeOut:0,//倒计时时间到
				dsq: '', //考试时间定时器
				dsqOne: '', //2秒提交答案定时器
			};
		},
		onLoad(option) {
			// 获取上个页面传过来的参数  本场考试的基本信息
			this.jbxx = JSON.parse(decodeURIComponent(option.obj));
		},
		onUnload() {
			//如果已展示的分，回到首页
			// if (this.dfShow) {
				uni.$emit('overFinish')
				uni.navigateBack({
					delta: 2
				});
			// } 
			// else {
			// 	this.subClick()
			// 	uni.$emit('overFinishTip')
			// 	uni.navigateBack({
			// 		delta: 2
			// 	});
			// }

		},
		mounted() {
			// 获取考试试题
			this.getExaminationTopic()

			//处理考试时长
			let _time = new Date(this.jbxx.KSJSSJ).getTime() - new Date().getTime()
			console.log('ssssss_time', _time)
			if (_time > 0) { //还有考试时间
				let _min = _time / 60000
				console.log('_min', _min)
				if (_min > 1 && _min < Number(this.jbxx.KSSC)) {
					this.fen = parseInt(_min)
				} else {
					this.fen = Number(this.jbxx.KSSC) - 1;
				}
			} else { //已超过考试时间
				console.log('已超过考试时间')
				this.isLate = 1
			}

			// this.fen = Number(this.jbxx.KSSC) - 1;

			//设置考试倒计时
			// this.setTestTime()
		},
		beforeDestroy() {
			clearInterval(this.dsq); // 清除定时器
			this.dsq = null;
		},
		methods: {
			// 获取考试试题
			getExaminationTopic() {
				let pam = {}
				pam.SJXH = this.jbxx.XH
				pam.YHID = uni.getStorageSync('userInfo').id
				getExaminationTopic(pam).then(res => {

					this.data = res.data_json
					this.data.forEach(element => {
						element.DA = []
					});

					if (this.isLate) {
						this.jsbt = "考试时间已过！考试结束，正在为您提交答案...";
						this.jsbtShow = true;
						this.dsqOne = setTimeout(() => {
							this.subClick()
							clearTimeout(this.dsqOne);
							this.dsqOne = null
						}, 2000);
						return;
					}

					//设置考试倒计时（加载到考题后在设置时间）
					this.setTestTime()

				})
			},
			// 提交按钮
			tjClick() {

				let flag = 0
				for (let i = 0; i < this.data.length; i++) {

					if (this.data[i].DA.length == 0) {
						flag++
					}
				}
				if (flag == 0) {
					this.jsbt = '是否结束考试?'
				} else {
					this.jsbt = '您还有' + flag + '道题未完成，是否结束考试?'
				}

				this.jsbtShow = true

			},

			// 提交答案 调接口
			subClick() {
				console.log('----------提交答案------------')
				// 组装答案参数
				let pam = {}
				pam.examinationId = this.jbxx.XH
				pam.examinationTime = Number(this.jbxx.KSSC) - Number(this.fen)
				pam.examination = [];
				this.data.forEach(element => {
					let obj = {};
					obj.id = element.id
					if (element.DA.length == 0) {
						obj.value = null
					} else {
						obj.value = element.DA.toString()
					}
					pam.examination.push(JSON.stringify(obj))
				});

				pam.examination = '[' + pam.examination.toString() + ']'
				pam.YHID = uni.getStorageSync('userInfo').id
				
				submitExamination(pam).then(res => {
					this.dfShow = true;

					this.jsbtShow = false;
					this.score = res.data_json

				})

			},
			// 选择答案
			xzdaClick(item, itemOne) {
				// 判断是否选过这个答案,选过 则 取消选择  没选过 则 选进去，同时没选过的话 再继续判断 是否多选
				if (item.DA.indexOf(itemOne.xh) != "-1") {
					for (let i = 0; i < item.DA.length; i++) {
						if (item.DA[i] == itemOne.xh) {
							item.DA.splice(i, 1);
						}
					}
				} else {
					// 判断  是否允许多选
					if (item.type == "FXT") {
						item.DA.push(itemOne.xh);
					} else {
						item.DA = [];
						item.DA.push(itemOne.xh);
					}
				}

			},
			// 跳转
			goClick(url) {
				// redirectTo
				// uni.navigateTo({
				//   url: url,
				// });
				uni.navigateBack({
					delta: 1
				});
			},
			//设置考试时间
			setTestTime() {
				this.dsq = setInterval(() => {
					this.miao--;
					if (this.miao < 0) {
						if (this.fen == 0) {
							// 清除定时器
							clearInterval(this.dsq);
							// 定时器已清除但是还会走一遍，所以强行设置0
							this.miao = 0;
							// 判断是否有题目没做完，给出提示  直接提交
							let flag = 0;

							this.jsbt = "时间到！考试结束，正在为您提交答案...";
							this.jsbtShow = true;
							
							//时间到
							this.timeOut = 1
							
							this.dsqOne = setTimeout(() => {
								this.subClick()
								clearTimeout(this.dsqOne);
								this.dsqOne = null
							}, 2000);
							return;

						}
						this.fen--;
						this.miao = 59;
					}
				}, 1000);
			}
		},
	};
</script>

<style scoped>
	/* .pd-inner{
		position: relative;
	} */
	.page-datiku .timu {
		height: 880rpx;
		overflow-y: auto;
	}
	
	.page-datiku .xiayiti{
		bottom: 20rpx;
	}
	
	.page-datiku .xiayiti p {
		width: 142rpx;
		line-height: 60rpx;
	}

	.page-datiku .xiayiti.shang {
		left: 0;
		right: unset;
	}

	.page-datiku .xiayiti.xia {
		left: unset;
		right: 0;
	}
</style>
