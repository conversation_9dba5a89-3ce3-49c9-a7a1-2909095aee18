<template>
	<body style="background: #f5fafd;">
	<header class="pd-header">
		<i class="goback" @click="goClick('/pages/study/study')"></i>
		<h1 class="pd-title">积分说明</h1>
	</header>
	<section class="pd-main">
		<div class="pd-inner">
			<div class="gap"></div>
			<div class="gap"></div>
			<dl class="pd-dlbx2b">
				<dt>1.登录</dt>
				<dd>
					<div class="icon">
						<image src="/static/study/images/aic1.png" alt="" />
					</div>
					<div class="cont">
						<h1>每日首次登录学习考试模块 </h1>
						<p>每日上限1次共1分</p>
					</div>
					<div class="numb">+1</div>
				</dd>
			</dl>
			<div class="gap"></div>
			<div class="gap"></div>
			<dl class="pd-dlbx2b">
				<dt>2.文章学习</dt>
				<dd>
					<div class="icon">
						<image src="~@/static/study/images/aic2.png" alt="" />
					</div>
					<div class="cont">
						<h1>每有效阅读一篇文字</h1>
						<p>每日上限10分，阅读时间不低于30秒</p>
					</div>
					<div class="numb">+1</div>
				</dd>
			</dl>
			<div class="gap"></div>
			<div class="gap"></div>
			<dl class="pd-dlbx2b">
				<dt>3.视频学习</dt>
				<dd>
					<div class="icon">
						<image src="~@/static/study/images/aic3.png" alt="">
					</div>
					<div class="cont">
						<h1>每有效观看一段视频</h1>
						<p>每日上限5篇共10分，时间不低于2分钟，不足两分钟的视频按2分钟算</p>
					</div>
					<div class="numb">+2</div>
				</dd>
			</dl>
			<div class="gap"></div>
			<div class="gap"></div>
			<dl class="pd-dlbx2b">
				<dt>4.点赞</dt>
				<dd>
					<div class="icon">
						<image src="~@/static/study/images/aic4.png" alt="">
					</div>
					<div class="cont">
						<h1>每点赞一篇文章/视频</h1>
						<p>每日上限4次共2分</p>
					</div>
					<div class="numb">+0.5</div>
				</dd>
			</dl>
			<div class="gap"></div>
			<div class="gap"></div>
			<dl class="pd-dlbx2b">
				<dt>5.分享</dt>
				<dd>
					<div class="icon">
						<image src="~@/static/study/images/aic5.png" alt="">
					</div>
					<div class="cont">
						<h1>每分享一篇文章/视频</h1>
						<p>每日上限两次2分</p>
					</div>
					<div class="numb">+1</div>
				</dd>
			</dl>
			<div class="gap"></div>
			<div class="gap"></div>
			<dl class="pd-dlbx2b">
				<dt>6.发布文章</dt>
				<dd>
					<div class="icon">
						<image src="~@/static/study/images/aic6.png" alt="">
					</div>
					<div class="cont">
						<h1>发布有效文章/视频审核通过</h1>
						<p>无上限</p>
					</div>
					<div class="numb">+20</div>
				</dd>
			</dl>
		</div>
	</section>
</body>
</template>

<script>
	export default {
		data() {
			return {
				
			}
		},
		methods: {
			// 跳转
    goClick(url) {
      uni.navigateTo({
        url: url,
      });
    },
		}
	}
</script>

<style>

</style>
