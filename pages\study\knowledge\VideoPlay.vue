<template>
	<div style="width: 100%; height: 100%; display: inline-block; position: relative;">
		<!-- 播放器容器 -->
		<view class="video" ref="videoEle" :id="idName" :index="index" :idName="idName" :videoUrl="videoUrl"
			:change:videoUrl="videos.init" :change:index="videos.changeIndex">
		</view>
		<!-- {{idName}} -->
	</div>
</template>

<script>
	export default {
		name: 'VideoPlay',
		props: {
			videoUrl: {
				type: String,
				default: ''
			},
			idName: {
				type: String,
				default: 'videoEle0'
			},
			index: {
				type: Number,
				default: 0
			}
		},
		data() {
			return {
				src: ''
				// videoUrl: 'your-video-url.mp4' // 替换为实际的视频地址
			};
		},
		mounted() {
			// 当页面初次渲染完成后，通知 renderjs 初始化播放器
			// console.log('onReady', this.videoUrl);
			// this.src = this.videoUrl;
			// this.$refs.videoContainer.callMethod('initPlayer', this.videoUrl);
		}
	};
</script>

<script module="videos" lang="renderjs">
	// import { DOWNLOAD_URL } from "@/common/config.js";
	import Videojs from 'video.js'; // videojs 引入
	import 'video.js/dist/video-js.css';

	export default {
		data() {
			return {
				videoPlayer: null, // 当前视频播放器
				videoIndex: 0
			}
		},
		mounted() {
			// console.log(this.videoUrl, 'init');

		},
		methods: {
			changeIndex(index) {
				// console.log(index, 'changeIndex')
				this.videoIndex = index;
			},
			init() {
				// console.log('init');
				setTimeout(() => {
					let src = this.videoUrl;
					// console.log(this.videoUrl, 'initvideoUrl');
					// console.log(this.idName, 'idName');
					// console.log(this.index, 'index');
					this.initVideoJS(src);
				}, 200);

			},
			// 初始化视频标签
			initVideoJS(src, lastVideoCurrentTime) {
				if (this.videoPlayer) {
					this.videoPlayer.dispose();
					this.videoPlayer = null;
				}
				let that = this;
				let videoEle = document.createElement('video');
				videoEle.style = 'width:100%; height:100%';
				videoEle.setAttribute("class", "video-js vjs-big-play-centered");

				// let index = this.index;
				// console.log(this.idName, 'idname');
				let videos = document.getElementById('videoEle' + this.videoIndex);
				// let videos = this.$refs.videoEle;
				// console.log(videos);
				videos.appendChild(videoEle)
				this.injectVideo(src, videoEle);
			},
			//注入视频
			injectVideo(src, videoEle) {
				let that = this;
				let option = {
					// errorDisplay: false,
					autoplay: false,
					controls: true, // 是否显示控制条
					preload: 'auto', // 是否预加载视频数据
					controlBar: { // 设置控制条组件
						children: [{
							name: 'controlBar'
						}]
					},
					// sources: [
					//     {
					//         src: src,
					//         type: 'video/mp4'
					//     }
					//   ]

				};

				// video.js初始化实例化的对象
				this.videoPlayer = Videojs(videoEle, option, function onPlayerReady() {

					// 开始视频
					this.on('play', function() {
						that.$ownerInstance.callMethod('onPlayerPlay');
					})

					// 暂停视频
					this.on('pause', function() {
						that.$ownerInstance.callMethod('onPlayerPause');
					})

					// 实时更新
					this.on('timeupdate', function() {
						let currentTime = that.videoPlayer ? that.videoPlayer.currentTime() : 0;
						that.$ownerInstance.callMethod('onTimeUpdate', currentTime);
					})

					// 视频播放结束
					this.on("ended", function() {
						that.$ownerInstance.callMethod('recordVideoProgress');
					});

				});
				this.videoPlayer.src({
					src: src,
					type: 'video/mp4'
				})
				// this.updateLastVideoCurrentTime();
			},
		}

	};
</script>

<style scoped>
	.video {
		width: 100%;
		height: 100%;
		position: relative;
		z-index: 1;
	}

	>>>.video-js {
		border: none;
		outline: none;
	}

	>>>.vjs-tech {
		border: none;
		outline: none;
	}

	>>>.vjs-button>.vjs-icon-placeholder:before {
		font-size: 34rpx;
		line-height: 56rpx;
		color: #fff;
	}

	>>>.video-js .vjs-play-control,
	>>>.video-js .vjs-mute-control,
	>>>.video-js .vjs-picture-in-picture-control,
	>>>.video-js .vjs-fullscreen-control {
		background-color: rgba(43, 51, 63, 0.7);
	}

	>>>.video-js .vjs-progress-control {
		visibility: hidden;
	}
</style>