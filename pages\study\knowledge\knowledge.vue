<template>
	<body style="background: #f7f7f7">
		<header class="pd-header">
			<i class="goback" @click="goBack"></i>
			<h1 class="pd-title">知识学习</h1>
			<i class="pd-srhbtn2" @click="goClick('/pages/study/knowledge/searchPage')"></i>
		</header>
		<section class="pd-main" style="width:100%">
			<div class="pd-inner" style="padding-bottom: 0">
				<div class="pd-ultbs1b">
					<ul>
						<li v-for="(item,index) in pdData" :key="index" @click="pdClick(item)"
							:class="{on : item.checked }"><i>{{
              item.DMMC
              }}</i></li>
					</ul>
					<p @click="AllpdShow = true"></p>
				</div>
				<div class="gap"></div>
				<scroll-view :scroll-y="true" @scrolltolower="loadMore" :style="pageListStyle">
					<ul class="pd-ullst1b">
						<!-- 没用视频且没有图片 -->
						<div v-for="(item,index) in data" :key="item.XH">
							<li v-show="item.video.length == '0' && item.picture.length == '0' "
								@click="wzClick(item.XH)">
								<h1>{{ item.WZBT }}</h1>
								<p>{{ item.CJSJ }}</p>
							</li>
							<!-- 有视频且,只放第一个视频 -->
							<li v-if="item.video.length > 0 && !isH5  " style="position: relative;">
								<h1 @click="wzClick(item.XH)">{{ item.WZBT }}</h1>
								<!-- <video :src="getVideoSrc(item.video[0])" style="width: 100%;"
									controls>
								</video> -->

								<!-- 新增一个播放图标，居中 -->
								<!-- <div class="play-icon">
									<image src="@/static/images/play.png" alt="" />
								</div> -->

								<!-- <div class="video-container">
									<div class="custom-play-icon" @click="playVideo(item)">
										<div class="play-button"></div>
									</div>
								</div> -->

								<!-- <view v-html="videoHtm(item)"></view> -->

								<VideoPlay style="width:100%;height: 500rpx"
								 :idName="'videoEle'+index" :index="index" :videoUrl="getVideoSrc(item.video[0])">
								</VideoPlay>

								<!-- <p>{{ item.CJSJ }}</p> -->
							</li>


							<!-- 没有视频 只放 一张图片 -->
							<li class="df"
								v-show="item.video.length == '0' && item.picture.length <= 2 && item.picture.length > 0 "
								@click="wzClick(item.XH)">
								<div class="fl">
									<h1>{{ item.WZBT }}</h1>
									<p>{{ item.CJSJ }}</p>
								</div>
								<div class="fr">
									<image :src="item.picture[0]" alt="" />
								</div>
							</li>

							<!-- 没有视频，放三张图片 -->
							<li v-show="item.video.length == '0' && item.picture.length >= 3 "
								@click="wzClick(item.XH)">
								<h1>{{ item.WZBT }}</h1>
								<p>{{ item.CJSJ }}</p>
								<ul class="pd-ulpic1">
									<li style="border: none;">
										<image :src="item.picture[0]" alt="" />
									</li>
									<li style="border: none;">
										<image :src="item.picture[1]" alt="" />
									</li>
									<li style="border: none;">
										<image :src="item.picture[2]" alt="" />
									</li>
								</ul>
							</li>
						</div>
					</ul>
				</scroll-view>
				<view class="list-show-info">{{pageShowIndex === 0 ? '上拉加载更多': '已经没有数据了'}}</view>
			</div>
		</section>

		<div class="pd-mask" style="display: block" v-show="AllpdShow"></div>

		<div class="pd-botdlg1" v-show="AllpdShow">
			<div class="pd-botdlghd">
				<span>全部频道</span>
				<i class="dlgcls" @click="AllpdShow = false"></i>
			</div>
			<div class="pd-botdlgbd">
				<ul class="pd-ullst2b">
					<li v-for="(item,index) in pdData" :key="index" @click="pdClick(item);AllpdShow = false"
						:class="{on : item.checked }"><i>{{
              item.DMMC
              }}</i></li>
				</ul>
			</div>
		</div>
	</body>
</template>

<script>
	import {
		isH5
	} from "@/common/config.js";
	import {
		journalismChannel,
		journalismArticle
	} from '@/api/study-test.js'

	import VideoPlay from './VideoPlay.vue';

	export default {
		components: {
			VideoPlay
		},
		data() {
			return {
				isH5: isH5,
				AllpdShow: false, //全部频道
				pdData: [], // 频道数据
				data: [], //文章数据
				searchText: '', // 搜索

				pageShowIndex: 0, //页面可滚动的状态码
				lastPageIndex: null,
				pageNum: 1,
				pageLoadType: true, //页面可滚动的状态
				isListShown: true, //列表是否显示
			};
		},
		computed: {
			pageListStyle() {
				return {
					height: 'calc(100vh - 230rpx)',
					backgroundColor: '#fff'
				};
			}
		},
		mounted() {
			this.journalismChannel()
		},
		methods: {
			onLoad(option) {
				// 获取上个页面传过来的搜索参数
				if (option.searchText == undefined) {
					this.searchText = ''
				} else {
					this.searchText = option.searchText
				}
			},
			// 获取文章信息
			journalismArticle() {
				let pam = {};
				for (let i = 0; i < this.pdData.length; i++) {
					if (this.pdData[i].checked == true) {
						pam.LM = this.pdData[i].DM
					}
				}
				pam.pageNum = this.pageNum
				pam.pageSize = '20'
				pam.searchText = this.searchText;
				journalismArticle(pam).then(res => {
					// this.data = res.data_json.list
					this.data.push(...res.data_json.list);

					this.lastPageIndex = res.data_json.lastPage;
					if (this.pageNum === res.data_json.pages) {
						this.pageShowIndex = 1
					}
					this.pageLoadType = true;
					if (this.data.length < 1) {
						this.isListShown = false;
					}

					this.$nextTick(() => {
						this.videoPlayOne()
					})
				})
			},
			// 获取频道信息
			journalismChannel() {
				journalismChannel({}).then(res => {
					this.pdData = res.data_json
					for (let i = 0; i < this.pdData.length; i++) {
						if (i == 0) {
							this.$set(this.pdData[i], 'checked', true)
						} else {
							this.$set(this.pdData[i], 'checked', false)
						}
					}
					this.journalismArticle()
				})
			},
			// 频道点击
			pdClick(item) {

				this.pdData.forEach(element => {
					element.checked = false
				});

				item.checked = true
				this.searchText = ''

				this.pageNum = 1
				this.pageShowIndex = 0, //页面可滚动的状态码
					this.lastPageIndex = null,
					this.pageLoadType = true, //页面可滚动的状态

					this.data = []
				this.journalismArticle()
			},
			// 文章详情跳转
			wzClick(XH) {
				uni.navigateTo({
					url: '/pages/study/knowledge/knowledgeDetails?XH=' + XH,
				});
			},
			// 跳转
			goClick(url) {
				uni.$once('searchBack', data => {
					this.searchText = data
					this.pageNum = 1
					this.pageShowIndex = 0, //页面可滚动的状态码
						this.lastPageIndex = null,
						this.pageLoadType = true, //页面可滚动的状态

						this.data = []
					this.journalismArticle()
				});
				uni.navigateTo({
					url: url,
				});
			},
			goBack() {
				uni.navigateBack({
					delta: 1
				});
			},
			//播放一个视频，其他视频暂停
			videoPlayOne() {
				var videos = document.getElementsByTagName('video');
				for (var i = videos.length - 1; i >= 0; i--) {
					(function() {
						var p = i;
						videos[p].addEventListener('play', function() {
							pauseAll(p);
						})
					})()
				}

				function pauseAll(index) {
					for (var j = videos.length - 1; j >= 0; j--) {
						if (j != index) videos[j].pause();
					}
				};
			},
			loadMore() {
				if (this.pageLoadType && this.pageShowIndex === 0) {
					this.pageNum++;
					if (this.pageNum > this.lastPageIndex) {
						this.pageShowIndex = 1
						uni.showToast({
							title: '已经没有数据了',
							duration: 2000,
							icon: 'none',
						});
					} else {
						this.journalismArticle();
					}
				} else {
					this.pageShowIndex = 1
				}
			},
			getVideoSrc(val) {
				if (val.indexOf('http') !== -1) {
					return val
				} else {
					return 'http://103.239.155.242:9001' + val
				}

			},

			videoHtm(item) {
				// return `<video controls="controls" controlslist="nodownload noplaybackrate" x5-playsinline="true" webkit-playsinline="true" playsinline="true" width="100%" height="calc((100Vw - 80upx) / 3)">
				// 			<source src="${this.getVideoSrc(item.video[0])}" type="video/mp4">
				// 		</video>`;

				return `<video controlslist="nodownload noplaybackrate" x5-playsinline="true" webkit-playsinline="true" playsinline="true" width="100%" height="calc((100Vw - 80upx) / 3)">
							<source src="${this.getVideoSrc(item.video[0])}" type="video/mp4">
						</video> 
						`;
			}

		},
	};
</script>

<style scoped>
	.list-show-info {
		width: 100%;
		color: #aaa;
		font-size: 14px;
		display: -webkit-box;
		display: -webkit-flex;
		display: flex;
		-webkit-box-pack: center;
		-webkit-justify-content: center;
		justify-content: center;
		padding-top: 4px;
	}

	>>>video::-webkit-media-controls-timeline,
	>>>video::-webkit-media-controls-current-time-display,
	>>>video::-webkit-media-controls-time-remaining-display {
		display: none !important;
	}
</style>