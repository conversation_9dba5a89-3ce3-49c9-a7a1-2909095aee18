<template>
	<body style="background: #f7f7f7">
		<header class="pd-header">
			<i class="goback" @click="goClick('/pages/study/knowledge/knowledge')"></i>
			<h1 class="pd-title">要闻推荐</h1>
		</header>

		<section class="pd-main" style="width:100%">
			<div class="pd-inner">
				<dl class="pd-dlbx1b">
					<dt>
						<h1>{{ jbxx.WZBT }}</h1>
						<p>{{ jbxx.BZRQ }}&emsp;&emsp;阅读：{{ jbxx.DJL }}</p>
					</dt>
					<dd style="padding-bottom: 10px;">
						<p v-html="jbxx.WZNR"></p>

						<VideoPlay style="width:100%;height: 500rpx" v-if="videoUrl" :videoUrl="videoUrl">
						</VideoPlay>
					</dd>
				</dl>
				<div class="gap"></div>

				<ul class="pd-ullst1b" style="padding-top: 10px;">
					<div class="pd-tithd">
						<span>相关推荐</span>
					</div>
					<div v-for="( item, index ) in jbxx.recommend" :key="index">
						<li class="df" v-show="item.picture.length > 0" @click="wzClick(item.XH)">
							<div class="fl">
								<h1>{{ item.WZBT }}</h1>
								<p>{{ item.CJSJ }}</p>
							</div>
							<div class="fr">
								<image :src="item.picture[0]" alt="">
							</div>
						</li>
						<li v-show="item.picture.length == 0" @click="wzClick(item.XH)">
							<h1>{{ item.WZBT }}</h1>
							<p>{{ item.CJSJ }}</p>
						</li>
					</div>
				</ul>
				<div class="gap"></div>

				<ul class="pd-ullst3b" style="    padding-top: 10px;">
					<div class="pd-tithd">
						<span>评论</span>
					</div>
					<li v-for="(item,index) in jbxx.comment" :key="index">
						<image src="~@/static/study/images/mn.png" alt="" v-show="item.XB == '1'">
							<image src="~@/static/study/images/wn.png" alt="" v-show="item.XB == '0'">
								<h1><em>{{ item.YHMC }}</em><i :class="{ on:item.SFDZ == 1?true:false }"
										@click="plZanClick(item)"> {{ item.PLNUM > 0?item.PLNUM:'赞' }} </i></h1>
								<p>{{ item.PLNR }}</p>
					</li>

				</ul>
				<p class="pd-tipnr">已显示全部评论</p>
			</div>
		</section>
		<footer class="pd-bottols" style="padding-right: 20px;">
			<input type="text" placeholder="写评论...." @confirm="plFbClick()" v-model="plValue" />
			<!-- <i class="iic1"><sup><em>12</em></sup></i> -->
			<i :class="['iic2',{on:collectionType===true}]" @click="scClick()"></i>
			<i :class="['iic3',{on:supportType===true}]" @click="dzClick()"></i>
			<!-- <i class="iic4" @click="shareShow=true"></i> -->
		</footer>

		<!-- 评论 -->
		<!-- <div class="pd-mask" style="display: block;" v-show="commentsShow"></div>
      	<dl class="pd-botdlg2" v-show="commentsShow">
      		<dt><em @click="commentsShow=false;focus1 = false">取消</em><i @click="commentsShow=false">发布</i></dt>
      		<dd>
      			<input class="pd-txtarea1" placeholder="请输入内容" :focus='focus1' :auto-focus="true" :fixed="true" />
      		</dd>
      	</dl> -->

		<!-- 分享 -->
		<div class="pd-mask" v-show="shareShow" style="display: block;"></div>
		<div class="pd-botdlg3" v-show="shareShow">
			<div class="pd-botdlghd">
				<span>分享至</span>
			</div>
			<ul class="pd-ulbx1b">
				<li>
					<image src="~@/static/study/images/icoic1.png" alt="">
						<p>微信好友</p>
				</li>
				<li>
					<image src="~@/static/study/images/icoic2.png" alt="">
						<p>朋友圈</p>
				</li>
				<li>
					<image src="~@/static/study/images/icoic3.png" alt="">
						<p>QQ</p>
				</li>
				<li>
					<image src="~@/static/study/images/icoic4.png" alt="">
						<p>截图分享</p>
				</li>
			</ul>
			<button type="button" class="pd-cnlbtn" @click="shareShow=false">取消</button>
		</div>

	</body>
</template>

<script>
	// 引入接口
	import {
		journalismDETAIL,
		journalismADD_COMMENT,
		journalismADD_STUDY_SCORE,
		journalismADD_COLLECTION,
	} from "@/api/study-test.js";


	import VideoPlay from './VideoPlay.vue';

	import {
		IP_URL
	} from '@/common/config.js'
	export default {
		components: {
			VideoPlay
		},
		data() {
			return {
				videoUrl: '',
				commentsShow: false, //评论框
				shareShow: false, //分享框显示状态
				supportType: false, //是否点赞
				collectionType: false, //是否收藏
				XH: "", //当前文章序号
				jbxx: {
					recommend: [],
					comment: [],
				}, //文章基本信息
				plValue: "", //评论
				timer: null, //定时器
				LXMC: '', //文章类型
				reader: null //提交 阅读量 的定时器
			};
		},
		onLoad(option) {
			// 获取上个页面传过来的参数  本场考试的基本信息
			this.XH = option.XH;
		},
		mounted() {
			this.journalismDETAIL();

			// 30S 有效阅读一篇文章则调用增加积分接口
			this.timer = setTimeout(() => {
				let pam = {};
				pam.method = "article";
				pam.articleId = this.XH;
				journalismADD_STUDY_SCORE(pam).then((res) => {
					// 清除定时器
					clearTimeout(this.timer);
					this.timer = null;
					uni.showToast({
						title: "恭喜，积分+1",
					});

				});
			}, 30000);

		},
		beforeDestroy() {
			// 清除定时器
			clearTimeout(this.timer);
			this.timer = null;

			clearTimeout(this.reader);
			this.reader = null;
		},
		// 清除定时器
		onHide() {

		},
		methods: {
			// 收藏文章
			scClick() {
				let pam = {};
				pam.articleId = this.XH;
				console.log(this.collectionType);
				if (this.collectionType == true) {

					pam.state = "0";
					this.collectionType = false
				} else {
					pam.state = "1";
					this.collectionType = true
				}

				journalismADD_COLLECTION(pam).then((res) => {
					if (this.collectionType == true) {
						uni.showToast({
							title: "收藏成功!",
						});
					}
				});
			},
			// 点赞文章
			dzClick() {
				let pam = {};
				pam.method = "praise";
				pam.articleId = this.XH;
				if (this.supportType == true) {
					pam.state = "0";
					this.supportType = false
				} else {
					pam.state = "1";
					this.supportType = true
				}
				journalismADD_STUDY_SCORE(pam).then((res) => {
					if (this.supportType == true) {
						uni.showToast({
							title: "点赞成功!",
						});
					}
				});
			},
			// 发布评论点击
			plFbClick() {
				let pam = {};
				pam.articleId = this.XH;
				pam.content = this.plValue;
				journalismADD_COMMENT(pam).then((res) => {
					this.journalismDETAIL();
					uni.showToast({
						title: "评论成功!",
					});
					uni.hideKeyboard();
					this.plValue = "";
				});
			},
			// 给评论点赞
			plZanClick(item) {
				let pam = {};
				pam.commentId = item.XH;
				if (item.SFDZ == "1") {
					// 取消点赞
					pam.state = "0";
					item.SFDZ = "0";
					item.PLNUM = Number(item.PLNUM) - 1;
				} else {
					// 点赞
					pam.state = "1";
					item.SFDZ = "1";
					item.PLNUM = Number(item.PLNUM) + 1;
				}

				journalismADD_COMMENT(pam).then((res) => {});
			},
			// 获取文章详情
			journalismDETAIL() {
				let pam = {};
				pam.articleId = this.XH;
				journalismDETAIL(pam).then((res) => {
					this.jbxx = res.data_json;
					this.LXMC = this.jbxx.LXMC

					if (this.jbxx.SFDZ == "1") {
						this.supportType = true;
					}

					console.log(this.jbxx);
					if (this.jbxx.SFSC == "1") {
						this.collectionType = true;
					}

					if (this.jbxx.recommend.length > 3) {
						this.jbxx.recommend.splice(3, 100);
					}

					if (this.jbxx.comment == undefined) {
						this.jbxx.comment = [];
					}

					this.jbxx.comment.forEach((element) => {
						if (element.PLNUM) {} else {
							this.$set(element, "PLNUM", 0);
						}

						if (element.SFDZ) {} else {
							this.$set(element, "SFDZ", "0");
						}
					});

					// this.jbxx.WZNR 正则匹配视频地址

					const html = this.jbxx.WZNR;
					// 匹配视频标签
					const regex = /<video.*?src="(.*?)".*?<\/video>/gi;

					// 提取 src 属性的值
					const srcValues = [];
					let match;
					while ((match = regex.exec(html)) !== null) {
						srcValues.push(match[1]);
					}

					const newHtml = html.replace(regex, '');

					this.jbxx.WZNR = newHtml;

					if (srcValues.length > 0) {
						this.videoUrl = IP_URL + srcValues[0];
					} else {
						this.videoUrl = ''
					}

					// this.jbxx.videoUrl = srcValues[0];

					console.log(srcValues);

					this.$nextTick(() => {
						this.videoPlayOne()
					})

					// this.postReadCount()
				});
			},
			// 文章详情跳转
			wzClick(XH) {
				// 清除定时器
				clearTimeout(this.timer);
				this.timer = null;

				clearTimeout(this.reader);
				this.reader = null;

				uni.navigateTo({
					url: "/pages/study/knowledge/knowledgeDetails?XH=" + XH,
				});
			},
			// 跳转
			goClick(url) {
				// 清除定时器
				clearTimeout(this.timer);
				this.timer = null;

				clearTimeout(this.reader);
				this.reader = null;

				uni.navigateBack()
				// uni.navigateTo({
				//   url: url,
				// });
			},
			//播放一个视频，其他视频暂停
			videoPlayOne() {
				var videos = document.getElementsByTagName('video');
				for (var i = videos.length - 1; i >= 0; i--) {
					(function() {
						var p = i;
						videos[p].addEventListener('play', function() {
							pauseAll(p);
						})
					})()
				}

				function pauseAll(index) {
					for (var j = videos.length - 1; j >= 0; j--) {
						if (j != index) videos[j].pause();
					}
				};
			},
			//提交阅读量
			postReadCount() {
				let timeCount
				if (this.LXMC === '文章') {
					timeCount = 2000
				} else if (this.LXMC === '视频') {
					timeCount = 30000
				}
				clearTimeout(this.reader);
				this.reader = null;
				this.reader = setTimeout(() => {
					console.log('文章类型', this.LXMC);
					console.log('===定时时间===', timeCount);
					// postCount({
					// 	xh:this.XH
					// }).then((res) => {
					// 	// 清除定时器
					// 	clearTimeout(this.reader);
					// 	this.reader = null;
					// });
				}, timeCount)
			}
		},
	};
</script>

<style scoped>
	.video-container:focus {
		outline: none;
	}
</style>