<template>
  <body style="background: #f7f7f7">
    <header class="pd-header">
      <i
        class="goback"
        @click="goBack"
      ></i>
      <div class="pd-srh1a">
        <form action="javascript:void 0;">
          <input
            type="text"
            placeholder="搜索"
            v-model="searchText"
            @confirm="searchBack()"
          />
		   <!-- @confirm="tapToSearch('/pages/study/knowledge/knowledge')" -->
        </form>
      </div>
	  <span class="searchBtn" @click="searchBack">搜索</span>
    </header>

    <section class="pd-main" style="width: 100%">
      <div class="pd-inner" style="padding-bottom: 0">
        <div class="pd-mod">
          <div class="gap"></div>
          <div class="pd-tithd">
            <span>搜索历史</span>
            <image src="~@/static/study/images/delic.png" class="del"  @click="delClick"/>
          </div>
          <ul class="pd-ulbx3b">
            <li v-for="(item, index) in list" :key="index" @click="listClick( item )">{{ item }}</li>
          </ul>
        </div>
      </div>
    </section>
  </body>
</template>

<script>
export default {
  data() {
    return {
      searchText: "",
      list: [], // 搜索历史
    };
  },
  created() {
    uni.getStorage({
      key: "list",
      success:(e)=> {
        this.list = e.data;
      },
    });

  },
  methods: {
	searchBack(){
		this.list.unshift( this.searchText )
		if(this.list.length > 20){
		  this.list.pop()
		}
		uni.setStorage({
		  key: "list",
		  data: this.list,
		});
		
		uni.$emit('searchBack',this.searchText)
		uni.navigateBack({
			delta: 1
		});
	},
    listClick(item){
      this.searchText = item
      // uni.navigateTo({
      //   url:  "/pages/study/knowledge/knowledge?searchText=" + this.searchText,
      // });
	  
	  uni.$emit('searchBack',this.searchText)
	  uni.navigateBack({
	  	delta: 1
	  });
    },
    delClick(){
      this.list = [];
      uni.setStorage({
        key: "list",
        data: this.list,
      });
    },
    tapToSearch(url) {
      this.list.unshift( this.searchText )
      if(this.list.length > 20){
        this.list.pop()
      }
      uni.setStorage({
        key: "list",
        data: this.list,
      });
      uni.navigateTo({
        url: url + "?searchText=" + this.searchText,
      });
    },
    // 跳转
    goClick(url) {
      uni.navigateTo({
        url: url,
      });
    },
	goBack(){
		uni.navigateBack({
			delta: 1
		});
	}
  },
};
</script>

<style>
.pd-main {
  background-color: #fff;
}
</style>
<style scoped>
	.pd-srh1a{
		margin-right: 90rpx;
	}
	.searchBtn{
		position: absolute;
		right: 10rpx;
		top: 18rpx;
		color: #fff;
	}
</style>
