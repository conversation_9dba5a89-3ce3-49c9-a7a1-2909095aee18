<template>
  <body style="background: #f7f7f7">
    <header class="pd-header">
      <i class="goback" @click="goClick('/pages/study/myExam/myExam')"></i>
      <h1 class="pd-title">{{ jbxx.KSMC.length>18?jbxx.KSMC.slice(0,18)+'...':jbxx.KSMC }}</h1>
    </header>
    <section class="pd-main" style="width:100%">
      <div class="pd-inner" style="padding-bottom: 0">
        <div v-for="(item,index) in data" :key="index">
          <dl class="pd-dlbx4" style="word-wrap: break-word;word-break: break-all;overflow: hidden">
            <dt>{{ index +1 }} . {{ item.name }}</dt>
			
            <dd>
              <ul class="xuanxiang">
				  <div v-for="( itemOne,indexOne ) in item.choice" :key="indexOne" >
                  <li class="correct" style="word-wrap: break-word;word-break: break-all;overflow: hidden;padding-right: 50px;" v-show="item.isRight == '1' && itemOne.check == '1'  "> 
						{{ itemOne.text  }} 
				</li>
				<li class="error" style="word-wrap: break-word;word-break: break-all;overflow: hidden;padding-right: 50px;" v-show="item.isRight == '0' && itemOne.check == '1'  "> 
						{{ itemOne.text  }} 
				</li>
				<li style="word-wrap: break-word;word-break: break-all;overflow: hidden;padding-right: 50px;" v-show="itemOne.check != '1'  "> 
						{{ itemOne.text  }} 
				</li>
				</div>
              </ul>
            </dd>
            <dd>
				<p>
                <span>正确答案：{{ item.zqda }}</span>
              </p>
              <p>
                 <span>解析：</span> {{ item.ztjx }}
              </p>
            </dd>
          </dl>
          <div class="gap"></div>
        </div>
      </div>
    </section>
  </body>
</template>

<script>
import { detailsExamination } from "@/api/study-test.js";
export default {
  data() {
    return {
      jbxx: {}, // 考试基本信息
      data: [],
    };
  },
  onLoad(option) {
    // 获取上个页面传过来的参数  本场考试的基本信息
    this.jbxx = JSON.parse(decodeURIComponent(option.obj));
	if(!this.jbxx.KSMC){
		this.jbxx.KSMC = ''
	}
  },
  mounted() {
    this.detailsExamination();
  },
  methods: {
    // 获取数据
    detailsExamination() {
      let pam = {};
      pam.examinationId = this.jbxx.XH;
      detailsExamination(pam).then((res) => {
        // 合并数组
        let array = res.data_json.DXT.concat(res.data_json.FXT);
        this.data = array.concat(res.data_json.PDT);
		// 找到正确答案
		for(let i= 0;i<this.data.length;i++){
			
			this.data[i].zqda = '';
			for(let j =0;j<this.data[i].choice.length;j++){
				
				if(this.data[i].choice[j].isRight == '1'){
					this.data[i].zqda += this.data[i].choice[j].text.slice(0,1) +' '
				}
				
			}
		}
		
      });
    },
    // 跳转
    goClick(url) {
      uni.navigateTo({
        url: url,
      });
    },
  },
};
</script>

<style>
</style>
