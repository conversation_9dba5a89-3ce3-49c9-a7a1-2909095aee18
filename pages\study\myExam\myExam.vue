<template>
	<body style="background: #f7f7f7;">
    <header class="pd-header">
        <i class="goback" @click="goClick('/pages/study/study')"></i>
        <h1 class="pd-title">我的考试</h1>
    </header>
    <section class="pd-main" style="width:100%">
        <div class="pd-inner">
            <ul class="zy-lianbing">
                <li v-for="(item,index) in data" :key="index" @click="myExamClick(item)">
                    <div class="line1">{{ item.KSMC }}</div>
                    <div class="line2">
                        <p class="p1">最终得分：<span>{{ item.FS }}</span></p>
                        <p class="p2">开始时间：{{ item.CJSJ }}</p>
                    </div>
                </li>
            </ul>
        </div>
    </section>

</body>
</template>

<script>
	import { myExamination } from '@/api/study-test.js'
	export default {
		data() {
			return {
				data:[]
			}
		},
		mounted(){
			this.myExamination()
		},
		methods: {
			// 获取 我的考试列表
			myExamination(){
				myExamination({}).then(res=>{
					this.data = res.data_json
				})
			},
			// 点击查看详情
			myExamClick(item){
				uni.navigateTo({
                   url: '/pages/study/myExam/details/details?obj=' + encodeURIComponent(JSON.stringify(item)),
              })
			},
			// 跳转
    goClick(url) {
      uni.navigateTo({
        url: url,
      });
    },
		}
	}
</script>

<style>

</style>
