<template>
	<body style="background: #f5fafd">
		<header class="pd-header xxks">
			<!-- <i class="goback" @click="goClick('/pages/main/portal')"></i> -->
			<h1 class="pd-title">执法云学堂</h1>
			<div class="gap"></div>
			<div class="gap"></div>
			<div class="gap"></div>
			<dl class="pd-dlbx3">
				<dt>
					<image src="~@/static/study/images/hdpic1.png" alt="" />
				</dt>
				<dd>
					<h1>{{studyTestData.NAME}}<em>{{studyTestData.JS}}</em></h1>
					<!-- <p><small>积分排名：{{studyTestData.JFPM}}</small></p> -->
				</dd>
				<!-- <a class="phb" @click="goClick('/pages/study/integral/integral')"></a> -->
			</dl>
		</header>
		<section class="pd-main" style="width:100%">
			<div class="pd-inner xxks">
				<ul class="pd-ulbx1a">
					<!-- <li>
						<h1>{{studyTestData.JF}}</h1>
						<p>积分</p>
					</li> -->
					<li @click="goClick('/pages/study/collect/collect')">
						<h1>{{studyTestData.SC}}</h1>
						<p>收藏</p>
					</li>
					<li @click="goClick('/pages/study/myExam/myExam')">
						<h1>{{studyTestData.KS}}</h1>
						<p>考试</p>
					</li>
				</ul>
				<div class="gap"></div>
				<div class="gap"></div>
				<ul class="pd-ulbx5 otr">
					<li @click="goClick('/pages/study/knowledge/knowledge')">
						<image src="@/static/study/images/cdic1.png" alt="" />
						<p>知识学习</p>
					</li>
					<li>
						<image src="~@/static/study/images/cdic2.png" alt=""
							@click="goClick('/pages/study/studyList/studyList')" />
						<p>知识竞赛</p>
						<i class="tipnum" v-show="data_lsit.length>0">{{data_lsit.length}}</i>
					</li>
					<li @click="goClick('/pages/study/wrong/wrongQuestion')">
						<image src="~@/static/study/images/cdic3.png" alt="" />
						<p>错题库</p>
					</li>
					<!-- <li>
						<image src="~@/static/study/images/cdic4.png" @click="ywjlClick()" />
						<p>业务交流</p>
					</li> -->
				</ul>
			</div>
		</section>
	</body>
</template>

<script>
	import {
		queryStudyTest,
		journalismADD_STUDY_SCORE,
		getExamination
	} from '@/api/study-test.js'
	export default {
		data() {
			return {
				YHID: '',
				studyTestData: {},
				data_lsit:'',
				tip:0
			};
		},
		mounted() {
			this.YHID = uni.getStorageSync('userInfo').id
			
		},

		onShow(){
			this.getData()
		},

		destroyed() {
			uni.$off('overFinish')
			uni.$off('overFinishTip')
		},
		

		methods: {
			getData() {
				// 登路学习模块 增加积分

				journalismADD_STUDY_SCORE({
					method: 'login'
				}).then(res => {
					// 获取信息
					this.getBaseData()
				})

			},
			getBaseData(){
				queryStudyTest({
					YHID: this.YHID
				}).then(res => {
					this.studyTestData = res.data_json	
				})
				getExamination({}).then(res => {
					this.data_lsit = res.data_json
					if(this.tip){
						uni.showToast({
							title: '因中途退出，考试结束，请在考试里查看得分',
							icon: 'none',
							duration:3000
						});
						this.tip = 0
					}
					
				})
			},
			ywjlClick() {
				uni.showToast({
					title: '开发中...',
					icon: 'loading'
				});
			},

			// 跳转
			goClick(url) {
				uni.$once('overFinish', src => {
					this.getBaseData()
				});
				
				uni.$once('overFinishTip', src => {
					this.tip = 1
					this.getBaseData()
				});
				
				uni.navigateTo({
					url: url,
				});
			},
			
		},
	};
</script>

<style scoped>
	.tipnum{
		position: absolute;
		top: 80rpx;
	}
</style>
