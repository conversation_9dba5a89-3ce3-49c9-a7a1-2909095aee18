<template>
	<body style="background: #f7f7f7">
		<header class="pd-header">
			<i class="goback" @click="goClick('/pages/study/study')"></i>
			<h1 class="pd-title">当前考试</h1>
		</header>
		<section class="pd-main" style="width:100%">
			<div class="pd-inner">
				<ul class="zy-lianbing">
					<li v-for="( item,index ) in data" :key="index">
						<div class="line1">{{ item.KSMC }} </div>
						<div class="line2">
							<p class="p2">开始时间：<span>{{ item.KSKSSJ }}</span></p>
						</div>
						<div class="line2">
							<p class="p2">结束时间：<span>{{ item.KSJSSJ }}</span></p>
						</div>
						<div class="line2">
							<p class="p1">考题数量：<span>{{ item.KTSL }}</span></p>
							<p class="p2">考试时长：{{ item.KSSC }} 分钟</p>
						</div>

						<span class="btn" @click="beginClick(item)">开始考试</span>
					</li>
				</ul>
			</div>
		</section>
	</body>
</template>

<script>
	import {
		getExamination,
		QUERY_EXAMINATION_CHECK
	} from '@/api/study-test.js'
	export default {
		data() {
			return {
				data: [], //考试列表数据
			};
		},
		mounted() {
			getExamination({}).then(res => {
				this.data = res.data_json
			})
		},
		methods: {
			beginClick(item) {
				let ksDate = new Date(item.KSKSSJ);
				let dqDate = new Date()
				if (dqDate.getTime() < ksDate.getTime()) {
					return uni.showToast({
						title: "考试未开始，请稍后",
						icon: 'none'
					})
				}
				
				uni.navigateTo({
					url: '/pages/study/bt/bt?obj=' + encodeURIComponent(JSON.stringify(item)),
				})
				
				// let pam = {}
				// pam.SJXH = item.XH
				// pam.YHID = uni.getStorageSync('userInfo').id
				// QUERY_EXAMINATION_CHECK(pam).then(res => {
				// 	if (res.msg == '当前用户未参加考试') {
				// 		uni.navigateTo({
				// 			url: '/pages/study/bt/bt?obj=' + encodeURIComponent(JSON.stringify(item)),
				// 		})
				// 	} else {
				// 		uni.showToast({
				// 			title: "当前用户该考试已结束",
				// 			icon: 'none'
				// 		})

				// 	}
				// })

			},
			// 跳转
			goClick(url) {
				uni.navigateBack({
					delta: 1
				});
				// uni.navigateTo({
				// 	url: url,
				// });
			},
		},
	};
</script>

<style>
</style>
