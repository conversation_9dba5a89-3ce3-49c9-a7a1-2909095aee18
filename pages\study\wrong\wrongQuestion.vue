<template>
  <body style="background: #f7f7f7">
    <header class="pd-header">
      <i class="goback" @click="goBack()"></i>
      <h1 class="pd-title">错题库</h1>
      <i class="ic-search" @click="textShow = true"></i>
    </header>
    <header class="pd-header" v-show="textShow">
      <i
        class="goback"
        @click="
          textShow = false;
          text = '';
          errorExamination()
        "
      ></i>
      <div class="pd-srh1a">
        <input type="text" placeholder="搜索" v-model="text" :radius="50"  @change="errorExamination()" @input="errorExamination()"/>
      </div>
    </header>
    <section class="pd-main" style="width:100%">
      <div class="pd-inner" style="padding-bottom: 0">
        <ul class="pd-ulbx6">
          <li>
            <h1 class="zy-w1">题型：</h1>
            <p>
              
              <i
                :class="{ on: DX }"
                @click="
                  
                  DX = true;
                  FX = false;
                  PD = false;
                  errorExamination()
                "
                >单选题</i
              ><i
                :class="{ on: PD }"
                @click="
                  
                  DX = false;
                  FX = false;
                  PD = true;
                  errorExamination()
                "
              >
                判断题</i
              ><i
                :class="{ on: FX }"
                @click="
                  
                  DX = false;
                  FX = true;
                  PD = false;
                  errorExamination()
                "
                >多选题</i
              >
            </p>
          </li>
        </ul>
        <div class="gap"></div>
        <div v-for="(item, index) in data" :key="index">
          <dl
            class="pd-dlbx4"
            style="
              word-wrap: break-word;
              word-break: break-all;
              overflow: hidden;
            "
          >
            <dt>{{ item.name }} （ ）</dt>
            <dd>
              <ul class="xuanxiang">
                <li
                  :class="itemOne.isRight == '1' ? 'correct' : 'error'"
                  v-for="(itemOne, indexOne) in item.choice"
                  :key="indexOne"
                  style="
                    word-wrap: break-word;
                    word-break: break-all;
                    overflow: hidden;
                    padding-right: 50px;
                  "
                >
                  {{ itemOne.text }}
                </li>
              </ul>
            </dd>
            <dd>
              <p>
                错误次数：<span style="color: red">{{ item.errorNum }}</span>
              </p>
            </dd>
            <dd>
              <p>
                解析：{{ item.ztjx }}
              </p>
            </dd>
          </dl>
          <div class="gap"></div>
        </div>
      </div>
    </section>
  </body>
</template>

<script>
import { errorExamination } from "@/api/study-test.js";
export default {
  data() {
    return {
      textShow: false, // 搜索框显示与隐藏
      text: "", //搜索框
      DX: true,
      FX: false,
      PD: false,
      data: [], // 数据源
      dxData: [], // 单选题数据
      fxData: [], // 复选题数据
      pdData: [], //判断题数据
    };
  },
  mounted() {
    this.errorExamination();
  },
  methods: {
    // 获取数据
    errorExamination() {
      let pam = {};
      pam.searchText = this.text
      errorExamination(pam).then((res) => {
        if(res.data_json.success == false){
          this.data = []
          return
        }
        if( this.DX == true ){
          this.data = res.data_json.DXT
        }else if(this.FX == true){
          this.data = res.data_json.FXT
        }else if(this.PD == true){
          this.data = res.data_json.PDT
        }
        
      });
    },
    // 跳转
    goClick(url) {
      uni.navigateTo({
        url: url,
      });
    },
	goBack(){
		uni.navigateBack({
			delta: 1
		});
	}
  },
};
</script>

<style>
</style>
