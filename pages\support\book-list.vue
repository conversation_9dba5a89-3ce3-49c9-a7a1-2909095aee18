<template>
    <Page :padding="false" title="帮扶台账">
        <section class="pd-main">
            <div>
                <uni-search-bar
                    placeholder="请输入想要查询的污染源企业"
                    cancelButton="none"
                    cancelText=""
                    clearButton="always"
                    bgColor="#F7F7F7"
                    :radius="50"
                    @input="searchByKeyword"
                />
                <view class="search-time">
                    <!-- <dropdown-menu v-if="taskTypeOptions.length > 0">
						<dropdown-item :list="taskTypeOptions"
						               v-model="taskTypeIndex" />
						<dropdown-item :list="deadlineTypeOptions"
						               v-model="deadlineTypeIndex" />
					</dropdown-menu> -->
                    检查日期：
                    <uni-tag
                        type="default"
                        :text="time ? time : '请选择检查时间'"
                        @click="$refs.timePicker.open()"
                        style="
                            display: inline-block;
                            margin: 4rpx;
                            width: 35%;
                            text-align: center;
                        "
                    ></uni-tag>
                </view>
                <div class="gap"></div>
                <dl class="pd-dlbx1">
                    <dt>
                        <span
                            >台账总数：<i>（{{ total }}）</i></span
                        >
                    </dt>
                </dl>
                <scroll-view
                    :scroll-y="true"
                    @scrolltolower="loadMore"
                    :style="pageListStyle"
                >
                    <dl
                        class="pd-dlbx1"
                        v-for="(book, index) in books"
                        @click="doRecordTask(book)"
                        :key="book.id"
                    >
                        <dd>
                            <image
                                style="z-index: 0"
                                src="/static/app/images/lstic1a.png"
                                class="imgic"
                            ></image>
                            <h2>
                                {{ book.WRYMC }}
                                <span class="mk" v-if="book.RWLXMC">
                                    {{ book.RWLXMC }}
                                </span>
                            </h2>
                            <!-- <p><em>执法人员：</em>{{ book.ZFRYMC }}</p> -->
                            <p><em>检查日期：</em>{{ book.KSSJ || '--' }}</p>
                            <small></small>
                        </dd>
                    </dl>
                    <NoData
                        v-if="!pageShow"
                        :type="books.length < 1 ? 'message' : ''"
                    />
                </scroll-view>
            </div>
        </section>

        <uni-calendar
            ref="timePicker"
            :insert="false"
            :date="time"
            @confirm="timeConfirm"
        />
    </Page>
</template>

<script>
import NoData from '@/components/no-data.vue';
import dropdownMenu from '@/components/ms-dropdown/dropdown-menu.vue';
import dropdownItem from '@/components/ms-dropdown/dropdown-item.vue';
import Page from '@/pages/component/Page.vue';
import { getWeatherBookList } from '@/api/record.js';
import uriUtil from '@/common/net/uri.js';
import loginService from '@/api/login-service.js';
import { postQueryWryjcTz } from '@/api/book.js';
export default {
    components: {
        Page,
        NoData,
        dropdownMenu,
        dropdownItem
    },

    data() {
        return {
            title: '执法台账',
            pageHeight: -1,
            time: '',
            books: [],
            total: '',

            lastPageIndex: null, //最大页面数
            pageNum: 1, //当前的页面数量
            pageLoadType: true, //页面可滚动的状态
            pageShow: true, //页面显示的状态
            searchValue: '' //污染源关键字
        };
    },

    watch: {},

    computed: {
        //计算页面可滚动距离的高度
        pageListStyle: function () {
            return {
                height: 'calc(100vh - 390upx)'
            };
        }
    },

    onLoad(options) {
        let data = uni.getStorageSync('user_info');
    },

    mounted() {
        //获取固定编码-任务类型的值（已缓存过）
        let self = this;
        this.loadBookList();
    },

    methods: {
        //获取台账的所有数据
        loadBookList() {
            this.pageShow = true;
            this.pageLoadType = false;
            getWeatherBookList({
                params: {
                    searchText: this.searchValue,
                    JCR: loginService.getAuthUserId() || 'SDSZFJ',
                    RWLX: '1'
                }
                // pageSize: 20,
                // pageNum: this.pageNum
            })
                .then(res => {
                    this.books.push(...res.data_json.list);
                    this.lastPageIndex = res.data_json.lastPage;
                    this.total = res.data_json.total;
                    this.pageLoadType = true;
                    if (this.books.length < 1) {
                        this.pageShow = false;
                    }
                })
                .catch(res => {
                    this.pageLoadType = true;
                    this.pageShow = false;
                });
        },

        timeConfirm(dateInfo) {
            this.time = dateInfo.fulldate;
            // this.loadBookList()
        },

        //页面跳转-跳转至台账详情页面
        doRecordTask(item) {
            let params = {
                // workflowId: item.LCBH,
                taskId: item.YWXTBH,
                recordId: item.XH,
                type: 'done'
            };
            uni.navigateTo({
                url: `/pages/support/support-detail?${uriUtil.transformObjectToUrlParams(
                    params
                )}`
            });
        },

        //关键字搜索
        searchByKeyword(parms) {
            this.searchValue = parms;
            this.books = [];
            this.loadBookList();
        },

        //滚动到下一页
        loadMore() {
            if (this.pageLoadType) {
                this.pageNum++;
                if (this.pageNum > this.lastPageIndex) {
                    uni.showToast({
                        title: '已经没有数据了',
                        duration: 2000,
                        icon: 'none'
                    });
                } else {
                    this.loadBookList();
                }
            }
        },

        //返回上一级
        backPage() {
            uni.navigateBack({
                delta: 1
            });
        }
    }
};
</script>

<style scoped>
.book {
    width: 100%;
    background-color: #f1f2f6;
}

.mk {
    background-color: #009bff;
    font-size: 22rpx;
    padding: 2rpx 2rpx;
    color: #fff;
    border-radius: 4rpx;
    margin-left: 2rpx;
}
.search-time {
    display: flex;
    position: relative;
    z-index: 99;
    justify-content: space-around;
    align-items: center;
}
</style>
