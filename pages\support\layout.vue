<template>
	<view style="width: 100%;">
		<header class="pd-header idx">
			<div class="pd-name" @click="back()"></div>
			<image src="../../static/img/logo_water_mark2.png" class="pd-logo1" />
			<div class="gap"></div>
			<div class="gap"></div>
			<div class="gap"></div>
		</header>

		<section class="pd-main">
			<show-modal></show-modal>
			<div class="pd-inner idx">
				<scroll-view :scroll-y="false" :style="pageListStyle">
					<ul class="pd-ulbx4">
						<!-- <li @click="openMenu('/pages/task/dispatch-task')">
							<image src="../../static/login/images/ulic1.png" alt="">
								<p>任务指派</p>
						</li> -->
						<li @click="openMenu('/pages/support/task-list?taskType=1')">
            <i v-if="taskNum>0">{{taskNum}}</i>
							<image src="@/static/login/images/ulic2.png" alt="">
								<p>帮扶任务</p>
						</li>
						<li @click="openMenu('/pages/support/task-list?taskType=2')">
            <i v-if="selfNum>0">{{selfNum}}</i>
							<image src="@/static/login/images/ulic1.png" alt="">
								<p>日常帮扶</p>
						</li>

						<li @click="openMenu('/pages/support/book-list')">
							<image src="@/static/login/images/ulic6.png" alt="">
								<p>帮扶台账</p>
						</li>
					</ul>
				</scroll-view>
			</div>
		</section>
	</view>
</template>

<script>

	import iconAvatar from '@/static/img/icon_avatar.png'
	import iconLogo from '@/static/img/logo.png'
	import {
		queryXcjcRwsl
	} from '@/api/predit.js'

	export default {
		components: {

		},

		data() {
			return {
        taskNum:0,
        selfNum:0,
				areaCode: '', //地区行政码
				userName: '', //用户名
				iconAvatar,
				iconLogo,
				// screenWidth: -1,
				taskTypeOptions: [], //任务类型集合
			}
		},

		computed: {
			pageListStyle() {
				return {
					height: 'calc(100vh - 580rpx)'
				};
			}
		},

		onShow() {
      queryXcjcRwsl().then(res => {
          this.taskNum = res.data_json.jddbrw;
          this.selfNum = res.data_json.jdrcjc;
			})
		},

		mounted() {
			let userInfo = uni.getStorageSync('userInfo');
			if (userInfo) {
				this.userName = userInfo.name;
				this.areaCode = userInfo.orgid.substring(0, 4)
			}
		},

		methods: {

			//跳转菜单相应页面
			openMenu(url) {
				uni.navigateTo({
					url: url
				})
			},

			back(){
				uni.navigateBack({
					delta:1
				})
			}
		}
	}
</script>

<style scoped>
	.upfont {
		font-size: 2rem;
	}

	.pd-ulbx4 li {
		position: relative;
	}

	.pd-ulbx4 li i {
		position: absolute;
		left: 50%;
		top: 13%;
		min-width: 15px;
		width: auto;
		height: 15px;
		border-radius: 50px;
		background: #e86767;
		font-size: 21.7391249rpx;
		color: #fff;
		text-align:
			center;
		line-height: 28.985475rpx;
		margin-left: 36.2319rpx;
	}

	.pd-ulbx4 li:active {
		/* background-color: #0FAEFF; */
		color: #fff;
	}

	.uni-input-placeholder {
		color: white;
	}

	.portal {
		width: 100%;
		height: 100%;
		position: relative;
	}
	.pd-inner.idx{
		margin-top: 80rpx;
		padding-top:350rpx;
		position: relative;
		z-index: 1000;
	}
	.pd-name{
		height: 40rpx;
		background-image: url(@/static/img/icon_back_white.png);
	}
	.pd-header.idx{
		background-image: url(@/static/app/images/bg1a2.png);
	}
</style>
