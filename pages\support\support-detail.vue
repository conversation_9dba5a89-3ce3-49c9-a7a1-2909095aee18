<!--
 * @Author: 姚进玺 <EMAIL>
 * @Date: 2022-09-23 18:02:03
 * @LastEditors: 姚进玺 <EMAIL>
 * @LastEditTime: 2022-10-11 12:01:41
 * @FilePath: /YDZF_APP/views/manageSide/workbench-jd/TaskStart.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
    <Page :padding="false" title="任务详情">
        <view class="task-info__select-wry">
            <input
                class="task-campany-input"
                v-model="pollutionName"
                :disabled="!editable"
                placeholder="请选择污染源"
            />
            <!-- <input v-else value="请选择污企业" /> -->
            <image
                v-if="editable"
                class="task-info__select-icon"
                @click="chooseCompany"
                :src="nextIcon"
            />
        </view>
        <DynamicForm
            v-if="dynamicFormStatus"
            ref="dynamicForm"
            :refName="refName"
            :templateId="templateId"
            :exteriorRecordId="recordId"
            :editable="editable"
            :formHeight="pageHeight"
            :changeFormStatus="changeFormStatus"
            :attachFormData="attachFormData"
            @onFormDataUpdate="onFormDataUpdate"
            @returnCheckRequireForm="returnCheckRequireForm"
            @formSaveSuccuss="saveSuccuss"
            @formSaveFail="saveFail"
            @getCraetRcordId="getRecordId"
            @returnTemplate="returnTemplate"
            @returnFormDataInfo="returnFormData"
            @getFormDataInfo="getFormDataInfo"
        />
        <PCard title="监督帮扶现场问题表" v-if="questionStatus">
            <template slot="option" v-if="editable">
                <view @click="addQuestion" class="support-add">
                    <view>新增问题</view>
                    <image class="support-add-icon" :src="iconMapAdd" />
                </view>
            </template>
            <scroll-view :scroll-y="true" :style="questionStyle">
                <view
                    v-show="questionList.length > 0"
                    class="support-list"
                    v-for="(item, index) in questionList"
                    :key="index"
                >
                    <view>{{ item.WTLBMC || '--' }}</view>
                    <view
                        class="support-list-info"
                        @click="editQuestion(item, index)"
                        >查看</view
                    >
                </view>
            </scroll-view>
        </PCard>
        <view v-if="editable" class="flex-row-layout task-detail__bottom-bar">
            <p-button
                v-for="(menu, index) in menus"
                :key="index"
                class="task-detail__bottom-btn"
                :style="{ background: menu.bg }"
                :name="menu.name"
                @click.native="clickMutableMenu(menu)"
            />
        </view>
    </Page>
</template>

<script>
import nextIcon from '@/static/img/navi_next_icon.png';
import { guid } from '@/common/uuid.js';
import styleUtil from 'bowo-sdk/util/style.js';
import Page from '@/pages/component/Page.vue';
import PCard from '@/pages/component/PCard.vue';
import { postCheckQuestionList, postTransferOrCheck } from '@/api/record.js';
import iconMapAdd from '@/static/online/images/ic_mapadd.png';
import PButton from '@/components/p-button';
import formService from '@/node_modules/bowo-form/components/service/form-service.js';
// import nextIcon from '@/static/img/navi_next_icon.png';
export default {
    components: {
        Page,
        PCard,
        PButton
    },
    data() {
        return {
            refName: 'test1',
            changeFormStatus: false,
            attachFormData: {},
            checkFormStatus: '', //表单提交的状态，1为暂存；2为提交
            template: {},
            pollutionName: '',
            iconMapAdd,
            nextIcon,
            editable: true,
            templateId: '20221001113849405bf017cf0b42d89b46b9f01e1228c6',
            recordId: '',
            pageHeight: null,
            taskType: '',
            questionList: [],
            menus: [],
            questionStatus: false,
            dynamicFormStatus: true,
            submitFormDataInfo: {},
            menus1: [
                {
                    name: '暂存',
                    type: 'cache',
                    bg: '#666'
                },
                {
                    name: '转办',
                    type: 'transfer',
                    bg: '#00cf6f'
                },
                {
                    name: '办结',
                    type: 'finish',
                    bg: '#009bff'
                }
            ],
            menus2: [
                {
                    name: '暂存',
                    type: 'cache',
                    bg: '#666'
                },
                {
                    name: '办结',
                    type: 'finish',
                    bg: '#009bff'
                }
            ]
        };
    },
    onShow() {
        this.getCheckQuestionList();
    },

    computed: {
        questionStyle() {
            return {
                height: '300rpx'
            };
        }
    },

    watch: {
        questionStatus() {
            this.getFormHeight();
        },

        pollutionName(val) {
            this.attachFormData.WRYMC = val;
        }
    },

    onLoad(options) {
        this.editable = options.type === 'done' ? false : true;
        this.templateId = '20221001113849405bf017cf0b42d89b46b9f01e1228c6';
        this.recordId = options.recordId;
        this.taskType = options.taskType;
        this.menus = options.taskType === '2' ? this.menus2 : this.menus1;
        if (options.recordId) {
            this.recordId = options.recordId;
            // this.getData();
        } else {
            // this.getTemplate();
            // this.recordId = guid();
        }
    },

    mounted() {
        this.getFormHeight();
        uni.$on('changeQuestion', res => {
            this.dynamicFormStatus = true;
        });
    },

    methods: {
        getFormHeight() {
            styleUtil.getPageLayout().then(layout => {
                let height = layout.height;
                if (!this.editable) {
                    height = height + uni.upx2px(80);
                }
                if (this.questionStatus) {
                    this.pageHeight = height - uni.upx2px(655);
                } else {
                    this.pageHeight = height - uni.upx2px(285);
                }
                // }
            });
        },

        onFormDataUpdate(data) {
            if (data.SFCZHJWT === '1') {
                this.questionStatus = true;
            } else {
                this.questionStatus = false;
            }
            this.submitFormDataInfo = data;
        },

        returnFormData(data) {
            if (data.WRYMC) {
                this.pollutionName = data.WRYMC;
            }
            if (data.SFCZHJWT === '1') {
                this.questionStatus = true;
            } else {
                this.questionStatus = false;
            }
            this.submitFormDataInfo = data;
        },

        chooseCompany() {
            uni.$on('choosePath', res => {
                this.pollutionName = res.WRYMC || '';
                this.attachFormData.WRYMC = res.WRYMC || '';
                this.attachFormData.WRYBH = res.WRYBH || '';
                this.attachFormData.WRYDZ = res.WRYDZ || '';
                this.attachFormData.SSDS = res.SSDS || '';
                this.attachFormData.SSQX = res.SSQX || '';
                this.attachFormData.TYSHXYDM = res.TYSHXYDM || '';
                this.changeFormStatus = !this.changeFormStatus;
            });
            uni.navigateTo({
                url: `/pages/wry/wry-list`
            });
        },

        returnTemplate(template) {
            this.template = template;
        },

        getFormDataInfo(data) {
            this.submitFormDataInfo = data;
            this.postFormData();
        },

        addQuestion() {
            let index = this.questionList.length + 1;
            this.dynamicFormStatus = false;
            this.onSaveForm();
            uni.navigateTo({
                url: `/pages/support/support-question-detail?ywxtbh=${
                    this.recordId
                }&listIndex=${index}&pageType=${'1'}&recordId=`
            });
        },

        editQuestion(item, index) {
            this.dynamicFormStatus = false;
            this.onSaveForm();
            uni.navigateTo({
                url: `/pages/support/support-question-detail?ywxtbh=${
                    this.recordId
                }&listIndex=${index + 1}&pageType=${'0'}&editable=${
                    this.editable
                }&recordId=${item.XH}`
            });
        },

        getCheckQuestionList() {
            postCheckQuestionList({
                XH: this.recordId
            }).then(res => {
                // console.log(this.questionStatus);
                this.questionList = res.data_json;
            });
        },

        onSaveForm() {
            formService.submitFormData(
                this.template,
                this.recordId,
                this.submitFormDataInfo
            );
        },

        submitForm() {
            if (this.pollutionName) {
                this.$refs.dynamicForm.submitFormData();
            } else {
                uni.showToast({
                    title: '请选择污染源',
                    icon: 'error'
                });
            }
        },

        clickMutableMenu(item) {
            if (item.type === 'transfer') {
                uni.navigateTo({
                    url: `/pages/support/support-transfer?recordId=${this.recordId}`
                });
            } else if (item.type === 'cache') {
                this.checkFormStatus = '1';
                this.$refs.dynamicForm.getFormData();
                // this.postFormData();
            } else if (item.type === 'finish') {
                this.checkFormStatus = '0';
                this.$refs.dynamicForm.checkRequireForm();
            }
        },

        returnCheckRequireForm(status) {
            if (status) {
                this.postFormData();
            }
        },

        postFormData() {
            uni.showLoading({
                title: '保存中'
            });
            this.submitFormDataInfo.SFZC = this.checkFormStatus;
            this.submitFormDataInfo.XH = this.recordId;
            if (this.pollutionName) {
                this.submitFormDataInfo.WRYMC = this.pollutionName;
            }
            if (this.taskType === '2') {
                this.submitFormDataInfo.SFJS = '0';
                this.submitFormDataInfo.RWLX = '2';
            }
            formService
                .submitFormData(
                    this.template,
                    this.recordId,
                    this.submitFormDataInfo
                )
                .then(res => {
                    if (this.checkFormStatus === '0') {
                        let prams = {
                            XH: this.recordId,
                            SFJS: '1',
                            DQZT: '2'
                        };
                        if (this.taskType === '2') {
                            prams.RWLX = '2';
                        }
                        postTransferOrCheck(prams).then(() => {
                            uni.hideLoading();
                            uni.showToast({
                                title: `办结成功`,
                                duration: 1000,
                                icon: 'none'
                            });
                            setTimeout(() => {
                                uni.navigateBack({
                                    delta: 1
                                });
                            }, 1000);
                        });
                    } else {
                        uni.hideLoading();
                        uni.showToast({
                            title: `暂存成功`,
                            duration: 1000,
                            icon: 'none'
                        });
                        setTimeout(() => {
                            uni.navigateBack({
                                delta: 1
                            });
                        }, 1500);
                    }
                });
        },

        saveSuccuss() {
            uni.showToast({
                title: '发起任务成功'
            });
            setTimeout(() => {
                uni.navigateBack({
                    delta: 1
                });
            }, 1500);
        },

        saveFail() {
            uni.showToast({
                title: '发起任务失败',
                icon: 'error'
            });
        },

        getRecordId(id) {
            this.recordId = id;
            this.getCheckQuestionList();
        }
    }
};
</script>

<style scoped>
.task-info__select-wry {
    width: calc(100% - 20px);
    height: 110rpx;
    line-height: 110rpx;
    background-color: #fff;
    font-size: 34rpx;
    color: #999;
    padding: 0 22rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.task-info__select-icon {
    width: 34rpx;
    height: 34rpx;
}

.support-add {
    display: flex;
    justify-content: center;
    align-items: center;
}

.support-add-icon {
    width: 40rpx;
    height: 40rpx;
}

.support-list {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 14rpx;
}

.support-list-info {
    color: #009bff;
}

.task-detail__bottom-bar {
    position: fixed;
    /* height: 50px; */
    font-size: 18px;
    bottom: 0;
}

.task-campany-input {
    color: #333;
    width: 80%;
}

.task-detail__bottom-btn {
    flex: 1;
    position: relative;
}
</style>
