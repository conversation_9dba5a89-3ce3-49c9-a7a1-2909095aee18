<template>
    <Page title="监督帮扶现场问题表" :padding="false">
        <template v-slot:bar>
            <NaviBar title="监督帮扶现场问题表" />
        </template>

        <view class="template-form-layout" id="formParent">
            <DynamicForm
                ref="dynamicForm"
                :templateId="templateId"
                :exteriorRecordId="recordId"
                :editable="editable"
                :formHeight="pageHeight"
                @onFormDataUpdate="onFormDataUpdate"
                @returnTemplate="returnTemplate"
                @getFormDataInfo="getFormDataInfo"
                @returnCheckRequireForm="returnCheckRequireForm"
            />
        </view>
        <show-modal></show-modal>
        <view v-if="editable" class="flex-row-layout task-detail__bottom-bar">
            <PButton
                name="删除"
                v-if="pageType === '0'"
                @click.native="deleteTask"
                style="background: #ff9300"
                class="task-detail__bottom-btn"
            />
            <PButton
                name="保存"
                style="background: #00cf6f"
                @click.native="postFormAxios"
                class="task-detail__bottom-btn"
            />
        </view>
    </Page>
</template>

<script>
import iconSave from '@/static/img/record/icon_form_save.png';
import NoData from '@/components/no-data.vue';
import Page from '@/pages/component/Page.vue';
import NaviBar from '@/pages/component/NaviBar.vue';
import { guid } from '@/common/uuid.js';
import { deleteQuestionList } from '@/api/record.js';
import styleUtil from 'bowo-sdk/util/style.js';
import formService from '@/node_modules/bowo-form/components/service/form-service.js';
import PButton from '@/components/p-button';

export default {
    components: {
        Page,
        NaviBar,
        NoData,
        PButton
    },

    data() {
        return {
            listIndex: null,
            submitFormDataInfo: {},
            recordId: '',
            templateId: '',
            template: {},
            formData: {},
            editable: true,
            pageHeight: 600,
            YWXTBH: '',
            pageType: '0',
            menus: [
                {
                    name: '保存',
                    icon: iconSave
                }
            ]
        };
    },

    destroyed() {
        uni.$emit('changeQuestion');
    },

    computed: {
        formLayoutHeight: function () {
            return this.pageHeight - 40;
        }
    },

    onLoad(options) {
        this.$store.state.verifyList = [];
        this.editable = options.editable === 'false' ? false : true;
        this.templateId = '202210011207562e5a0c056733400d91afe080aa00e173';
        this.YWXTBH = options.ywxtbh;
        this.listIndex = options.listIndex;
        this.pageType = options.pageType;
        if (options.recordId) {
            this.recordId = options.recordId;
        } else {
            this.recordId = guid();
        }
        this.getFormHeight();
    },

    methods: {
        getFormHeight() {
            styleUtil.getPageLayout().then(layout => {
                if (!this.editable) {
                    this.pageHeight = layout.height;
                } else {
                    this.pageHeight = layout.height - uni.upx2px(185);
                }
            });
        },

        onFormDataUpdate(data) {
            console.log(data, '123');
            this.submitFormDataInfo = data;
        },

        postFormAxios() {
            this.$refs.dynamicForm.getFormData();
            // this.$refs.dynamicForm.checkRequireForm();
        },

        getFormDataInfo(data) {
            this.submitFormDataInfo = data;
            this.$refs.dynamicForm.checkRequireForm();
        },

        returnCheckRequireForm(status) {
            if (status) {
                this.postFormData();
            }
        },

        returnTemplate(template) {
            console.log(template, 'template');
            this.template = template;
        },

        postFormData() {
            this.submitFormDataInfo.YWXTBH = this.YWXTBH;
            this.submitFormDataInfo.PXH = this.listIndex;
            formService
                .submitFormData(
                    this.template,
                    this.recordId,
                    this.submitFormDataInfo
                )
                .then(res => {
                    uni.showToast({
                        title: `保存成功`,
                        duration: 1000,
                        icon: 'none'
                    });
                    setTimeout(() => {
                        uni.navigateBack({
                            delta: 1
                        });
                    }, 1000);
                });
        },

        deleteTask() {
            deleteQuestionList({
                ids: `${this.recordId}@${this.templateId}`
            }).then(res => {
                uni.showToast({
                    title: `删除成功`,
                    duration: 1000,
                    icon: 'none'
                });

                setTimeout(() => {
                    uni.navigateBack({
                        delta: 1
                    });
                }, 1000);
            });
        }
    }
};
</script>

<style scoped>
.template-form-layout {
    /* border-radius: 5rpx; */
    /* padding: 10rpx; */
    width: calc(100%);
    background-color: #fff;
    margin-top: 0rpx;
    height: calc(100% - 140rpx);
}

.template-form-tabsList {
    margin-top: 60upx;
}

.pd-ultbs1 {
    white-space: nowrap;
    overflow-x: scroll;
    overflow-y: hidden;
    -webkit-overflow-scrolling: touch;
}

.pd-ultbs1 li {
    font-size: 20px;
    margin: 10rpx 12rpx;
    display: inline-block;
    vertical-align: top;
}

/* .template-form-layout {
		border-radius: 5px;
		padding: 10px;
		width: calc(100% - 20px);
		background-color: #f4f4f4;
	} */

.form-menu-layout {
    margin-top: 70rpx;
    height: 70rpx;
    z-index: 10;
    background-color: white;
}

.form-operate-menu-item {
    flex: 1;
    padding: 22rpx 0;
}

.form-operate-menu-item:active {
    background-color: #ccc;
}

.form-menu-icon {
    width: 40rpx;
    height: 40rpx;
}

.form-menu-text {
    margin-top: 2px;
    font-size: 26rpx;
    color: white;
}

.form-content-layout {
    width: 100%;
    height: 100%;
}

.form-menu-layout {
    height: 50px;
    background-color: #fff;
    /* box-shadow: 0 -1px 1px 0 #ccc; */
}

.form-list-tab {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20rpx 0;
    color: #fff;
}

.task-detail__bottom-btn {
    flex: 1;
    position: relative;
}

.task-detail__bottom-bar {
    position: fixed;
    font-size: 18px;
    bottom: 0;
}
</style>
