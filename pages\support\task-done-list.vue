<template>
    <section class="pd-main">
        <div class="pd-inner" style="padding: 0 0 0 0">
            <view class="search-content">
                <view class="search-bar">
                    <uni-search-bar
                        placeholder="请输入想要查询的污染源企业"
                        cancelButton="none"
                        cancelText=""
                        clearButton="always"
                        bgColor="#F7F7F7"
                        :radius="50"
                        @input="searchByKeyword"
                    />
                    <!-- <dropdown-menu v-if="taskTypeOptions.length > 0">
						<dropdown-item :list="taskTypeOptions"
						               v-model="taskTypeIndex" />
						<dropdown-item :list="deadlineTypeOptions"
						               v-model="deadlineTypeIndex" />
					</dropdown-menu> -->
                </view>
                <view class="more-search" @click="showSearch"></view>
            </view>
            <div class="gap"></div>
            <scroll-view
                :scroll-y="true"
                @scrolltolower="loadMore"
                :style="pageListStyle"
            >
                <view
                    class="company-list"
                    @click="doRecordTask(item)"
                    v-for="(item, index) in companyList"
                    :key="index"
                >
                    <view class="flex-row-layout">
                        <text class="company-title company-title-text">{{
                            item.WRYMC
                        }}</text>
                    </view>

                    <view class="flex-row-layout" style="margin-bottom: 8px">
                        <view class="flex-row-layout" style="width: auto">
                            <view
                                style="margin-left: 5px"
                                class="company-distance"
                            >
                                任务类型：{{ item.RWLXMC }}
                            </view>
                        </view>

                        <text class="company-tips">
                            <text>时间：{{ item.KSSJ }}</text>
                        </text>
                    </view>
                </view>
                <NoData
                    v-if="!pageShow"
                    :type="companyList.length < 1 ? 'data' : ''"
                />
            </scroll-view>
        </div>
    </section>
</template>

<script>
import nextIcon from '@/static/img/navi_next_icon.png';
import { getWeatherSupportList } from '@/api/record.js';
import NoData from '@/components/no-data.vue';
import loginService from '@/api/login-service.js';
import uriUtil from '@/common/net/uri.js';
export default {
    name: 'TaskDoneList',
    components: {
        NoData
    },

    data() {
        return {
            nextIcon,
            pageNum: 1, //当前的页面数量
            pageShow: true, //页面显示的状态
            searchValue: '',
            companyList: []
        };
    },

    props: {
        pageListStyle: {
            type: Object,
            default: function () {
                return {
                    height: 'calc(100vh - 280rpx)',
                    backgroundColor: '#fff'
                };
            }
        },
        refreshType: {
            type: Boolean
        },
        conditionData: {
            type: Object
        },

        taskType: {
            type: String,
            default: '1'
        }
    },

    watch: {
        refreshType(val) {
            this.pageNum = 1;
            this.companyList = [];
            this.getData();
        }
    },

    mounted() {
        this.getData();
    },

    methods: {
        searchByKeyword(parms) {
            this.searchValue = parms;
            this.pageNum = 1;
            this.companyList = [];
            this.getData();
        },

        loadMore() {
            if (this.pageLoadType) {
                this.pageNum++;
                if (this.pageNum > this.lastPageIndex) {
                    uni.showToast({
                        title: '已经没有数据了',
                        duration: 2000,
                        icon: 'none'
                    });
                } else {
                    this.getData();
                }
            }
        },

        getData() {
            this.pageShow = true;
            let params = {};
            params.params = {
                searchText: this.searchValue,
                JCR: loginService.getAuthUserId() || 'SDSZFJ',
                RWLX: this.taskType,
                SFJS: '1'
            };
            getWeatherSupportList(params)
                .then(res => {
                    this.companyList.push(...res.data_json.list);
                    this.lastPageIndex = res.data_json.lastPage;
                    this.pageLoadType = true;
                    if (this.companyList.length < 1) {
                        this.pageShow = false;
                    }
                })
                .catch(res => {
                    this.pageLoadType = true;
                    this.pageShow = false;
                });
        },
        // 已办任务详情
        doRecordTask(item) {
            let params = {
                // workflowId: item.LCBH,
                taskId: item.YWXTBH,
                recordId: item.XH,
                type: 'done'
            };
            uni.navigateTo({
                url: `/pages/support/support-detail?${uriUtil.transformObjectToUrlParams(
                    params
                )}`
            });
        },
        showSearch() {
            this.$emit('showSearchBox');
        }
    }
};
</script>

<style scoped>
.record-task-list {
    margin-top: 10px;
    background-color: #fff;
}

.book {
    width: 100%;
    background-color: #f1f2f6;
}

.mk {
    background-color: #009bff;
    font-size: 22rpx;
    padding: 2rpx 2rpx;
    color: #fff;
    border-radius: 4rpx;
    margin-left: 2rpx;
}

.company-list {
    margin-left: 28rpx;
    background-color: #fff;
    border-bottom: 1rpx solid #eeeeee;
}

.company-title {
    font-weight: 500;
    color: #333333;
    font-size: 30rpx;
    padding: 16rpx 0 12rpx 0;
}

.company-footer {
    display: flex;
    align-items: center;
    width: 100%;
    padding-bottom: 12rpx;
}

.company-time {
    width: 46%;
    display: flex;
    align-items: center;
}

.company-time image {
    width: 24rpx;
    height: 24rpx;
}

.company-type {
    width: 46%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin-bottom: 10rpx;
}

.company-type text {
    background-color: #3f97dc;
    color: #fff;
    font-size: 24rpx;
    padding: 2rpx 20rpx;
    border-radius: 20rpx;
}

.company-time text {
    font-size: 26rpx;
    color: #666;
    padding-left: 12rpx;
}

.company-router image {
    width: 28rpx;
    height: 28rpx;
    position: relative;
    bottom: 30rpx;
    left: 16rpx;
}

.company-delay {
    color: red;
}

.search-bar {
    width: 90%;
}
.more-search {
    width: 10%;
    background: url('~@/static/img/icon_search_blue.png') no-repeat center;
    background-color: #ffffff;
    background-size: 50rpx auto;
}
.search-content {
    display: flex;
}
</style>
