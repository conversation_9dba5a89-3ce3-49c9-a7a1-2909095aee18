<template>
    <Page :padding="false" @layoutAware="onFixedContentHeight">
        <template v-slot:bar>
            <NaviBar title="帮扶待办">
                <template v-slot:navi>
                    <image
                        :src="backurl"
                        style="width: 39rpx; height: 39rpx; margin-left: 2rpx"
                    ></image>
                </template>
                <template v-slot:option v-if="taskType === '2'">
                    <view @click="addTask()">发起任务</view>
                </template>
            </NaviBar>
        </template>

        <ul class="pd-ultbs1" style="position: inherit">
            <li
                :class="{ on: taskProcessStatus == 'todo' }"
                @click="changeTaskProcessStatus('todo')"
            >
                <i>待办任务</i>
            </li>
            <li
                :class="{ on: taskProcessStatus == 'done' }"
                @click="changeTaskProcessStatus('done')"
            >
                <i>已办任务</i>
            </li>
        </ul>

        <task-done-list
            v-if="taskProcessStatus == 'done'"
            @showSearchBox="showSearch"
            :refreshType="refreshType"
            :conditionData="paramsData"
            :taskType="taskType"
        >
        </task-done-list>

        <section class="pd-main" v-show="taskProcessStatus == 'todo'">
            <div class="pd-inner" style="padding: 0 0 0 0">
                <view class="search-content">
                    <view class="search-bar">
                        <uni-search-bar
                            placeholder="请输入想要查询的污染源企业"
                            cancelButton="none"
                            cancelText=""
                            clearButton="always"
                            bgColor="#F7F7F7"
                            :radius="50"
                            @input="searchByKeyword"
                        />
                    </view>
                </view>
                <div class="gap"></div>
                <scroll-view
                    :scroll-y="true"
                    @scrolltolower="loadMore"
                    :style="pageListStyle"
                >
                    <view
                        class="company-list"
                        @click="doRecordTask(item)"
                        v-for="(item, index) in companyList"
                        :key="index"
                    >
                        <view class="flex-row-layout">
                            <text class="company-title company-title-text">{{
                                item.WRYMC || '--'
                            }}</text>
                        </view>

                        <view
                            class="flex-row-layout"
                            style="margin-bottom: 8px"
                        >
                            <view class="flex-row-layout" style="width: auto">
                                <view
                                    style="margin-left: 5px"
                                    class="company-distance"
                                >
                                    任务类型：{{ item.RWLXMC || '' }}
                                </view>
                            </view>

                            <text class="company-tips" v-if="taskType === '1'">
                                <text>任务时限：{{ item.RWQX }}</text>
                            </text>
                            <text class="company-tips" v-if="taskType === '2'">
                                <text>检查时间：{{ item.KSSJ }}</text>
                            </text>
                        </view>
                    </view>

                    <NoData
                        v-if="!isListShown"
                        :type="companyList.length < 1 ? 'data' : ''"
                    />
                </scroll-view>
            </div>
        </section>
    </Page>
</template>

<script>
import iconAbsentStamp from '@/static/img/task/icon_absent_stamp.png';
import iconDistanceMark from '@/static/img/task/icon_distance_mark.png';
import timeIcon from '@/static/img/time_template.png';
import nextIcon from '@/static/img/navi_next_icon.png';
import NoData from '@/components/no-data.vue';
import { guid } from '@/common/uuid.js';
import uriUtil from '@/common/net/uri.js';
import navition from '@/api/map-navigation.js';
import TaskDoneList from './task-done-list.vue';
import Page from '@/pages/component/Page.vue';
import NaviBar from '@/pages/component/NaviBar.vue';
import loginService from '@/api/login-service.js';
import listIcon from '@/static/app/images/lstic1a.png';
import defaultNaviIcon from '@/static/img/icon_back_white.png';
import { getWeatherSupportList } from '@/api/record.js';
import time from '@/common/timePicker.js';
import dayjs from 'dayjs';
// import taskService from '@/api/task-service.js';

//任务类型定义
const TASK_TYPE_ZHZF = 'ZHZF';
const TASK_TYPE_XFYQ = 'XFYQ';

export default {
    components: {
        Page,
        NaviBar,
        NoData,
        TaskDoneList
    },

    data() {
        return {
            taskProcessStatus: 'todo', //todo 待办，done 已办结
            iconAbsentStamp,
            iconDistanceMark,
            nextIcon,
            timeIcon,
            searchValue: '',
            pageHeight: -1,
            listimg: listIcon,
            backurl: defaultNaviIcon,

            companyList: [],
            timeOutTipType: true,
            taskType: '1', //任务类型，1是派发，2是检查
            lastPageIndex: null, //最大页面数
            pageNum: 1, //当前的页面数量
            pageLoadType: true, //页面可滚动的状态
            isListShown: true, //列表是否显示
            deloutType: '',
            resume: false,
            paramsData: {}, //筛选 数据 参数
            refreshType: false
        };
    },

    watch: {},

    computed: {
        pageListStyle() {
            return {
                height: 'calc(100vh - 300rpx)',
                backgroundColor: '#fff'
            };
        }
    },

    onLoad(options) {
        this.taskType = options.taskType;
    },

    mounted() {
        let self = this;
    },

    onShow() {
        this.companyList = [];
        this.getList();
        //获取列表数据
        if (this.resume === true) {
            // this.refreshTask();
            this.resume = false;
        }
    },

    onHide() {
        this.resume = true;
    },

    methods: {
        //切换任务
        changeTaskProcessStatus(status) {
            this.taskProcessStatus = status;

            if (this.taskProcessStatus == 'todo') {
                // this.refreshTask();
            }
        },

        /**
         * 查询待办任务列表
         */
        getList() {
            this.isListShown = true;
            let params = {};
            params.params = {
                searchText: this.searchValue,
                JCR: loginService.getAuthUserId() || 'SDSZFJ',
                RWLX: this.taskType,
                SFJS: '0'
            };
            if (this.taskType === '2') {
                params.params.SFZC = '1';
            }

            getWeatherSupportList(params)
                .then(res => {
                    let taskList = res.data_json.list || [];
                    this.companyList.push(...taskList);
                    this.lastPageIndex = res.data_json.lastPage;
                    this.pageLoadType = true;
                    if (this.companyList.length < 1) {
                        this.isListShown = false;
                    }
                })
                .catch(error => {
                    this.pageLoadType = true;
                    this.isListShown = false;
                });
        },

        refreshTask() {
            this.pageNum = 1;
            this.companyList = [];
            this.lastPageIndex = 1;
            this.getList();
        },

        doRecordTask(item) {
            let params = {
                // workflowId: item.LCBH,
                taskId: item.YWXTBH,
                recordId: item.XH,
                taskType: this.taskType,
                type: 'do'
            };
            uni.navigateTo({
                url: `/pages/support/support-detail?${uriUtil.transformObjectToUrlParams(
                    params
                )}`
            });
        },

        addTask() {
            let params = {
                type: 'do',
                taskType: this.taskType
            };
            uni.navigateTo({
                url: `/pages/support/support-detail?${uriUtil.transformObjectToUrlParams(
                    params
                )}`
            });
        },

        //关键字搜索
        searchByKeyword(parms) {
            this.searchValue = parms;
            this.pageNum = 1;
            this.companyList = [];
            this.getList();
        },

        loadMore() {
            if (this.pageLoadType) {
                this.pageNum++;
                if (this.pageNum > this.lastPageIndex) {
                    uni.showToast({
                        title: '已经没有数据了',
                        duration: 2000,
                        icon: 'none'
                    });
                } else {
                    this.getList();
                }
            }
        },

        onFixedContentHeight(layout) {
            this.pageHeight = layout.height;
        }
    }
};
</script>

<style scoped>
.record-task-list {
    margin-top: 10px;
    background-color: #fff;
}

.book {
    width: 100%;
    background-color: #f1f2f6;
}

.mk {
    background-color: #009bff;
    font-size: 22rpx;
    padding: 2rpx 2rpx;
    color: #fff;
    border-radius: 4rpx;
    margin-left: 2rpx;
}

.company-list {
    margin-left: 28rpx;
    background-color: #fff;
    border-bottom: 1rpx solid #eeeeee;
}

.company-title {
    font-weight: 500;
    color: #333333;
    font-size: 30rpx;
    padding: 16rpx 0 12rpx 0;
}

.company-footer {
    display: flex;
    align-items: center;
    width: 100%;
    padding-bottom: 12rpx;
}

.company-time {
    width: 46%;
    display: flex;
    align-items: center;
}

.company-time image {
    width: 24rpx;
    height: 24rpx;
}

.company-type {
    width: 46%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin-bottom: 10rpx;
}

.company-type text {
    background-color: #3f97dc;
    color: #fff;
    font-size: 24rpx;
    padding: 2rpx 20rpx;
    border-radius: 20rpx;
}

.company-time text {
    font-size: 26rpx;
    color: #666;
    padding-left: 12rpx;
}

.company-router image {
    width: 28rpx;
    height: 28rpx;
    position: relative;
    bottom: 30rpx;
    left: 16rpx;
}

.company-delay {
    margin-left: auto;
    margin-right: 10px;
    color: red;
}

.company-tips {
    margin-left: auto;
    margin-right: 10px;
    color: #ff9900;
}
.search-bar {
    width: 100%;
}
.more-search {
    width: 10%;
    background: url('~@/static/img/icon_search_blue.png') no-repeat center;
    background-color: #ffffff;
    background-size: 50rpx auto;
}
.search-content {
    display: flex;
}
</style>
