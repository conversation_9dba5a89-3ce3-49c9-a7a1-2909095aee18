<template>
    <Page
        :padding="false"
        :mainStyle="mainStyle"
        @layoutAware="onFixedContentHeight"
    >
        <template v-slot:bar>
            <NaviBar title="监督帮扶现场检查表" />
        </template>

        <view class="template-form-layout" id="formParent">
            <template-form
                ref="templateForm"
                :constraints="constraints"
                :parentHeight="formLayoutHeight"
                :editable="editable"
                :template="template"
                :form-data="formData"
                :record-id="recordId"
            />

            <PCard title="监督帮扶现场问题表">
                <template slot="option" v-if="editable">
                    <view @click="addQuestion" class="support-add">
                        <view>新增问题</view>
                        <image class="support-add-icon" :src="iconMapAdd" />
                    </view>
                </template>
                <scroll-view
                    v-if="questionList.length > 0"
                    :scroll-y="true"
                    :style="questionStyle"
                >
                    <view
                        class="support-list"
                        v-for="(item, index) in questionList"
                        :key="index"
                    >
                        <view>{{ item.WTLBMC }}</view>
                        <view
                            class="support-list-info"
                            @click="editQuestion(item, index)"
                            >查看</view
                        >
                    </view>
                </scroll-view>
            </PCard>

            <view
                v-if="editable"
                class="flex-row-layout task-detail__bottom-bar"
            >
                <p-button
                    v-for="(menu, index) in menus"
                    :key="index"
                    class="task-detail__bottom-btn"
                    :style="{ background: menu.bg }"
                    :name="menu.name"
                    @click.native="clickMutableMenu(menu)"
                />
            </view>
        </view>
        <show-modal></show-modal>
    </Page>
</template>

<script>
import iconHistory from '@/static/img/record/icon_form_history.png';
import iconSave from '@/static/img/record/icon_form_save.png';
import iconChange from '@/static/img/record/icon_form_change.png';
import iconMapAdd from '@/static/online/images/ic_mapadd.png';
import NoData from '@/components/no-data.vue';
import Page from '@/pages/component/Page.vue';
import NaviBar from '@/pages/component/NaviBar.vue';
import templateForm from '@/pages/form/template-form.vue';
import formOperateMenuItem from '@/pages/record/form-operate-menu-item.vue';
import styleUtil from '@/common/style.js';
import formUtil from '@/pages/form/Form.js';
import onform from '@/api/onsaveForm.js';
import PCard from '@/pages/component/PCard.vue';
import { guid } from '@/common/uuid.js';
import { deepCopyObject } from '@/common/merge.js';
import { postQueryWryjcTzForm, postQueryWryjcTzInfo } from '@/api/book.js';
import {
    dynamicformsave,
    postCheckQuestionList,
    postTransferOrCheck,
    queryElementConstraints
} from '@/api/record.js';
import loginService from '@/api/login-service.js';
import PButton from '@/components/p-button';

export default {
    components: {
        Page,
        NaviBar,
        NoData,
        templateForm,
        formOperateMenuItem,
        PButton,
        PCard
    },

    data() {
        return {
            constraints: [],
            checkFormStatus: '', //表单提交的状态，1为暂存；2为提交
            recordId: '',
            templateId: '',
            taskType: '1',
            formData: {},
            template: {},
            editable: true,
            pageHeight: 600,
            iconMapAdd,
            questionList: [],
            menus: [],
            menus1: [
                {
                    name: '暂存',
                    icon: iconSave,
                    type: 'cache',
                    bg: '#666'
                },
                {
                    name: '转办',
                    icon: iconSave,
                    type: 'transfer',
                    bg: '#00cf6f'
                },
                {
                    name: '办结',
                    icon: iconSave,
                    type: 'finish',
                    bg: '#009bff'
                }
            ],
            menus2: [
                {
                    name: '暂存',
                    icon: iconSave,
                    type: 'cache',
                    bg: '#666'
                },
                {
                    name: '办结',
                    icon: iconSave,
                    type: 'finish',
                    bg: '#009bff'
                }
            ],
            mrzList: []
        };
    },

    computed: {
        formLayoutHeight: function () {
            return this.pageHeight - 150;
        },

        questionStyle() {
            return {
                height: '300rpx'
            };
        },

        mainStyle() {
            return {
                'background-color': '#fff'
            };
        }
    },

    onShow() {
        this.$store.state.verifyList = [];
        this.getTemplate();
        this.getCheckQuestionList();
    },

    onLoad(options) {
        this.editable = options.type === 'done' ? false : true;
        this.templateId = '20221001113849405bf017cf0b42d89b46b9f01e1228c6';
        this.recordId = options.recordId;
        this.taskType = options.taskType;
        this.$store.state.mrzList = [];
        this.menus = options.taskType === '2' ? this.menus2 : this.menus1;
        if (options.recordId) {
            this.recordId = options.recordId;
            this.getData();
        } else {
            this.getTemplate();
            this.recordId = guid();
        }

        // this.getTemplate();
    },

    methods: {
        getData() {
            postQueryWryjcTzForm({
                service: 'QUERY_DYNAMICFORM_DATA',
                recordId: this.recordId,
                mbbh: this.templateId
            }).then(res => {
                // let defaultValues = this.extractDefaultValues(this.mrzList);
                this.formData = res.data;
                setTimeout(() => {
                    let defaultValues = this.extractDefaultValues(this.mrzList);
                    this.formData = Object.assign(defaultValues, this.formData);
                }, 8000);
                this.getTemplate();
            });
        },

        extractDefaultValues(elementValues) {
            let defaultValues = {};
            let self = this;
            elementValues.forEach(element => {
                let field = element.dbcolum;
                let value = element.MRZ;
                if (value) {
                    defaultValues[field] = value;
                }
            });
            return defaultValues;
        },

        onFixedContentHeight(layout) {
            this.pageHeight = layout.height;
        },

        getTemplate() {
            queryElementConstraints(this.templateId)
                .then(res => {
                    res.data.forEach(element => {
                        let constraintList = {};
                        constraintList.active_id = element.GLID;
                        constraintList.follow_id = element.EID;
                        constraintList.rule = element.JSGS;
                        this.constraints.push(constraintList);
                    });
                })
                .finally(() => {
                    let userInfo = uni.getStorageSync('userInfo');
                    let userId = '';
                    if (userInfo) {
                        userId = userInfo.id;
                    }
                    postQueryWryjcTzForm({
                        service: 'GET_DYNAMICFORM_MODEL',
                        bbxh: this.templateId,
                        userId: userId || 'SDSZFJ'
                    }).then(res => {
                        this.template = formUtil.packPeerElementIntoGroup(
                            res.datas_json
                        );
                        this.mrzList = deepCopyObject(res.datas_json.child);
                        // this.$nextTick(function () {
                        //     this.bindFormData();
                        // });
                    });
                    // this.getTemplateForm();
                });
        },

        bindFormData() {},

        onMenuClick() {
            let self = this;
            styleUtil.getNodeLayout(this, '#formParent').then(layout => {
                let dataType = onform.saveTemplateForm(
                    self.formData,
                    self,
                    layout.top
                );
                if (dataType === true) {
                    this.postFormAxios();
                }
            });
        },

        getCheckQuestionList() {
            postCheckQuestionList({
                XH: this.recordId
            }).then(res => {
                this.questionList = res.data_json;
            });
        },

        addQuestion() {
            let index = this.questionList.length + 1;
            uni.navigateTo({
                url: `/pages/support/support-question-detail?ywxtbh=${
                    this.recordId
                }&listIndex=${index}&pageType=${'1'}&recordId=`
            });
        },

        editQuestion(item, index) {
            //  let params = {
            //       // workflowId: item.LCBH,
            //       taskId: item.YWXTBH,
            //       recordId: item.XH,
            //       type: 'do'
            //   };
            uni.navigateTo({
                url: `/pages/support/support-question-detail?ywxtbh=${
                    this.recordId
                }&listIndex=${index + 1}&pageType=${'0'}&editable=${
                    this.editable
                }&recordId=${item.XH}`
            });
        },

        postFormAxios() {
            let self = this;
            let form = {
                client_type: 'mobile_web',
                record_id: this.recordId,
                service: 'DYNAMICFORM_SAVE',
                template_id: self.templateId,
                user_id: loginService.getAuthUserId() || 'SDSZFJ'
            };
            let tableName = self.template['templateTable'];
            let data = this.$store.state.formData || this.formData;
            data.SFZC = this.checkFormStatus;
            data.XH = this.recordId;
            if (this.taskType === '2') {
                data.SFJS = '0';
                data.RWLX = '2';
            }
            data = deepCopyObject(data);
            form[tableName] = data;
            dynamicformsave(form)
                .then(res => {
                    if (this.checkFormStatus === '0') {
                        let prams = {
                            XH: this.recordId,
                            SFJS: '1',
                            DQZT: '2'
                        };
                        if (this.taskType === '2') {
                            prams.RWLX = '2';
                        }
                        postTransferOrCheck(prams).then(() => {
                            uni.showToast({
                                title: `办结成功`,
                                duration: 1000,
                                icon: 'none'
                            });
                            setTimeout(() => {
                                uni.navigateBack({
                                    delta: 1
                                });
                            }, 1000);
                        });
                    } else {
                        uni.showToast({
                            title: `暂存成功`,
                            duration: 1000,
                            icon: 'none'
                        });
                    }
                })
                .catch(error => {
                    uni.showToast({
                        title: `保存失败`,
                        duration: 1000,
                        icon: 'none'
                    });
                });
        },

        clickMutableMenu(item) {
            if (item.type === 'transfer') {
                uni.navigateTo({
                    url: `/pages/support/support-transfer?recordId=${this.recordId}`
                });
            } else if (item.type === 'cache') {
                this.checkFormStatus = '1';
                this.postFormAxios();
            } else if (item.type === 'finish') {
                this.checkFormStatus = '0';
                this.onMenuClick();
            }
        }
    }
};
</script>

<style scoped>
.template-form-layout {
    /* border-radius: 5rpx; */
    /* padding: 10rpx; */
    width: calc(100%);
    background-color: #fff;
    margin-top: 0rpx;
    /* height: calc(100% - 140rpx); */
}

.template-form-tabsList {
    margin-top: 60upx;
}

.pd-ultbs1 {
    white-space: nowrap;
    overflow-x: scroll;
    overflow-y: hidden;
    -webkit-overflow-scrolling: touch;
}

.pd-ultbs1 li {
    font-size: 20px;
    margin: 10rpx 12rpx;
    display: inline-block;
    vertical-align: top;
}

/* .template-form-layout {
		border-radius: 5px;
		padding: 10px;
		width: calc(100% - 20px);
		background-color: #f4f4f4;
	} */

.form-menu-layout {
    margin-top: 70rpx;
    height: 70rpx;
    z-index: 10;
    background-color: white;
}

.form-operate-menu-item {
    flex: 1;
    padding: 22rpx 0;
}

.form-operate-menu-item:active {
    background-color: #ccc;
}

.form-menu-icon {
    width: 40rpx;
    height: 40rpx;
}

.form-menu-text {
    margin-top: 2px;
    font-size: 26rpx;
    color: white;
}

.form-content-layout {
    width: 100%;
    height: 100%;
}

.form-menu-layout {
    height: 50px;
    background-color: #fff;
    /* box-shadow: 0 -1px 1px 0 #ccc; */
}

.form-list-tab {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20rpx 0;
    color: #fff;
}

.task-detail__bottom-bar {
    position: fixed;
    /* height: 50px; */
    font-size: 18px;
    bottom: 0;
}

.support-add {
    display: flex;
    justify-content: center;
    align-items: center;
}

.support-add-icon {
    width: 60rpx;
    height: 60rpx;
}

.support-list {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 14rpx;
}

.support-list-info {
    color: #009bff;
}

.task-detail__bottom-btn {
    flex: 1;
    position: relative;
}
</style>
