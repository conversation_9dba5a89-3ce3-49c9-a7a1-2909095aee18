<!-- @format -->

<template>
	<MainPage :padding="false">
	    <template v-slot:bar>
	        <NaviBar :title="template.templateName">
				<template v-slot:option>
					<!-- <span v-show="isShowPrint" @click="printPdf()">打印</span> -->
				</template>
	        </NaviBar>
	    </template>
		<div class="template-detail">
			<template-form
				style="height: calc(100vh - 200rpx);overflow: auto;"
				:style="{height: editable? 'calc(100vh - 210rpx)': 'calc(100vh - 80rpx)' }"
				ref="templateForm"
				:editable="editable"
				:recordId="recordId"
				:template="template"
				:form-data="formData"
				@onFormMounted="onFormMounted"
			/>
			<div style="height: 20rpx"></div>
			
			
		</div>
		<ul class="lr1-8-btm" v-if="editable">
		    <!-- <li @click="submitCache">暂存</li> -->
		    <li class="bg" @click="submitFormData" style="width: 94%;">提交</li>
		</ul>
        <!-- show-cancel-button -->
        <u-modal
            :content-style="{
                color: 'red'
            }"
            @confirm="show = false"
            v-model="show"
            :content="'严禁在本互联网非涉密平台处理、传输国家秘密，请确认扫描、传输的文件资料不涉及国家秘密'"
        ></u-modal>
	</MainPage>
</template>

<!-- @format -->

<script>
import templateService from '@/node_modules/bowo-form/components/service/template-service.js';
import templateUtil from '@/node_modules/bowo-form/components/template.js';
import formService from '@/node_modules/bowo-form/components/service/form-service.js';
import templateForm from '@/node_modules/bowo-form';
import onform from "@/api/onsaveForm.js";

import { guid } from "bowo-sdk/util/uuid.js";
import printTemplate from '@/api/print-template.js';
import styleUtil from 'bowo-sdk/util/style.js';
import NaviBar from "@/pages/component/NaviBar.vue";

import dayjs from 'dayjs';

import {
	getPollutionData
} from "@/api/record.js";
export default {
    components: {
        templateForm,
        NaviBar
    },
    data() {
        return {
            addObj: {}, // 上个页面保存表单需要新增的参数
			isShowPrint: false,
			pollutionId: '',
			isSubmit: true,
            pageHeight: 600,
            editable: true,
            template: {},
            formData: {},
            recordId: '',
            templateId: '',
            editRecordId: '',
            editTemplateId: '',
            show: true
        };
    },
    onLoad(options) {
		if(decodeURIComponent(options.addObj) !== 'undefined') {
            this.addObj = JSON.parse(decodeURIComponent(options.addObj));;
        }
		// alert(1)
        this.templateId = options.templateId; // 污染源信息变更模板
		this.pollutionId = options.recordId;
        

		if(options.editable == '1'){ // 0 是可不可编辑  1是编辑 不传是新增
			// this.isShowPrint = true;
			// this.editable = false;
			this.recordId = options.recordId;
			// console.log('ccc')
		}else if(options.editable == '0'){
			this.editable = false;
		}else{
			this.getRecordId();
		}

		// getPollutionData({
		// 	XH: '1613638967891028188672',
		// 	WRYBH: '5bf44676-3b26-11ed-84c0-fa163effa54c'
		// }).then(res=>{
		// 	// console.log(res)
		// })
        // this.editRecordId = options.editRecordId;
        // this.editTemplateId = options.editTemplateId;
    },
    mounted() {
        let templateId = this.templateId;
		let pollutionId = this.pollutionId;
        styleUtil.getPageLayout('').then((layout) => {
            // #ifdef APP-PLUS
            this.pageHeight = layout.height;
            // #endif
            this.pageHeight = layout.height - 64;
        });

        templateService.getTemplateById(templateId).then((templateJson) => {
            let groupedChildTemplate =
                templateUtil.packPeerElementIntoGroup(templateJson);
            this.template = groupedChildTemplate;

            formService
                .getRecordDetailById(templateId, pollutionId)
                .then((detail) => {
                    this.formData = detail;
					// console.log(uni.getStorageSync('userInfo'));
					// this.formData.TBRMC = uni.getStorageSync('userInfo').name;
					// this.formData.TBR = uni.getStorageSync('userInfo').yhid;
					// this.formData.SHZT = '0';
					// this.formData.TBSJ = dayjs(new Date()).format('YYYY-MM-DD HH:mm:ss')
                    // // console.log(`表单数据：${JSON.stringify(detail, null, 4)}`);
                });

            // this.resetAttachElementMenu();
        });
    },
    computed: {
        formStyle: function () {
            return {};
        }
    },
    methods: {
		printPdf(){
			let record_id = this.recordId;
			let template_id = this.templateId;
			
			let data = [{
				record_id: record_id,
				template_id: template_id
			}]
			printTemplate.getCallPrinter(data,true);
		},
		
		getRecordId() {
		   this.recordId = guid();
		},

        go(url) {
            uni.navigateTo({
                url: url
            });
        },

        onFormMounted() {
            // this.resetAttachElementMenu();
        },
        back() {
            uni.navigateBack();
        },

        getFormRef() {
            return this.$refs.templateForm;
        },

        submitFormData() {
            let requiredFields = this.getFormRef().checkRequireFields();
            console.log(requiredFields);
				if (requiredFields.length > 0) {
					uni.showToast({
						title: '请检查必填项',
						icon: 'none',
						duration: 2000
					});
					return;
			}
            uni.showLoading({
				title: '正在提交数据'
			});
            let formData = Object.assign({}, this.getFormRef().getFormData(), this.addObj);
                formService
                    .submitFormData(this.template, this.recordId, formData)
                    .then((resp) => {
                        uni.setStorageSync('FileFormData', this.recordId);
                        setTimeout(() => {
                           uni.redirectTo({
                              url: `/pages/record/record-resolver?targetAssigned=false`,
                            });
                        }, 1000);
                        uni.showToast({
                            title: '提交成功'
                        });
                        this.isSubmit = false;
                    })

                    .catch((error) => {
                        uni.showModal({
                            title: '提交出错',
                            content: error
                        });
                    })
                    .finally(() => {
                        uni.hideLoading();
                    });
            // let dataType = onform.saveTemplateForm(this.formData, this);
            // if (dataType === true) {
            //         uni.showLoading({
            //         title: '正在提交数据'
            //     });
               
            // }
            // let formData = Object.assign({}, this.getFormRef().getFormData(), this.addObj);
            // console.log(formData);
            // let requiredFields = this.getFormRef().checkRequireFields();
        //    if (requiredFields.length > 0) {
        //        uni.showToast({
        //            title: '请检查必填项或格式信息',
        //            icon: 'none',
        //            duration: 2000
        //        });
        //        this.getFormRef().scrollToElement(requiredFields[0]);
        //        // console.log(
        //        //     `必填项：${JSON.stringify(requiredFields, null, 4)}`
        //        // );
        //        return;
        //    }
        }
    }
};
</script>

<style lang="scss" scoped>
.template-detail {
    display: flex;
    flex-flow: column;
    justify-content: flex-start;
    align-items: center;
    width: 100%;
    height: 100%;
}

.pd-btn {
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
}

.zy-btn1 {
    width: 95vw;
}

.pd-botbtn1 {
    width: 80%;
    margin-top: 28rpx;
}


.lr1-8-btm {
    position: fixed;
    bottom: 0;
    display: flex;
    justify-content: space-around;
    align-items: center;
    width: 100%;
    height: 100rpx;
    background: #fff;
    /* padding: 0 21rpx; */
}

.lr1-8-btm>li {
    width: 40%;
    line-height: 80rpx;
    font-size: 30rpx;
    color: #3C95FF;
    text-align: center;
    border-radius: 12rpx;
    cursor: pointer;
    border: 1px solid #3C95FF;
}

.lr1-8-btm>li.bg {
    color: #fff;
    background: #3C95FF;
}

.lr1-8-btm.m2>li {
    width: calc(33.3333% - 21rpx);
}

.lr1-8-btm.m2 .bg2 {
    color: #999;
    border-color: #999;
}
</style>
