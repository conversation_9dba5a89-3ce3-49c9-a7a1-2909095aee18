<template>
    <Page :padding="false" title="自然保护地列表">
        <view class="search-bar">
            <uni-search-bar
                placeholder="请输入想要查询的自然保护地"
                cancelButton="none"
                cancelText=""
                clearButton="always"
                bgColor="#F7F7F7"
                :radius="50"
                @input="searchByKeyword"
            />
        </view>
        <dl class="pd-dlbx1" v-if="list.length > 0">
            <dt>
                <span
                    >查询到 <i>（{{ total }}）</i> 条记录</span
                >
            </dt>
        </dl>
        <scroll-view
            class="record-task-list"
            scroll-y="true"
            style="height: calc(100vh - 280rpx)"
            @scrolltolower="onReachBottomEvent"
            v-if="list.length > 0"
        >
            <dl class="pd-dlbx1" v-for="(item, index) in list" :key="index">
                <div>
                    <dd @click="showDetail(item)">
                        <image :src="listIcon" class="imgic" />
                        <h2>{{ item.ZRBHDMC }}</h2>
                        <p>
                            <em>地址：</em
                            ><span>{{
                                item.SSDSMC + item.SSQXMC
                            }}</span>
                        </p>
                        <!-- <small></small> -->
                    </dd>
                </div>
            </dl>
        </scroll-view>
        <noDataAdd v-if="list.length == 0"></noDataAdd>
    </Page>
</template>

<script>
import Page from '@/pages/component/Page.vue';
import listIcon from '@/static/app/images/lstic1a.png';
import { getAreaTaskList } from '@/api/record.js';
import noDataAdd from '@/components/no-data-add.vue';
import loginService from '@/api/login-service.js';

export default {
    components: { noDataAdd, Page },
    data() {
        return {
            listIcon,
            search: '',
            type: 'WRY',
            pageSize: 10,
            pageNum: 1,
            list: [],
            total: 0,
            isLastPage:false
        };
    },

    mounted() {
        this.searchData();
    },
    methods: {
        showDetail(item) {
            // uni.setStorageSync('area_detail', item);
            // uni.navigateTo({
            //     url: `/pages/task/area/area-detail`
            // });
            uni.navigateBack({
                delta: 1
            });
            uni.$emit('onAllPollutionSelect', item);
        },
        back() {
            uni.navigateBack();
        },
        searchByKeyword(parms) {
            this.search = parms;
            this.pageNum = 1;
            this.list = [];
            this.searchData();
        },
        onReachBottomEvent() {
            this.pageNum++;
            if(!this.isLastPage){
                this.searchData();
            }else{
                uni.showToast({
                    title: '已经没有数据了',
                    duration: 2000,
                    icon:'none'
                });
            }
        },
        changeType(type) {
            this.pageNum = 1;
            this.type = type;
            this.list = [];
            this.searchData();
        },
        searchData() {
            let userId = loginService.getAuthUserId();
            getAreaTaskList({
                naturalAreaName: this.search,
                pageSize: 20,
                pageNum: this.pageNum
            }).then(res => {
                // console.log(this.list)
                // debugger
                this.list.push(...res.data_json.list);
                this.total = res.data_json.total;
                this.isLastPage = res.data_json.isLastPage;
            });
        }
    }
};
</script>

<style>
.pd-main {
    width: 100vw;
}
.pd-dlbx1 + .pd-dlbx1 {
    margin-top: 0px;
}
</style>
