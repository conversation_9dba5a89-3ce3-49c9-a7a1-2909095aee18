<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-06-24 14:42:35
 * @LastEditTime: 2021-06-24 16:05:28
 * @LastEditors: Please set LastEditors
 * @Description: 用于处理筛选条件的工具
 * @FilePath: /YDZF_APP/pages/statistics/condition-screening.vue
-->

<template>
	<view>
		<PCard title='任务类型' v-if="rwlxList.length>0">
			<uni-tag :type="item.value === paramsData.RWLX ? 'warning' : 'default'"
					 v-for="(item,index) in rwlxList"
					 :key="index"
					 :text="item.text"
					 @click="tagClick(item.value,'RWLX')"
					 style="display: inline-block;margin: 4rpx;"></uni-tag>
		</PCard>
		<PCard title='行政区划'>
			<picker :range="SSDSList"
					@change="ssdsChange"
					:range-key="'XZQH'"
					:value="SSDSIndex"
					style="width:45%;display: inline-block; margin-right: 20rpx;"
					>
					<!-- style="width:40%;display: inline-block;text-align: center;" -->
				<view class="xzqh-picker">{{SSDSList[SSDSIndex]?SSDSList[SSDSIndex].XZQH:'请选择'}}</view>
			</picker>
			<picker :range="SSQXList"
					@change="ssqxChange"
					:range-key="'XZQH'"
					:value="SSQXIndex"
					style="width:45%;display: inline-block;margin-right: 20rpx;">
				<view class="xzqh-picker" >{{SSQXList[SSQXIndex]?SSQXList[SSQXIndex].XZQH:'请选择'}}</view>
			</picker>
		</PCard>
		<PCard title='监管级别'>
			<uni-tag :type="item.value === paramsData.WRYJGJB ? 'warning' : 'default'"
					 v-for="(item,index) in jgjbList"
					 :key="index"
					 :text="item.text"
					 @click="tagClick(item.value,'WRYJGJB')"
					 style="display: inline-block;margin: 4rpx;"></uni-tag>
		</PCard>
		<PCard title='是否特殊源'>
			<uni-tag :type="item.value === paramsData.TSJG ? 'warning' : 'default'"
					 v-for="(item,index) in tsyList"
					 :key="index"
					 :text="item.text"
					 @click="tagClick(item.value,'TSJG')"
					 style="display: inline-block;margin: 4rpx;"></uni-tag>
		</PCard>
		<PCard title='任务来源'>
			<uni-tag :type="item.value === paramsData.XZJB ? 'warning' : 'default'"
					 v-for="(item,index) in rwlyList"
					 :key="index"
					 :text="item.text"
					 @click="tagClick(item.value,'XZJB')"
					 style="display: inline-block;margin: 4rpx;"></uni-tag>
		</PCard>
		<PCard title='时间选择'>
			<uni-tag type="default"
					 :text="paramsData.JCKSSJ?paramsData.JCKSSJ:'开始时间'"
					 @click="$refs.startTiem.open()"
					 style="display: inline-block;margin: 4rpx;width:35%;text-align: center;"></uni-tag>
					 ~
			<uni-tag type="default"
					 :text="paramsData.JCJSSJ?paramsData.JCJSSJ:'结束时间'"
					 @click="$refs.endTime.open()"
					 style="display: inline-block;margin: 4rpx;width:35%;text-align: center;"></uni-tag>
			<uni-calendar ref="startTiem" :insert="false" :date="paramsData.JCKSSJ" @confirm="startConfirm" />
			<uni-calendar ref="endTime" :insert="false" :date="paramsData.JCJSSJ" @confirm="endConfirm" />
		</PCard>
		<PButton style="width: 33%;background: #ccc;left: 0;"
				 @click.native="onClose"
				 name="取消"></PButton>
		<PButton style="width: 33%;background: #f0ad4e;left: 33%;"
				 @click.native="reset"
				 name="重置"></PButton>
		<PButton style="width: 33%;background: #1f95ef; left: 66%;"
				 @click.native="onForm"
				 name="确定"></PButton>
	</view>
</template>

<script>
	import PButton from '@/components/p-button';
	import PCard from '@/pages/component/PCard.vue'
	import time from '@/common/timePicker.js';
	import taskService from '@/api/task-service.js'
	export default {
		components: {
			PCard,
			PButton
		},

		props: {
			cityData: {
				type: Array
			},

			conditionData: {
				type: Object
			}
		},

		data() {
			return {				
				SSDSList:[],
				SSDSIndex:'',//行政区划的索引
				SSQXList:[],
				SSQXIndex:'',//行政区划的索引
				
				// SSDS:'',//地市名称
				// SSDSDM:'',//地市代码
				// SSQX:'',//区县名称
				// SSQXDM:'',//区县代码
				
				rwlxList:[],
				jgjbList:[
					{text:'全部',value:''},
					{text:'一般源',value:'WRYJGJB_0'},
					{text:'重点源',value:'WRYJGJB_1'}
				],
				tsyList:[
					{text:'全部',value:''},
					{text:'特殊源',value:'TSJG_1'},
					{text:'省级重点监管',value:'TSJG_2'}
				],
				rwlyList:[
					{text:'全部',value:''},
					{text:'省级',value:'1'},
					{text:'市级',value:'2'},
					{text:'区县',value:'3'}
				],
				paramsData: {
					RWLX:'', //任务类型
					WRYJGJB:'', //监管级别
					TSJG:'', //是否特殊源
					XZJB:'', //任务来源（行政级别）
					JCKSSJ:'', //开始时间
					JCJSSJ:'', //结束时间
					DSDM:'', //地市代码(待办用)
					QXDM:'', //区县代码(待办用)
					SSDS:'', //地市代码(已办用)
					SSQX:''//区县代码(已办用)
				},
				flag:0,
				flag2:0
			};
		},

		mounted() {
			this.rwlxList = uni.getStorageSync('YDZF_RWLX_DATA')
			let conditionDataArr = Object.keys(this.conditionData);
			// 判断conditionData是否为空
			if(conditionDataArr.length > 0){
				this.paramsData = this.conditionData
				if(this.paramsData.DSDM){
					this.flag = 1
				}
				if(this.paramsData.QXDM){
					this.flag2 = 1
				}
			}
			this.getXzqh('370000','SSDSList')
		},

		methods: {
			//获取行政区划
			getXzqh(data,type){
				taskService.queryXzqhList(data).then(res => {
					this[type] = res.data_json
					
					if(this.flag&&this.paramsData.DSDM&&this.SSDSList.length>0){
						this.SSDSIndex = this.getXzqhIndex(this.SSDSList,this.paramsData.DSDM)
						this.getXzqh(this.paramsData.DSDM,'SSQXList')
						this.flag = 0
					}
					if(this.flag2&&this.paramsData.QXDM&&this.SSQXList.length>0){
						this.SSQXIndex = this.getXzqhIndex(this.SSQXList,this.paramsData.QXDM)
						this.flag2 = 0
					}
				})
			},
			//获取行政区划
			// getXzqh(data,type){
			// 	taskService.queryXzqhList(data).then(res => {
			// 		this[type] = res.data_json

			// 	})
			// },
			//通过行政区划代码获取对应的index
			getXzqhIndex(arr,xzqhdm){
				let _index = ''
				arr.forEach((item,index) => {
					if(item.XZQHDM === xzqhdm){
						_index = index
						return
					}
				})
				return _index
			},
			//开始时间
			startConfirm(dateInfo) {
				this.paramsData.JCKSSJ = dateInfo.fulldate;
			},
			//结束时间
			endConfirm(dateInfo) {
				this.paramsData.JCJSSJ = dateInfo.fulldate;
			},
			//取消
			onClose() {
				this.$emit('close')
			},
			//确定
			onForm() {
				// console.log('=============',this.paramsData);
				this.$emit('changeData', this.paramsData)
				this.$emit('close')
			},


			tagClick(value,type) {
				this.paramsData[type] = value
			},

			ssdsChange(data) {
				this.SSDSIndex = data.target.value
				
				this.paramsData.DSDM = this.SSDSList[this.SSDSIndex].XZQHDM
				this.paramsData.SSDS = this.SSDSList[this.SSDSIndex].XZQHDM
				
				this.getXzqh(this.paramsData.DSDM,'SSQXList')
				this.SSQXIndex = ''
				this.paramsData.QXDM = ''
				this.paramsData.SSQX = ''
			},
			ssqxChange(data) {
				this.SSQXIndex = data.target.value
				
				this.paramsData.QXDM = this.SSQXList[this.SSQXIndex].XZQHDM
				this.paramsData.SSQX = this.SSQXList[this.SSQXIndex].XZQHDM
			},
			reset(){
				this.paramsData = {
					RWLX:'', //任务类型
					WRYJGJB:'', //监管级别
					TSJG:'', //是否特殊源
					XZJB:'', //任务来源（行政级别）
					JCKSSJ:'', //开始时间
					JCJSSJ:'', //结束时间
					DSDM:'', //地市代码(待办用)
					QXDM:'', //区县代码(待办用)
					SSDS:'', //地市代码(已办用)
					SSQX:''//区县代码(已办用)
				}
				this.SSDSIndex = ''
				this.SSQXIndex = ''
				
			}
		},
	};
</script>

<style scoped>
	.picker-modal{
		left:0
	}
	.xzqh-picker{
		width:100%;
		display: inline-block;
		text-align: center;
		height: 60rpx;
		line-height: 60rpx;
		background-color: #f8f8f8;
	}
</style>
