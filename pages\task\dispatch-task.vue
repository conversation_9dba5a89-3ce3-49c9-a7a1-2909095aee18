<template>
	<Page :padding="false" title="信访舆情指派">
		<task-info-editor :companyInfo="companyInfo" />
	</Page>
</template>

<script>
	import taskInfoEditor from './task-info-editor.vue'
	import Page from '@/pages/component/Page.vue'
	import NaviBar from '@/pages/component/NaviBar.vue'
	export default {
		components: {
			Page,
			NaviBar,
			taskInfoEditor
		},
		data() {
			return {
				companyInfo: {}
			};
		},

		onLoad() {
			//开启监听选择收货地址事件
			uni.$on('choosePath', (res) => {
				this.companyInfo = res
			})
		},
		
		destroyed() {
			uni.$off('choosePath')
		}
	};
</script>
