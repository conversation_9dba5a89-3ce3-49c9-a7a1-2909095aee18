<template>
  <view style="z-index: 99">
    <template-form
      ref="templateForm"
      style="background-color: #fff"
      :form-data="formData"
      :template="template"
      :editable="editable"
      :parentHeight="formLayoutHeight"
    />
  </view>
</template>
<script>
import { queryTaskFormTemplate, queryRecordData } from "@/api/record.js";
import templateForm from "@/pages/form/template-form.vue";
export default {
  data() {
    return {
      recordId: "",
      templateId: "202301161455320c96da3b39e64b9cbbb1e19839bee6d3",
      formData: {},
      template: {},
      editable: false,
      pageHeight: 600,
    };
  },
  props: ["YCJDXH"],
  components: {
    templateForm,
  },
  computed: {
    formLayoutHeight: function () {
      return this.pageHeight - 20;
    },
  },
  mounted() {
    this.recordId = this.YCJDXH;
    console.log(this.recordId);
    queryTaskFormTemplate("", this.templateId).then((res) => {
      this.template = res;
      queryRecordData(this.templateId, this.recordId).then((r) => {
        if (r) {
          this.formData = r;
          console.log(r);
          //   this.formData.RWBT = uni.getStorageSync("record-data").RWBT;
          //   this.showDetailType = true;
        }
      });
    });
  },
};
</script>
<style scoped>
uni-scroll-view {
  height: 100% !important;
  margin-bottom: 20rpx;
}
</style>
