<!--
 * @Author: 姚进玺 <EMAIL>
 * @Date: 2021-07-15 14:55:24
 * @LastEditors: 姚进玺 <EMAIL>
 * @LastEditTime: 2022-05-25 15:29:34
 * @FilePath: /UNI_APP_ShanDong/pages/task/inspect-object-item.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
    <view class="flex-row-layout inspect-object-item" @click="turnToEditRecord">
        <text style="flex: 1">{{ inspectObject.RWMC }}</text>
        <image class="navi-next" />
        <view @click.stop="finishItemTask()" class="navi-btn"> 任务结束</view>
    </view>
</template>

<script>
export default {
    name: 'InspectObjectItem',
    props: {
        inspectObject: {
            type: Object,
            default: () => {
                return {
                    RWMC: '污染源名称',
                    RWBH: '综合执法任务',
                    WRYBH: '污染源编号',
                    XFYQBH: '信访舆情编号'
                };
            }
        }
    },

    methods: {
        /**
         * 跳转编辑笔录页面
         */
        turnToEditRecord() {
            let recordResolverUrl =
                '/pages/record/record-resolver?canFinish=false&targetAssigned=true';
            let target = this.inspectObject;
            uni.setStorageSync('record-data', target);
            uni.navigateTo({
                url: recordResolverUrl
            });
        },

        finishItemTask() {
            console.log(this.inspectObject);
            this.$emit('finishtask', this.inspectObject);
        }
    }
};
</script>

<style scoped>
.inspect-object-item {
    height: 92rpx;
}

.navi-btn {
    padding: 0 20rpx;
    border: 1rpx solid #eee;
    color: #fff;
    background: #009bff;
    border-radius: 16rpx;
}
</style>
