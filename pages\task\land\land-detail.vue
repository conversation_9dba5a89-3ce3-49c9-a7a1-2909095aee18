<template>
    <Page :padding="false" title="地块表单">
        <view
            class="flex-column-layout form-content-layout"
            style="justify-content: flex-start"
        >
            <!-- <view
            v-if="explicit"
            id="selectWryItem"
            class="select-wry-list"
            @click="chooseCompany"
        >
            <view>{{ companyInfo.WRYMC || defaultInfo }}</view>
            <image class="nextImg" :src="nextIcon"> </image>
        </view> -->
            <view
                id="formParent"
                style="width: 100%; height: calc(100% - 96rpx)"
            >
                <template-form
                    :parentHeight="height"
                    :template="template"
                    :editable="editable"
                    :record-id="recordId"
                    :form-data="formData"
                />
            </view>
            <view
                id="dispatchButton"
                class="power-button power-button-primary dispatch-button"
                @click="saveForm"
            >
                保存
            </view>
        </view>
    </Page>
</template>

<script>
import Page from '@/pages/component/Page.vue';
import { postQueryFormList } from '@/api/new.js';
import { dynamicformsave } from '@/api/record.js';
import { deepCopyObject } from '@/common/merge.js';
import { guid } from '@/common/uuid.js';
import onform from '@/api/onsaveForm.js';
import formUtil from '@/pages/form/Form.js';
import styleUtil from '@/common/style.js';
import templateForm from '@/pages/form/template-form.vue';
import nextIcon from '@/static/img/navi_next_icon.png';
import loginService from '@/api/login-service.js';
const TASK_INFO_TEMPLATE_ID = '20220518165703bd29d2e037c341ac8e48f0aca85b9f06';

export default {
    name: 'TaskInfoEditor',
    props: {
        companyInfo: {
            type: Object,
            default: () => {
                return null;
            }
        }
    },

    components: {
        templateForm,
        Page
    },

    data() {
        let defaultRecordId = guid();
        return {
            nextIcon,
            explicit: false,
            editable: true,
            mrzList: [],
            recordId: defaultRecordId,
            defaultInfo: '选择企业',
            height: -1,
            template: {}, //动态表单模板
            formData: {} //动态表单数据
        };
    },

    mounted() {
        let self = this;
        this.$store.state.mrzList = [];
        this.$store.state.verifyList = [];
        this.getList();

        // #ifdef APP-PLUS
        this.height = 600;
        let dimens = [
            styleUtil.getNodeLayout(this, '#formParent'),
            styleUtil.getNodeLayout(this, '#selectWryItem'),
            styleUtil.getNodeLayout(this, '#dispatchButton')
        ];

        Promise.all(dimens).then(layouts => {
            let totalHeight = layouts[0].height;
            let selectWryItemHeight = layouts[1].height;
            let dispatchButtonHeight = layout[2].height;
            this.height = totalHeight - dispatchButtonHeight;
        });

        styleUtil.getNodeLayout(this, '#formParent').then(layout => {
            this.height = layout.height;
        });
        // #endif
    },

    methods: {
        //获取表单数据及污染源的数据
        getList() {
            postQueryFormList({
                service: 'GET_DYNAMICFORM_MODEL',
                bbxh: TASK_INFO_TEMPLATE_ID,
                userId: loginService.getAuthUserId() || 'SDSZFJ'
            }).then(res => {
                this.template = formUtil.packPeerElementIntoGroup(
                    res.datas_json
                );
                this.mrzList = deepCopyObject(templateInfo.child);
                // 加载附件
                let defaultValues = this.extractDefaultValues(this.mrzList);
                this.formData = Object.assign({}, defaultValues);
            });
        },

        //从模板配置中抽取默认值
        extractDefaultValues(elementValues) {
            let defaultValues = {};
            let self = this;
            elementValues.forEach(element => {
                let field = element.dbcolum;
                let value = element.MRZ;
                if (value) {
                    defaultValues[field] = value;
                }
            });
            return defaultValues;
        },

        //企业的跳转
        chooseCompany() {
            uni.navigateTo({
                url: `/pages/wry/wry-list`
            });
        },

        //保存表单
        saveForm() {
            // 表单验证，首先要获取距离顶部的高度，在判断必填项是否填写
            let self = this;
            styleUtil.getNodeLayout(self, '#formParent').then(layout => {
                let dataType = onform.saveTemplateForm(
                    self.formData,
                    self,
                    layout.top
                );
                if (dataType === true) {
                    self.postSaveForm();
                }
            });
        },

        postSaveForm() {
            let formData = this.$store.state.formData || this.formData;
            this.log(this.formData, '表单数据');
            let self = this;

            //
            let form = {
                batch_id: self.recordId,
                // xh:self.recordId,
                bzbh: '',
                ywlcdybh: '',
                client_type: 'mobile_web',
                service: 'DYNAMICFORM_SAVE',
                record_id: self.recordId,
                template_id: TASK_INFO_TEMPLATE_ID,
                user_id: loginService.getAuthUserId() || 'SDSZFJ'
            };
            let tableName = self.template['templateTable'];
            let data = deepCopyObject(formData);
            data.WRYBH = uni.getStorageSync('water_detail').WRYBH;
            data.XH = self.recordId;
            if (
                this.explicit &&
                (this.companyInfo === 'undefined' || this.companyInfo === null)
            ) {
                uni.showToast({
                    title: '请选择企业',
                    duration: 2000,
                    icon: 'success'
                });
                return;
            }
            form[tableName] = data;
            dynamicformsave(form)
                .then(res => {
                    uni.showToast({
                        title: '保存成功！',
                        duration: 2000,
                        icon: 'success'
                    });
                    setTimeout(() => {
                        uni.navigateBack({
                            delta: 1
                        });
                    }, 1000);
                })
                .catch(() => {
                    uni.showToast({
                        title: '保存失败，请重新保存',
                        duration: 2000,
                        icon: 'none'
                    });
                });
        }
    }
};
</script>

<style scoped>
.form-content-layout {
    width: 100%;
    height: 100%;
}

.template-form-layout {
    border-radius: 5px;
    padding: 10px;
    width: calc(100% - 20px);
    background-color: #fff;
}

.select-wry-list {
    width: calc(100% - 20px);
    height: 110rpx;
    line-height: 110rpx;
    background-color: #fff;
    font-size: 34rpx;
    color: #999;
    padding: 0 22rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.nextImg {
    width: 34rpx;
    height: 34rpx;
}

.record-fragment {
    height: 100%vh;
}

.dispatch-button {
    position: fixed;
    bottom: 0;
    width: calc(100% - 64rpx);
    height: 96rpx;
    line-height: 96rpx;
    padding-top: 0;
    border-radius: 0;
}
</style>
