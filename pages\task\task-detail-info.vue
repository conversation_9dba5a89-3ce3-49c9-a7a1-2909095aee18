<!--
 * @Author: <PERSON><PERSON>inx
 * @Date: 2021-05-20 14:46:26
 * @LastEditTime: 2022-07-28 10:59:36
 * @LastEditors: 姚进玺 <EMAIL>
 * @Description: In User Settings Edit
 * @FilePath: /UNI_APP_ShanDong/pages/task/task-detail-info.vue
-->

<template>
    <view>
        <dl class="pd-dlbx5">
            <dt>执法记录</dt>
            <dd @click="InfoRouter('enforceMent')">
                <h1>
                    <em class="emic1"
                        >执法次数<i style="color: #2084ea">{{
                            enforceMent.ALLNUM
                        }}</i></em
                    >
                    <em class="emic2"
                        >发现问题数<i style="color: #f49127">{{
                            enforceMent.FXWFNUM
                        }}</i></em
                    >
                </h1>
                <h2 v-if="enforceMent.FXWFNUM > 0">
                    <em class="emic5">发现问题</em>
                </h2>
                <p v-for="(item, index) in enforceMent.wflx" :key="index">
                    {{ item }}
                </p>
            </dd>
            <div
                style="padding: 0 30rpx; color: red"
                v-if="enforceMent.ALLNUM > 1"
            >
                该企业今年已被检查{{ enforceMent.ALLNUM }}次，请注意企业检查频次
            </div>
        </dl>
        <div class="gap"></div>
        <div class="gap"></div>
        <dl class="pd-dlbx5">
            <dt>处罚记录</dt>
            <dd @click="InfoRouter('punishList')">
                <h1>
                    <em class="emic3"
                        >处罚次数<i style="color: #2084ea">{{
                            punishList.ALLNUM
                        }}</i></em
                    ><em class="emic4"
                        >处罚金额<i style="color: #f49127"
                            >{{ punishList.CFJE }}万</i
                        ></em
                    >
                </h1>
                <h2 v-if="punishList.ALLNUM > 0">
                    <em class="emic5">违法行为</em>
                </h2>
                <p v-for="(item, index) in punishList.wfxw" :key="index">
                    {{ item }}
                </p>
            </dd>
        </dl>
    </view>
</template>

<script>
import templateForm from '@/pages/form/template-form.vue';
import {
    queryPunishAndInspectRecord,
    queryTaskFormTemplate,
    queryRecordData
} from '@/api/record.js';
export default {
    props: {
        wrybh: {
            type: String
        },

        xczfbh: {
            type: String
        }
    },
    components: {
        templateForm
    },
    data() {
        return {
            punishList: {}, //处罚的详情
            enforceMent: {} //执法的详情
        };
    },
    computed: {
        formLayoutHeight: function () {
            return this.pageHeight - 80;
        }
    },
    watch: {},

    mounted() {
        queryPunishAndInspectRecord(this.xczfbh, this.wrybh).then(res => {
            this.punishList = res.data_json.cfjl || {};
            this.enforceMent = res.data_json.zfjl || {};
        });
    },

    methods: {
        InfoRouter(type) {
            // return; //这里只是暂时屏蔽，后续会根据业务场景来放开
            uni.navigateTo({
                url: `/pages/task/task-info-list?wrybh=${this.wrybh}&xczfbh=${this.xczfbh}&type=${type}`
            });
        }
    }
};
</script>

<style scoped>
.pd-dlbx5 {
    background: #fff;
    border-radius: 9.0579rpx;
    margin: 0 24.1545rpx;
    position: relative;
}

.pd-dlbx5 dt {
    background: url(~@/static/img/task/images/titbg1.png) no-repeat center;
    background-size: 303.7439rpx 42.2705rpx;
    font-size: 32.9854rpx;
    color: #fff;
    text-align: center;
    line-height: 42.2705rpx;
    position: relative;
    top: -9.6618rpx;
}

.pd-dlbx5 dd {
    padding: 18.1159rpx 30.1932rpx 30.1932rpx;
}

.pd-dlbx5 dd p + p {
    padding-top: 18.1159rpx;
}

.pd-dlbx5 dd p {
    font-size: 28.57rpx;
    color: #333;
}

.pd-dlbx5 dd p em {
    color: #666;
}

.pd-dlbx5 dd h1 {
    border-bottom: 1px solid #ddd;
    display: flex;
    justify-content: space-between;
    padding-bottom: 27.1739rpx;
}

.pd-dlbx5 dd h1 em {
    background-repeat: no-repeat;
    background-position: left center;
    background-size: 36.2319rpx 36.2319rpx;
    padding-left: 56.1594rpx;
    font-size: 32.57rpx;
    color: #333;
}

.pd-dlbx5 dd h1 em i {
    font-size: 30.1932rpx;
    margin-left: 24.1545rpx;
}

.pd-dlbx5 dd h1 em.emic1 {
    background-image: url(~@/static/img/task/images/emic1.png);
}

.pd-dlbx5 dd h1 em.emic2 {
    background-image: url(~@/static/img/task/images/emic2.png);
}

.pd-dlbx5 dd h1 em.emic3 {
    background-image: url(~@/static/img/task/images/emic3.png);
}

.pd-dlbx5 dd h1 em.emic4 {
    background-image: url(~@/static/img/task/images/emic4.png);
}

.pd-dlbx5 dd h2 {
    padding: 24.1545rpx 0;
}

.pd-dlbx5 dd h2 em {
    font-size: 30.7777rpx;
    background: url(~@/static/img/task/images/emic5.png) no-repeat left center;
    padding-left: 56.1594rpx;
    background-size: 36.2319rpx 36.2319rpx;
}
</style>
