<template>
    <Page :padding="false" @layoutAware="onFixedContentHeight">
        <template v-slot:bar>
            <NaviBar title="任务详情">
                <template v-slot:option>
                    <view class="record-list">
                        <image
                            @click="goCompanyDetail()"
                            :src="record_list"
                            class="record-ilist-image"
                        />
                        <image
                            :src="record_navagation"
                            @click="navigation()"
                            class="record-ilist-image"
                            v-if="!isH5"
                        />
                    </view>
                    <!-- <view @click="goCompanyDetail()">污染源详情</view> -->
                </template>
            </NaviBar>
        </template>
        <template-form
            ref="templateForm"
            style="
                background-color: #fff;
                height: calc(100vh - 180rpx);
                overflow: auto;
            "
            :round="false"
            :form-data="formData"
            :template="template"
            :editable="editable"
            :parentHeight="formLayoutHeight"
        >
            <PCard
                v-if="supportMultiInspectObject"
                title="检查企业"
                :round="false"
            >
                <template v-for="(target, index) in targets">
                    <view
                        v-if="index > 0"
                        style="width: 100%; height: 1px; background-color: #ccc"
                    />
                    <inspect-object-item
                        @finishtask="finishtask"
                        :key="index"
                        :inspectObject="target"
                    />
                </template>
            </PCard>
            <!-- 远程监督帮扶的附件 -->
            <helpFjList :YCJDXH="YCJDXH" v-if="YCJDXH != ''" />
            <task-detail-info
                v-if="workflowId !== WORKFLOW_TYPE_XFYQ"
                v-show="showDetailType"
                :wrybh="companyId"
                :xczfbh="xczfbh"
            />
        </template-form>

        <navi-bar-option-menu
            ref="chooseMenuDialog"
            :menus="chooseFileMenus"
            @menuClick="onChooseMenuClick"
        />
        <operate-menu-dialog
            ref="inspectTypeDialog"
            :menus="inspectTypes"
            @menuClick="onInspectTypeChoosed"
        />

        <view
            v-if="canForward && supportMultiInspectObject"
            class="flex-column-layout task-detail__float-button"
            @click="transferTask()"
        >
            <image
                style="width: 16px; height: 16px"
                mode="aspectFit"
                :src="iconTransfer"
            />
            <text>转办</text>
        </view>
        <show-modal></show-modal>
        <view class="flex-row-layout task-detail__bottom-bar">
            <p-button
                v-if="showMutableMenu"
                class="task-detail__bottom-btn"
                style="background: #00cf6f"
                :name="mutableMenuText"
                @click.native="clickMutableMenu()"
            />
            <p-button
                name="执法"
                class="task-detail__bottom-btn"
                style="background: #ff9300"
                @click.native="checkPollutionHealth()"
            />
        </view>
    </Page>
</template>

<script>
import naviBarOptionMenu from '@/pages/component/dialog/right-top-dialog.vue';
import operateMenuDialog from '@/pages/component/dialog/operate-menu-dialog.vue';

import record_addIcon from '@/static/img/record/record_add.png';
import record_list from '@/static/img/record/record_list.png';
import record_navagation from '@/static/img/record/record_navagation.png';
import taskDetailInfo from './task-detail-info.vue';
import iconSave from '@/static/img/record/icon_form_save.png';
import iconTransfer from '@/static/img/task/icon_transfer.png';
import NoData from '@/components/no-data.vue';
import Page from '@/pages/component/Page.vue';
import NaviBar from '@/pages/component/NaviBar.vue';
// import templateForm from "@/pages/form/template-form.vue";
import helpFjList from './help-fj-list.vue'; //远程监督帮扶附件
import formOperateMenuItem from '@/pages/record/form-operate-menu-item.vue';
import PCard from '@/pages/component/PCard.vue';
import uriUtil from '@/common/net/uri.js';
import navition from '@/api/map-navigation.js';
import { isH5 } from '@/common/config.js';
import {
    WORKFLOW_TYPE_XFYQ,
    queryTaskFormTemplate,
    queryRecordData,
    checkPollutionPositive
} from '@/api/record.js';
import taskService from '@/api/task-service.js';
import PButton from '@/components/p-button';
import InspectObjectItem from './inspect-object-item.vue';
import loginService from '@/api/login-service.js';

import formService from 'bowo-form/components/service/form-service.js';

export default {
    components: {
        naviBarOptionMenu,
        operateMenuDialog,
        Page,
        NaviBar,
        PCard,
        NoData,
        formOperateMenuItem,
        PButton,
        InspectObjectItem,
        taskDetailInfo,
        helpFjList
    },

    data() {
        return {
            isH5: isH5,
            WORKFLOW_TYPE_XFYQ,
            chooseFileMenus: [
                {
                    name: '导航',
                    id: 'navigation'
                },
                {
                    name: '污染源详情',
                    id: 'enterpriseDetails'
                }
            ],
            inspectTypes: [
                {
                    name: '非现场执法',
                    id: 'unpresent'
                },
                {
                    name: '现场执法',
                    id: 'present'
                }
            ],
            record_addIcon, //打开拓展区域的图标
            record_list, //污染源详情的图标
            record_navagation, //导航图标
            iconTransfer,
            title: '公司名称',
            recordId: '',
            xczfbh: '',
            templateId: '',
            formData: {},
            template: {},
            editable: false,
            pageHeight: 600,
            menus: [
                {
                    name: '保存',
                    icon: iconSave
                }
            ],
            workflowId: '',
            taskId: '',
            stepId: '',
            supportMultiInspectObject: false, //是否支持多家检查企业
            targets: [],
            companyId: '',
            hided: false,
            showDetailType: false,
            canForward: false, //是否可转发任务
            YCJDXH: '' //远程监督帮扶的表单recordId
        };
    },

    computed: {
        formLayoutHeight: function () {
            return this.pageHeight - 80;
        },

        mutableMenuText: function () {
            return this.supportMultiInspectObject ? '结束任务' : '转办';
        },

        showMutableMenu: function () {
            return this.supportMultiInspectObject || this.canForward;
        }
    },

    onLoad(options) {
        this.$store.state.verifyList = [];
        this.workflowId = options.workflowId;
        this.taskId = options.taskId;
        this.stepId = options.stepId;
        this.YCJDXH = options.YCJDXH;
        this.xczfbh = uni.getStorageSync('record-data').YWXTBH;
        this.companyId = uni.getStorageSync('record-data').WRYBH;
        let xfbs = options.XFBS;
        this.supportMultiInspectObject = '1' === xfbs;
    },

    mounted() {
        this.canForward = loginService.isAdministrator();
        this.queryTaskTemplateId();
        this.loadInspectObjects();
    },

    onHide() {
        this.hided = true;
    },

    onShow() {
        if (this.hided) {
            this.loadInspectObjects();
            this.hided = false;
            this.getData();
        }
    },

    methods: {
        // 企业详情
        goCompanyDetail() {
            uni.navigateTo({
                url: `/pages/main/PollutionDetail?title=污染源详情&recordId=${this.companyId}`
            });
        },

        /**
         * 综合执法、信访舆情任务底部第一个菜单不一样，需要分别处理
         */
        clickMutableMenu() {
            if (this.supportMultiInspectObject) {
                this.askBeforeFinish();
            } else {
                this.transferTask();
            }
        },

        askBeforeFinish() {
            let _self = this;
            if (this.targets.length > 0) {
                uni.showToast({
                    title: '需要办结完所有任务才能结束',
                    duration: 2000,
                    icon: 'none'
                });
                return;
            }
            this.$showModal({
                title: '结束任务',
                content: '确定结束当前任务？',
                success(resp) {
                    if (resp.confirm) {
                        taskService
                            .finishXfyqTask(
                                _self.taskId,
                                _self.stepId,
                                _self.xczfbh,
                                '1'
                            )
                            .then(resp => {
                                uni.navigateBack({
                                    delta: 1
                                });
                            })
                            .catch(error => {
                                console.log(
                                    `结束任务出错：${JSON.stringify(
                                        error,
                                        null,
                                        4
                                    )}`
                                );
                            });
                    }
                }
            });
        },

        // 转办
        transferTask() {
            let urlParams = {
                taskId: this.taskId,
                stepId: this.stepId
            };
            uni.navigateTo({
                url: `/pages/task/task-transfer?${uriUtil.transformObjectToUrlParams(
                    urlParams
                )}`
            });
        },

        queryTaskTemplateId() {
            taskService.queryTaskMainTemplate(this.workflowId).then(resp => {
                this.templateId = resp.data_json;
                let taskInfo = uni.getStorageSync('record-data');
                this.recordId = taskInfo.YWXTBH;
                this.title = taskInfo.RWBT;
                this.getData();
            });
        },

        /**
         * 加载已关联检查对象（污染源）
         */
        loadInspectObjects() {
            if (this.supportMultiInspectObject) {
                taskService.queryTaskInspectObjects(this.taskId).then(resp => {
                    let inspectObjects = resp.data_json;
                    if (Array.isArray(inspectObjects)) {
                        //防止未发起流程的企业，做一次过滤
                        this.targets = inspectObjects.filter(io => {
                            let taskId = io.YWXTBH || '';
                            return taskId !== '';
                        });
                    }
                });
            }
        },

        getData() {
            queryTaskFormTemplate('', this.templateId).then(res => {
                this.template = res;
                // queryRecordData(this.templateId, this.recordId).then(r => {
                //     if (r) {
                //         // console.log(r);
                //         this.formData = r;
                //         this.formData.RWBT =
                //             uni.getStorageSync('record-data').RWBT;
                //         this.showDetailType = true;
                //     }
                // });
                formService
                    .getRecordDetailById(this.templateId, this.recordId)
                    .then(r => {
                        if (r) {
                            // console.log(r);
                            this.formData = r;
                            this.formData.RWBT =
                                uni.getStorageSync('record-data').RWBT;
                            this.showDetailType = true;
                        }
                    });
            });
        },

        onFixedContentHeight(layout) {
            this.pageHeight = layout.height;
        },

        /**
         * 校验企业正面清单情况
         */
        checkPollutionHealth() {
            let taskInfo = uni.getStorageSync('record-data');
            let positive = (taskInfo.SFZMQD || '0') === '1';
            if (positive) {
                this.$refs.inspectTypeDialog.show();
                // this.doAbsentInspect();
            } else {
                this.doPresentInspect();
            }
        },

        onInspectTypeChoosed(type) {
            if (type.id === 'present') {
                this.doPresentInspect();
            } else {
                this.doAbsentInspect();
            }
        },

        /**
         * 现场检查
         */
        doPresentInspect() {
            let recordResolverUrl = '/pages/record/record-resolver';
            if (this.supportMultiInspectObject) {
                uni.setStorageSync('record-data', {
                    relateTaskId: this.taskId,
                    JD: '',
                    WD: '',
                    RWBT: ''
                });
                recordResolverUrl += `?relateTaskId=${this.taskId}&canFinish=false&targetAssigned=false`;
            } else {
                recordResolverUrl += '?targetAssigned=true';
            }
            uni.navigateTo({
                url: recordResolverUrl
            });
        },

        /**
         * 非现场执法
         */
        doAbsentInspect() {
            let absentInspectUrl =
                '/pages/record/absent/absent-record-resolver';
            let originalTaskInfo = uni.getStorageSync('record-data');
            let pollution = {
                id: originalTaskInfo.WRYBH,
                name: originalTaskInfo.RWBT
            };
            let taskInfoParam = encodeURIComponent(
                JSON.stringify(originalTaskInfo)
            );
            let pollutionParam = encodeURIComponent(JSON.stringify(pollution));
            let recordMasterId = originalTaskInfo.YWXTBH;
            absentInspectUrl += `?taskInfo=${taskInfoParam}&pollution=${pollutionParam}&foreignId=${recordMasterId}`;
            uni.navigateTo({
                url: absentInspectUrl
            });
        },

        seeImg() {
            this.$refs.chooseMenuDialog.show = true;
        },

        onChooseMenuClick(menu) {
            if (menu.id == 'enterpriseDetails') {
                uni.navigateTo({
                    url: `/pages/main/PollutionDetail?title=污染源详情&recordId=${this.companyId}`
                });
            }
            if (menu.id === 'navigation') {
                this.navigation();
            }
        },

        //企业的导航
        navigation() {
            navition.getMapNavigation(
                this.formData.WRYDZ,
                this.formData.JD,
                this.formData.WD
            );
        },

        finishtask(data) {
            let _self = this;
            taskService
                .finishXfyqTask(_self.taskId, _self.stepId, data.YWXTBH, '0')
                .then(resp => {
                    this.loadInspectObjects();
                })
                .catch(error => {
                    console.log(
                        `结束任务出错：${JSON.stringify(error, null, 4)}`
                    );
                });
        }
    }
};
</script>

<style scoped>
.task-detail__float-button {
    width: 42px;
    height: 42px;
    position: fixed;
    bottom: 64px;
    right: 16px;
    border-radius: 21px;
    background-color: #37d4de;
    color: white;
    font-size: 12px;
}

.record-list {
    width: 120rpx;
    display: flex;
    align-items: center;
    justify-content: space-around;
}

.record-ilist-image {
    height: 40rpx;
    width: 40rpx;
    margin-right: 6rpx;
}

.task-detail__bottom-bar {
    position: fixed;
    font-size: 18px;
    bottom: 0;
}

.task-detail__bottom-btn {
    flex: 1;
    position: relative;
}
</style>
