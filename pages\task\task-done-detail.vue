<template>
	<Page :padding="false" @layoutAware="onFixedContentHeight" style="overflow: auto;background-color: #F4F4F4;">
		<template v-slot:bar>
			<NaviBar :title="title" />
		</template>
		<PCard title="基本信息"></PCard>
			<view class="template-form-layout" id="formParent">
				<template-form ref="templateForm" :form-data="formData" :template="template" :editable="editable" :parentHeight="formLayoutHeight"></template-form>
			</view>
		
		<PCard title="笔录信息">
			<template v-slot:option>
				<span v-show="taskStatus == 'Finished'" @click="showDetail()" style="color:#0090EF">点击查看详情></span>
			</template>
			<view style="padding: 30rpx;" v-show="taskStatus != 'Finished'">任务未结束，暂无信息</view>
			
			<!--  -->
		</PCard>
		<view class="margin-top10"></view>
		<PCard title="办理过程"></PCard>
		<ul class="pd-ulaxis margin-top10">
			<li v-for="item in taskList">
				<small>{{item.YHM}}</small>
				<dl>
					<dt>{{item.BZMC}}  <span style="float: right;">{{item.JSSJ}}</span></dt>
					<dd>{{item.YHM}}:{{item.CLRYJ}}</dd>
					<dd></dd>
				</dl>
			</li>
		</ul>
		
	</Page>
</template>

<script>
	import { queryTaskProcessSteps } from '@/api/record.js';
	import NaviBar from '@/pages/component/NaviBar.vue';
	import Page from '@/pages/component/Page.vue';
	import templateForm from '@/pages/form/template-form.vue';
	import formOperateMenuItem from '@/pages/record/form-operate-menu-item.vue';
	import PButton from '@/components/p-button';
	import { postQueryWryjcTzForm} from '@/api/book.js';
	import PCard from '@/pages/component/PCard.vue';
	import formUtil from '@/pages/form/Form.js';
	export default {
		components: {
			PCard,
			Page,
			NaviBar,
			templateForm,
			formOperateMenuItem,
			PButton
		},
		
		data(){
			return {
				taskList: [],
				title: '公司名称',
				recordId: '',
				templateId: '',
				formData: {},
				template: {},
				editable: false,
				pageHeight: 600,
			}
		},
		
		computed: {
			formLayoutHeight: function() {
				return this.pageHeight - 180;
			}
		},
		
		onLoad(options) {
			this.templateId = '2020111014083832fdc394d7734dd4b0bdb87c1287253f';
			this.recordId = options.recordId;  // 记录id
			this.title = options.title; // 标题
			this.taskId = options.taskId;  // 任务id
			this.taskStatus = options.taskStatus;
			this.getData();
			this.renderTaskSteps();
		},
		
		methods:{
			// 获取任务流程
			renderTaskSteps(){
				queryTaskProcessSteps(this.taskId)
					.then(res=>{
						this.taskList = res.data_json.list;
					})
			},
			
			onFixedContentHeight(layout) {
				this.pageHeight = layout.height;
			},
			
			getData() {
				postQueryWryjcTzForm({
					service: 'QUERY_DYNAMICFORM_DATA',
					recordId: this.recordId,
					mbbh: this.templateId
				}).then(res => {
					this.formData = res.data;
					this.getTemplate();
				});
			},
			
			getTemplate() {
				let userInfo = uni.getStorageSync('userInfo');
				let userId = '';
				if (userInfo) {
					userId = userInfo.id;
				}
				let self = this;
				postQueryWryjcTzForm({
					service: 'GET_DYNAMICFORM_MODEL',
					bbxh: this.templateId,
					userId: userId || 'SDSZFJ'
				}).then(res => {
					this.template = formUtil.packPeerElementIntoGroup(res.datas_json);
				
					// 加载附件
					this.$templateUtils.loadAttachRecord(this.templateId, this.recordId, this.formData, this)
				
				});
			},
			// 显示详情
			showDetail(){
				uni.navigateTo({
					url: `/pages/book/book-info?xh=${this.recordId}`
				});
			}
		}
	}
</script>

<style>
	.margin-top10{
		margin-top: 30rpx;
		margin-bottom: 30rpx;
	}
</style>
