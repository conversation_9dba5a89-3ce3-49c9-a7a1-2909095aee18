<!--
 * @Author: your name
 * @Date: 2021-05-22 10:55:47
 * @LastEditTime: 2021-07-08 18:18:55
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /UNI_APP_ShanDong/pages/task/task-info-list.vue
-->

<template>
	<Page :title="this.infoType === 'cf'?'处罚列表':'执法列表'">
        <scroll-view :scroll-y="true" :style="pageListStyle">
            <uni-list v-if="infoList.length > 0">
                <uni-list-item v-for="(item,index) in infoList"
                            clickable  
                            @click="infoRouTer(item.XH)"
                            :key="index"
                            :title="item.RWLX"
                            :note="item.JSSJ"
                            :rightTextColor="item.SFFXSXHJWFXW === '1' ? '#CC3333' : '#00FF66'"
                            :rightText="item.SFFXSXHJWFXW === '1' ? '是' : '否'"></uni-list-item>
            </uni-list>
			<NoData 
							:type="infoList.length < 1 ? 'data' : ''" />
        </scroll-view>
	</Page>
</template>

<script>
	import NoData from '@/components/no-data.vue';
	import Page from '@/pages/component/Page.vue';
	import {
		postPunishiAndEnforceMentInfo
	} from '@/api/record.js'
	export default {
		components: {
			Page,
			NoData
		},

		data() {
			return {
                infoType:'cf',
				wrybh: '',
				xczfbh: '',
				infoList: [] //处罚或执法的列表
			};
		},

        computed: {
            pageListStyle() {
                return {
                    height: 'calc(100vh - 160rpx)',
                };
            }
        },

		onLoad(options) {
            this.infoType = options.type === 'enforceMent'? 'zf':'cf'
			this.wrybh = options.wrybh,
			this.xczfbh = options.xczfbh
			this.getInfoList()
		},

		methods: {
			getInfoList() {
				postPunishiAndEnforceMentInfo({
					wrybh: this.wrybh,
					method: this.infoType,
					xczfbh: this.xczfbh
				}).then((res) => {
					this.infoList = res.data_json.data
				})
			},

            infoRouTer(xh){
                //页面跳转-跳转至台账详情页面
                uni.navigateTo({
                        url: `/pages/book/book-info?xh=${xh}`
                    })
            }
		},
	};
</script>

<style scoped
	   lang="stylus">

</style>
