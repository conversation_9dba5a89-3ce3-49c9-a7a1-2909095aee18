<template>
    <Page :padding="false" @layoutAware="onFixedContentHeight">
        <template v-slot:bar>
            <NaviBar title="执法任务">
                <template v-slot:navi>
                    <image
                        @click="onBackIndex"
                        :src="backurl"
                        style="width: 39rpx; height: 39rpx; margin-left: 2rpx"
                    >
                    </image>
                </template>
                <template v-slot:option>
                    <view @click="addNewRecord()">发起检查</view>
                </template>
            </NaviBar>
        </template>

        <uni-drawer
            :width="340"
            ref="showRight"
            mode="right"
            :mask-click="true"
        >
            <scroll-view style="height: 93%" scroll-y="true">
                <ConditionScreening
                    @close="clickClose"
                    :conditionData="paramsData"
                    @changeData="changeData"
                />
            </scroll-view>
        </uni-drawer>

        <ul class="pd-ultbs1" style="position: inherit">
            <li
                :class="{ on: taskProcessStatus == 'todo' }"
                @click="changeTaskProcessStatus('todo')"
            >
                <i>待办任务</i>
            </li>
            <li
                :class="{ on: taskProcessStatus == 'done' }"
                @click="changeTaskProcessStatus('done')"
            >
                <i>已办任务</i>
            </li>
        </ul>

        <view class="layerBox" v-if="show">
            <view class="mask" @click="show = false"></view>
            <view class="dialog-container">
                <!-- 标题 -->
                <view class="title">提醒</view>

                <!-- 提示内容 -->
                <view class="content-box">
                    你有<i style="color: #e53e31">{{ WJSBJ_NUM }}</i
                    >条任务未及时办结，<i style="color: #e53e31">{{
                        WJSKZ_NUM
                    }}</i
                    >条任务未及时开展，请尽快处理
                </view>

                <!-- 按钮组 -->
                <view class="btn-group">
                    <button class="btn confirm" @click="show = false">
                        确定
                    </button>
                </view>
            </view>
        </view>

        <task-done-list
            v-if="taskProcessStatus == 'done'"
            @showSearchBox="showSearch"
            :refreshType="refreshType"
            :conditionData="paramsData"
        >
        </task-done-list>

        <section class="pd-main" v-show="taskProcessStatus == 'todo'">
            <div class="pd-inner" style="padding: 0 0 0 0">
                <view class="search-content">
                    <view class="search-bar">
                        <uni-search-bar
                            placeholder="请输入想要查询的污染源企业"
                            cancelButton="none"
                            cancelText=""
                            clearButton="always"
                            bgColor="#F7F7F7"
                            :radius="50"
                            @input="searchByKeyword"
                        />
                    </view>
                    <view class="more-search" @click="showSearch"></view>
                </view>
                <div class="gap"></div>
                <scroll-view
                    :scroll-y="true"
                    @scrolltolower="loadMore"
                    :style="pageListStyle"
                >
                    <view
                        class="company-list"
                        v-for="task in companyList"
                        @click="doRecordTask(task)"
                        :key="task.id"
                    >
                        <view class="flex-row-layout">
                            <text class="company-title company-title-text">{{
                                task.RWBT
                            }}</text>
                            <image
                                v-if="task.SFZMQD === '1'"
                                style="
                                    width: 62px;
                                    height: 20px;
                                    margin-left: 5px;
                                "
                                :src="iconAbsentStamp"
                            />
                        </view>
                        <view class="company-footer">
                            <view class="company-time">
                                <image :src="timeIcon" />
                                <text v-if="task.RQX"
                                    >任务期限：{{
                                        task.RQX.substring(0, 10)
                                    }}</text
                                >
                            </view>
                            <view class="company-type">
                                <text v-if="task.RWLXMC">{{
                                    task.RWLXMC
                                }}</text>
                            </view>
                            <view class="company-router">
                                <image :src="nextIcon" />
                            </view>
                        </view>
                        <view
                            class="flex-row-layout"
                            style="margin-bottom: 8px"
                        >
                            <view class="flex-row-layout" style="width: auto">
                                <image
                                    style="width: 27px; height: 22px"
                                    mode="aspectFit"
                                    :src="iconDistanceMark"
                                />
                                <view
                                    style="margin-left: 5px"
                                    class="company-distance"
                                >
                                    {{ getDistance(task.ZXJL || '--') }}
                                </view>
                            </view>
                            <!-- <text v-if="task.cgqx" class="company-delay">
                超期： {{ getDelay(task.cgqx) }}
              </text> -->
                            <text v-if="task.TXZT" class="company-delay">
                                {{ task.TXZT }}
                            </text>
                            <!-- <text v-else class="company-tips">
                <text>{{ getShowTips(task) }}</text>
              </text> -->
                        </view>
                    </view>
                    <operate-menu-dialog
                        ref="inspectTypeDialog"
                        :menus="inspectTypes"
                        @menuClick="onInspectTypeChoosed"
                    />
                    <NoData
                        v-if="!isListShown"
                        :type="companyList.length < 1 ? 'data' : ''"
                    />
                </scroll-view>
            </div>
        </section>
    </Page>
</template>

<script>
import { getUserInfoPermissions } from '@/api/predit.js';
import operateMenuDialog from '@/pages/component/dialog/operate-menu-dialog.vue';
import iconAbsentStamp from '@/static/img/task/icon_absent_stamp.png';
import iconDistanceMark from '@/static/img/task/icon_distance_mark.png';
import timeIcon from '@/static/img/time_template.png';
import nextIcon from '@/static/img/navi_next_icon.png';
import NoData from '@/components/no-data.vue';
import uriUtil from '@/common/net/uri.js';
import navition from '@/api/map-navigation.js';
import TaskDoneList from './task-done-list.vue';
import Page from '@/pages/component/Page.vue';
import NaviBar from '@/pages/component/NaviBar.vue';
import dropdownMenu from '@/components/ms-dropdown/dropdown-menu.vue';
import dropdownItem from '@/components/ms-dropdown/dropdown-item.vue';
import listIcon from '@/static/app/images/lstic1a.png';
import defaultNaviIcon from '@/static/img/icon_back_white.png';
import { postQueryWryjcTz, QUERY_RWDB_NUM } from '@/api/record.js';
import time from '@/common/timePicker.js';
import dayjs from 'dayjs';
import taskService from '@/api/task-service.js';

import ConditionScreening from './condition-screening.vue';
import { guid } from 'bowo-sdk/util/uuid.js';

//任务类型定义
const TASK_TYPE_ZHZF = 'ZHZF';
const TASK_TYPE_XFYQ = 'XFYQ';

export default {
    components: {
        Page,
        NaviBar,
        dropdownMenu,
        dropdownItem,
        NoData,
        TaskDoneList,
        ConditionScreening,
        operateMenuDialog
    },

    data() {
        return {
            show: false,
            WJSBJ_NUM: 0, //未及时办结
            WJSKZ_NUM: 0, //未及时开展
            taskProcessStatus: 'todo', //todo 待办，done 已办结
            iconAbsentStamp,
            iconDistanceMark,
            nextIcon,
            timeIcon,
            searchValue: '',
            pageHeight: -1,
            listimg: listIcon,
            backurl: defaultNaviIcon,
            timeInfo: {
                kssj: '',
                jssj: ''
            }, //时间的选择
            companyList: [],
            timeOutTipType: true,
            taskTypeOptions: [], //任务类型集合
            deadlineTypeOptions: [
                {
                    value: 'all',
                    text: '时间选择'
                },
                {
                    value: 'week',
                    text: '本周'
                },
                {
                    value: 'month',
                    text: '本月'
                },
                {
                    value: 'season',
                    text: '本季度'
                }
            ],
            deadlineTypeIndex: 0, //绑定的时间参数
            lastPageIndex: null, //最大页面数
            pageNum: 1, //当前的页面数量
            pageLoadType: true, //页面可滚动的状态
            isListShown: true, //列表是否显示
            deloutType: '',
            resume: false,
            paramsData: {}, //筛选 数据 参数
            refreshType: false,
            inspectTypes: [
                {
                    name: '水源地检查',
                    id: 'water'
                },
                {
                    name: '现场执法',
                    id: 'present'
                }
            ]
        };
    },

    watch: {
        //查询时间的变化
        deadlineTypeIndex(val) {
            this.companyList = [];
            this.changeTime(val);
        }
    },

    computed: {
        pageListStyle() {
            return {
                height: 'calc(100vh - 300rpx)',
                backgroundColor: '#fff'
            };
        }
    },

    onLoad(options) {
        this.deloutType = options.sfcq;

        QUERY_RWDB_NUM({}).then(res => {
            // console.log(res);
            this.WJSBJ_NUM = res.data_json.WJSBJ_NUM;
            this.WJSKZ_NUM = res.data_json.WJSKZ_NUM;
            if (this.WJSBJ_NUM > 0 || this.WJSKZ_NUM > 0) {
                // this.show = true;
            }
        });
    },

    mounted() {
        this.companyList = [];
        let self = this;
        uni.getStorage({
            key: 'YDZF_RWLX_DATA',
            success: function (res) {
                self.taskTypeOptions = res.data;
            }
        });
        this.getList();
    },

    onShow() {
        //获取列表数据
        if (this.resume === true) {
            this.refreshTask();
            this.resume = false;
        }
        uni.setStorageSync('task_type', '');
        uni.setStorageSync('FileFormData', '');
    },

    onHide() {
        this.resume = true;
    },

    methods: {
        //切换任务
        changeTaskProcessStatus(status) {
            this.taskProcessStatus = status;

            if (this.taskProcessStatus == 'todo') {
                this.refreshTask();
            }
        },

        /**
         * 查询待办任务列表
         */
        getList() {
            this.isListShown = true;
            taskService
                .queryTodoTasks(
                    this.searchValue,
                    this.pageNum,
                    this.deloutType,
                    this.paramsData
                )
                .then(res => {
                    let taskList = res.data_json.list || [];
                    this.companyList.push(...taskList);
                    this.lastPageIndex = res.data_json.lastPage;
                    this.pageLoadType = true;
                    if (this.companyList.length < 1) {
                        this.isListShown = false;
                    }
                })
                .catch(error => {
                    this.pageLoadType = true;
                    this.isListShown = false;
                });
        },

        refreshTask() {
            this.pageNum = 1;
            this.companyList = [];
            this.lastPageIndex = 1;
            this.getList();
        },

        // 新增新的记录
        addNewRecord() {
            getUserInfoPermissions({
                CDLX: 'FQRWLX'
            }).then(res => {
                this.inspectTypes = res.data_json.map(element => {
                    return {
                        name: element.CDMC,
                        id: element.CDWYBZ
                    };
                });
                this.$refs.inspectTypeDialog.show();
            });
            // uni.setStorageSync('record-data', {
            //     JD: '',
            //     WD: '',
            //     RWBT: ''
            // });
            // uni.navigateTo({
            //     url: `/pages/record/record-resolver?targetAssigned=false`
            // });
        },

        onInspectTypeChoosed(type) {
            if (type.id === 'SYDJC') {
                uni.setStorageSync('task_type', 'water');
            } else if (type.id === 'DKJC') {
                uni.setStorageSync('task_type', 'land');
            } else if (type.id === 'ZRBHQ') {
                uni.setStorageSync('task_type', 'area');
            } else {
                uni.setStorageSync('task_type', '');
            }
            uni.setStorageSync('record-data', {
                JD: '',
                WD: '',
                RWBT: ''
            });

            // let personOrgId = uni.getStorageSync("userInfo").orgid;
            // if (personOrgId === "370000") {
            //   uni.navigateTo({
            //     url:
            //       "/pages/task/Template?templateId=202405071150502372eeaf9da5401fa39f4286df38bf06&recordId=" +
            //       guid() +
            //       "&editable=1",
            //   });
            // } else {
            //     uni.navigateTo({
            //       url: `/pages/record/record-resolver?targetAssigned=false`,
            //     });
            // }
            uni.navigateTo({
                url:
                    '/pages/task/Template?templateId=202405071150502372eeaf9da5401fa39f4286df38bf06&recordId=' +
                    guid() +
                    '&editable=1'
            });
        },

        doWaterInspect() {
            uni.navigateTo({
                url: `/pages/task/water/water-list`
            });
        },

        //时间的变化选择
        changeTime(val) {
            this.timeInfo = time.timePicker(val);
            this.getList();
        },

        doRecordTask(item) {
            uni.setStorageSync('record-data', item);
            if (item.RWLX === 'SYDJC') {
                uni.setStorageSync('task_type', 'water');
            }
            uni.$on('overFinish', src => {
                this.companyList = [];
                this.getList();
            });
            let params = {
                workflowId: item.LCBH,
                taskId: item.YWXTBH,
                stepId: item.BZBH,
                XFBS: item.xfbs,
                YCJDXH: item.YCJDXH
            };
            uni.navigateTo({
                url: `/pages/task/task-detail?${uriUtil.transformObjectToUrlParams(
                    params
                )}`
            });
        },

        //关键字搜索
        searchByKeyword(parms) {
            this.searchValue = parms;
            this.pageNum = 1;
            this.companyList = [];
            this.getList();
        },

        loadMore() {
            if (this.pageLoadType) {
                this.pageNum++;
                if (this.pageNum > this.lastPageIndex) {
                    uni.showToast({
                        title: '已经没有数据了',
                        duration: 2000,
                        icon: 'none'
                    });
                } else {
                    this.getList();
                }
            }
        },

        //判断超期的时间
        getDelay(num) {
            num = num.replace('-', '');
            num = parseInt(num);
            let days = Math.floor(num / 24);
            let hours = num - days * 24;
            return days + '天' + hours + '小时';
        },

        //判断未超期的时间
        getShowTips(item) {
            let time1 = item.RQX;
            let time2 = dayjs(new Date()).format('YYYY-MM-DD:HH:MM');
            let time3 = dayjs(time1).diff(dayjs(time2), 'hour');
            // return ''
            if (time3 > 0) {
                let days = Math.floor(time3 / 24);
                let hours = time3 - days * 24;
                return '距任务期限剩余：' + days + '天' + hours + '小时';
            } else {
                time3 = Math.abs(time3);
                let days = Math.floor(time3 / 24);
                let hours = time3 - days * 24;
                return '超期' + days + '天' + hours + '小时';
            }
        },

        getDistance(index) {
            if (index !== '--') {
                return (index / 1000).toFixed(3) + 'km';
            } else {
                return index;
            }
        },

        onFixedContentHeight(layout) {
            this.pageHeight = layout.height;
        },

        //返回上一页，并根据进入的最大步骤来缓存步骤
        onBackIndex() {
            uni.reLaunch({
                url: '/pages/main/portal'
            });
        },

        taskNavigation(data) {
            navition.getMapNavigation(data.RWBT, data.JD, data.WD);
        },

        //显示筛选弹窗
        showSearch() {
            this.$refs.showRight.open();
        },
        clickClose() {
            this.$refs.showRight.close();
        },
        changeData(data) {
            this.paramsData = data;

            if (this.taskProcessStatus == 'done') {
                this.refreshType = !this.refreshType;
            } else {
                this.refreshTask();
            }
        }
    }
};
</script>

<style scoped>
.record-task-list {
    margin-top: 10px;
    background-color: #fff;
}

.book {
    width: 100%;
    background-color: #f1f2f6;
}

.mk {
    background-color: #009bff;
    font-size: 22rpx;
    padding: 2rpx 2rpx;
    color: #fff;
    border-radius: 4rpx;
    margin-left: 2rpx;
}

.company-list {
    margin-left: 28rpx;
    background-color: #fff;
    border-bottom: 1rpx solid #eeeeee;
}

.company-title {
    font-weight: 500;
    color: #333333;
    font-size: 30rpx;
    padding: 16rpx 0 12rpx 0;
}

.company-footer {
    display: flex;
    align-items: center;
    width: 100%;
    padding-bottom: 12rpx;
}

.company-time {
    width: 46%;
    display: flex;
    align-items: center;
}

.company-time image {
    width: 24rpx;
    height: 24rpx;
}

.company-type {
    width: 46%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin-bottom: 10rpx;
}

.company-type text {
    background-color: #3f97dc;
    color: #fff;
    font-size: 24rpx;
    padding: 2rpx 20rpx;
    border-radius: 20rpx;
}

.company-time text {
    font-size: 26rpx;
    color: #666;
    padding-left: 12rpx;
}

.company-router image {
    width: 28rpx;
    height: 28rpx;
    position: relative;
    bottom: 30rpx;
    left: 16rpx;
}

.company-delay {
    margin-left: auto;
    margin-right: 10px;
    color: red;
}

.company-tips {
    margin-left: auto;
    margin-right: 10px;
    color: #ff9900;
}

.search-bar {
    width: 90%;
}

.more-search {
    width: 10%;
    background: url('~@/static/img/icon_search_blue.png') no-repeat center;
    background-color: #ffffff;
    background-size: 50rpx auto;
}

.search-content {
    display: flex;
}

.layerBox {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 999;
}

.mask {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 998;
    display: block;
}

.dialog-container {
    width: 80%;
    background: #fff;
    border-radius: 12rpx;
    padding: 30rpx;
    z-index: 999;
    /* 确保对话框在遮罩层之上 */
}

.title {
    font-size: 32rpx;
    font-weight: bold;
    text-align: center;
    color: #333;
    margin-bottom: 20rpx;
}

.content-box {
    font-size: 28rpx;
    color: #666;
    text-align: center;
    margin-bottom: 30rpx;
}

.input-box {
    margin-bottom: 30rpx;
}

.reason-input {
    width: 100%;
    height: 160rpx;
    border: 1rpx solid #ddd;
    border-radius: 8rpx;
    padding: 20rpx;
    font-size: 28rpx;
    box-sizing: border-box;
}

.btn-group {
    display: flex;
    justify-content: center;
}

.btn {
    width: 45%;
    height: 80rpx;
    line-height: 80rpx;
    text-align: center;
    border-radius: 40rpx;
    font-size: 28rpx;
}

.cancel {
    background: #f5f5f5;
    color: #666;
}

.confirm {
    background: #0faeff;
    color: #fff;
}
</style>
