<template>
    <Page title="转办"
		:padding="false">
        <view class="template-form-layout" id="formParent">
			<template-form 
				ref="templateForm" 
				:form-data="formData" 
				:template="template" 
				:editable="editable" 
				:parentHeight="formLayoutHeight">
				<view
					style="width: 100%; height: 1px; background-color: #f4f4f4;"
				/>
			</template-form>
			
		</view>
        <PButton 
			name="已确认"
			@click.native="fixedPersons" 
		/>
    </Page>
</template>

<script>
	import templateForm from '@/pages/form/template-form.vue';
	import { EVENT_SELECT_CONFIRM } from '@/pages/department/department.js';
	import { deepCopyObject } from '@/common/merge.js';
	import NaviBar from '@/pages/component/NaviBar.vue'
	import Page from '@/pages/component/Page.vue'
	import nextIcon from '@/static/img/navi_next_icon.png';
	import PButton from '@/components/p-button'
	import formUtil from '@/pages/form/Form.js';
	import onform from '@/api/onsaveForm.js';
	import styleUtil from '@/common/style.js';
	import { postQueryWryjcTzForm} from '@/api/book.js';
	import { guid } from '@/common/uuid.js';
	import { postForwardPerson, dynamicformsave } from '@/api/record.js'
	import loginService from '@/api/login-service.js'
	
export default {
    components: {
		Page,
		NaviBar,
		PButton,
		templateForm
	},
	
    data() {
        return {
            nextIcon,
            pageHeight: 600,
            formData:{},
            template:{},
            defaultInfo: '执法人员',
            inspectors: [],
            templateId:'202102261114400fa115d8a830487da98c3250d8ef5465',
            editable:true,
            //是否注册选择人员监听
            registered: false,
			taskId: '',
			stepId: ''
        };
    },
	
    computed: {
        formLayoutHeight: function() {
			return this.pageHeight - 40;
		},

        //姓名的渲染
        names: function() {
            return this.inspectors.map(p => p.YHMC).join(',')
        },

        //针对选择的执法人员
        onlyID(){
            return this.inspectors.map(p => p.YHID).join('#')
        }
    },
	
	onLoad(options) {
		this.taskId = options.taskId
		this.stepId = options.stepId
	},

    mounted(){
        this.$store.state.verifyList = [];
        this.recordId = guid()
        this.getTemplate()
    },

    methods: {
        chooseCompany(){
			let _self = this
			this.registered = true
			uni.$once(EVENT_SELECT_CONFIRM, (persons) => {
				_self.registered = true
				_self.inspectors = persons
			})
			uni.navigateTo({
				url: `/pages/department/department?multi=true`
			})
        },

        //转办
        fixedPersons(){
            let self = this;
			styleUtil.getNodeLayout(this, '#formParent').then(layout => {
				let success = onform.saveTemplateForm(self.formData, self, layout.top);
				if (success === true) {
					self.postFormAxios();
				}
			});
        },

        //提交
        postFormAxios(){
            let self = this;
			let from = {
				client_type: 'mobile_web',
				service: 'DYNAMICFORM_SAVE',
				record_id: self.recordId,
                template_id: self.templateId,
				user_id: loginService.getAuthUserId() || 'SDSZFJ'
			};
			let tableName = self.template['templateTable'];
			let data = this.$store.state.formData || this.formData;
			data = deepCopyObject(data);
            from[tableName] = data;
            data.YWXTBH = this.taskId
			dynamicformsave(from)
				.then(res => {
                    self.postForwardPerson()
				})
				.catch(() => {
					uni.showToast({
						title: '转办失败',
						duration: 2000,
						icon: 'none'
					});
				});
        },

        postForwardPerson(){
            postForwardPerson({
                stepId: this.stepId,
                opinion: this.formData.BZ,
                selectUsers: this.formData.BLR.replace(/,/g,"#"),
            }).then((res)=>{
                if(res.status_code === '0'){
                    uni.showToast({
						title: '转办成功',
						duration: 1500,
						icon: 'success'
					});
                    setTimeout(() => {
                        uni.redirectTo({
                                url: '/pages/task/task-list'
						});
					}, 1600)
                }
            })
        },

        getTemplate() {
			let userInfo = uni.getStorageSync('userInfo');
			let userId = '';
			if (userInfo) {
				userId = userInfo.id;
			}
			postQueryWryjcTzForm({
				service: 'GET_DYNAMICFORM_MODEL',
				bbxh: this.templateId,
				userId: userId || 'SDSZFJ'
			}).then(res => {
				this.template = formUtil.packPeerElementIntoGroup(res.datas_json);
			});
		},
    },
};
</script>

<style scoped>
.template-form-layout {
	width: calc(100%);
	background-color: #fff;
	margin-top: 0rpx;
	height: calc(100% - 140rpx);
}
</style>
