<template>
    <Page :padding="true" title="问题发现">
        <view
            class="flex-column-layout form-content-layout"
            style="justify-content: flex-start"
        >
            <view
                id="formParent"
                class="wtfx-form-layout"
                style="width: 100%; height: calc(100% - 96rpx)"
            >
                <uni-forms
                    ref="customForm"
                    label-width="240"
                    label-position="top"
                >
                    <uni-forms-item
                        label="驳回原因"
                        required
                        name="SHYJ"
                        v-if="turnDown || SHYJ"
                    >
                        <div>
                            <uni-easyinput
                                v-model="SHYJ"
                                disabled
                                trim
                                auto-height
                                type="textarea"
                            />
                        </div>
                    </uni-forms-item>
                    <uni-forms-item label="处置结论" required name="CZJL">
                        <div>
                            <uni-easyinput
                                v-model="CZJL"
                                :disabled="!editable"
                                placeholder="请填写处置结论"
                                trim
                                auto-height
                                disableColor="none"
                                type="textarea"
                            />
                        </div>
                    </uni-forms-item>
                </uni-forms>
            </view>
            <!-- <view
                id="dispatchButton"
                class="power-button power-button-primary dispatch-button"
                @click="saveForm"
            >
                保存
            </view> -->

            <view class="flex-row-layout form-menu-layout" v-if="editable">
                <view
                    v-if="turnDown"
                    class="flex-row-layout form-list-tab"
                    style="background-color: #fd7b47"
                    @click="reCheck"
                >
                    重新检查
                </view>
                <view
                    class="flex-row-layout form-list-tab"
                    style="background-color: #e4a54d"
                    @click="saveForm('ZC')"
                >
                    暂存
                </view>
                <view
                    class="flex-row-layout form-list-tab"
                    style="background-color: rgb(0, 155, 255)"
                    @click="saveForm('TJ')"
                >
                    提交
                </view>
            </view>
        </view>
        <show-modal></show-modal>
    </Page>
</template>

<script>
import Page from '@/pages/component/Page.vue';

import { deepCopyObject } from '@/common/merge.js';
import { guid } from '@/common/uuid.js';

import styleUtil from '@/common/style.js';
import nextIcon from '@/static/img/navi_next_icon.png';
import loginService from '@/api/login-service.js';

import { XS_CZJL_SAVE, XS_RECHECK } from '@/api/record.js';

export default {
    components: {
        Page
    },

    data() {
        return {
            type: '',
            itemData: {},
            SHYJ: '',
            CZJL: '',
            nextIcon,
            editable: true,
            height: -1
        };
    },
    computed: {
        turnDown() {
            return this.itemData.CZLX === 'BH';
        }
    },

    onLoad(options) {
        this.itemData = uni.getStorageSync('wtfx_detail') || {};
        console.log(this.itemData);

        this.SHYJ = this.itemData.SHYJ || '';
        this.CZJL = this.itemData.CZJL || '';
        let type = options.type;
        this.editable = type === 'TYPE_DB';
    },

    destroyed() {
        uni.$off('wtfx_list_refresh');
    },

    mounted() {
        // #ifdef APP-PLUS
        this.height = 600;
        styleUtil.getNodeLayout(this, '#formParent').then(layout => {
            this.height = layout.height;
        });
        // #endif
    },

    methods: {
        //企业的跳转
        reCheck() {
            uni.navigateTo({
                url: `/pages/task/wtfx/wtfx-recheck`
            });
        },

        //保存表单
        saveForm(type) {
            let self = this;
            if (!this.CZJL) {
                uni.showToast({
                    title: '请填写处置结论！',
                    duration: 2000,
                    icon: 'none'
                });
                return;
            }
            if (type === 'TJ') {
                this.$showModal({
                    title: '提示',
                    content: '您确定要提交吗？',
                    success: function (r) {
                        if (r.confirm) {
                            self.XS_CZJL_SAVE(type);
                        }
                    }
                });
                return;
            }
            this.XS_CZJL_SAVE(type);
        },
        XS_CZJL_SAVE(type) {
            let params = {
                CZLX: type,
                HDLX: 'WTFX',
                CCJGXH: this.itemData.CCJGXH,
                XCZFXH: this.itemData.XCZFXH,
                CZJLXH: this.itemData.CZJLXH,
                CZLX_OLD: this.itemData.CZLX,
                CZJL: this.CZJL
            };
            XS_CZJL_SAVE(params).then(res => {
                uni.showToast({
                    title: '保存成功！',
                    duration: 2000,
                    icon: 'success'
                });

                uni.$emit('wtfx_list_refresh');
                setTimeout(() => {
                    uni.navigateBack({
                        delta: 1
                    });
                }, 1000);
            });
        }
    }
};
</script>

<style scoped>
.form-content-layout {
    width: 100%;
    height: 100%;
}

.template-form-layout {
    border-radius: 5px;
    padding: 10px;
    width: calc(100% - 20px);
    background-color: #fff;
}

.select-wry-list {
    width: calc(100% - 20px);
    height: 110rpx;
    line-height: 110rpx;
    background-color: #fff;
    font-size: 34rpx;
    color: #999;
    padding: 0 22rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.nextImg {
    width: 34rpx;
    height: 34rpx;
}

.record-fragment {
    height: 100%vh;
}

.dispatch-button {
    position: fixed;
    bottom: 0;
    width: calc(100% - 64rpx);
    height: 96rpx;
    line-height: 96rpx;
    padding-top: 0;
    border-radius: 0;
}
::v-deep .is-disabled {
    color: #333333;
}
.form-menu-layout {
    height: 50px;
    background-color: #fff;
}

.form-list-tab {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 50px;
    color: #ffffff;
}
</style>
