<template>
    <Page :padding="false" title="问题发现">
        <ul class="pd-ultbs1" style="position: inherit">
            <li
                :class="{ on: type == 'TYPE_DB' }"
                @click="changeType('TYPE_DB')"
            >
                <i>待办</i>
            </li>
            <li
                :class="{ on: type == 'TYPE_YB' }"
                @click="changeType('TYPE_YB')"
            >
                <i>已办</i>
            </li>
        </ul>
        <!-- <view class="search-bar">
            <uni-search-bar
                placeholder="请输入想要查询的自然保护地"
                cancelButton="none"
                cancelText=""
                clearButton="always"
                bgColor="#F7F7F7"
                :radius="50"
                @input="searchByKeyword"
            />
        </view> -->
        <div class="gap"></div>

        <scroll-view
            class="record-task-list"
            scroll-y="true"
            style="height: calc(100vh - 200rpx); background-color: #fff"
            @scrolltolower="onReachBottomEvent"
            v-if="list.length > 0"
        >
            <view
                class="company-list"
                style="position: relative"
                v-for="(item, index) in list"
                :key="index"
                @click="showDetail(item)"
            >
                <view class="flex-row-layout">
                    <text class="company-title company-title-text">{{
                        item.MS
                    }}</text>
                </view>

                <view class="company-footer">
                    <view class="company-type"> {{ item.CZSJ }} </view>
                    <view class="company-type" v-if="item.CZLX === 'BH'">
                        <text>驳回</text>
                    </view>
                </view>
                <view class="company-router">
                    <image :src="nextIcon" />
                </view>
            </view>
        </scroll-view>
        <noDataAdd v-if="list.length == 0"></noDataAdd>
    </Page>
</template>

<script>
import Page from '@/pages/component/Page.vue';
import listIcon from '@/static/app/images/lstic1a.png';
import { XS_QUERY_LIST } from '@/api/record.js';
import noDataAdd from '@/components/no-data.vue';
import nextIcon from '@/static/img/navi_next_icon.png';

export default {
    components: { noDataAdd, Page },
    data() {
        return {
            nextIcon,
            listIcon,
            type: 'TYPE_DB',
            search: '',
            pageSize: 10,
            pageNum: 1,
            list: [],
            total: 0,
            isLastPage: false
        };
    },

    mounted() {
        this.searchData();
    },
    methods: {
        showDetail(item) {
            uni.setStorageSync('wtfx_detail', item);
            uni.$once('wtfx_list_refresh', () => {
                this.pageNum = 1;
                this.list = [];

                this.searchData(true);
            });
            uni.navigateTo({
                url: `/pages/task/wtfx/wtfx-detail?type=${this.type}`
            });
        },

        searchByKeyword(parms) {
            this.search = parms;
            this.pageNum = 1;
            this.list = [];
            this.searchData();
        },

        onReachBottomEvent() {
            this.pageNum++;
            if (!this.isLastPage) {
                this.searchData();
            } else {
                uni.showToast({
                    title: '已经没有数据了',
                    duration: 2000,
                    icon: 'none'
                });
            }
        },
        changeType(type) {
            this.pageNum = 1;
            this.type = type;
            this.list = [];
            this.searchData();
        },
        searchData(flag) {
            XS_QUERY_LIST({
                HDLX: 'WTFX',
                TYPE: this.type,
                pageSize: 20,
                pageNum: this.pageNum,
                ZFRYID: uni.getStorageSync('userInfo').id
            }).then(res => {
                if (flag) {
                    this.list = [];
                }
                this.list.push(...res.data_json.list);
                this.total = res.data_json.total;
                this.isLastPage = res.data_json.isLastPage;
            });
        }
    }
};
</script>

<style>
.pd-main {
    width: 100vw;
}
.pd-dlbx1 + .pd-dlbx1 {
    margin-top: 0px;
}

.company-list {
    margin-left: 28rpx;
    background-color: #fff;
    border-bottom: 1rpx solid #eeeeee;
}

.company-title {
    font-weight: 500;
    color: #333333;
    font-size: 30rpx;
    padding: 16rpx 0 12rpx 0;
    width: 90%;
}

.company-footer {
    display: flex;
    align-items: center;
    width: 100%;
    padding-bottom: 12rpx;
}

.company-time {
    width: 46%;
    display: flex;
    align-items: center;
}

.company-time image {
    width: 24rpx;
    height: 24rpx;
}

.company-type {
    width: 46%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin-bottom: 10rpx;
    font-size: 30rpx;
}

.company-type text {
    background-color: #3f97dc;
    color: #fff;
    font-size: 24rpx;
    padding: 2rpx 20rpx;
    border-radius: 20rpx;
}

.company-time text {
    font-size: 26rpx;
    color: #666;
    padding-left: 12rpx;
}

.company-router image {
    width: 28rpx;
    height: 28rpx;
    position: relative;
    bottom: 30rpx;
    left: 16rpx;
}
.company-router {
    position: absolute;
    right: 20rpx;
    top: 50%;
}

.company-delay {
    margin-left: auto;
    margin-right: 10px;
    color: red;
}

.company-tips {
    margin-left: auto;
    margin-right: 10px;
    color: #ff9900;
}
</style>
