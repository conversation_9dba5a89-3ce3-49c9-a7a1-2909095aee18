<template>
    <Page :padding="true" title="重新检查">
        <div style="width: 100%; height: calc(100% - 96rpx)">
            <div style="color: #666">*请选择重新检查的执法人员</div>
            <div class="gap"></div>
            <view class="flex-row-layout">
                <text class="cert-prop" style="margin-left: 10px"
                    >执法人员</text
                >
                <view class="conton">
                    <view
                        class="container"
                        v-for="(item, index) in inspectors"
                        :key="index"
                    >
                        <image
                            src="/static/img/record/icon_delete_corner.png"
                            @click.stop="deletePerson(index)"
                            class="imgic"
                        />
                        <view class="content">{{ item.YHMC }}</view>
                    </view>
                </view>
                <image
                    class="navi-next"
                    :src="person_choose_icon"
                    @click="selectPerson"
                />
            </view>
            <view class="list-divider" />
        </div>
        <view class="flex-row-layout form-menu-layout" style="position: ">
            <view
                class="flex-row-layout form-list-tab"
                style="background-color: rgb(0, 155, 255)"
                @click="submitTask"
            >
                提交
            </view>
        </view>
        <show-modal></show-modal>
    </Page>
</template>

<script>
import Page from '@/pages/component/Page.vue';
import person_choose_icon from '@/static/img/record/person_choose.png';

import { XS_RECHECK } from '@/api/record.js';

import { EVENT_SELECT_CONFIRM } from '@/pages/department/department.js';

export default {
    components: {
        Page
    },

    data() {
        return {
            person_choose_icon,
            registered: false,
            itemData: {},
            inspectors: [], //选择的人员信息
            onlyID: ''
        };
    },
    computed: {
        //姓名的渲染
        names: function () {
            return this.inspectors.map(p => p.YHMC).join(',');
        },

        //姓名加ID的选择
        nameIDs() {
            return this.inspectors
                .map(p => p.YHMC + `(${p.ZFZHSD || p.ZFZHHBB})`)
                .join(',');
        }
    },
    watch: {
        nameIDs(val) {
            let that = this;
            this.onlyID = this.inspectors.map(p => p.YHID).join(',');
        }
    },

    onLoad() {
        this.itemData = uni.getStorageSync('wtfx_detail') || {};
    },

    mounted() {},

    destroyed() {
        if (this.registered) {
            uni.$off(EVENT_SELECT_CONFIRM);
        }
        uni.$off('wtfx_list_refresh');
    },

    methods: {
        selectPerson() {
            let _self = this;
            this.registered = true;
            uni.$once(EVENT_SELECT_CONFIRM, persons => {
                _self.registered = true;
                _self.inspectors = persons;
            });
            uni.navigateTo({
                url:
                    '/pages/department/department?multi=' +
                    true +
                    '&checked=' +
                    _self.onlyID
            });
        },
        deletePerson(index) {
            this.inspectors.splice(index, 1);
        },

        submitTask() {
            let self = this;
            this.$showModal({
                title: '提示',
                content: '您确定要提交吗？',
                success: function (r) {
                    if (r.confirm) {
                        self.XS_RECHECK();
                    }
                }
            });
        },

        XS_RECHECK() {
            let params = {
                HDLX: 'WTFX',
                XCZFXH: this.itemData.XCZFXH,
                ZFRYID: this.onlyID,
                ZFRYMC: this.names
            };

            XS_RECHECK(params).then(res => {
                uni.showToast({
                    title: '操作成功！',
                    duration: 2000,
                    icon: 'success'
                });

                uni.$emit('wtfx_list_refresh');
                setTimeout(() => {
                    uni.navigateBack({
                        delta: 2
                    });
                }, 1000);
            });
        }
    }
};
</script>

<style scoped>
.record-cert-confirm {
    width: 100%;
    height: 100%;
}

.cert-prop {
    height: 42px;
    line-height: 42px;
    font-size: 16px;
    color: #666;
}

.cert-prop-value {
    flex: 1;
    margin: 0 10px;
    text-align: right;
    color: #666;
}

.cert-speaker {
    width: 36px;
    height: 36px;
    background-size: 22px 22px;
    background: url(~@/static/img/record/icon_speaker.png) no-repeat center;
}

.conton {
    display: flex;
    flex-wrap: wrap;
    padding: 10rpx 10rpx 0 10rpx;
    width: 68%;
}

.container {
    position: relative;
    margin-right: 16rpx;
    margin-bottom: 14rpx;
}

.content {
    padding: 12rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #eeeeee;
    color: #333333;
    font-size: 28rpx;
    border-radius: 10rpx;
}

.navi-next {
    width: 50rpx;
    height: 50rpx;
}

.flex-row-layout {
    display: flex;
    /* justify-content: s; */
    align-items: center;
}

.imgic {
    position: absolute;
    top: -13rpx;
    right: -13rpx;
    width: 32rpx;
    height: 32rpx;
    z-index: 10;
}

.confirm-info {
    text-indent: 66rpx;
}

.form-menu-layout {
    height: 50px;
    background-color: #fff;
}

.form-list-tab {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 50px;
    color: #ffffff;
}
</style>
