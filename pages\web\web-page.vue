<!--
 * @Author: 姚进玺 <EMAIL>
 * @Date: 2022-02-21 09:29:37
 * @LastEditors: 姚进玺 <EMAIL>
 * @LastEditTime: 2022-08-12 15:12:32
 * @FilePath: /UNI_APP_ShanDong/pages/web/web-page.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <view>
    <web-view
      :webview-styles="webviewStyle"
      :src="learningPageUrl()"
      @message="handleMessage"
    ></web-view>
  </view>
</template>

<script>
// import webFormUrl from '../../static/h5/';
const POWER_CONSUME_PAGE = "power-consume";
const POWER_CONSUME_URL = "http://***************:8006/#/";
import { isH5 } from "@/common/config.js";
//http://***************:8006/#/?type=login&username=xxx&password=xxxx

export default {
  data() {
    return {
      webviewStyle: {
        progress: {
          color: "#ffaa7f",
        },
      },
      targetPage: null,
      wrybh: "",
    };
  },

  onLoad(options) {
    this.targetPage = options.page;
    this.wrybh = options.WRYBH;
  },

  methods: {
    learningPageUrl() {
      if (this.targetPage === "vibration") {
        // return 'http://*************:8085/?routerType=record';
        return "http://iot.powerdata.com.cn:8084/shakeMonitor/index.html#/?routerType=record";
      }
      if (this.targetPage === "vibrationDetail") {
        return `http://iot.powerdata.com.cn:8084/shakeMonitor/index.html#/pages/realtime/enterpriseInfo?WRYBH=${this.wrybh}&routerType=record`;
      }
      if (this.targetPage === POWER_CONSUME_PAGE) {
        let username = uni.getStorageSync("username");
        // let password = uni.getStorageSync('password');
        let password = "hbyd@12369";
        let _url = `${POWER_CONSUME_URL}?type=login&username=${username}&password=${password}`;
        return _url;
      }
      let token = uni.getStorageSync("token");
      console.log(`传递的token: ${token}`);
      //对接山东通，执法云学堂h5地址区分
      if (isH5) {
        return `http://************:11901/zhzf/dist/app/index.html?page=${this.targetPage}&token=${token}`;
      } else {
        return `http://***************:9001/zhzf/dist/app/index.html?page=${this.targetPage}&token=${token}`;
      }
    },

    handleMessage(evt) {
      console.log(evt);
      let info = evt.detail.data[0].action;
      if (info === "closeWebview") {
        uni.navigateBack({
          delta: 1,
        });
      }
    },
  },
};
</script>

<style></style>
