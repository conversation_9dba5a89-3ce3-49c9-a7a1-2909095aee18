<template>
    <Page :padding="false" title="企业选择">
        <section class="pd-main">
            <view>
                <uni-search-bar
                    placeholder="请输入想要查询的污染源企业"
                    cancelButton="none"
                    cancelText=""
                    clearButton="always"
                    bgColor="#F7F7F7"
                    :radius="50"
                    @input="searchByKeyword"
                />
                <div class="gap"></div>
                <scroll-view
                    :scroll-y="true"
                    @scrolltolower="loadMore"
                    :style="pageListStyle"
                >
                    <dl
                        class="pd-dlbx1"
                        v-for="(item, index) in companylist"
                        @click="clickRouter(item)"
                        :key="item.id"
                    >
                        <dd>
                            <image
                                src="/static/app/images/lstic1a.png"
                                class="imgic"
                            ></image>
                            <h2>
                                {{ item.WRYMC }}
                                <span class="mk" v-if="item.RWLXMC">
                                    {{ item.RWLXMC }}
                                </span>
                            </h2>
                            <p><em>污染源地址：</em>{{ item.WRYDZ }}</p>
                            <small></small>
                        </dd>
                    </dl>
                    <NoData
                        v-if="companylist.length < 1"
                        :type="companylist.length < 1 ? 'message' : ''"
                    />
                </scroll-view>
            </view>
        </section>
    </Page>
</template>

<script>
import NoData from '@/components/no-data.vue';
import Page from '@/pages/component/Page.vue';
import NaviBar from '@/pages/component/NaviBar.vue';
import { postQueryWrylx } from '@/api/new.js';
export default {
    name: 'WryList',
    components: {
        Page,
        NaviBar,
        NoData
    },
    data() {
        return {
            companylist: [], //污染源列表
            lastPageIndex: null, //最大页面数
            pageNum: 1, //当前的页面数量
            pageLoadType: true, //页面可滚动的状态
            searchValue: '' //想要查询的污染源关键字
        };
    },

    computed: {
        pageListStyle: function () {
            return {
                height: 'calc(100vh - 220upx)'
            };
        }
    },

    mounted() {
        this.getList();
    },

    methods: {
        //获取企业数据
        getList() {
            this.pageLoadType = false;
            postQueryWrylx({
                service: 'QUERY_WRYXX_SERVICE',
                searchText: this.searchValue,
                pageSize: 30,
                pageNum: this.pageNum
            }).then(res => {
                this.companylist.push(...res.data_json.list);
                this.lastPageIndex = res.data_json.lastPage;
                this.pageLoadType = true;
            });
        },

        //选择对应的企业跳转
        clickRouter(item) {
            uni.$emit('choosePath', item);
            uni.navigateBack({
                delta: 1
            });
        },

        //关键字搜索
        searchByKeyword(parms) {
            this.companylist = [];
            this.searchValue = parms;
            this.getList();
        },

        //下拉刷新
        loadMore() {
            if (this.pageLoadType) {
                this.pageNum++;
                if (this.pageNum > this.lastPageIndex) {
                    uni.showToast({
                        title: '已经没有数据了',
                        duration: 2000,
                        icon: 'none'
                    });
                } else {
                    this.getList();
                }
            }
        }
    }
};
</script>

<style scoped></style>
