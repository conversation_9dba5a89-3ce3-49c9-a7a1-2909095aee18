<template>
	<Page :title="title" @layoutAware="onFixedDimen">
		<scroll-view 
			scroll-y="true"
			:style="scrollStyle">
			<p-card v-for="(group, index) in tags"
				:key="index"
				:title="group.DMMC">
				<view 
					style="display: block; padding-right: 10rpx; padding-bottom: 10rpx;"
					class="flex-column-layout wry-tag-group">
					<uni-tag
						v-for="(tag, ti) in group.children"
						style="display: inline-block; margin-left: 10rpx; margin-top: 10rpx;"
						:key="ti"
						:type="tag.check === '1' ? 'primary' : 'default'"
						:text="tag.DMMC"
					/>
				</view>
			</p-card>
		</scroll-view>
	</Page>
</template>

<script>
	import Page from '@/pages/component/Page.vue'
	import PCard from '@/pages/component/PCard.vue'
	
	import styleUtil from '@/common/style.js'
	import { queryPollutionTags } from '@/api/wry-service.js'
	
	export default {
		components: {
			Page, PCard
		},
		
		data() {
			return {
				title: '标签管理',
				scrollHeight: 600,
				pollutionId: null,
				tags: []
			}
		},
		
		computed: {
			scrollStyle: function() {
				let style = {
					height: `${this.scrollHeight}px`
				}
				return styleUtil.wrapStyleObject(style)
			}
		},
		
		onLoad(option) {
			this.pollutionId = option.pollutionId || null
		},
		
		mounted() {
			this.loadPollutionTags()
		},
		
		methods: {
			onFixedDimen(dimen) {
				this.scrollHeight = dimen.height - 16
			},
			
			loadPollutionTags() {
				if(this.pollutionId === null) {
					return
				}
				let _self = this
				queryPollutionTags(this.pollutionId)
					.then(tags => {
						_self.tags = tags
					})	
			}
		}
	}
</script>

<style>
	.wry-tag-group {
		
	}
</style>
