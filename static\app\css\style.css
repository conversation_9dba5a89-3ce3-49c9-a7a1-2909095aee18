@charset "utf-8"; /** * mobile reset **/
html,
body,
div,
span,
ol,
ul,
applet,
object,
iframe,
h1,
h2,
h3,
h4,
h5,
h6,
p,
blockquote,
pre,
a,
abbr,
acronym,
address,
big,
cite,
code,
del,
dfn,
em,
img,
ins,
kbd,
q,
s,
samp,
small,
strike,
strong,
sub,
sup,
tt,
var,
b,
u,
i,
center,
dl,
dt,
dd,
ol,
ul,
li,
fieldset,
form,
label,
legend,
table,
caption,
tbody,
tfoot,
thead,
tr,
th,
td,
article,
aside,
canvas,
details,
embed,
figure,
figcaption,
footer,
header,
hgroup,
menu,
nav,
output,
ruby,
section,
summary,
time,
mark,
audio,
video {
    margin: 0;
    padding: 0;
    border: 0;
    font: inherit;
    font-size: 100%;
    vertical-align: baseline;
}
ol,
ul {
    list-style: none;
}
input,
button,
textarea,
select {
    -webkit-appearance: none;
    appearance: none;
    /* box-sizing: border-box; */
    border-radius: 0;
    padding: 0;
    margin: 0;
    border: none;
    outline: none;
}
table {
    border-collapse: collapse;
    border-spacing: 0;
}
caption,
th,
td {
    font-weight: normal;
    vertical-align: middle;
}
q,
blockquote {
    quotes: none;
}
q:before,
q:after,
blockquote:before,
blockquote:after {
    content: '';
    content: none;
}
a img {
    border: none;
}
article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
menu,
nav,
section,
summary {
    display: block;
}
a {
    text-decoration: none;
}
a:hover,
a:active {
    outline: none;
}
html {
    font-family: Helvetica, 'STHeiti STXihei', 'Microsoft JhengHei',
        'Microsoft YaHei', 'Noto Sans CJK SC', 'Source Han Sans CN' Tohoma,
        Arial;
    -webkit-text-size-adjust: 100%;
    -webkit-tap-highlight-color: transparent;
    height: 100%;
    overflow-x: hidden;
}
body {
    color: #333;
    background-color: #fff;
    -webkit-backface-visibility: hidden; /*line-height: 1;*/
    height: 100%;
} /*common*/
.gap {
    height: 18.11595rpx;
}
.mask {
    position: fixed;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.4);
    z-index: 1000;
    display: none;
} /*page*/

.pd-title {
    text-align: center;
    font-size: 31.401rpx;
    color: #fff;
    line-height: 79.71015rpx;
}
.pd-main {
    overflow-y: auto;
    overflow-x: hidden;
    -webkit-overflow-scrolling: touch;
    height: 100%;
}
.pd-inner {
    padding: 79.71015rpx 0 90.579675rpx;
}
.pd-inner.map {
    position: absolute;
    left: 0;
    right: 0;
    top: 164.2511999rpx;
    bottom: 0;
    padding-top: 0;
}
.pd-map {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
}
.btn-lst {
    position: absolute;
    left: 0;
    top: 0;
    width: 88.164225rpx;
    height: 79.71015rpx;
    background: url(~@/static/app/images/lstic.png) no-repeat center;
    background-size: 36.2319rpx 29.589375rpx;
}
.pd-ultbs1 {
    position: absolute;
    left: 0;
    right: 0;
    top: 96rpx;
    height: 72.4638rpx;
    display: flex;
    display: -webkit-box;
    background: #fff;
    z-index: 999;
}
.pd-ultbs1 li {
    flex: 1;
    -webkit-box-flex: 1;
    text-align: center;
}
.pd-ultbs1 li i {
    font-size: 27.77775rpx;
    color: #666;
    border-bottom: 3.623175rpx solid transparent;
    display: inline-block;
    padding: 0 6.038625rpx;
    height: 72.4638rpx;
    line-height: 72.4638rpx;
    box-sizing: border-box;
}
.pd-ultbs1 li.on i {
    color: #099af6;
    border-bottom-color: #099af6;
}
.pd-topsite {
    padding: 0 21.7391249rpx;
    font-size: 26.570025rpx;
    color: #fff;
    position: relative;
    height: 60.386475rpx;
    line-height: 60.386475rpx;
    background: rgba(80, 142, 182, 0.52);
}
.pd-topsite:after {
    content: '';
    position: absolute;
    right: 22.94685rpx;
    top: 0;
    bottom: 0;
    background: url(~@/static/app/images/rtic.png) no-repeat center;
    background-size: 15.7005rpx 25.9662rpx;
    width: 15.7005rpx;
}
.pd-maptols {
    position: absolute;
    right: 18.11595rpx;
    top: 82.7295rpx;
}
.pd-datebx {
    width: 187.19805rpx;
    height: 54.34785rpx;
    line-height: 54.34785rpx;
    background: white;
    border-radius: 9.057975rpx;
    box-shadow: 0 1.811625rpx 4.8309rpx rgba(50, 50, 50, 0.17);
    text-indent: 18.11595rpx;
    font-size: 25.3623rpx;
    color: #666;
    position: relative;
}
.pd-datebx i {
    background: url(~@/static/app/images/dateic.png) no-repeat center;
    background-size: 25.9662rpx 25.9662rpx;
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0;
    width: 48.3091499rpx;
}
.pd-ullst1 {
    width: 70.0482749rpx;
    float: right;
}
.pd-ullst1 > li {
    height: 70.0482749rpx;
    line-height: 70.0482749rpx;
    border-radius: 9.057975rpx;
    background: #fff;
    font-size: 24.1545749rpx;
    color: #333;
    text-align: center;
    background-repeat: no-repeat;
    background-position: center;
    background-size: 38.64735rpx 38.64735rpx;
    margin-top: 12.077325rpx;
    box-shadow: 0 1.811625rpx 4.8309rpx rgba(50, 50, 50, 0.17);
    position: relative;
}
.pd-ullst1 > li.lstic1 {
    background-image: url(~@/static/app/images/lstic1.png);
}
.pd-ullst1 > li.lstic2 {
    background-image: url(~@/static/app/images/lstic2.png);
}
.pd-ullst1 > li.lstic3 {
    background-image: url(~@/static/app/images/lstic3.png);
}
.pd-ullst1 > li.on.lstic1 {
    background-image: url(~@/static/app/images/lstic1s.png);
}
.pd-ullst1 > li.on.lstic2 {
    background-image: url(~@/static/app/images/lstic2s.png);
}
.pd-ullst1 > li.on.lstic3 {
    background-image: url(~@/static/app/images/lstic3s.png);
}
.pd-subli {
    position: absolute;
    right: 100%;
    top: 0;
    background: #fff;
    border-radius: 9.057975rpx;
    display: flex;
    display: -webkit-box;
    width: 166.062825rpx;
    margin-right: 25.3623rpx;
    filter: drop-shadow(0 1.811625rpx 4.8309rpx rgba(50, 50, 50, 0.17));
    visibility: hidden;
}
.pd-subli:after {
    content: '';
    position: absolute;
    left: 100%;
    top: 50%;
    border-style: solid;
    border-width: 15.7005rpx 0 15.7005rpx 15.7005rpx;
    border-color: transparent transparent transparent white;
    width: 0;
    height: 0;
    transform: translateY(-50%);
}
.pd-subli li {
    flex: 1;
    -webkit-box-flex: 1;
    font-size: 24.1545749rpx;
    color: #333;
    line-height: 70.0482749rpx;
    position: relative;
}
.pd-subli li + li:before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    width: 1px;
    height: 44.685975rpx;
    background: #ddd;
    transform: translateY(-50%);
}
.pd-subli li.on {
    color: #099af6;
}
.pd-ullst1 > li.lstic0.on .pd-subli {
    visibility: visible;
}
.pd-tuli1 {
    position: absolute;
    left: 12.077325rpx;
    bottom: 120.77295rpx;
    width: 148.5507rpx;
    height: 193.840575rpx;
}
.pd-footer {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    height: 90.579675rpx;
    border-top: 1px solid #dce0e6;
    box-sizing: border-box;
    background: #fff;
    z-index: 899;
}
.pd-menu {
    display: flex;
    display: -webkit-box;
    height: 100%;
    overflow: hidden;
    padding: 0 24.1545749rpx;
}
.pd-menu li {
    flex: 1;
    -webkit-box-flex: 1;
    height: 100%;
}
.pd-menu li a {
    position: relative;
    display: block;
    height: 100%;
    overflow: hidden;
}
.pd-menu li a p {
    font-size: 20.5313999rpx;
    color: #666;
    text-align: center;
    padding-top: 3.75rpx;
}
.pd-menu li a i {
    display: block;
    background-repeat: no-repeat;
    background-position: center;
    height: 38.04345rpx;
    background-size: contain;
    margin-top: 12.077325rpx;
}
.pd-menu li.on p {
    color: #0379f6;
}
.menu-icon1 {
    background-image: url(~@/static/app/images/menu_icon1.png);
}
.menu-icon2 {
    background-image: url(~@/static/app/images/menu_icon2.png);
}
.menu-icon3 {
    background-image: url(~@/static/app/images/menu_icon3.png);
}
.menu-icon4 {
    background-image: url(~@/static/app/images/menu_icon4.png);
}
.menu-icon5 {
    background-image: url(~@/static/app/images/menu_icon5.png);
}
.pd-menu li.on .menu-icon1 {
    background-image: url(~@/static/app/images/menu_icon1s.png);
}
.pd-menu li.on .menu-icon2 {
    background-image: url(~@/static/app/images/menu_icon2s.png);
}
.pd-menu li.on .menu-icon3 {
    background-image: url(~@/static/app/images/menu_icon3s.png);
}
.pd-menu li.on .menu-icon4 {
    background-image: url(~@/static/app/images/menu_icon4s.png);
}
.pd-menu li.on .menu-icon5 {
    background-image: url(~@/static/app/images/menu_icon5s.png);
}
.pd-siderBar {
    position: absolute;
    right: -100%;
    top: 79.71015rpx;
    bottom: 0;
    width: 80%;
    background: #fff;
    z-index: 1000;
}
.choose-mod {
    padding: 18.11595rpx 0;
    margin: 0 24.1545749rpx;
}
.choose-mod dt {
    font-size: 27.77775rpx;
    color: #333;
    padding-bottom: 10.8696rpx;
}
.choose-mod dd ul {
    margin: 0 -10.8696rpx;
    overflow: hidden;
}
.choose-mod dd ul li {
    float: left;
    padding: 10.8696rpx 9.057975rpx;
    width: 33.333333%;
    box-sizing: border-box;
}
.choose-mod dd ul li a {
    display: block;
    background: #eee;
    color: #666;
    font-size: 26.570025rpx;
    text-align: center;
    border-radius: 3.623175rpx;
    line-height: 65.821275rpx;
    border-radius: 300px;
}
.choose-mod dd ul li.on a {
    background: #099af6;
    color: #fff;
}
.btn-mod {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 71.256075rpx;
    display: flex;
    display: -webkit-box;
    border-top: 1px solid #cfcfcf;
}
.btn-mod .btn {
    width: 50%;
    font-size: 27.77775rpx;
    color: #333;
    height: 100%;
    flex: 1;
    -webkit-box-flex: 1;
    display: block;
    background: white;
}
.btn-mod .btn.on {
    background: #099af6;
    color: white;
}
.pd-botsiderBar {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    background: #fff;
    border-radius: 24.1545749rpx 24.1545749rpx 0 0;
    z-index: 1000;
    display: none;
}
.pd-bstophd {
    padding: 24.1545749rpx 24.1545749rpx 9.057975rpx;
    overflow: hidden;
}
.pd-fwinp {
    float: left;
    font-size: 26.570025rpx;
    color: #333;
}
.pd-fwinp input {
    width: 157.00485rpx;
    height: 48.3091499rpx;
    line-height: 48.3091499rpx;
    text-indent: 14.492775rpx;
    box-sizing: border-box;
    border: 1px solid #ddd;
    border-radius: 9.057975rpx;
    font-size: 26.570025rpx;
    color: #666;
}
.pd-ultbs2 {
    float: right;
}
.pd-ultbs2 li {
    float: left;
    border: 1px solid #ddd;
    border-radius: 300px;
    height: 48.3091499rpx;
    line-height: 48.3091499rpx;
    font-size: 25.3623rpx;
    color: #666;
    padding: 0 21.7391249rpx;
}
.pd-ultbs2 li + li {
    margin-left: 12.077325rpx;
}
.pd-ultbs2 li.on {
    border-color: #099af6;
    color: #099af6;
}
.pd-ullst2 {
    padding: 0 24.1545749rpx 24.1545749rpx;
}
.pd-ullst2 li {
    padding: 18.11595rpx 0;
    display: flex;
    display: -webkit-box;
    border-bottom: 1px solid #e0e0e0;
}
.pd-ullst2 li dl {
    flex: 1;
    -webkit-box-flex: 1;
    white-space: nowrap;
}
.pd-ullst2 li dl dt {
    font-size: 30.1932rpx;
    color: #333;
}
.pd-ullst2 li dl dd {
    padding-top: 9.057975rpx;
}
.pd-ullst2 li dl dd i {
    font-size: 25.3623rpx;
    color: #666;
    display: inline-block;
    position: relative;
    padding: 0 21.1353rpx;
}
.pd-ullst2 li dl dd i + i:before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    width: 1px;
    height: 18.11595rpx;
    background: #ddd;
    transform: translateY(-50%);
}
.pd-ullst2 li dl dd i:first-child {
    padding-left: 0;
}
.pd-ullst2 li a {
    display: block;
    background: url(~@/static/app/images/dzqic.png) no-repeat center top;
    background-size: 44.685975rpx 44.685975rpx;
    font-size: 21.7391249rpx;
    color: #8097af;
    padding-top: 53.140125rpx;
}
.pd-ltsiderBar {
    width: 70%;
    background: #fff;
    position: absolute;
    left: -100%;
    top: 0;
    bottom: 0;
    z-index: 1100;
}
.pd-hdinfo {
    height: 211.352625rpx;
    margin: 111.714975rpx 33.81645rpx 48.3091499rpx;
    border-radius: 18.11595rpx;
    box-shadow: 0 4.227075rpx 19.323675rpx rgba(185, 185, 185, 0.24);
}
.pd-hdinfo dt {
    float: left;
    margin: 0 24.1545749rpx 0 28.985475rpx;
    width: 106.280175rpx;
    height: 100%;
    position: relative;
}
.pd-hdinfo dt img {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    margin: auto;
    width: 106.280175rpx;
    height: 106.280175rpx;
}
.pd-hdinfo dd {
    overflow: hidden;
    padding-top: 60.386475rpx;
}
.pd-hdinfo dd h1 {
    font-size: 35.024175rpx;
    color: #333;
}
.pd-hdinfo dd p {
    font-size: 25.3623rpx;
    color: #666;
    padding-top: 9.057975rpx;
}
.pd-ullst3 {
    padding: 0 33.81645rpx;
}
.pd-ullst3 li {
    overflow: hidden;
    padding: 30.1932rpx 0;
}
.pd-ullst3 li em {
    float: left;
    font-size: 28.985475rpx;
    color: #333;
    background-repeat: no-repeat;
    background-position: left center;
    background-size: 35.024175rpx 32.608725rpx;
    padding-left: 54.34785rpx;
    position: relative;
}
.pd-ullst3 li em.otic1 {
    background-image: url(~@/static/app/images/otic1.png);
}
.pd-ullst3 li em.otic2 {
    background-image: url(~@/static/app/images/otic2.png);
    padding-right: 25.3623rpx;
}
.pd-ullst3 li em.otic2:before {
    content: '';
    position: absolute;
    right: 0;
    top: 50%;
    width: 12.077325rpx;
    height: 12.077325rpx;
    background: #ed4849;
    border-radius: 50%;
    transform: translateY(-50%);
}
.pd-ullst3 li em.otic3 {
    background-image: url(~@/static/app/images/otic3.png);
}
.pd-ullst3 li em.otic4 {
    background-image: url(~@/static/app/images/otic4.png);
}
.pd-ullst3 li em.otic5 {
    background-image: url(~@/static/app/images/otic5.png);
}
.pd-ullst3 li i {
    float: right;
    color: #999;
    font-size: 26.570025rpx;
}
.pd-botextbtn {
    height: 72.4638rpx;
    line-height: 72.4638rpx;
    border-radius: 9.057975rpx;
    background: #e8f1f7;
    position: absolute;
    bottom: 36.2319rpx;
    left: 30.1932rpx;
    right: 30.1932rpx;
    font-size: 26.570025rpx;
    color: #099af6;
    text-align: center;
}
.pd-tuli2 {
    position: absolute;
    left: 12.077325rpx;
    bottom: 120.77295rpx;
    width: 115.33815rpx;
    height: 193.840575rpx;
}
.point-box {
    background-color: #fff;
    box-shadow: 0 4.5rpx 15rpx rgba(0, 0, 0, 0.3);
    position: absolute;
    bottom: 116.25rpx;
    left: 14.492775rpx;
    right: 14.492775rpx;
    border-radius: 12.68115rpx;
    min-height: 144.927525rpx;
    box-sizing: border-box;
    padding: 18.11595rpx;
}
.point-box h2 {
    font-size: 31.401rpx;
    color: #333333;
}
.point-box h2 em {
    display: inline-block;
    background-color: #eebe16;
    color: #fff;
    line-height: 36.2319rpx;
    padding: 0 18.11595rpx;
    font-size: 24.1545749rpx;
    border-radius: 5.4348rpx;
}
.point-box h2 i {
    display: inline-block;
    color: #eebe16;
    border: 1px solid #eebe16;
    line-height: 36.2319rpx;
    padding: 0 18.11595rpx;
    font-size: 24.1545749rpx;
    border-radius: 5.4348rpx;
    margin-left: 12.077325rpx;
}
.point-box p {
    position: relative;
    font-size: 27.77775rpx;
    color: #666666;
    padding-top: 18.11595rpx;
}
.point-box p span sub {
    color: #73bb31;
}
.point-box p span + span {
    margin-left: 48.3091499rpx;
}
.look-detail {
    position: absolute;
    right: 18.11595rpx;
    top: 50%;
    transform: translateY(-50%);
    width: 18.11595rpx;
    height: 33.81645rpx;
}
.pd-ullst1 li p {
    line-height: normal;
}
.pd-potdlg {
    position: absolute;
    left: 50%;
    top: 50%;
    width: 553.140075rpx;
    height: 425.120775rpx;
    transform: translate(-50%, -50%);
    background: #fff;
    box-shadow: 0 1.811625rpx 4.8309rpx rgba(50, 50, 50, 0.17);
    border-radius: 9.057975rpx;
}
.pd-potdlg:after {
    content: '';
    position: absolute;
    left: 50%;
    top: 100%;
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 22.3430249rpx 22.3430249rpx 0 22.3430249rpx;
    border-color: white transparent transparent transparent;
}
.pd-potdlghd {
    padding: 18.11595rpx;
    overflow: hidden;
}
.pd-potdlghd strong {
    float: left;
    font-size: 30.1932rpx;
    color: #333;
    background: url(~@/static/app/images/posic.png) no-repeat left center;
    padding-left: 33.21255rpx;
    background-size: 20.5313999rpx 27.77775rpx;
} /* zzy */
.goback {
    position: absolute;
    top: 0;
    height: 100%;
    left: 30.1932rpx;
    width: 17.51205rpx;
    background: url(~@/static/app/images/goback.png) 0 center no-repeat;
    background-size: 17.51205rpx 30.7971rpx;
}
.map-details .inner {
    padding-bottom: 0;
}
.map-details .cell {
    padding: 0 24.1545749rpx;
    background-color: #fff;
    margin-bottom: 18.11595rpx;
}
.map-details .cell .hd {
    line-height: 78.502425rpx;
    height: 78.502425rpx;
    font-size: 30.1932rpx;
    color: #1e1e1e;
    padding-left: 24.1545749rpx;
    position: relative;
}
.map-details .cell .hd::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    margin-top: -6.0386251rpx;
    width: 12.077325rpx;
    height: 12.077325rpx;
    border-radius: 6.038625rpx;
    background-color: #099af6;
    border-bottom: 1px solid #ddd;
}
.map-details .cell .bd {
}
.map-details .cell .bd .liebiao {
}
.map-details .cell .bd .liebiao li {
    overflow: hidden;
    border-bottom: 1px solid #ddd;
}
.map-details .cell .bd .liebiao li .p1 {
    float: left;
    font-size: 30.1932rpx;
    line-height: 90.579675rpx;
    color: #1e1e1e;
}
.map-details .cell .bd .liebiao li .p2 {
    float: right;
    font-size: 28.985475rpx;
    line-height: 90.579675rpx;
    color: #666666;
}
.map-details .cell .bd .liebiao li .biaoqian {
    float: right;
}
.map-details .cell .bd .liebiao li .biaoqian .grade {
    float: left;
    font-size: 28.985475rpx;
    line-height: 90.579675rpx;
    font-weight: bold;
}
.map-details .cell .bd .liebiao li .biaoqian .type {
    float: left;
    width: 64.00965rpx;
    height: 36.2319rpx;
    font-size: 26.570025rpx;
    line-height: 36.2319rpx;
    text-align: center;
    border-radius: 3.01935rpx;
    margin-top: 26.570025rpx;
    color: #fff;
    margin-left: 15.0966rpx;
}
.map-details .cell .bd .tu {
    padding-bottom: 30.1932rpx;
    padding-top: 12.077325rpx;
}
.map-details .cell .bd .tu .tuli {
    text-align: right;
    line-height: 36.2319rpx;
}
.map-details .cell .bd .tu .tuli img {
    margin-top: 10.8696rpx;
}
.map-details .cell .bd .tu .tuli span {
    font-size: 28.985475rpx;
    color: #666;
    line-height: 36.2319rpx;
}
.map-details .cell .bd .shuizhi-jibie {
    text-align: center;
    width: 205.314rpx;
    height: 205.314rpx;
    border: 18.11595rpx solid #ffcd21;
    background-color: #fff;
    border-radius: 50%;
    margin: 0 auto;
}
.map-details .cell .bd .shuizhi-jibie .p1 {
    font-size: 31.401rpx;
    line-height: 48.3091499rpx;
    margin-top: 32.608725rpx;
}
.map-details .cell .bd .shuizhi-jibie .p2 {
    font-size: 33.81645rpx;
    font-weight: bold;
    line-height: 48.3091499rpx;
}
.map-details .cell .bd .shuizhi-jibie .p3 {
    width: 55.555575rpx;
    height: 30.1932rpx;
    line-height: 30.1932rpx;
    color: #fff;
    margin: 0 auto;
    margin-top: 12.077325rpx;
    border-radius: 5px;
}
.map-details .cell .bd .data-table {
    margin-left: -24.1545749rpx;
    margin-right: -24.1545749rpx;
}
.map-details .cell .bd .data-table table {
    width: 100%;
}
.map-details .cell .bd .data-table table th {
    font-size: 27.77775rpx;
    color: #42677f;
    line-height: 84.54105rpx;
}
.map-details .cell .bd .data-table table thead {
    background-color: #f0f9ff;
    border-bottom: 1px solid #ddd;
}
.map-details .cell .bd .data-table table td {
    font-size: 26.570025rpx;
    color: #333;
    line-height: 82.1255999rpx;
    text-align: center;
}
.map-details .cell .bd .data-table table tbody tr {
    border-bottom: 1px dashed #ddd;
}
.wuranyuan-details {
}
.wuranyuan-details .cell {
    margin-bottom: 0;
}
.wuranyuan-details .cell .bd .teshu {
    height: 102.657rpx;
    margin-left: -24.1545749rpx;
    margin-right: -24.1545749rpx;
    background-color: #f8f8f8;
    box-sizing: border-box;
}
.wuranyuan-details .cell .bd .teshu .s1 {
    font-size: 32.608725rpx;
    color: #1e1e1e;
    line-height: 102.657rpx;
    margin-left: 24.1545749rpx;
    float: left;
}
.wuranyuan-details .cell .bd .teshu .s2 {
    margin-top: 32.004825rpx;
    float: left;
    width: 92.3913rpx;
    height: 38.64735rpx;
    border-radius: 19.323675rpx;
    font-size: 24.1545749rpx;
    line-height: 38.64735rpx;
    text-align: center;
    margin-left: 24.1545749rpx;
    color: #fff;
}
.wuranyuan-details .cell .bd .teshu .chaobiao {
    background-color: #ed5959;
}
.pd-ultbs1.otr li i {
    border-bottom: none;
    padding: 0 35.024175rpx 0 0;
    background: url(~@/static/app/images/slecbot.png) no-repeat right center;
    background-size: 20.5313999rpx 12.077325rpx;
}
.pd-ultbs1.otr li.on i {
    color: #666;
}
.pd-inner.pt1 {
    padding-top: 170.289825rpx;
}
.pd-tablebx {
    width: 100%;
    overflow-x: auto;
    overflow-y: hidden;
}
.pd-table1 {
    width: 120%;
    background: #fff;
}
.pd-table1 th {
    font-size: 27.77775rpx;
    color: #42677f;
    line-height: 84.54105rpx;
}
.pd-table1 thead {
    background-color: #f0f9ff;
    border-bottom: 1px solid #ddd;
}
.pd-table1 td {
    font-size: 26.570025rpx;
    color: #333;
    line-height: 82.1255999rpx;
    text-align: center;
}
.pd-table1 tbody tr {
    border-bottom: 1px dashed #ddd;
} /*1-19*/
.kaohe .date {
    padding-top: 22.3430249rpx;
    line-height: 35.024175rpx;
    font-size: 24.1545749rpx;
    color: #999;
    padding-left: 30.1932rpx;
}
.kaohe .cell {
    margin: 0 24.1545749rpx;
    margin-bottom: 19.323675rpx;
}
.kaohe .cell .hd {
    line-height: 79.71015rpx;
    font-size: 30.1932rpx;
    color: #1e1e1e;
    padding-left: 39.855075rpx;
    position: relative;
}
.kaohe .cell .hd span {
    position: absolute;
    right: 0;
    top: 0;
    height: 79.71015rpx;
    line-height: 79.71015rpx;
    font-size: 26.570025rpx;
    color: #999;
}
.kaohe .cell .hd::before {
    content: '';
    position: absolute;
    top: 50%;
    margin-top: -6.0386251rpx;
    left: 12.077325rpx;
    width: 12.077325rpx;
    height: 12.077325rpx;
    background: #099af6;
    border-radius: 50%;
}
.kaohe .cell .bd {
    box-shadow: 0 0 12.077325rpx rgba(0, 0, 0, 0.2);
    background-color: #fff;
    border-radius: 6.038625rpx;
}
.kaohe .cell .bd .s-hd {
    font-size: 27.77775rpx;
    color: #333;
}
.kaohe .cell .bd .s-bd {
    font-size: 27.77775rpx;
    color: #099af6;
}
.kaohe .cell .bd .bili {
    color: #999;
    font-size: 24.1545749rpx;
}
.kaohe .cell .bd .bili .s2 {
    display: inline-block;
    padding-left: 27.173925rpx;
}
.kaohe .cell .bd .bili .reduce {
    background: url(~@/static/app/images/reduce.png) 6.038625rpx center
        no-repeat;
    background-size: 15.0966rpx 21.1353rpx;
}
.kaohe .cell .bd .bili .add {
    background: url(~@/static/app/images/add.png) 6.038625rpx center no-repeat;
    background-size: 15.0966rpx 21.1353rpx;
}
.kaohe .cell .bd .guokao {
    padding: 12.077325rpx 30.1932rpx;
}
.kaohe .cell .bd .guokao .p1 {
    overflow: hidden;
    line-height: 51.9324rpx;
}
.kaohe .cell .bd .guokao .p1 .s-hd {
    float: left;
}
.kaohe .cell .bd .guokao .p1 .s-bd {
    float: right;
}
.kaohe .cell .bd .guokao ul li {
    padding: 15.7005rpx 0;
    border-bottom: 1px solid #eeeeee;
}
.kaohe .cell .bd .guokao ul li .s-hd {
    line-height: 55.555575rpx;
}
.kaohe .cell .bd .guokao ul li .data {
    overflow: hidden;
    height: 55.555575rpx;
}
.kaohe .cell .bd .guokao ul li .data .lp {
    overflow: hidden;
    width: 50%;
    float: left;
}
.kaohe .cell .bd .guokao ul li .data .lp .s1 {
    float: left;
    line-height: 55.555575rpx;
    font-size: 42.270525rpx;
    color: #099af6;
    margin-right: 24.1545749rpx;
}
.kaohe .cell .bd .guokao ul li .data .lp .s1 em {
    font-size: 22.94685rpx;
}
.kaohe .cell .bd .guokao ul li .data .lp .s2 {
    float: left;
    height: 35.024175rpx;
    width: 82.1255999rpx;
    border-radius: 17.51205rpx;
    color: #37b761;
    font-size: 22.94685rpx;
    line-height: 35.024175rpx;
    text-align: center;
    background-color: #e1f4e9;
    margin-top: 10.2656999rpx;
}
.kaohe .cell .bd .guokao ul li .data .rp {
    overflow: hidden;
    width: 50%;
    float: right;
}
.kaohe .cell .bd .guokao ul li .data .rp .bili {
    line-height: 55.555575rpx;
    float: left;
    width: 50%;
}
.kaohe .cell .bd .guokao ul li .data .rp .bili:last-child {
    text-align: right;
    position: relative;
}
.kaohe .cell .bd .guokao ul li .data .rp .bili:last-child::before {
    position: absolute;
    top: 50%;
    left: 1px;
    transform: translateY(-50%);
    content: '';
    width: 1px;
    height: 19.323675rpx;
    background-color: #eeeeee;
}
.kaohe .cell .bd .guokao ul li:last-child {
    border: none;
}
.kaohe .cell .bd .shikao {
    overflow: hidden;
    padding: 30.1932rpx 0;
}
.kaohe .cell .bd .shikao .lp {
    float: left;
    width: 50%;
    text-align: center;
}
.kaohe .cell .bd .shikao .lp img {
    width: 225.8454rpx;
}
.kaohe .cell .bd .shikao .rp {
    float: left;
}
.kaohe .cell .bd .shikao .rp .p1 {
    margin-top: 9.057975rpx;
    line-height: 57.971025rpx;
}
.kaohe .cell .bd .shikao .rp .p1 .s-hd {
    margin-right: 40.4589rpx;
}
.kaohe .cell .bd .shikao .rp .bili {
    line-height: 42.270525rpx;
}
.kaohe .cell .bd .yinyongshui {
    padding: 24.1545749rpx 0;
}
.kaohe .cell .bd .yinyongshui ul {
    overflow: hidden;
}
.kaohe .cell .bd .yinyongshui ul li {
    float: left;
    width: 50%;
    padding-left: 36.2319rpx;
    box-sizing: border-box;
}
.kaohe .cell .bd .yinyongshui ul li .s-hd {
    line-height: 54.34785rpx;
}
.kaohe .cell .bd .yinyongshui ul li .p2 {
    line-height: 55.555575rpx;
    height: 55.555575rpx;
}
.kaohe .cell .bd .yinyongshui ul li .p2 .s1 {
    float: left;
    line-height: 55.555575rpx;
    font-size: 42.270525rpx;
    color: #099af6;
    margin-right: 24.1545749rpx;
}
.kaohe .cell .bd .yinyongshui ul li .p2 .s1 em {
    font-size: 22.94685rpx;
}
.kaohe .cell .bd .yinyongshui ul li .p2 .s2 {
    float: left;
    height: 35.024175rpx;
    width: 82.1255999rpx;
    border-radius: 17.51205rpx;
    color: #37b761;
    font-size: 22.94685rpx;
    line-height: 35.024175rpx;
    text-align: center;
    background-color: #e1f4e9;
    margin-top: 10.2656999rpx;
}
.kaohe .cell .bd .yinyongshui ul li:last-child {
    border-left: 1px solid #eeeeee;
}
.kaohexiangqing .cell {
    background-color: #fff;
    padding: 12.077325rpx 0;
    margin-bottom: 21.1353rpx;
}
.kaohexiangqing .cell .hd {
    line-height: 79.71015rpx;
    font-size: 30.1932rpx;
    color: #1e1e1e;
    padding-left: 48.3091499rpx;
    position: relative;
}
.kaohexiangqing .cell .hd::before {
    content: '';
    position: absolute;
    top: 50%;
    margin-top: -6.0386251rpx;
    left: 24.1545749rpx;
    width: 12.077325rpx;
    height: 12.077325rpx;
    background: #099af6;
    border-radius: 50%;
}
.kaohexiangqing .cell .bd {
    padding: 6.038625rpx 18.11595rpx;
}
.kaohexiangqing .cell .bd .tu {
    text-align: center;
}
.kaohexiangqing .cell .bd .tu img {
    width: 100%;
}
.xietong .top {
    height: 457.7295rpx;
    background: url(~@/static/app/images/top-bg.png) 0 0 no-repeat;
    background-size: 100% auto;
    padding-top: 79.71015rpx;
    box-sizing: border-box;
}
.xietong .top .kuang {
    width: 301.932375rpx;
    height: 301.932375rpx;
    box-sizing: border-box;
    border: 2px solid #fff;
    border-radius: 50%;
    margin: 0 auto;
    position: relative;
    background-color: rgba(255, 255, 255, 0.1);
}
.xietong .top .kuang .p1 {
    margin-top: 76.086975rpx;
    line-height: 48.3091499rpx;
    font-size: 25.3623rpx;
    color: #e3f3ff;
    text-align: center;
}
.xietong .top .kuang .p2 {
    line-height: 78.502425rpx;
    font-size: 72.4638rpx;
    color: #fff;
    text-align: center;
}
.xietong .top .kuang .bolang {
    position: absolute;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    top: 0;
    left: 0;
    overflow: hidden;
}
.xietong .top .kuang .bolang .bolang1 {
    position: absolute;
    bottom: 0;
    left: 0;
    animation: bolang1 3s linear infinite alternate;
    width: 300.12075rpx;
    height: 300.72465rpx;
}
.xietong .top .kuang .bolang .bolang2 {
    position: absolute;
    bottom: 0;
    left: 0;
    animation: bolang1 3s linear 2s infinite alternate;
    width: 300.12075rpx;
    height: 300.72465rpx;
}
.xietong .top .kuang .waiquan {
    position: absolute;
    top: -23.55075rpx;
    left: -23.55075rpx;
    width: 344.2029rpx;
    height: 344.2029rpx;
    transform: rotate(45deg);
    text-align: right;
    animation: zhuanquan 4s linear infinite;
}
.xietong .top .kuang .waiquan .quanquan {
    width: 213.76815rpx;
    height: 355.0725rpx;
}
.xietong .type {
    width: 100%;
    background-color: #fff;
}
.xietong .type td {
    width: 50%;
    height: 314.009625rpx;
    border: 1px solid #eeeeee;
    padding-top: 190.2174rpx;
    box-sizing: border-box;
}
.xietong .type td p {
    line-height: 60.386475rpx;
    font-size: 30.1932rpx;
    color: #1e1e1e;
    text-align: center;
}
.xietong .type .td1 {
    background: url(~@/static/app/images/td1.png) center center no-repeat;
    background-size: 82.1255999rpx 78.502425rpx;
}
.xietong .type .td2 {
    background: url(~@/static/app/images/td2.png) center no-repeat;
    background-size: 93.599025rpx 85.748775rpx;
}
.xietong .type .td3 {
    background: url(~@/static/app/images/td3.png) center no-repeat;
    background-size: 77.2947rpx 80.917875rpx;
}
.top-bar {
    width: 100%;
    height: 78.502425rpx;
    position: absolute;
    top: 79.71015rpx;
    left: 0;
    background-color: #fff;
    z-index: 99;
    box-shadow: 0 2px 0 rgba(0, 0, 0, 0.1);
}
.select-p .lp {
    width: 50%;
    float: left;
}
.select-p .lp .you-select {
    position: relative;
    z-index: 99;
}
.select-p .lp .you-select p {
    font-size: 26.570025rpx;
    color: #333;
    line-height: 78.502425rpx;
    text-align: center;
    background: url(~@/static/app/images/slecbot.png) 76.666% center no-repeat;
    background-size: 20.5313999rpx 12.077325rpx;
}
.select-p .lp .you-select ul {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background-color: #fff;
    display: none;
    border: 1px solid #ddd;
}
.select-p .lp .you-select ul li {
    font-size: 26.570025rpx;
    line-height: 54.34785rpx;
    text-align: center;
    color: #333;
}
.xt-xiansuo .pd-inner {
    padding-top: 175.1208rpx;
}
.xt-xiansuo .pd-inner .list {
    padding-left: 33.21255rpx;
    background-color: #fff;
}
.xt-xiansuo .pd-inner .list li {
    padding: 18.11595rpx 0;
    border-bottom: 1px solid #eeeeee;
    position: relative;
}
.xt-xiansuo .pd-inner .list li .til {
    font-size: 28.985475rpx;
    color: #1e1e1e;
    line-height: 56.7632999rpx;
}
.xt-xiansuo .pd-inner .list li .p2 {
    overflow: hidden;
}
.xt-xiansuo .pd-inner .list li .p2 .s1 {
    float: left;
    padding-left: 39.855075rpx;
    background: url(~@/static/app/images/xt-xiansuo-s1.png) 0 center no-repeat;
    background-size: 25.3623rpx;
    line-height: 50.724675rpx;
    font-size: 25.3623rpx;
    color: #666;
}
.xt-xiansuo .pd-inner .list li .p2 .s2 {
    float: left;
    padding-left: 39.855075rpx;
    background: url(~@/static/app/images/xt-xiansuo-s2.png) 0 center no-repeat;
    background-size: 24.758475rpx 21.7391249rpx;
    margin-left: 60.386475rpx;
    line-height: 50.724675rpx;
    font-size: 25.3623rpx;
    color: #666;
}
.xt-xiansuo .pd-inner .list li .right-arrow {
    position: absolute;
    right: 24.1545749rpx;
    top: 0;
    background: url(~@/static/app/images/icon_look_detail.png) 0 center
        no-repeat;
    height: 100%;
    width: 18.11595rpx;
    background-size: 18.11595rpx 33.81645rpx;
}
.xt-laiyuan .tips {
    font-size: 25.3623rpx;
    color: #999;
    line-height: 60.386475rpx;
    text-align: center;
}
.xt-laiyuan .ctx .cell {
    background-color: #fff;
}
.xt-laiyuan .ctx .cell .hd {
    padding-left: 48.3091499rpx;
    font-size: 30.1932rpx;
    line-height: 78.502425rpx;
    color: #333; /*font-weight: bold;*/
    position: relative;
}
.xt-laiyuan .ctx .cell .hd::before {
    content: '';
    position: absolute;
    top: 50%;
    margin-top: -6.0386251rpx;
    left: 21.1353rpx;
    width: 12.077325rpx;
    height: 12.077325rpx;
    background: #099af6;
    border-radius: 50%;
}
.xt-laiyuan .ctx .cell .bd {
    padding: 0 24.1545749rpx;
}
.xt-laiyuan .ctx .cell .bd ul li {
    overflow: hidden;
    border-bottom: 1px solid #eeeeee;
}
.xt-laiyuan .ctx .cell .bd ul li .s1 {
    float: left;
    font-size: 28.985475rpx;
    line-height: 82.1255999rpx;
    color: #333;
}
.xt-laiyuan .ctx .cell .bd ul li .s2 {
    float: right;
    font-size: 28.985475rpx;
    line-height: 48.3091499rpx;
    padding: 16.908225rpx 0;
    color: #666;
    width: 417.874425rpx;
    text-align: right;
}
.zhifa-tabs {
    padding-left: 30.1932rpx;
    padding-right: 30.1932rpx;
    overflow-x: auto;
}
.zhifa-tabs ul {
    position: absolute;
    width: 100%;
}
.zhifa-tabs ul li {
    float: left;
    padding: 0 6.038625rpx;
    color: #666;
    font-size: 27.77775rpx;
    line-height: 78.502425rpx;
    margin-right: 60.386475rpx;
    position: relative;
}
.zhifa-tabs ul li.cur::after {
    position: absolute;
    content: '';
    bottom: 1px;
    left: 0;
    width: 100%;
    height: 3.623175rpx;
    background-color: #099af6;
}
.zf-yiju .zf-list {
    padding-left: 0;
    background-color: #fff;
}
.zf-yiju .zf-list li {
    border-bottom: 1px solid #eeeeee;
    overflow: hidden;
    padding-left: 30.1932rpx;
    padding-right: 30.1932rpx;
}
.zf-yiju .zf-list li .s1 {
    float: left;
    font-size: 28.985475rpx;
    color: #333;
    line-height: 48.3091499rpx;
    padding: 16.908225rpx 0;
    width: 80%;
}
.zf-yiju .zf-list li .s2 {
    float: right;
    height: 82.1255999rpx;
    width: 38.64735rpx;
    background: url(~@/static/app/images/xiazai.png) 0 center no-repeat;
    background-size: 38.64735rpx 30.1932rpx;
}
.zf-faze .pd-inner {
    background-color: #fff;
    padding-left: 24.1545749rpx;
    padding-right: 24.1545749rpx;
    box-sizing: border-box;
}
.zf-faze h1 {
    font-size: 38.64735rpx;
    line-height: 108.695625rpx;
    color: #1e1e1e;
}
.zf-faze .miaoshu {
    margin-bottom: 18.11595rpx;
}
.zf-faze .miaoshu p {
    line-height: 60.386475rpx;
    font-size: 30.1932rpx;
    color: #666;
}
.zf-faze .index {
    margin-bottom: 18.11595rpx;
}
.zf-faze .index p {
    line-height: 60.386475rpx;
    font-size: 30.1932rpx;
    color: #666;
}
.zf-faze .ctx .cell {
    margin-bottom: 12.077325rpx;
}
.zf-faze .ctx .cell .til {
    line-height: 60.386475rpx;
    font-size: 30.1932rpx;
    color: #666;
    text-align: center;
    transform: translateX(-4%);
}
.zf-faze .ctx .cell p {
    line-height: 60.386475rpx;
    font-size: 30.1932rpx;
    color: #666;
    text-indent: 60.386475rpx;
}
.fenxi-shouye {
    background-color: #fff;
}
.fenxi-shouye ul li {
    width: 701.08695rpx;
    height: 196.256025rpx;
    margin: 0 auto;
    box-sizing: border-box;
    padding-left: 141.30435rpx;
    border-radius: 9.057975rpx;
    margin-top: 30.1932rpx;
    box-shadow: 0 0 12.077325rpx rgba(0, 0, 0, 0.1);
}
.fenxi-shouye ul li .p1 {
    padding-top: 50.120775rpx;
    font-size: 30.1932rpx;
    line-height: 50.120775rpx;
    color: #fff;
    font-weight: 700;
}
.fenxi-shouye ul li .p2 {
    line-height: 46.4976rpx;
    font-size: 25.3623rpx;
    color: #fff;
    opacity: 0.8;
}
.fenxi-shouye .li1 {
    background: url(~@/static/app/images/fenxi-li1.png) 0 0 no-repeat;
    background-size: 100%;
}
.fenxi-shouye .li2 {
    background: url(~@/static/app/images/fenxi-li2.png) 0 0 no-repeat;
    background-size: 100%;
}
.fenxi-shouye .li3 {
    background: url(~@/static/app/images/fenxi-li3.png) 0 0 no-repeat;
    background-size: 100%;
}
.fenxi-shouye .li4 {
    background: url(~@/static/app/images/fenxi-li4.png) 0 0 no-repeat;
    background-size: 100%;
}
.fenxi-paiming .tips {
    font-size: 25.3623rpx;
    line-height: 83.333325rpx;
    color: #1e1e1e;
    vertical-align: baseline;
    padding-left: 24.1545749rpx;
}
.fenxi-paiming .tips span {
    font-size: 32.608725rpx;
    font-weight: bold;
}
.fenxi-paiming .cell {
    background-color: #fff;
    margin-bottom: 18.11595rpx;
    position: relative;
}
.fenxi-paiming .cell .hd {
    line-height: 79.71015rpx;
    font-size: 30.1932rpx;
    color: #1e1e1e;
    padding-left: 39.855075rpx;
    position: relative;
}
.fenxi-paiming .cell .hd::before {
    content: '';
    position: absolute;
    top: 50%;
    margin-top: -6.0386251rpx;
    left: 12.077325rpx;
    width: 12.077325rpx;
    height: 12.077325rpx;
    background: #099af6;
    border-radius: 50%;
}
.fenxi-paiming .cell .bd {
    padding-bottom: 30.1932rpx;
    padding-top: 12.077325rpx;
}
.fenxi-paiming .cell .bd .tu {
    padding: 0 18.11595rpx;
    text-align: center;
}
.fenxi-paiming .cell .bd .tu img {
    max-width: 100%;
}
.fenxi-paiming .cell .bd .zi {
    font-size: 27.77775rpx;
    line-height: 36.2319rpx;
    color: #1e1e1e;
    text-align: center;
    margin-top: 30.1932rpx;
}
.fenxi-paiming .cell .bd .zi .num {
    color: #099af6;
}
.fenxi-paiming .cell .bd .data {
    padding: 0 24.1545749rpx;
}
.fenxi-paiming .cell .bd .data ul {
    overflow: hidden;
}
.fenxi-paiming .cell .bd .data ul li {
    float: left;
    width: 33%;
    text-align: center;
}
.fenxi-paiming .cell .bd .data ul li .p2 {
    text-align: center;
    line-height: 30.1932rpx;
    font-size: 24.1545749rpx;
    color: #1e1e1e;
    margin-top: 18.11595rpx;
}
.fenxi-paiming .cell .qiehuan {
    position: absolute;
    border-radius: 6.038625rpx;
    overflow: hidden;
    top: 15.7005rpx;
    right: 24.1545749rpx;
    border: 1px solid #099af6;
}
.fenxi-paiming .cell .qiehuan span {
    float: left;
    width: 90.579675rpx;
    height: 48.3091499rpx;
    box-sizing: border-box;
    text-align: center;
    line-height: 48.3091499rpx;
    font-size: 25.3623rpx;
    color: #666;
}
.fenxi-paiming .cell .qiehuan span.cur {
    color: #fff;
    background-color: #099af6;
}
.shuju-shouye {
    background-color: #f7f7f7;
}
.shuju-shouye ul li .p1 {
    color: #1e1e1e;
}
.shuju-shouye ul li .p2 {
    color: #666;
}
.shuju-shouye .li1 {
    background: #fff url(~@/static/app/images/shuju-li1.png) 30.1932rpx center
        no-repeat;
    background-size: 90.579675rpx;
}
.shuju-shouye .li2 {
    background: #fff url(~@/static/app/images/shuju-li2.png) 30.1932rpx center
        no-repeat;
    background-size: 90.579675rpx;
}
.shuju-shouye .li3 {
    background: #fff url(~@/static/app/images/shuju-li3.png) 30.1932rpx center
        no-repeat;
    background-size: 90.579675rpx;
}
.shuju-shouye .li4 {
    background: #fff url(~@/static/app/images/shuju-li4.png) 30.1932rpx center
        no-repeat;
    background-size: 90.579675rpx;
}
.shuju-shui10 .search {
    padding: 18.11595rpx;
}
.shuju-shui10 .search input {
    width: 100%;
    padding-left: 54.34785rpx;
    box-sizing: border-box;
    border: 1px solid #eeeeee;
    border-radius: 6.038625rpx;
    font-size: 25.3623rpx;
    color: #333;
    line-height: 60.386475rpx;
    background-color: #fff;
    background: #fff url(~@/static/app/images/gray-search.png) 21.1353rpx center
        no-repeat;
    background-size: 23.55075rpx 22.94685rpx;
}
.shuju-shui10 .list li {
    background-color: #fff;
    padding: 0 30.1932rpx;
    margin-bottom: 18.11595rpx;
}
.shuju-shui10 .list li .line1 {
    overflow: hidden;
}
.shuju-shui10 .list li .line1 .p1 {
    float: left;
    line-height: 76.086975rpx;
    font-size: 30.1932rpx;
    color: #1e1e1e;
}
.shuju-shui10 .list li .line1 .p2 {
    float: right;
    font-size: 25.3623rpx;
    line-height: 76.086975rpx;
    color: #999;
}
.shuju-shui10 .list li .line2 .bar {
    background: #f3f7fa;
    height: 21.7391249rpx;
    border-radius: 10.8696rpx;
    position: relative;
}
.shuju-shui10 .list li .line2 .bar .xiao {
    position: absolute;
    width: 30%;
    height: 100%;
    top: 0;
    left: 0;
    border-radius: 10.8696rpx;
    background: linear-gradient(to right, #a3dbff, #37adf7);
    text-align: right;
    line-height: 21.7391249rpx;
    color: #fff;
    padding-right: 6.038625rpx;
    box-sizing: border-box;
    font-size: 18.11595rpx;
}
.shuju-shui10 .list li .line3 p {
    line-height: 67.632825rpx;
    font-size: 25.3623rpx;
    color: #666;
}
.shuju-wuranyuan {
    background-color: #fff;
}
.shuju-wuranyuan .list {
    padding-left: 33.21255rpx;
}
.shuju-wuranyuan .list li {
    overflow: hidden;
    border-bottom: 1px solid #eeeeee;
}
.shuju-wuranyuan .list li .s1 {
    float: left;
    font-size: 28.985475rpx;
    color: #333;
    line-height: 83.333325rpx;
    position: relative;
    padding-left: 26.570025rpx;
}
.shuju-wuranyuan .list li .s1::before {
    content: '';
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    left: 0;
    width: 12.077325rpx;
    height: 12.077325rpx;
    background-color: #099af6;
    border-radius: 50%;
}
.shuju-wuranyuan .list li .s2 {
    float: right;
    font-size: 26.570025rpx;
    color: #666;
    line-height: 83.333325rpx;
    padding-right: 58.57485rpx;
    background: url(~@/static/app/images/arrow-right2.png) 90.579675rpx center
        no-repeat;
    background-size: 15.7005rpx 25.9662rpx;
}
.shuju-wuranyuan-jichu .search {
    padding: 18.11595rpx;
    padding-bottom: 0;
    position: relative;
}
.shuju-wuranyuan-jichu .search input {
    width: 100%;
    padding-left: 54.34785rpx;
    box-sizing: border-box;
    border: 1px solid #eeeeee;
    border-radius: 6.038625rpx;
    font-size: 25.3623rpx;
    color: #333;
    line-height: 60.386475rpx;
    background-color: #fff;
    background: #fff url(~@/static/app/images/gray-search.png) 21.1353rpx center
        no-repeat;
    background-size: 23.55075rpx;
}
.shuju-wuranyuan-jichu .search .close {
    position: absolute;
    top: 36.2319rpx;
    right: 32.004825rpx;
    width: 25.9662rpx;
    height: 25.9662rpx;
    background: url(~@/static/app/images/input-close.png) 0 0 no-repeat;
    background-size: 100%;
}
.shuju-wuranyuan-jichu .result {
    font-size: 25.3623rpx;
    color: #666;
    line-height: 70.0482749rpx;
    padding-left: 18.11595rpx;
}
.shuju-wuranyuan-jichu .result span {
    color: #f49836;
    margin: 0 3.01935rpx;
}
.shuju-wuranyuan-jichu .list > li {
    margin-bottom: 18.11595rpx;
    background-color: #fff;
}
.shuju-wuranyuan-jichu .list > li .hd {
    line-height: 36.2319rpx;
    padding: 24.1545749rpx 0;
    padding-left: 80.917875rpx;
    color: #333;
    font-size: 28.985475rpx;
    background: url(~@/static/app/images/hd-bg.png) 28.985475rpx center
        no-repeat;
    border-bottom: 1px solid #ddd;
    background-size: 33.21255rpx 31.401rpx;
}
.shuju-wuranyuan-jichu .list > li .hd span {
    color: #ff3434;
}
.shuju-wuranyuan-jichu .list > li .bd {
    overflow: hidden;
}
.shuju-wuranyuan-jichu .list > li .bd ul li {
    float: left;
    width: 33.333%;
    text-align: center;
    padding: 24.1545749rpx 0;
    position: relative;
}
.shuju-wuranyuan-jichu .list > li .bd ul li .p1 {
    font-size: 26.570025rpx;
    line-height: 45.289875rpx;
    color: #333;
}
.shuju-wuranyuan-jichu .list > li .bd ul li .p2 {
    font-size: 26.570025rpx;
    line-height: 48.3091499rpx;
    color: #666;
}
.shuju-wuranyuan-jichu .list > li .bd ul li:nth-of-type(1)::after,
.shuju-wuranyuan-jichu .list > li .bd ul li:nth-of-type(2)::after {
    content: '';
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    right: 0;
    width: 1.207725rpx;
    height: 69.44445rpx;
    background-color: #eeeeee;
}
@keyframes zhuanquan {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}
@keyframes bolang1 {
    0% {
        bottom: 0;
    }
    100% {
        bottom: -15.0966rpx;
    }
} /* 3-17 */
.pd-ultbs3 {
    width: 110%;
    overflow-y: hidden;
    overflow-x: scroll;
    -webkit-overflow-scrolling: touch;
}
.pd-ultbs3 ul {
    display: flex;
    display: -webkit-box;
}
.pd-ultbs3 ul li {
    flex: 1;
    -webkit-box-flex: 1;
    font-size: 26.570025rpx;
    color: #666;
    white-space: nowrap;
}
.pd-ultbs3 ul li.on {
    color: #099af6;
}
.pd-ullst4 {
    padding: 0 18.11595rpx;
}
.pd-ullst4 li {
    background: #fff;
    border-radius: 9.057975rpx;
    height: 134.058rpx;
    display: flex;
    display: -webkit-box;
}
.pd-ullst4 li p {
    width: 114.734325rpx;
    position: relative;
}
.pd-ullst4 li p img {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 63.405825rpx;
    height: 70.652175rpx;
}
.pd-ullst4 li dl {
    flex: 1;
    -webkit-box-flex: 1;
}
.pd-ullst4 li dl dt {
    font-size: 27.77775rpx;
    color: #333;
    padding: 24.1545749rpx 0 9.057975rpx;
}
.pd-ullst4 li dl dt em {
    color: #099af6;
}
.pd-ullst4 li dl dd span {
    font-size: 24.1545749rpx;
    color: #999;
    float: left;
}
.pd-ullst4 li dl dd em {
    float: right;
    padding-top: 18.11595rpx;
}
.pd-ullst4 li dl dd em i {
    float: left;
    width: 25.9662rpx;
    height: 18.11595rpx;
    background-repeat: no-repeat;
    background-position: center;
    background-size: contain;
    padding: 0 18.11595rpx;
    position: relative;
}
.pd-ullst4 li dl dd em i.view {
    background-image: url(~@/static/app/images/viewic.png);
}
.pd-ullst4 li dl dd em i.dlod {
    background-image: url(~@/static/app/images/dlodic.png);
}
.pd-ullst4 li dl dd em i + i:before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    width: 1px;
    height: 13.888875rpx;
    background: #ddd;
    transform: translateY(-50%);
}
.pd-ullst4 li + li {
    margin-top: 18.11595rpx;
}
.pd-ultbs4 {
    height: 72.4638rpx;
    display: flex;
    display: -webkit-box;
    background: #fff;
    border-bottom: 1px solid #eee;
}
.pd-ultbs4 li {
    flex: 1;
    -webkit-box-flex: 1;
    text-align: center;
}
.pd-ultbs4 li i {
    font-size: 27.77775rpx;
    color: #666;
    display: inline-block;
    padding: 0 35.024175rpx 0 0;
    height: 72.4638rpx;
    line-height: 72.4638rpx;
    box-sizing: border-box;
    background: url(~@/static/app/images/slecbot.png) no-repeat right center;
    background-size: 20.5313999rpx 12.077325rpx;
}
.pd-datebx1 {
    height: 78.502425rpx;
    padding: 0 18.11595rpx;
    display: flex;
    display: -webkit-box;
    background: #fff;
    align-items: center;
    -webkit-box-align: center;
}
.pd-datebx1 label {
    display: block;
    font-size: 26.570025rpx;
    color: #333;
}
.pd-datebx1 input {
    flex: 1;
    -webkit-box-flex: 1;
    background: url(~@/static/app/images/dateico.png) no-repeat right center;
    background-size: 27.173925rpx 25.9662rpx;
    display: block;
    font-size: 26.570025rpx;
    color: #666;
}
.pd-slecbx {
    position: relative;
    background: url(~@/static/app/images/slecbot2.png) no-repeat right center;
    padding-right: 42.270525rpx;
    background-size: 20.5313999rpx 12.077325rpx;
}
.pd-mask {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: #000;
    opacity: 0.5;
    z-index: 1000;
    display: none;
}
.pd-slecdlg {
    position: absolute;
    right: 0;
    width: 211.352625rpx;
    top: 100%;
    margin-top: 18.11595rpx;
    background: #fff;
    border-radius: 9.057975rpx;
    z-index: 1001;
    display: none;
}
.pd-slecdlg li {
    font-size: 26.570025rpx;
    color: #333;
    line-height: 72.4638rpx;
    padding-left: 24.1545749rpx;
    text-align: left;
}
.pd-slecdlg li + li {
    border-top: 1px solid #ddd;
}
.pd-slecdlg li.on {
    color: #099af6;
}
.pd-slecdlg:before {
    content: '';
    position: absolute;
    left: 60%;
    bottom: 100%;
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 0 16.908225rpx 16.908225rpx 16.908225rpx;
    border-color: transparent transparent white transparent;
}
.pd-topabs {
    position: absolute;
    left: 0;
    right: 0;
    top: 79.71015rpx;
    z-index: 999;
    background: #f7f7f7;
}
.pd-tablebx1 {
    position: absolute;
    left: 0;
    right: 0;
    top: 327.294675rpx;
    bottom: 0;
    overflow-x: auto;
    overflow-y: hidden;
}
.pd-tablebx1 .pd-table1 {
    width: 100%;
}
.pd-tablehd {
    position: absolute;
    left: 0;
    width: 120%;
    top: 0;
}
.pd-tablebd {
    position: absolute;
    left: 0;
    width: 120%;
    top: 84.54105rpx;
    bottom: 0;
    overflow-x: hidden;
    overflow-y: scroll;
}
.kaohe .date {
    position: relative;
}
.kaohe .date em {
    font-size: 26.570025rpx;
    color: #666;
    position: absolute;
    right: 24.1545749rpx;
    top: 15.0966rpx;
}
.kaohe .date em select {
    border: 1px solid #ddd;
    height: 48.3091499rpx;
    line-height: 48.3091499rpx;
    border-radius: 300px;
    font-size: 26.570025rpx;
    color: #666;
    width: 204.106275rpx;
    text-indent: 18.11595rpx;
    background: url(~@/static/app/images/slecbot3.png) no-repeat 90% center;
    background-size: 15.7005rpx 10.2656999rpx;
} /* 9-8 报告列表 */
.uploadic {
    position: absolute;
    right: 0;
    top: 0;
    width: 74.27535rpx;
    height: 100%;
    background: url(~@/static/app/images/uploadic.png) no-repeat center;
    background-size: 38.04345rpx 25.9662rpx;
}
.pd-timebx1 {
    height: 78.502425rpx;
    border-radius: 7.24635rpx;
    border: 1px solid #efefef;
    background: #fff;
    margin: 0 18.11595rpx;
    padding: 0 33.21255rpx;
    display: flex;
    justify-content: space-between;
    line-height: 78.502425rpx;
}
.pd-timebx1 span {
    font-size: 26.570025rpx;
    color: #1e1e1e;
}
.pd-timebx1 span i {
    color: #666;
}
.pd-ullst4.otr li dl dd em i {
    width: 25.9662rpx;
    height: 20.5313999rpx;
    background-size: 25.9662rpx 20.5313999rpx;
}
.pd-ullst4.otr li dl dd em i.tol1a {
    background-image: url(~@/static/app/images/tolic1.png);
}
.pd-ullst4.otr li dl dd em i.tol2a {
    background-image: url(~@/static/app/images/tolic2.png);
}
.pd-ullst4.otr li dl dd em i.tol3a {
    background-image: url(~@/static/app/images/tolic3.png);
}
.char1x {
    width: 239.13045rpx;
    height: 239.13045rpx;
    display: block;
    margin: 0 auto;
}
.pd-txt1x {
    margin: 0 24.1545749rpx;
    border-top: 1px solid #e5e5e5;
    height: 87.5604rpx;
    line-height: 87.5604rpx;
    display: flex;
    justify-content: space-between;
}
.pd-txt1x span {
    font-size: 28.985475rpx;
    color: #000;
}
.pd-txt1x i {
    font-size: 28.985475rpx;
    color: #eebe16;
}
.pd-txt1x i b {
    display: inline-block;
    height: 36.2319rpx;
    line-height: 36.2319rpx;
    border-radius: 5.4348rpx;
    background: #24bd5d;
    font-size: 26.570025rpx;
    color: white;
    padding: 0 9.057975rpx;
}
.pd-more1a {
    height: 88.768125rpx;
    line-height: 88.768125rpx;
    text-align: center;
}
.pd-more1a a {
    font-size: 26.570025rpx;
    color: #333;
    background: url(~@/static/app/images/arwbot1.png) no-repeat left center;
    padding-left: 32.608725rpx;
    background-size: 20.5313999rpx 20.5313999rpx;
}
.pd-ultbs1a {
    display: flex;
    position: relative;
}
.pd-ultbs1a i {
    position: absolute;
    top: 50%;
    width: 13.28505rpx;
    height: 22.94685rpx;
    background-repeat: no-repeat;
    background-position: center;
    background-size: 13.28505rpx 22.94685rpx;
    transform: translateY(-50%);
}
.pd-ultbs1a .prevbtn {
    right: 100%;
    background-image: url(~@/static/app/images/arwlt.png);
}
.pd-ultbs1a .nextbtn {
    left: 100%;
    background-image: url(~@/static/app/images/arwrt.png);
}
.xt-laiyuan .ctx .cell .bd ul.pd-ultbs1a li.on {
    color: #099af6;
}
.xt-laiyuan .ctx .cell .bd ul.pd-ultbs1a li {
    font-size: 26.570025rpx;
    color: #666;
    text-align: center;
    flex: 1;
    border-bottom: none;
}
.pd-slecbx1a {
    background: #fff;
    height: 78.502425rpx;
    line-height: 78.502425rpx;
    display: flex;
    justify-content: space-between;
    padding: 0 24.1545749rpx;
}
.pd-slecbx1a i {
    flex: 1;
    text-align: right;
}
.pd-slecbx1a span {
    font-size: 26.570025rpx;
    color: #1e1e1e;
}
.pd-slect1a {
    border: 1px solid #ddd;
    height: 48.3091499rpx;
    line-height: 48.3091499rpx;
    border-radius: 300px;
    font-size: 26.570025rpx;
    color: #666;
    width: 204.106275rpx;
    text-indent: 18.11595rpx;
    background: url(~@/static/app/images/slecbot3.png) no-repeat 90% center;
    background-size: 15.7005rpx 10.2656999rpx;
}
.tu1a {
    float: left;
    margin-left: 48.3091499rpx;
}
.tu1a img {
    display: block;
    width: 239.13045rpx;
    height: 239.13045rpx;
}
.inf1a {
    overflow: hidden;
    padding-top: 18.11595rpx;
}
.inf1a table {
    width: 100%;
}
.inf1a table tr td.td-hd {
    font-size: 27.77775rpx;
    color: #333;
    text-align: right;
}
.inf1a table tr td {
    font-size: 30.1932rpx;
    color: #099af6;
    padding: 9.057975rpx 0;
}
.fenxi-paiming .cell .qiehuan.otr span {
    width: auto;
    padding: 0 9.057975rpx;
}
.pd-ulbx1 {
    position: relative;
    padding-left: 48.3091499rpx;
    background: #fff;
}
.pd-ulbx1:before {
    content: '';
    position: absolute;
    left: 31.401rpx;
    top: 54.34785rpx;
    bottom: 45.289875rpx;
    width: 1px;
    background: #b1b8c9;
}
.pd-ulbx1 h1 {
    height: 72.4638rpx;
    position: relative;
    font-size: 28.985475rpx;
    color: #2c323f;
    line-height: 72.4638rpx;
    padding-left: 9.057975rpx;
}
.pd-ulbx1 h1:before {
    content: '';
    position: absolute;
    right: 100%;
    top: 50%;
    background: url(~@/static/app/images/zzic1.png) no-repeat;
    width: 26.570025rpx;
    height: 24.1545749rpx;
    background-size: contain;
    transform: translateY(-50%);
}
.pd-ulbx1 li {
    height: 90.579675rpx;
    display: flex;
    justify-content: space-between;
    position: relative;
    padding-left: 15.0966rpx;
    border-top: 1px solid #ededed;
}
.pd-ulbx1 > li:before {
    content: '';
    position: absolute;
    left: -18.1159501rpx;
    top: 50%;
    height: 1px;
    width: 24.1545749rpx;
    background: #b1b8c9;
    transform: translateY(-50%);
}
.pd-ulbx1 > li:after {
    content: '';
    position: absolute;
    right: 18.11595rpx;
    top: 0;
    bottom: 0;
    background: url(~@/static/app/images/arwbot.png) no-repeat center;
    background-size: 21.7391249rpx 12.68115rpx;
    width: 21.7391249rpx;
}
.pd-ulbx1 input[type='checkbox'] {
    background: url(~@/static/app/images/checkic.png) no-repeat;
    width: 22.94685rpx;
    height: 22.94685rpx;
    background-size: 22.94685rpx 22.94685rpx;
    margin-right: 10.8696rpx;
}
.pd-ulbx1 input[type='checkbox']:checked {
    background-image: url(~@/static/app/images/checkicon.png);
}
.pd-ulbx1 li span {
    line-height: 90.579675rpx;
    font-size: 28.985475rpx;
    color: #2c323f;
}
.pd-ulbx1 li p {
    line-height: 90.579675rpx;
    padding-right: 18.11595rpx;
}
.pd-ulbx1 li p img {
    width: 26.570025rpx;
    height: 27.77775rpx;
}
.pd-ulbx1 li p label + label {
    margin-left: 48.3091499rpx;
}
.pd-ulbx1 ul {
    display: none;
}
.pd-ulbx1 li.on + ul {
    display: block;
}
.pd-ulbx1 > li.on span {
    color: #0099ff;
}
.pd-ulbx1 > li.on:after {
    content: '';
    background-image: url(~@/static/app/images/arwtop.png);
}
.pd-srh1a {
    margin-left: 72.4638rpx;
    margin-right: 18.11595rpx;
    padding-top: 6.038625rpx;
}
.pd-srh1a input {
    width: 100%;
    height: 66.4251rpx;
    background: #fff url(~@/static/app/images/srhic1a.png) no-repeat 18.11595rpx
        center;
    border-radius: 9.057975rpx;
    text-indent: 57.367125rpx;
    background-size: 27.77775rpx 27.77775rpx;
    font-size: 27.77775rpx;
    color: #2c323f;
}
.pd-srh1a input::-webkit-input-placeholder {
    color: #b1b8c8;
    line-height: 66.4251rpx;
}
.pd-inner.pt2 {
    padding-top: 152.17395rpx;
    padding-bottom: 0;
}
.pd-tips {
    font-size: 26.570025rpx;
    color: #646c7f;
    line-height: 60.386475rpx;
    text-align: center;
}
.pd-tips i {
    color: #009bff;
}
.pd-dlbx1 + .pd-dlbx1 {
    margin-top: 18.11595rpx;
}
.pd-dlbx1 {
    background: #fff;
}
.pd-dlbx1 dt {
    height: 72.4638rpx;
    border-bottom: 1px solid #ededed;
}
.pd-dlbx1 dt span {
    float: left;
    font-size: 28.985475rpx;
    color: #2c323f;
    line-height: 72.4638rpx;
    padding-left: 18.11595rpx;
}
.pd-dlbx1 dt span i {
    color: #009bff;
}
.pd-dlbx1 dd {
    padding: 18.11595rpx 0;
    position: relative;
}
.pd-dlbx1 dd .imgic {
    position: absolute;
    left: 18.11595rpx;
    top: 18.11595rpx;
    width: 78.502425rpx;
    height: 78.502425rpx;
}
.pd-dlbx1 dd h2 .mk {
    width: 96.618375rpx;
    height: 26.570025rpx;
}
.pd-dlbx1 dd h2 {
    font-size: 27.77775rpx;
    color: #2c323f;
    padding-left: 114.734325rpx;
}
.pd-dlbx1 dd p {
    font-size: 27.77775rpx;
    color: #2c323f;
    padding-left: 114.734325rpx;
    padding-top: 9.057975rpx;
}
.pd-dlbx1 dd p em {
    color: #646c7f;
}
.pd-dlbx1 dd p i {
    font-size: 27.77775rpx;
    color: #ef482f;
}
.pd-dlbx1 dd small {
    background: url(~@/static/app/images/arwrt1.png) no-repeat;
    background-size: 12.68115rpx 21.7391249rpx;
    width: 12.68115rpx;
    height: 21.7391249rpx;
    position: absolute;
    right: 18.11595rpx;
    top: 50%;
    transform: translateY(-50%);
}
.pd-dlbx1 dd + dd {
    border-top: 1px solid #ededed;
}
.pd-ulbx2 {
    display: flex;
}
.pd-ulbx2 li {
    flex: 1;
    text-align: center;
    position: relative;
}
.pd-ulbx2 li i {
    font-size: 27.77775rpx;
    color: #45a4fc;
    background-repeat: no-repeat;
    background-position: right center;
    background-size: 24.1545749rpx 27.77775rpx;
    padding-right: 36.2319rpx;
}
.pd-ulbx2 li i.lsts1 {
    background-image: url(~@/static/app/images/lsts1.png);
}
.pd-ulbx2 li i.lsts2 {
    background-image: url(~@/static/app/images/lsts2.png);
}
.pd-ulbx2 li + li:before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    width: 1px;
    height: 30.1932rpx;
    background: #b1b8c8;
    transform: translateY(-50%);
}
.pd-txtbtn {
    position: absolute;
    right: 18.11595rpx;
    top: 0;
    line-height: 79.71015rpx;
    font-size: 26.570025rpx;
    color: #fff;
}
.pd-srhbtn {
    position: absolute;
    right: 0;
    top: 0;
    width: 70.652175rpx;
    height: 79.71015rpx;
    background: url(~@/static/app/images/srhic2.png) no-repeat center;
    background-size: 34.4202749rpx 34.4202749rpx;
}
.pd-ullst1a {
    background: #fff;
    padding-left: 18.11595rpx;
}
.pd-ullst1a li {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    padding-right: 18.11595rpx;
    height: 90.579675rpx;
}
.pd-ullst1a li em {
    font-size: 27.77775rpx;
    color: #b1b8c9;
    line-height: 90.579675rpx;
}
.pd-ullst1a li i {
    font-size: 27.77775rpx;
    color: #2c323f;
    line-height: 90.579675rpx;
}
.pd-ullst1a li.on em {
    font-size: 19.323675rpx;
    width: 100%;
    line-height: 1;
    padding: 12.077325rpx 0 6.038625rpx;
}
.pd-ullst1a li.on i {
    width: 100%;
    line-height: 1;
}
.pd-ullst1a li + li {
    border-top: 1px solid #ededed;
}
.pd-ullst2a {
    padding-left: 18.11595rpx;
    background: #fff;
}
.pd-ullst2a h1 {
    font-size: 31.401rpx;
    color: #252525;
    height: 90.579675rpx;
    line-height: 90.579675rpx;
}
.pd-ullst2a li {
    display: flex;
    height: 90.579675rpx;
    justify-content: space-between;
    line-height: 90.579675rpx;
}
.pd-ullst2a li em {
    font-size: 27.77775rpx;
    color: #646c7f;
}
.pd-ullst2a li i {
    font-size: 27.77775rpx;
    color: #2c323f;
    padding-right: 18.11595rpx;
}
.pd-ullst2a li + li {
    border-top: 1px solid #ededed;
}
.pd-ullst2a li.nfx {
    display: list-item;
    height: auto;
    line-height: normal;
    padding: 24.1545749rpx 0;
}
.pd-ullst2a li.nfx p {
    line-height: 1.5;
    padding-top: 18.11595rpx;
    font-size: 27.77775rpx;
    color: #333;
}
.pd-ullst2a li.nfx textarea {
    display: block;
    width: 100%;
    height: 157.00485rpx;
    background: #f5f5f5;
    margin-top: 24.1545749rpx;
}
.pd-ullst2a.otr {
    padding-right: 18.11595rpx;
}
.pd-botbtn {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    height: 72.4638rpx;
    line-height: 72.4638rpx;
    font-size: 31.401rpx;
    color: #fff;
    text-align: center;
    background: #009bff;
}
.pd-fixbtn {
    position: fixed;
    right: 12.077325rpx;
    bottom: 158.2125749rpx;
    width: 107.4879rpx;
    height: 107.4879rpx;
    background: url(~@/static/app/images/fixbtn.png) no-repeat;
    background-size: 107.4879rpx 107.4879rpx;
}
.pd-srh1a.otr {
    padding-right: 78.502425rpx;
}
.pd-ullst3a {
    background: #fff;
    padding-left: 18.11595rpx;
}
.pd-ullst3a li {
    height: 90.579675rpx;
    font-size: 28.985475rpx;
    color: #2c323f;
    line-height: 90.579675rpx;
    background: url(~@/static/app/images/lstico1.png) no-repeat left center;
    background-size: 30.1932rpx 30.1932rpx;
    padding-left: 36.2319rpx;
    position: relative;
}
.pd-ullst3a li + li {
    border-top: 1px solid #ededed;
}
.pd-ullst3a li:after {
    content: '';
    position: absolute;
    right: 18.11595rpx;
    top: 0;
    bottom: 0;
    width: 18.719775rpx;
    background: url(~@/static/app/images/arwrt2.png) no-repeat center;
    background-size: 18.719775rpx 35.628rpx;
}
.pd-txtarea {
    width: 100%;
    height: 301.932375rpx;
    font-size: 27.77775rpx;
    color: #2c323f;
    padding: 18.11595rpx;
}
.pd-txtarea::-webkit-input-placeholder {
    color: #b1b8c8;
} /* login */
.pd-header.log {
    background: url(~@/static/app/images/log_bg.png) no-repeat;
    background-size: 100% 100%;
    height: 612.9227249rpx;
    z-index: auto;
}
.pd-header.log image {
    width: 400rpx;
    height: 218.59905rpx;
    display: block;
    margin: 140.70045rpx auto 0;
}
.pd-main.log {
    position: relative;
    z-index: 1;
}
.pd-main.log .pd-inner {
    padding: 486.111075rpx 68.8405499rpx 0;
    height: 100%;
    box-sizing: border-box;
}
.pd-loginmod {
    background: #fff;
    box-shadow: 0 0 54.34785rpx rgba(20, 37, 50, 0.18);
    border-radius: 9.057975rpx;
    overflow: hidden;
}
.pd-loginmod dt {
    font-size: 48.3091499rpx;
    color: #0f1a35;
    padding: 24.1545749rpx 54.34785rpx 84.54105rpx;
}
.pd-loginmod dd {
    padding: 30rpx 21.1353rpx;
}
.pd-loginmod dd ul li {
    height: 72.4638rpx;
    position: relative;
}
.pd-loginmod dd ul li + li {
    margin-top: 20.405825rpx;
}
.pd-loginmod dd ul li .pd-ipt {
    height: 72.4638rpx;
    line-height: 72.4638rpx;
    display: block;
    width: 100%;
    background-repeat: no-repeat;
    background-position: left center;
    background-size: 35.024175rpx 36.2319rpx;
    padding-left: 57.367125rpx;
    border-bottom: 1px solid #eee;
    font-size: 28.985475rpx;
    color: #333;
}
.pd-loginmod dd ul li .pd-ipt.txt {
    background-image: url(~@/static/app/images/log_user.png);
}
.pd-loginmod dd ul li .pd-ipt.pwd {
    background-image: url(~@/static/app/images/log_key.png);
}
.pd-loginmod dd ul li .pd-ipt::-webkit-input-placeholder {
    color: #999;
}
.pd-loginmod dd ul li img {
    width: 34.4202749rpx;
    height: 25.9662rpx;
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
}
.pd-loginmod dd ul p {
    overflow: hidden;
    padding-top: 24.1545749rpx;
}
.pd-loginmod dd ul p label {
    font-size: 21.7391249rpx;
    color: #666;
    float: left;
}
.pd-loginmod dd ul p a {
    font-size: 21.7391249rpx;
    color: #666;
    float: right;
}
.pd-loginmod dd ul p input[type='checkbox'] {
    background: url(~@/static/app/images/ic_checkbox.png) no-repeat;
    width: 18.719775rpx;
    height: 18.719775rpx;
    background-size: contain;
    margin-right: 9.057975rpx;
}
.pd-loginmod dd ul p input[type='checkbox']:checked {
    background-image: url(~@/static/app/images/ic_checkbox_on.png);
}
.log-btn {
    width: 541.66665rpx;
    height: 76.6908rpx;
    border-radius: 300px;
    background: #4089ef;
    color: white;
    font-size: 32.608725rpx;
    display: block;
    margin: 84.54105rpx auto 48.3091499rpx;
    box-shadow: 0 5.4348rpx 27.77775rpx rgba(75, 150, 255, 0.36);
}
.log-bot {
    font-size: 24.1545749rpx;
    color: #a3adbd;
    text-align: center;
    position: absolute;
    bottom: 30.1932rpx;
    left: 0;
    right: 0;
}
.pd-header.idx {
    background: url(~@/static/app/images/bg1a.png) no-repeat;
    background-size: 100% 100%;
    height: 477.053175rpx;
}
.pd-name {
    background: url(~@/static/app/images/usric1.png) no-repeat left center;
    padding-left: 41.0627999rpx;
    color: #fff;
    font-size: 26.570025rpx;
    margin: 24.1545749rpx 24.1545749rpx 0;
    background-size: 25.9662rpx 28.985475rpx;
}
.pd-logo1 {
    display: block;
    width: 327.898575rpx;
    height: 181.159425rpx;
    margin: 0 auto;
}
.pd-inpsrh1 {
    height: 72.4638rpx;
    width: 690.217425rpx;
    display: block;
    margin: 0 auto;
    border-radius: 300px;
    background: rgba(255, 255, 255, 0.5) url(~@/static/app/images/srhic1.png)
        no-repeat 26.570025rpx center;
    text-indent: 62.801925rpx;
    line-height: 72.4638rpx;
    font-size: 28.985475rpx;
    color: #fff;
    background-size: 25.9662rpx 25.9662rpx;
}
.pd-inpsrh1::-webkit-input-placeholder {
    color: #fff;
}
.pd-ulbx3 {
    height: 169.0821rpx;
    background: #fff;
    border-radius: 12.077325rpx;
    position: absolute;
    left: 30.1932rpx;
    right: 30.1932rpx;
    top: 85%;
    box-shadow: 0 4.227075rpx 19.323675rpx rgba(161, 161, 161, 0.3);
    display: flex;
}
.pd-ulbx3 li {
    flex: 1;
    text-align: center;
}
.pd-ulbx3 li h2 {
    font-size: 26.570025rpx;
    color: #999;
    padding-top: 39.855075rpx;
}
.pd-ulbx3 li p {
    font-size: 38.64735rpx;
    color: #333;
    padding-top: 18.11595rpx;
}
.pd-inner.idx {
    padding-top: 579.710175rpx;
    padding-bottom: 0;
}
.pd-ulbx4 {
    background: #fff;
    border-radius: 12.077325rpx;
    box-shadow: 0 4.227075rpx 19.323675rpx rgba(161, 161, 161, 0.3);
    display: flex;
    flex-wrap: wrap;
    margin: 27.173925rpx 30.1932rpx 0;
    padding: 24.1545749rpx 0;
}
.pd-ulbx4 li {
    width: 33.33%;
    padding: 48.3091499rpx 0;
}
.pd-ulbx4 li img {
    width: 72.4638rpx;
    height: 60.990375rpx;
    display: block;
    margin: 0 auto;
}
.pd-ulbx4 li p {
    font-size: 26.570025rpx;
    color: #333;
    text-align: center;
    padding-top: 18.11595rpx;
}
.pd-dlbx2 {
    background: #fff;
    border-radius: 12.077325rpx;
    box-shadow: 0 4.227075rpx 21.1353rpx rgba(161, 161, 161, 0.15);
    margin: 18.11595rpx 18.11595rpx 0;
}
.pd-dlbx2 dt {
    padding: 24.1545749rpx 24.1545749rpx 0;
    overflow: hidden;
}
.pd-dlbx2 dt em {
    float: left;
    font-size: 27.77775rpx;
    color: #333;
}
.pd-dlbx2 dt i {
    float: right;
    font-size: 25.3623rpx;
    color: #999;
}
.pd-dlbx2 dd {
    padding: 0 24.1545749rpx;
    margin-top: 18.11595rpx;
}
.pd-dlbx2 dd p {
    font-size: 26.570025rpx;
    color: #474747;
    line-height: 1.8;
}
.pd-dlbx2 dd + dd {
    border-top: 1px solid #dcdcdc;
}
.pd-dlbx2 dd button {
    width: 100%;
    height: 78.502425rpx;
    font-size: 25.3623rpx;
    color: #5094f2;
    background: transparent;
}
.pd-header.hjxf {
    background: url(~@/static/app/images/topbg1.png) no-repeat;
    background-size: 100% 100%;
    height: 419.0821499rpx;
    z-index: auto;
}
.pd-header.hjxf .goback {
    height: 79.71015rpx;
}
.pd-ulbx3.otr {
    position: static;
    margin: 0 30.1932rpx;
    background: rgba(255, 255, 255, 0.18);
}
.pd-ulbx3.otr li h2 {
    color: #e2eeff;
}
.pd-ulbx3.otr li p {
    color: #fff;
}
.pd-inner.hjxf {
    padding-top: 356.280225rpx;
}
.pd-ulbx5 {
    background: #fff;
    border-radius: 12.077325rpx;
    display: flex;
    flex-wrap: wrap;
    position: relative;
    margin: 0 30.1932rpx;
}
.pd-ulbx5 li {
    width: 50%;
    padding: 102.657rpx 0;
    position: relative;
    box-sizing: border-box;
    border-left: 1px solid #ddd;
    border-top: 1px solid #ddd;
}
.pd-ulbx5 li:nth-child(2n-1) {
    border-left: none;
}
.pd-ulbx5 li:nth-child(1),
.pd-ulbx5 li:nth-child(2) {
    border-top: none;
}
.pd-ulbx5 li image {
    display: block;
    width: 85.748775rpx;
    height: 78.502425rpx;
    margin: 0 auto;
}
.pd-ulbx5 li p {
    font-size: 27.77775rpx;
    color: #333;
    text-align: center;
    padding-top: 21.1353rpx;
}
.pd-ulbx5 li i {
    position: absolute;
    left: 50%;
    top: 25%;
    width: 28.985475rpx;
    height: 28.985475rpx;
    border-radius: 50%;
    background: #e86767;
    font-size: 21.7391249rpx;
    color: #fff;
    text-align: center;
    line-height: 28.985475rpx;
    margin-left: 36.2319rpx;
}
.pd-srhbtn1 {
    background: url(~@/static/app/images/srhic3.png) no-repeat center;
    position: absolute;
    right: 0;
    top: 0;
    height: 79.71015rpx;
    width: 73.671525rpx;
    background-size: 27.77775rpx 27.77775rpx;
}
.pd-ullst5 {
    background: #fff;
    padding-left: 30.1932rpx;
}
.pd-ullst5 li {
    padding: 24.1545749rpx 0;
    position: relative;
}
.pd-ullst5 li:after {
    content: '';
    position: absolute;
    right: 24.1545749rpx;
    top: 0;
    bottom: 0;
    width: 15.7005rpx;
    background: url(~@/static/app/images/arwrt3.png) no-repeat center;
    background-size: 15.7005rpx 25.9662rpx;
}
.pd-ullst5 li h2 {
    font-size: 28.985475rpx;
    color: #333;
}
.pd-ullst5 li p {
    font-size: 25.3623rpx;
    color: #666;
    padding-top: 18.11595rpx;
}
.pd-ullst5 li + li {
    border-top: 1px solid #eee;
}
.pd-ullst5 li i {
    position: absolute;
    right: 60.386475rpx;
    top: 50%;
    transform: translateY(-50%);
}
.pd-ullst5 li i img {
    float: left;
    width: 41.0627999rpx;
    height: 41.0627999rpx;
    margin-left: 10.8696rpx;
} /* 考试 */
.dati-body {
    background: #f1f9ff url(~@/static/app/images/zy-dati-bg.png) center -36.2319001rpx
        no-repeat;
    background-size: 100%;
}
.dati-body .pd-header {
    background: none;
}
.page-datiku .til {
    font-size: 30.1932rpx;
    color: #fff;
    height: 102.657rpx;
    line-height: 102.657rpx;
    text-align: center;
    margin-bottom: 72.4638rpx;
}
.page-datiku .til span {
    color: #ffd21d;
}
.page-datiku .timu {
    margin: 0 18.11595rpx;
    padding: 48.3091499rpx 48.3091499rpx 12.077325rpx 48.3091499rpx;
    border-radius: 9.057975rpx;
    background-color: #fff;
    position: relative;
    box-shadow: 0 0 12.077325rpx rgba(157, 190, 215, 0.25);
}
.page-datiku .timu .ti {
    text-align: center;
    padding-bottom: 18.11595rpx;
}
.page-datiku .timu .ti em {
    font-size: 30.1932rpx;
    color: #009bff;
}
.page-datiku .timu .ti i {
    font-size: 28.985475rpx;
    color: #999;
}
.page-datiku .timu .zi {
    font-size: 28.985475rpx;
    color: #232323;
    line-height: 48.3091499rpx;
    margin-bottom: 36.2319rpx;
}
.page-datiku .timu .xuanxiang li {
    font-size: 28.985475rpx;
    color: #232323;
    line-height: 72.4638rpx;
    margin-bottom: 24.1545749rpx;
    background-color: #f4f4f4;
    border-radius: 36.2319rpx;
    padding-left: 36.2319rpx;
}
.page-datiku .timu .xuanxiang li.cur {
    color: #24a0ff;
    background-color: #e2f3ff;
}
.page-datiku .timu .xuanxiang li.correct {
    color: #19ad2d;
    background: #e8f7ea url(~@/static/app/images/zy-correct.png) 92% center
        no-repeat;
    background-size: 30.1932rpx;
}
.page-datiku .timu .xuanxiang li.error {
    color: #e95660;
    background: #fff1f2 url(~@/static/app/images/zy-error.png) 92% center
        no-repeat;
    background-size: 22.3430249rpx;
}
.page-datiku .timu .laiyuan {
    display: flex;
    justify-content: flex-end;
}
.page-datiku .timu .laiyuan p {
    font-size: 26.570025rpx;
    color: #31a8ff;
    line-height: 1rem;
    padding-left: 40.4589rpx;
    background: url(~@/static/app/images/zy-chazhao.png) 0 center no-repeat;
    background-size: 30.7971rpx;
}
.page-datiku .timu .xuhao {
    position: absolute;
    top: -18.7197751rpx;
    left: 50%;
    transform: translateX(-50%);
    font-size: 28.985475rpx;
    color: #fff;
    line-height: 72.4638rpx;
    text-align: center;
    width: 390.7005rpx;
    height: 72.4638rpx;
    background: url(~@/static/app/images/zy-xuhao.png) no-repeat;
    background-size: 100%;
}
.page-datiku .timu-num {
    padding-top: 79.71015rpx;
}
.page-datiku .tijiao {
    padding: 0 74.87925rpx;
    margin-top: 199.275375rpx;
}
.page-datiku .tijiao p {
    font-size: 28.985475rpx;
    line-height: 72.4638rpx;
    background: linear-gradient(to right, #55ddff, #24a0ff);
    color: #fff;
    border: none;
    border-radius: 36.2319rpx;
    text-align: center;
}
.page-datiku .btns {
    padding: 0 72.4638rpx;
    display: flex;
    justify-content: space-between;
    position: absolute;
    left: 0;
    right: 0;
    bottom: 60.386475rpx;
}
.page-datiku .btns .fanhui {
    width: 292.8744rpx;
    height: 72.4638rpx;
    box-sizing: border-box;
    border-radius: 36.2319rpx;
    border: 1px solid transparent;
    font-size: 28.985475rpx;
    color: #31a8ff;
    line-height: 72.4638rpx;
    text-align: center;
    background-color: #fff;
}
.page-datiku .btns .jixu {
    background: linear-gradient(to right, #55ddff, #24a0ff);
    border: none;
    width: 292.8744rpx;
    height: 72.4638rpx;
    font-size: 28.985475rpx;
    color: #fff;
    line-height: 72.4638rpx;
    text-align: center;
    border-radius: 36.2319rpx;
}
.page-datiku .xiayiti {
    padding: 0 74.87925rpx;
    position: absolute;
    left: 0;
    right: 0;
    bottom: 60.386475rpx;
}
.page-datiku .xiayiti p {
    font-size: 28.985475rpx;
    line-height: 72.4638rpx;
    background: linear-gradient(to right, #55ddff, #24a0ff);
    color: #fff;
    border: none;
    border-radius: 36.2319rpx;
    text-align: center;
}
.pd-dlg {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    z-index: 1000;
}
.pd-dlg .dlgcls {
    position: absolute;
    left: 50%;
    bottom: 0;
    width: 62.801925rpx;
    height: 62.801925rpx;
    transform: translateX(-50%);
}
.pd-dlg.dlg1 {
    width: 651.570075rpx;
    height: 957.125625rpx;
    background: url(~@/static/app/images/dlgbg1.png) no-repeat;
    background-size: 651.570075rpx 957.125625rpx;
}
.pd-dlg.dlg1 h1 {
    font-size: 33.81645rpx;
    color: #fff;
    text-align: center;
    padding-top: 463.768125rpx;
}
.pd-dlg.dlg1 h2 {
    font-size: 30.1932rpx;
    color: #999;
    text-align: center;
    padding-top: 66.4251rpx;
}
.pd-dlg.dlg1 h3 {
    font-size: 72.4638rpx;
    color: #ff960e;
    text-align: center;
    padding-top: 24.1545749rpx;
}
.pd-dlg.dlg1 h3 sub {
    font-size: 30.1932rpx;
}
.pd-dlg.dlg1 p {
    font-size: 27.77775rpx;
    color: #333;
    text-align: center;
    padding-top: 36.2319rpx;
}
.pd-dlg.dlg1 p i {
    color: #ff960e;
}
.pd-dlg.dlg2 {
    width: 689.613525rpx;
    top: auto;
    bottom: 157.00485rpx;
    transform: translate(-50%, 0);
    box-sizing: border-box;
    background: #fff;
    padding: 30.1932rpx 36.2319rpx;
    border-radius: 24.1545749rpx;
}
.pd-dlg.dlg2 h1 {
    font-size: 28.985475rpx;
    color: #333;
}
.pd-dlg.dlg2 p {
    font-size: 27.77775rpx;
    color: #666;
    line-height: 1.8;
    padding-top: 18.11595rpx;
}
.pd-dlg.dlg2 .dlgcls {
    position: absolute;
    left: 50%;
    bottom: 0;
    width: 62.801925rpx;
    height: 62.801925rpx;
    transform: translateX(-50%);
    background: url(~@/static/app/images/dlgcls.png) no-repeat;
    background-size: 62.801925rpx 62.801925rpx;
    top: 100%;
    bottom: auto;
    margin-top: 28.985475rpx;
}
.pd-header.xxks {
    background: url(~@/static/app/images/topbg2.png) no-repeat;
    background-size: 100% 100%;
    height: 419.0821499rpx;
    z-index: auto;
}
.pd-header.xxks .goback {
    height: 79.71015rpx;
}
.pd-inner.xxks {
    padding-top: 356.280225rpx;
}
.pd-dlbx3 {
    padding-left: 18.11595rpx;
    position: relative;
}
.pd-dlbx3 dt {
    float: left;
    margin-right: 24.1545749rpx;
}
.pd-dlbx3 dt image {
    display: block;
    width: 118.3575rpx;
    height: 118.3575rpx;
}
.pd-dlbx3 dd {
    overflow: hidden;
}
.pd-dlbx3 dd h1 {
    font-size: 33.81645rpx;
    color: #fff;
    padding-top: 9.057975rpx;
}
.pd-dlbx3 dd p {
    padding-top: 18.11595rpx;
}
.pd-dlbx3 dd p i {
    display: inline-block;
    border-radius: 300px;
    background: rgba(255, 255, 255, 0.3);
    height: 41.0627999rpx;
    line-height: 41.0627999rpx;
    padding: 0 18.11595rpx;
    font-size: 22.94685rpx;
    color: #fff;
}
.pd-dlbx3 dd p i + i {
    margin-left: 12.077325rpx;
}
.pd-dlbx3 .phb {
    background: url(~@/static/app/images/phb.png) no-repeat;
    width: 145.5314249rpx;
    height: 42.270525rpx;
    background-size: 145.5314249rpx 42.270525rpx;
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
}
.pd-ulbx5.otr li img {
    width: 75.483075rpx;
    height: 80.313975rpx;
}
.pd-ulbx5.otr li p + p {
    font-size: ;
    color: #e98a1a;
    font-size: 25.3623rpx;
    padding-top: 9.057975rpx;
}
.pd-ulbx5.otr li p + p sub {
    font-size: 22.94685rpx;
    color: #333;
}
.pd-ulbx6 {
    background: #fff;
    padding: 9.057975rpx 0;
}
.pd-ulbx6 li {
    display: flex;
    padding: 10.2656999rpx 0;
}
.pd-ulbx6 li h1 {
    width: 22%;
    text-align: right;
    font-size: 27.77775rpx;
    color: #333;
}
.pd-ulbx6 li p {
    flex: 1;
}
.pd-ulbx6 li p i {
    display: inline-block;
    width: 120.77295rpx;
    height: 42.270525rpx;
    line-height: 42.270525rpx;
    border-radius: 300px;
    background: #eaeaea;
    font-size: 26.570025rpx;
    color: #666;
    text-align: center;
    margin-left: 18.11595rpx;
}
.pd-ulbx6 li p i.on {
    background: #009bff;
    color: #fff;
}
.pd-dlbx4 {
    background: #fff;
    padding: 0 21.1353rpx;
}
.pd-dlbx4 dt {
    font-size: 28.985475rpx;
    color: #232323;
    padding-top: 24.1545749rpx;
}
.pd-dlbx4 dd .xuanxiang {
    padding-top: 24.1545749rpx;
}
.pd-dlbx4 dd .xuanxiang li {
    font-size: 28.985475rpx;
    color: #232323;
    line-height: 72.4638rpx;
    margin-bottom: 24.1545749rpx;
    background-color: #f4f4f4;
    border-radius: 36.2319rpx;
    padding-left: 36.2319rpx;
}
.pd-dlbx4 dd .xuanxiang li.cur {
    color: #24a0ff;
    background-color: #e2f3ff;
}
.pd-dlbx4 dd .xuanxiang li.correct {
    color: #19ad2d;
    background: #e8f7ea url(~@/static/app/images/zy-correct.png) 92% center
        no-repeat;
    background-size: 30.1932rpx;
}
.pd-dlbx4 dd .xuanxiang li.error {
    color: #e95660;
    background: #fff1f2 url(~@/static/app/images/zy-error.png) 92% center
        no-repeat;
    background-size: 22.3430249rpx;
}
.pd-dlbx4 dd p {
    font-size: 28.985475rpx;
    color: #666;
    line-height: 1.5;
    padding-bottom: 24.1545749rpx;
}
