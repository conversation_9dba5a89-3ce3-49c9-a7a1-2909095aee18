.form-element-group {
	border-radius: 10rpx;
	background-color: #fff;
}

.form-label {
	font-size: 32rpx;
	color: #999;
}
     
.form-value {
	font-size: 32rpx;
	color: #333;
	user-select: text;
}


/* #ifdef H5 || APP-PLUS */
.form-element-style {
	display: flex;
	width: calc(100% - 40rpx);
	padding-left: 20rpx;
	padding-right: 20rpx;
	min-height: 110rpx;
}
/* #endif */

/* #ifdef MP */
.form-element-style {
	display: flex;
	flex-flow: row nowrap;
	align-items: center;
	width: calc(100% - 20rpx);
	padding-left: 10rpx;
	padding-right: 10rpx;
	background-color: #fff;
	min-height: 110rpx;
}
/* #endif */

.form-element-divider {
	width: 100%;
	height: 2rpx;
	background-color: #f4f4f4;
}

.form-select-action-indicator {
	margin-left: 5px;
	width: 16px;
	height: 16px;
}

.form-verify {
	color: brown;
	width:20rpx;
}

.form-label-required {
	color: brown;
	margin-right: 6rpx;
}