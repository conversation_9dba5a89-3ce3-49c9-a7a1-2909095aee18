.flex-row-layout {
	display: flex;
	display: -webkit-flex;
	flex-flow: row nowrap;
	-webkit-flex-flow: row nowrap;
	justify-content: flex-start;
	-webkit-justify-content: flex-start;
	align-items: center;
	-webkit-align-items: center;
	width: 100%;
}

.flex-column-layout {
	display: flex;
	display: -webkit-flex;
	flex-flow: column;
	-webkit-flex-flow: column;
	justify-content: center;
	-webkit-justify-content: center;
	align-items: center;
	-webkit-align-items: center;
	width: 100%;
}

.flex-fix-center-layout {
	display: flex;
	flex-flow: row nowrap;
	justify-content: center;
	align-items: center;
}

.flex-auto-feedline-layout {
	display: flex;
	flex-flow: row wrap;
	justify-content: flex-start;
	align-items: center;
}

.loading-circle-tick {
	width: 32px;
	height: 32px;
	background: url(~@/static/img/loading_icon.png) no-repeat center;
	background-size: 22px 22px;
	animation: selfRevolve 1.3s linear infinite;
}

@keyframes selfRevolve{
	from{
		transform:rotate(0deg);
	}
	to{
		transform:rotate(360deg);
	}
}

.power-page{
	display: flex;
	flex-flow: column;
	justify-content: flex-start;
	align-items: center;
	width: 100%;
	height: 100%;
}

.navi-next {
	width: 32px;
	height: 32px;
	background: url(~@/static/img/navi_next_icon.png) no-repeat center;
	background-size: 16px 16px;
}

.power-checkbox {
	transform: scale(.68);
}

.power-button {
	/* #ifdef H5 */
	height: 68rpx;
	line-height: 68rpx;
	padding: 4rpx 32rpx 0 32rpx;
	/* #endif */
	/* #ifndef H5 */
	height: 72rpx;
	line-height: 72rpx;
	padding: 0rpx 32rpx;
	/* #endif */
	font-size: 32rpx;
	text-align: center;
}

.power-button:active {
	color: #fff;
	background-color: #1E8EEF;
}

.power-button-primary {
	background-color: #0FAEFF;
	border-radius: 6rpx;
	color: #fff;
}
