@charset "utf-8"; /** * mobile reset **/
html,
body,
div,
span,
ol,
ul,
applet,
object,
iframe,
h1,
h2,
h3,
h4,
h5,
h6,
p,
blockquote,
pre,
a,
abbr,
acronym,
address,
big,
cite,
code,
del,
dfn,
em,
img,
ins,
kbd,
q,
s,
samp,
small,
strike,
strong,
sub,
sup,
tt,
var,
b,
u,
i,
center,
dl,
dt,
dd,
ol,
ul,
li,
fieldset,
form,
label,
legend,
table,
caption,
tbody,
tfoot,
thead,
tr,
th,
td,
article,
aside,
canvas,
details,
embed,
figure,
figcaption,
footer,
header,
hgroup,
menu,
nav,
output,
ruby,
section,
summary,
time,
mark,
audio,
video {
    margin: 0;
    padding: 0;
    border: 0;
    font: inherit;
    font-size: 100%;
    vertical-align: baseline;
}
ol,
ul {
    list-style: none;
}
input,
button,
textarea,
select {
    -webkit-appearance: none;
    appearance: none;
    /* box-sizing: border-box; */
    border-radius: 0;
    padding: 0;
    margin: 0;
    border: none;
    outline: none;
}
table {
    border-collapse: collapse;
    border-spacing: 0;
}
caption,
th,
td {
    font-weight: normal;
    vertical-align: middle;
}
q,
blockquote {
    quotes: none;
}
q:before,
q:after,
blockquote:before,
blockquote:after {
    content: '';
    content: none;
}
a img {
    border: none;
}
article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
menu,
nav,
section,
summary {
    display: block;
}
a {
    text-decoration: none;
}
a:hover,
a:active {
    outline: none;
}
html {
    font-family: Helvetica, 'STHeiti STXihei', 'Microsoft JhengHei',
        'Microsoft YaHei', 'Noto Sans CJK SC', 'Source Han Sans CN' Tohoma,
        Arial;
    -webkit-text-size-adjust: 100%;
    -webkit-tap-highlight-color: transparent;
    height: 100%;
    overflow-x: hidden;
}
body {
    color: #333;
    background-color: #fff;
    -webkit-backface-visibility: hidden; /*line-height: 1;*/
    height: 100%;
} /*common*/
.gap {
    height: 18.1159rpx;
}
.mask {
    position: fixed;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.4);
    z-index: 1000;
    display: none;
} /*page*/
.header {
    background: #fff;
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    z-index: 999;
    height: 79.7101rpx;
}
.title {
    text-align: center;
    font-size: 30.1932rpx;
    color: #fff;
    line-height: 79.7101rpx;
}
.main {
    overflow-y: auto;
    overflow-x: hidden;
    -webkit-overflow-scrolling: touch;
    height: 100%;
}
.inner {
    padding: 79.7101rpx 0 90.5796rpx;
} /*------------*/
.header {
    background-color: #0faeff;
}
.ic-back {
    position: absolute;
    left: 28.9854rpx;
    top: 50%;
    transform: translateY(-50%);
    width: 21.7391rpx;
    height: 36.2319rpx;
    background: url(~@/static/freedom/images/ic-back.png) no-repeat;
    background-size: 100%;
}
.hd-span {
    position: absolute;
    right: 24.1545rpx;
    top: 50%;
    transform: translateY(-50%);
    font-size: 31.401rpx;
    color: #fff;
}
.zy-list1 {
    padding: 0 24.1545rpx;
    background-color: #fff;
}
.zy-list1 li {
    border-bottom: 1px solid #ddd;
    padding-top: 9.0579rpx;
}
.zy-til1 {
    padding-left: 48.3091rpx;
    font-size: 28.9854rpx;
    color: #333;
    line-height: 82.1255rpx;
}
.til1 {
    background: url(~@/static/freedom/images/til1.png) 0 center no-repeat;
    background-size: 28.9854rpx;
}
.til2 {
    background: url(~@/static/freedom/images/til2.png) 1.8116rpx 28.9854rpx
        no-repeat;
    background-size: 24.1545rpx;
}
.til3 {
    background: url(~@/static/freedom/images/til3.png) 0 center no-repeat;
    background-size: 28.9854rpx;
}
.til4 {
    background: url(~@/static/freedom/images/til4.png) 0 center no-repeat;
    background-size: 22.343rpx;
}
.til5 {
    background: url(~@/static/freedom/images/til5.png) 0 center no-repeat;
    background-size: 28.9854rpx;
}
.til6 {
    background: url(~@/static/freedom/images/til6.png) 0 center no-repeat;
    background-size: 25.3623rpx;
}
.til7 {
    background: url(~@/static/freedom/images/til7.png) 0 center no-repeat;
    background-size: 29.5893rpx;
}
.input-group {
    background-color: #f2f2f2;
    border-radius: 6.0386rpx;
    overflow: hidden;
}
.input-group .item {
    position: relative;
    border-bottom: 1px solid #ddd;
    height: 90.5796rpx;
}
.input-group .item input {
    width: 100%;
    height: 90.5796rpx;
    box-sizing: border-box;
    padding-left: 25.9662rpx;
    font-size: 27.7777rpx;
    color: #333;
    line-height: 90.5796rpx;
    background: none;
}
.input-group .item .clear {
    position: absolute;
    right: 74.2753rpx;
    top: 50%;
    transform: translateY(-50%);
    width: 30.1932rpx;
    height: 30.1932rpx;
    background: url(~@/static/freedom/images/ic-clear.png);
    background-size: 100%;
}
.input-group .item span {
    position: absolute;
    top: 0;
    right: 25.3623rpx;
    font-size: 27.7777rpx;
    color: #333;
    line-height: 90.5796rpx;
}
.zy-checkbox {
    display: flex;
}
.label-group .zy-checkbox {
    margin-bottom: 42.2705rpx;
}
.zy-checkbox input {
    display: none;
}
.zy-checkbox i {
    width: 30.1932rpx;
    height: 30.1932rpx;
    background: url(~@/static/freedom/images/zy-nocheck.png);
    background-size: 100%;
    margin-right: 18.1159rpx;
}
.zy-checkbox span {
    font-size: 27.7777rpx;
    color: #666;
    line-height: 27.7777rpx;
    flex: 1;
}
.zy-checkbox input:checked ~ i {
    background-image: url(~@/static/freedom/images/zy-checked.png);
}
.has-selected p {
    font-size: 27.7777rpx;
    color: #666;
    line-height: 27.7777rpx;
    margin-bottom: 42.2705rpx;
}
.zy-bot-btn {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 78.5024rpx;
    background-color: #0faeff;
}
.zy-bot-btn p {
    font-size: 31.401rpx;
    color: #fff;
    line-height: 78.5024rpx;
    text-align: center;
    letter-spacing: 6.0386rpx;
}
.zy-input1 {
    padding: 0 24.1545rpx;
    padding-top: 36.2319rpx;
    background-color: #fff;
}
.zy-input1 input {
    width: 100%;
    height: 72.4638rpx;
    box-sizing: border-box;
    padding-left: 61.5942rpx;
    font-size: 27.7777rpx;
    color: #333;
    line-height: 61.5942rpx;
    background: #eeeeee url(~@/static/freedom/images/zy-search1.png) 24.1545rpx
        center no-repeat;
    border-radius: 36.2319rpx;
    background-size: 22.9468rpx;
}
.zy-list2 {
    background-color: #fff;
    padding: 0 24.1545rpx;
}
.zy-list2 li {
    padding-top: 27.7777rpx;
}
.zy-checkbox .wenzi {
    flex: 1;
    border-bottom: 1px solid #ddd;
    padding-bottom: 24.1545rpx;
}
.zy-checkbox .wenzi p {
    font-size: 27.7777rpx;
    color: #666;
    line-height: 45.8937rpx;
}
.zy-checkbox .wenzi h3 {
    font-size: 28.9854rpx;
    color: #0faeff;
    line-height: 45.8937rpx;
    margin-bottom: 9.0579rpx;
}
.zy-checkbox2 i {
    margin-top: 7.8502rpx;
}
.zy-list2 li:last-child .zy-checkbox p {
    border: none;
}
.cailiang-alert {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    margin: auto;
    z-index: 1111;
    width: 653.3816rpx;
    height: 1095.4106rpx;
    box-sizing: border-box;
    background: url(~@/static/freedom/images/alert-bg.png) center top no-repeat;
    background-size: 100% auto;
    padding: 0 24.1545rpx;
    padding-top: 215.5797rpx;
}
.cailiang-alert h3 {
    font-size: 33.8164rpx;
    color: #333;
    line-height: 45.8937rpx;
    text-align: center;
}
.cailiang-alert .zy-close {
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 66.4251rpx;
    height: 66.4251rpx;
    background: url(~@/static/freedom/images/zy-close.png) no-repeat;
    background-size: 100%;
}
.zy-til2 {
    font-size: 28.9854rpx;
    color: #333;
    line-height: 47.1014rpx;
}
.zy-txt1 {
    font-size: 38.6473rpx;
    color: #ff993e;
    line-height: 47.1014rpx;
    padding-bottom: 24.1545rpx;
    border-bottom: 1px solid #ddd;
}
.zy-txt2 {
    background-color: #f2f2f2;
    border-radius: 6.0386rpx;
    font-size: 27.7777rpx;
    color: #666;
    line-height: 47.1014rpx;
    padding: 12.0773rpx 24.1545rpx;
}
.zy-btn1 {
    border: none;
    outline: none;
    display: block;
    margin: 0 auto;
    width: 398.5507rpx;
    height: 78.5024rpx;
    border-radius: 6.0386rpx;
    background-color: #0faeff;
    color: #fff;
    line-height: 78.5024rpx;
    font-size: 31.401rpx;
    text-align: center;
    letter-spacing: 6.0386rpx;
}
.zy-line {
    display: flex;
    justify-content: space-between;
}
.zy-list3 {
    background-color: #fff;
    padding: 0 24.1545rpx;
}
.zy-list3 > li {
    border-bottom: 1px solid #ddd;
}
.zy-list4 {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
}
.zy-list4 li {
    display: flex;
    justify-content: center;
    align-items: center;
    box-sizing: border-box;
    width: 163.0434rpx;
    height: 141.9082rpx;
    border: 1.2077rpx solid #f2f2f2;
    background-color: #f2f2f2;
    margin-bottom: 18.1159rpx;
    border-radius: 10px;
}
.zy-list4 li.cur {
    border: 1.2077rpx solid #0faeff;
    background: #fff url(~@/static/freedom/images/list4-cur.png) right bottom
        no-repeat;
    background-size: 36.8357rpx;
}
.zy-list4 li.cur p {
    color: #0faeff;
}
.zy-list4 li p {
    font-size: 26.57rpx;
    color: #333;
    line-height: 39.855rpx;
    text-align: center;
}
.zy-more {
    font-size: 27.7777rpx;
    color: #999;
    padding-right: 30.1932rpx;
    background: url(~@/static/freedom/images/arrow-right.png) right center
        no-repeat;
    background-size: 13.285rpx;
    line-height: 82.1255rpx;
}
.zy-selected {
    font-size: 27.7777rpx;
    color: #333;
    line-height: 45.8937rpx;
    background-color: #f2f2f2;
    border-radius: 6.0386rpx;
    padding: 12.0773rpx 24.1545rpx;
    margin-bottom: 18.1159rpx;
}
.zy-selected span {
    color: #0faeff;
    font-weight: bold;
    margin-right: 9.0579rpx;
}
.zy-fakuan {
    background-color: #f2f2f2;
    border-radius: 6.0386rpx;
    overflow: hidden;
}
.zy-fakuan .line1 {
    display: flex;
    border-bottom: 1px solid #ddd;
}
.zy-fakuan .line1 p {
    flex: 1;
    font-size: 27.7777rpx;
    color: #666;
    text-align: center;
    line-height: 89.3719rpx;
}
.zy-fakuan .line1 p:first-child {
    border-right: 1px solid #ddd;
}
.zy-fakuan .line2 {
    height: 89.3719rpx;
    position: relative;
}
.zy-fakuan .line2 input {
    width: 100%;
    height: 89.3719rpx;
    line-height: 89.3719rpx;
    font-size: 27.7777rpx;
    color: #666;
    text-align: center;
    background: none;
}
.zy-fakuan .line2 span {
    position: absolute;
    top: 0;
    right: 25.3623rpx;
    font-size: 27.7777rpx;
    color: #333;
    height: 89.3719rpx;
    line-height: 89.3719rpx;
}
.zy-txt3 {
    font-size: 27.7777rpx;
    color: #666;
    line-height: 45.8937rpx;
}
.zy-list5 {
    margin: 24.1545rpx;
}
.zy-list5 li {
    background: linear-gradient(#bae1fe, #fff 15%);
    border-radius: 12.0773rpx;
    overflow: hidden;
    padding: 0 24.1545rpx;
    margin-bottom: 18.1159rpx;
}
.zy-list5 li .top {
    background: url(~@/static/freedom/images/arrow-right-blue.png) right center
        no-repeat;
    background-size: 13.285rpx;
}
.zy-list5 li .top p {
    height: 84.541rpx;
    line-height: 84.541rpx;
    font-size: 31.401rpx;
    color: #0faeff;
    background: url(~@/static/freedom/images/til3-t1.png) 0 center no-repeat;
    background-size: 27.1739rpx;
    padding-left: 45.2898rpx;
    font-weight: bold;
}
.zy-til3 {
    text-align: center;
}
.zy-til3 p {
    display: inline-block;
    padding: 0 100.8453rpx;
    background: url(~@/static/freedom/images/p-left.png) left center no-repeat,
        url(~@/static/freedom/images/p-right.png) right center no-repeat;
    background-size: 83.9372rpx auto, 83.9372rpx auto;
    font-size: 28.9854rpx;
    color: #333;
    line-height: 72.4638rpx;
}
.zy-list5 li.li-green {
    background: linear-gradient(#b9efd4, #fff 20%);
}
.zy-list5 li.li-green .top {
    background: url(~@/static/freedom/images/arrow-right-green.png) right center
        no-repeat;
    background-size: 13.285rpx;
}
.zy-list5 li.li-green .top p {
    color: #07b860;
    background-image: url(~@/static/freedom/images/til3-t2.png);
    background-size: 28.9854rpx;
}
.zy-list5 li.li-green .zy-til3 p {
    background: url(~@/static/freedom/images/p-left2.png) left center no-repeat,
        url(~@/static/freedom/images/p-right2.png) right center no-repeat;
    background-size: 83.9372rpx auto, 83.9372rpx auto;
}
.zy-tu {
    text-align: center;
}
.gongshi p {
    width: 50%;
    font-size: 27.7777rpx;
    color: #666;
    line-height: 45.8937rpx;
}
