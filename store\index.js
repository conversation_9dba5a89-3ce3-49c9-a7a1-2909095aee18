import Vue from 'vue';
import Vuex from 'vuex';
import { updateFormData } from '@/pages/form/Form.js'

Vue.use(Vuex)

const store = new Vuex.Store({
	state: {
		token: null,
		/**
		 * 是否需要强制登录
		 */
		forcedLogin: false,
		hasLogin: false,
		userName: "",
		verifyList: [],
		historyList: [],
		mrzList: [],
		formData: null,
		tempBlobUrlSet: new Set()
	},
	
	mutations: {
		showLoading(state) {
			state.showLoading = true;
			uni.showLoading({
			    title: '加载中'
			});
		},
		hideLoading(state) {
			state.showLoading = false;
			uni.hideLoading({
			    title: '加载中'
			});
		},// todo
		errorMsg(state, error) {
			uni.showToast({
			    title: error.msg,
				icon: 'none',
			    duration: 2000
			});
		},
		login(state, userName) {
			state.userName = userName || '新用户';
			state.hasLogin = true;
		},
		
		logout(state) {
			state.userName = "";
			state.hasLogin = false;
		},

		getTemplateForm(state) {
			state.templateLoading = true;
			uni.showLoading({
			    title: '加载中'
			});
			setTimeout(()=>{
				state.templateLoading = false;
				uni.hideLoading()
			},500)
		},
		
		/**
		 * 重置表单数据。小程序因为不能通过子组件修改父组件传递的对象属性的方式修改表单数据，只能所以只能通过
		 * vuex来存储，注意formData在整个应用中只有一份，每进入一张表单都会重置，因此需要注意退出表单时保存
		 * 好数据
		 * @param {Object} state
		 * @param {Object} formData
		 */
		resetFormData(state, formData) {
			state.formData = formData || {}
		},
		
		/**
		 * 更新表单数据, 因为小程序的子组件无法更新父组件传的属性，只能通过vuex来穿透所有组件，进行数据的集中修改
		 * @param {Object} state
		 * @param {Object} data
		 */
		updateFormData(state, data) {
			updateFormData(state, data);
		},
		
		/**
		 * 注册临时文件地址
		 * @param {Object} state
		 * @param {Object} blobUrl
		 */
		registerTempBlob(state, blobUrl) {
			state.tempBlobUrlSet.add(blobUrl)
		},
		
		/**
		 * 判断是否已缓存指定地址
		 * @param {Object} state
		 * @param {Object} blobUrl
		 */
		existBlobUrl(state, blobUrl) {
			return state.tempBlobUrlSet.has(blobUrl)
		}
	}
})

export default store
