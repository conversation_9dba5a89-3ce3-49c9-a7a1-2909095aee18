/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */

/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
//  @import '@/components/uview-ui/theme.scss';

 /* 颜色变量 */

 /* 行为相关颜色 */
 @import '@/components/uview-ui/theme.scss';
/* 颜色变量 */
:root {
	/* 行为相关颜色 */  // TODO 动态表单样式调整，替换肤色
	--color-primary: #007aff;
	--color-success: #4cd964;
	--color-warning: #f0ad4e;
	--color-error: #dd524d;
	--color-primary-active: rgba(56, 115, 255, .5);	
}





/* 行为相关颜色 */
$uni-color-primary: var(--color-primary);
$uni-color-success: var(--color-success);
$uni-color-warning: var(--color-warning);
$uni-color-error: var(--color-error);
$uni-color-primary-active: var(--color-primary-active);

/* 文字基本颜色 */
$uni-text-color:#333;//基本色
$uni-text-color-inverse:#fff;//反色
$uni-text-color-grey:#999;//辅助灰色，如加载更多的提示信息
$uni-text-color-placeholder: #808080;
$uni-text-color-disable: #c0c0c0;

/* 背景颜色 */
$uni-bg-color:#ffffff;
$uni-bg-color-grey:#f8f8f8;
$uni-bg-color-hover:#f1f1f1;//点击状态颜色
$uni-bg-color-mask:rgba(0, 0, 0, 0.4);//遮罩颜色

/* 边框颜色 */
$uni-border-color:#c8c7cc;

/* 尺寸变量 */

/* 文字尺寸 */
$uni-font-size-sm:28upx;
$uni-font-size-base:32upx;
$uni-font-size-lg:35upx;

/* 图片尺寸 */
$uni-img-size-sm:40upx;
$uni-img-size-base:52upx;
$uni-img-size-lg:80upx;

/* Border Radius */
$uni-border-radius-sm: 4upx;
$uni-border-radius-base: 6upx;
$uni-border-radius-lg: 12upx;
$uni-border-radius-circle: 50%;

/* 水平间距 */
$uni-spacing-row-sm: 10px;
$uni-spacing-row-base: 20upx;
$uni-spacing-row-lg: 30upx;

/* 垂直间距 */
$uni-spacing-col-sm: 8upx;
$uni-spacing-col-base: 16upx;
$uni-spacing-col-lg: 22upx;

/* 透明度 */
$uni-opacity-disabled: 0.3; // 组件禁用态的透明度

/* 文章场景相关 */
$uni-color-title: #2C405A; // 文章标题颜色
$uni-font-size-title:40upx;
$uni-color-subtitle: #555555; // 二级标题颜色
$uni-font-size-subtitle:36upx;
$uni-color-paragraph: #3F536E; // 文章段落颜色
$uni-font-size-paragraph:30upx;

$form-color-primary: var(--color-primary); //主题色，尽量跟$uni-color-primary保持一致
$form-color-primary-active: var(--color-primary-active); //按钮点击显示的颜色
$form-color-required: brown; //元素必填项星号的颜色
$form-color-disable: #999; //元素禁时字体颜色
$form-color-explain: #e35555; //元素说明字体颜色
$form-color-label: #999;
$form-color-label-disable: #999;
$form-font-size-primary: 32rpx;
$form-card-title-size: 18px;
$form-card-title-weight: 500;

$form-attach-name-size: 32rpx;

.detail-region .form-element__disable{
	color: #3d3d3d!important;
}

.detail-region .form__require-symbol{
	display: none;
}
// 动态表单修改开始
uni-view {
    line-height: inherit;
    font-size: 30rpx;
}

.form-element__label-bar {
    margin-top: 12rpx !important;
    margin-bottom: 10rpx !important;
}

uni-radio-group uni-label,
uni-checkbox-group uni-label {
    padding-bottom: 12rpx !important;
    padding-right: 20px !important;
}

uni-radio {
    position: relative !important;
    left: -4px !important;
    bottom: -2px !important;
}

uni-checkbox {
    position: relative !important;
    left: -4px !important;
    bottom: 1px !important;
}

uni-radio .uni-radio-input-checked{
	background-color: $form-color-primary!important;
	border-color: $form-color-primary!important;
}


// 动态表单修改结束

 @import './node_modules/bowo-form/static/css/form.scss';
 @import  './node_modules/bowo-form/static/css/button.scss'