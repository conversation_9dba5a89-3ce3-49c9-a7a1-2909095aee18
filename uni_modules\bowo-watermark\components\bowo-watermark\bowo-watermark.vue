<template>
    <view class="bowo-watermark">
        <canvas 
            :id="canvasId"
            :canvas-id="canvasId"
            :style="canvasDimens"
        />
    </view>
</template>

<script>
    import watermark from './watermark.js'
    
    const DIMEN_BASE_WIDTH = 1365
    const DIMEN_BASE_HEIGHT = 2208
    const FONT_SIZE_BASE_WIDTH = '455'
    
    export default {
        name: 'BowoWatermark',
        props: {
            canvasId: {
                type: String,
                default: 'watermark'
            },
            
            color: {
                type: String,
                default: '#ff0000'
            },
            
            fontSize: {
                type: Number,
                default: 10
            }
        },
        
        data() {
            return {
                originalDimens: [600, 1200],
                dimens: [600, 1200],
                canvasContext: null,
            }
        },
        
        computed: {
            canvasDimens: function() {
                return `width: ${this.dimens[0]}px; height: ${this.dimens[1]}px`
            }
        },
        
        mounted() {
            this.canvasContext = uni.createCanvasContext(this.canvasId, this)
        },
        
        methods: {
            /**
             * 在图片上绘制水印
             * @param {String} imageUrl
             */
            paintWatermark(imageUrl, multiLineTexts, fileName) {
                if((imageUrl || null) === null) {
                    throw '加水印原图地址不能为空'
                }
                
                if(this.canvasContext === null) {
                    throw 'canvasContext未正确初始化'
                }
                let taskParams = {
                    imageUrl,
                    multiLineTexts,
                    fileName
                }
                
                return this.resolveImageDimens(taskParams)
                    .then(this.drawImageAndWatermark)
                    .then(this.exportImage)
            },
            
            /**
             * 确定原图的尺寸
             * @param {Object} taskParams
             */
            resolveImageDimens(taskParams) {
                return new Promise((resolve, reject) => {
                    watermark.resolveImageDimens(taskParams.imageUrl)
                        .then(dimens => {
                            this.originalDimens = dimens
                            this.dimens = watermark.shouldScaleImage() ? this.scaleDimens(dimens) : dimens
                            //canvas尺寸变更，会进行重绘，需要延迟一段时间canvas尺寸重绘完成
                            setTimeout(() => {
                                resolve(taskParams)
                            }, 150)
                        })
                        .catch(error => {
                            console.log(`解析图片尺寸出错：${error}`)
                            reject(error)
                        })
                })
            },
            
            /**
             * 绘制原图并在原图上绘制水印文字
             * @param {Object} taskParams
             */
            drawImageAndWatermark(taskParams) {
                return new Promise((resolve, reject) => {
                    let canvas = this.canvasContext
                    if(watermark.shouldScaleImage()) {
                        canvas.drawImage(taskParams.imageUrl, 0, 0, this.originalDimens[0], this.originalDimens[1], 0, 0, this.dimens[0], this.dimens[1])
                    } else {
                        canvas.drawImage(taskParams.imageUrl, 0, 0)
                    }
                    this.drawWatermark(canvas, taskParams.multiLineTexts)
                    canvas.draw(false, () => {
                        resolve(taskParams)
                    })
                })
            },
            
            drawWatermark(canvas, multiLineTexts) {
                if(multiLineTexts.length === 0) {
                    return
                }
                
                canvas.fillStyle = this.color
                let fontSize = this.calculateAdapterFontSize()
                canvas.setFontSize(fontSize)
                let lineMargin = this.calculateLineMargin()
                
                let startX = this.calculateStartX()
                let startY = this.dimens[1] - this.calculateStartY()
                let index = multiLineTexts.length - 1
                for(index; index >=0; index--) {
                    let lineText = multiLineTexts[index]
                    canvas.fillText(lineText, startX, startY)
                    startY -= fontSize + lineMargin
                }
            },
            
            /**
             * 导出带水印的图片
             */
            exportImage(taskParams) {
                let { imageUrl, fileName } = taskParams
                let imageFileName = this.parseFileName(imageUrl, fileName)
                let suffix = this.resolveImageSuffix(imageFileName)
                let width = this.dimens[0]
                let height = this.dimens[1]
                return new Promise((resolve, reject) => {
                    uni.canvasToTempFilePath({
                        canvasId: this.canvasId,
                        x: 0,
                        y: 0,
                        width,
                        height,
                        destWidth: width,
                        destHeight: height,
                        fileType: suffix,
                        success(resp) { 
                            let pathOrBase64 = resp.tempFilePath
                            //h5为base64
                            // #ifdef H5
                            let blobFile = watermark.base64ToBlobFile(resp.tempFilePath, imageFileName)
                            resolve({
                                path: window.URL.createObjectURL(blobFile),
                                file: blobFile
                            })
                            // #endif
                            
                            // #ifndef H5
                            resolve({
                                path: pathOrBase64
                            })
                            // #endif
                        },
                        fail(error) {
                            reject(error)
                        }
                    }, this)
                })
            },
            
            calculateStartX() {
                return this.adaptePixleToImageDimen(10)
            },
            
            calculateStartY() {
                return this.adaptePixleToImageDimen(10)
            },
            
            calculateAdapterFontSize() {
                return this.adaptePixleToImageDimen(this.fontSize)
            },
            
            /**
             * 导出行间距
             */
            calculateLineMargin() {
                return this.adaptePixleToImageDimen(5)
            },
            
            adaptePixleToImageDimen(pixle) {
                let adaptedPixle = pixle * this.dimens[0] / FONT_SIZE_BASE_WIDTH
                let greaterAdaptedPixle = Math.floor(adaptedPixle)
                return greaterAdaptedPixle % 2 === 0 ? greaterAdaptedPixle : Math.ceil(adaptedPixle)
            },
            
            parseFileName(imageUrl, fileName) {
                if(fileName) {
                    return fileName
                }
                let lastSlashIndex = imageUrl.lastIndexOf('/')
                if(lastSlashIndex !== -1) {
                    return imageUrl.substring(lastSlashIndex + 1)
                }
                return null
            },
            
            resolveImageSuffix(fileName) {
                let notNullFileName = fileName || ''
                let lastDotIndex = notNullFileName.lastIndexOf('.')
                if(lastDotIndex !== -1) {
                    return notNullFileName.substring(lastDotIndex + 1)
                }
                return 'png'
            },
            
            scaleDimens(dimens) {
                let width = Math.min(dimens[0], dimens[1])
                let height = Math.max(dimens[0], dimens[1])
                let widthRatio = width / DIMEN_BASE_WIDTH
                let heghtRatio = height / DIMEN_BASE_HEIGHT
                if(widthRatio <=1 && heghtRatio <= 1) {
                    return dimens
                }
                
                let effectiveRatio = Math.min(widthRatio, heghtRatio)
                let scaleWidth = Math.floor(width / effectiveRatio)
                let scaleHeight = Math.floor(height / effectiveRatio)
                return [scaleWidth, scaleHeight]
            }
        }
    }
</script>

<style scoped>
    .bowo-watermark {
        width: 0;
        height: 0;
        overflow: hidden;
    }
</style>
