import {
	btoa
} from 'js-base64';
module.exports = {
	data() {
		let token = uni.getStorageSync('token');

		// console.log(uni.getStorageSync('record-data'));

		let webData = {
			code: 'sdzf-pollution-information-assistant',
			token: btoa(token),
			question: uni.getStorageSync('record-data').RWBT
		}

		webData = JSON.stringify(webData);

		console.log('http://117.73.252.129:8001/h5/#/views/bigmodel/helper-page?data=' + webData, 'webData')

		return {
			webSrc: `http://117.73.252.129:8001/h5/#/views/bigmodel/helper-page?data=${webData}`
			// webSrc: 'http://172.16.30.184:8088/#/views/bigmodel/helper-page?data=%7B%22token%22%3A%22aVppVHNBQjgyZGdhWVBJZWs2bEFTZ1VUd0VBQW9lbEFKL1ZNaXk4UjhsODRzb3NKTzRJaE0wS0hWOUw1NWJBRU51aWhiK2tVbEtqZ1lvd1JNem1nVEFXNFBSaXFnZy8wb1IwWlhVaGNWSCtwcit5L1R6RUZhMkR1M2JNZlI0VFc2QmFoNHVVTEs4MzFWUHhmcXFpY1FhblhJdGUzRkh0WDJIYXNwWlF6QzRsdXppR3I0RE0za2hKUVFYQmtZb01uWnp2eUM1bGZ5TzJaVzM2N3E0OHIyaStZVzhpR2U0OEcrWDJOWkE3dUtFRTV3UzEvOGdzc0tURDVMbTVvVlg1cnN5TnVDMnk5ck5kZE50aEJ5czZDUjFYa0RzRTZBdUMvNTh6UjRFS0cyZjFZOTUvQmFycDAwSzMzVnBMTUVTVnp6eEJMYzhqR1UxUnB4QXR4L0NPV3lyUEtSQmlLTmlPZ2UzVGpqTFhVeFAyTm14ZjFOM3NrSkV2eWdBcnRmdDlxYm90eEo1S0lnNDA4RE9OM01CRGJHNlgzK2V2UFc4cTEzSVFHRllBYzczcDUrMnJjMXQxSTJEc2FVNTBmOFN1QnN5TnVDMnk5ck5kZE50aEJ5czZDUnd4bjdTZE52VWdtZFFYUWdSYk1lSzZFVVBpWnlKY2lmQ2RHUGRSamllYlJOMUc3anNaUG41c1FBQ1c4ZUJ5Z0RsKzBEemNTTTVXRQ%3D%3D%22,%22question%22%3A%223334343%22%7D'
		}
	},
	mounted() {
		// #ifdef APP-PLUS
		this.setHeight();
		// #endif
	},
	onShow() {
		// #ifdef APP-PLUS
		uni.onWindowResize((res) => {
			console.log(res);
			setTimeout(() => {
				this.resizeHeight(res.size.windowHeight);
			}, 100)
		})
		// #endif
	},
	watch: {
		showAi: 'setHeight'
	},
	methods: {
		resizeHeight(height) {
			// #ifdef APP-PLUS
			let currentWebview = this.$scope.$getAppWebview(); //获取当前web-view
			let wv = currentWebview.children()[0];
			if (!this.showAi) {
				wv.setStyle({
					//设置web-view距离顶部的距离以及自己的高度，单位为px
					top: uni.upx2px(1233250), //此处是距离顶部的高度，应该是你页面的头部
					height: height - uni.upx2px(280)
				});
			} else {
				wv.setStyle({
					//设置web-view距离顶部的距离以及自己的高度，单位为px
					top: uni.upx2px(280), //此处是距离顶部的高度，应该是你页面的头部
					height: height - uni.upx2px(280)
				});
			}
			// #endif
		},

		setHeight() {
			let height = 0; //定义动态的高度变量，如高度为定值，可以直接写
			uni.getSystemInfo({
				success: (sysinfo) => {
					height = sysinfo.windowHeight; //自行修改，自己需要的高度 此处如底部有其他内容，可以直接---(-50)这种
					this.resizeHeight(height)
				},
				complete: () => {}
			});
		}
	}
}