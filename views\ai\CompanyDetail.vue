<template>
	<body class="yy-darkbg" style="height: 100vh; overflow: auto;">


		<AiFab v-show="!showAi" :title="'点击查看更多企业历史检查情况！'" :isDock="true" :existTabBar="true" :isOpenLayer="true"
			@openLayer="showAi = true;">
		</AiFab>

		<div class="bg1" style=" width: 100vw;">
			<div class="page-header">
				<i class="ic-back" @click="backEvent()"></i>
				<h1 class="c-title">{{recordData.RWBT || '-'}}</h1>
			</div>
			<scroll-view style="height: calc(100vh - 140rpx);" scroll-y="true">
				<div class="yy-pd35">
				<div class="yy0723-infobox1">
					<p class="p1">{{pollutionInfo.KSSJ}} ~ {{pollutionInfo.JSSJ}}</p>
					<div class="gap50"></div>
					<ul class="flx1 ac yy0723-jcul1">
						<li>
							<h1>{{pollutionInfo.ZFJCNUM }}</h1>
							<h2>执法检查</h2>
						</li>
						<li>
							<h1>{{pollutionInfo.FXWTNUM }}</h1>
							<h2>发现问题</h2>
						</li>
					</ul>
					<div class="gap50"></div>
					<p class="flx1 ac p2"> <img src="images/yy0723-pos1.png" alt="" class="yy0723-pos1">
						{{pollutionInfo.WRYDZ || '-'}}
					</p>
				</div>
				<div class="gap"></div>
				<div class="yy0723-gsmod">
					<h1>检查主体</h1>
					<div class="gap60"></div>
					<ul class="flx1 ac ja yy0723-jcztul">
						<!-- <li>
							<p class="p1">6</p>
							<p class="p2">部级</p>
						</li> -->
						<li @click="openJumpCity('ST', checkData.ST)">
							<p class="p1">{{checkData.ST}}</p>
							<p class="p2">省级</p>
						</li>
						<li @click="openJumpCity('DS', checkData.DS)">
							<p class="p1">{{checkData.DS,}}</p>
							<p class="p2">市级</p>
						</li>
						<li @click="openJumpCity('QX', checkData.QX)">
							<p class="p1">{{checkData.QX}}</p>
							<p class="p2">区县</p>
						</li>
					</ul>
				</div>
				<div class="gap"></div>
				<div class="yy0723-gsmod">
					<h1>任务来源</h1>
					<div class="gap60"></div>
					<ul class="flx1 ac ja yy0723-jcztul">
						<li v-for="item in resourceData">
							<p class="p1">{{item.RWLXNUM}}</p>
							<p class="p2">{{item.RWLXMC}}</p>
						</li>
						<!-- <li>
							<p class="p1">6</p>
							<p class="p2">日常随机</p>
						</li>
						<li>
							<p class="p1">6</p>
							<p class="p2">信访及舆情</p>
						</li>
						<li>
							<p class="p1">6</p>
							<p class="p2">临时检查</p>
						</li> -->
					</ul>
				</div>
				<div class="gap"></div>
				<div class="yy0723-gsmod">
					<h1>问题类型</h1>
					<div class="gap60"></div>
					<div class="txtMarquee-top1">
						<div class="bd">
							<div class="box">
								<ul class="yy0729-wttypeul">
									<li v-for="(item, index) in issueType" :key="index" @click="openJumpType(item)">
										<!-- <img src="images/yy0723-top1.png" alt="" class="yy0723-rank"> -->
										<i :class='`num n${index + 1}`'>{{ index + 1 }}</i>
										<div class="flowbox">
											<div class="flx1 ac jb top">
												<span class="f1">{{ item[0] || '-' }}</span>
												<span class="f2">{{ item[1] || '0' }}</span>
											</div>
											<p class="bar"><b :style="`width: ${(item[1] / issueTypeTotal) * 100}%;`"></b></p>
										</div>
									</li>
								</ul>
							</div>
						</div>
					</div>
				</div>
				<div class="gap"></div>
				<div class="yy0723-gsmod">
					<div class="flx1 ac jb">
						<h1>问题记录</h1>
						<!-- <p class="yy-more">查看更多 <img src="images/yy0723-rtarr.png" alt="" class="yy0723-rtarr"></p> -->
					</div>

					<div class="gap60"></div>
					<scroll-view style="height: 600rpx;" class="record-task-list" scroll-y="true"
						@scrolltolower="onReachBottomEvent" v-if="problemList.length > 0">

						<ul class="yy0723-dstep">
							<li class="on" v-for="item in problemList" @click="goDetail(item)">
								<img src="images/yy0723-rtarr.png" alt="" class="yy0723-rtarr">
								<div class="flx1 ac yy0204-row">
									<p class="time1">{{item.JSSJ}} </p>
									<b class="zxbtn">{{item.RWLXMC}}</b>
								</div>
								<p class="p1">{{item.BMMC}}</em> {{item.JCRMC}}</p>
								<div class="stepcont">
									<p class="p2" v-html="item.AISBMSG"></p>
									<!-- <p class="p2">排放口未纳入许可证或与许可证记录不一致</p> -->
									<!-- {{item.AISBMSG}}
								
									 -->
								</div>
							</li>
							<!-- <li class="cur">
								<img src="images/yy0723-rtarr.png" alt="" class="yy0723-rtarr">
								<div class="flx1 ac yy0204-row">
									<p class="time1">2024-03-20 </p>
									<b class="zxbtn">专项行动</b>
								</div>
								<p class="p1">滨州市<em style="color:rgba(63, 193, 97, 1);">滨城分局</em> 王帅、刘瑞台</p>
								<div class="stepcont">
									<p class="p2">自动监控设备不正常运行</p>
									<p class="p2">排放口未纳入许可证或与许可证记录不一致</p>
								</div>
							</li>
							<li>
								<img src="images/yy0723-rtarr.png" alt="" class="yy0723-rtarr">
								<div class="flx1 ac yy0204-row">
									<p class="time1">2024-03-20 </p>
									<b class="zxbtn">专项行动</b>
								</div>
								<p class="p1">滨州市<em style="color:rgba(63, 193, 97, 1);">滨城分局</em> 王帅、刘瑞台</p>
								<div class="stepcont">
									<p class="p2">自动监控设备不正常运行</p>
									<p class="p2">排放口未纳入许可证或与许可证记录不一致</p>
								</div>
							</li> -->

						</ul>
					</scroll-view>
					<!-- <NoData v-if="problemList.length == 0"></NoData> -->
				</div>
				<div class="gap60"></div>
				<!-- <div class="gap60"></div>
				<div class="yy0723-footrobot" @click="showAi = true;">
					<img src="images/yy0723-robot1.png" alt="" class="yy0723-robot1">
					<image src="./images/robot.png" alt="" class="yy0723-robot1"></image>
					<p class="p1">快来看看该企业频繁超标情况吧！</p>
				</div>
				<div class="gap"></div> -->
			</div>
			</scroll-view>
		</div>


		<!-- <AILayer></AILayer> -->

		<div class="mask" style="display: block" v-show="showAi"></div>
		<div class="zy0315-fujian-alert yy0723-fjalert2" v-show="showAi">
			<i class="close" style="z-index: 1000;" @click="showAi = false"></i>
			<div class="yy0723-titbox">
				<h1 class="til1">企业信息助手</h1>
				<image src="images/yy0723-qh.png" alt="" class="yy0723-qh"></image>
			</div>
			<div class="yy0723-msgcont" style="width: 100%; height: calc(100vh - 290rpx);">
				<!-- #ifdef H5 -->
				<iframe :src="webSrc" style="width: 100%; height: calc(100vh - 290rpx);"></iframe>
				<!-- #endif -->
				<!-- #ifdef APP-PLUS -->
				<web-view :src="webSrc"></web-view>
				<!-- #endif -->
			</div>
		</div>

		<!-- <Info></Info> -->

	</body>
</template>

<script>
	import AILayer from './AILayer.js';
	import Info from './components/Info.vue';

	import {
		QUERY_YDZFAI_WRYJBXX,
		QUERY_YDZFAI_JCZT,
		QUERY_YDZFAI_RWLY,
		QUERY_YDZFAI_WTJL,
		QUERY_YDZFAI_WTLB,
		QUERY_ZFZT_WTJL,
		QUERY_YDZFAI_ZFJCJL
	} from '@/api/ai.js';
	export default {
		mixins: [AILayer],
		components: {
			Info
		},
		data() {
			return {
				recordData: uni.getStorageSync('record-data'),
				pollutionInfo: {}, // 污染源基本信息
				checkData: {}, // 检查主题
				issueType: [], // 问题类型
				issueTypeTotal: 0, // 问题类型总数
				resourceData: [], // 来源
				problemList: [],
				showAi: false,
				pageNum: 1,
				pageSize: 10
			}
		},
		created() {
			this.getData();
		},
		methods: {
			getData() {
				QUERY_YDZFAI_WRYJBXX({
					WRYBH: this.recordData.WRYBH
				}).then(res => {
					this.pollutionInfo = res.data_json;
				})

				QUERY_YDZFAI_JCZT({
					WRYBH: this.recordData.WRYBH
				}).then(res => {
					this.checkData = res.data_json;
				})

				QUERY_YDZFAI_RWLY({
					WRYBH: this.recordData.WRYBH
				}).then(res => {
					this.resourceData = res.data_json;
				})
				console.log(this.recordData);
				// 获取问题类型
				QUERY_YDZFAI_WTLB({
					WRYBH: this.recordData.WRYBH,
					WRYMC: this.recordData.RWBT
				}).then(res => {
					this.issueTypeTotal = 0;
					this.issueType = Object.entries(res.data_json || {}).sort((a, b) => b[1] - a[1])
					this.issueType.map(e=>{
						this.issueTypeTotal += e[1];
					})
				})

				// 获取问题记录列表
				this.getProbleList();
			},

			getProbleList() {
				QUERY_YDZFAI_WTJL({
					WRYBH: this.recordData.WRYBH,
					pageNum: this.pageNum,
					pageSize: this.pageSize
				}).then(res => {
					if (this.pageNum > res.data_json.pages) {
						uni.showToast({
							icon: 'none',
							title: '到底了！',
							duration: 2000
						})
					} else {
						this.problemList.push(...res.data_json.list);
					}
				})
			},

			onReachBottomEvent() {
				this.pageNum++;
				this.getProbleList();
			},

			backEvent() {
				uni.navigateBack();
			},

			openJumpCity(type, num) {
				uni.navigateTo({
					url: '/views/ai/ProblemList?CITY=' + type
				})
			},

			openJumpType(item) {
				// console.log(item);
				// 只有一条记录的时候直接跳到执法归档页面
				if(item[1] == 1) {
					QUERY_YDZFAI_ZFJCJL({
						WRYBH: this.recordData.WRYBH,
						WTDL: item[0],
					}).then(res=>{
						uni.setStorageSync('ai-finish-data', res.data_json[0]);
						uni.navigateTo({
							url: '/views/ai/Finish'
						})
					})
				} else {
					uni.navigateTo({
						url: '/views/ai/ProblemList?WTDL=' + item[0]
					})
				}
			},

			goDetail(item) {
				uni.setStorageSync('ai-finish-data', item);

				uni.navigateTo({
					url: '/views/ai/Finish'
				})
			}
		}
	}
</script>

<style>
@import "./css/reset.css";
@import "./css/zy0303.css";
@import "./css/style.css";

.zy0315-fujian-alert.yy0723-fjalert2 {
  height: calc(100vh - 80px);
}

.til1 {
  background: none;
}
</style>