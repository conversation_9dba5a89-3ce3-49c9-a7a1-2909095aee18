<template>
	<div class="zy0723-page yy-darkbg" style="width: 100vw;">
		<!-- 	<header class="blue-header" style="background: none">
			<i class="backbtn"></i>
			<h1 class="c-title" style="font-weight: bold">执法归档</h1>
		</header> -->

		<div class="page-header">
			<i class="ic-back" @click="backEvent()"></i>
			<h1 class="c-title">执法归档</h1>
		</div>

		<section class="main">
			<div class="inner" style="height: calc(100vh - 140rpx); overflow: auto;">
				<div class="gap"></div>
				<div class="zy0723-data1">
					<image :src="peopleIcon" style="width: 133rpx;height: 133rpx; position: relative; z-index: 10000;" mode="aspectFit">
					</image>
					<div class="info">
						<p class="p1">{{resData.WRYMC}}</p>
						<p class="p2">{{resData.JCRMC}}</p>
						<p class="p3">{{resData.JSSJ}}</p>
					</div>
					<p class="type" v-if="resData.SFSXHJWFWT == '1'" style="white-space: nowrap;">涉嫌环境违法问题</p>
				</div>
				<div class="gap"></div>
				<div class="zy0723-box1">
					<p class="zy0723-til1">现场检查结论</p>
					<div class="gap"></div>
					<div class="zy0723-data2">
						<div class="txt">
							{{resData.XCJCJL}}
						</div>
						<div class="jieda">
							<div class="q1">疑似问题识别是否准确？</div>
							<div class="a1" v-html="resData.AISBMSG"></div>
							<!-- <div class="a1">
								排放口未纳入许可证或与许可证记录不一致
							</div> -->
						</div>
						<!-- <div class="pingjia">
							<div class="item agree">准确</div>
							<div class="item oppose">错误</div>
						</div> -->
					</div>
				</div>
				<div class="gap"></div>
				<div class="zy0723-box1">
					<p class="zy0723-til1">执法笔录</p>
					<div class="gap"></div>
					<div class="zy0723-data3" style="display: block; overflow: auto;">
						<template v-for="item in recordList">
						<div class="item" 
							style="margin-right: 1%; float: left;"
							v-if="item.mbbh !== '202011161722220c3f488fabc7497d90931b5f904b9109'"
							@click="previewRecord(item)"
						>
							<text
								style="color: black; font-size: 22rpx; position: absolute;top: 15rpx; width: 80%; text-align: center;z-index: 100;">
								{{item.mbmc}}
							</text>
							<image mode="widthFix" :src="iconSubmitted" alt="" class="file"></image>
						</div>
						</template>
						

						<NoData v-if="recordList.length == 0" :boxHeight="'400rpx'" />
					</div>
				</div>
				<div class="gap"></div>
				<div class="zy0723-box1">
					<p class="zy0723-til1">执法证据</p>
					<div class="gap"></div>
					<div class="zy0723-data4" style="display: block; overflow: auto;">
						<!-- <image src="@/views/ai/images/zy0723_d4_img1.png" class="img"></image>
						<image src="@/views/ai/images/zy0723_d4_img2.png" class="img"></image>
						<image src="@/views/ai/images/zy0723_d4_img3.png" class="img"></image>
						<image src="@/views/ai/images/zy0723_d4_img1.png" class="img"></image>
						<image src="@/views/ai/images/zy0723_d4_img2.png" class="img"></image>
						<image src="@/views/ai/images/zy0723_d4_img3.png" class="img"></image> -->
						<!-- <image src="@/views/ai/images/zy0723_d4_img1.png" class="img"></image> -->


						<image style="width: 162rpx; height: 162rpx;" v-for="item in picArr" mode="widthFix"
							:src="DOWNLOAD_URL+'?wdbh='+item.WDBH" alt="" class="img"
							@click="onPreviewAttach(DOWNLOAD_URL+'?wdbh='+item.WDBH)">
						</image>
						<NoData :boxHeight="'400rpx'" v-if="picArr.length == 0" />
					</div>
				</div>
			</div>
		</section>
	</div>

</template>

<script>
	import {
		QUERY_BL_LIST,
		QUERY_DYNAMICFORM_DATA
	} from '@/api/ai.js';

	import {
		MODE_MODIFY,
		EVENT_PREVIEW_RECORD
	} from '@/api/record.js';
	import iconSubmitted from '@/static/img/record/form_submitted.png';

	import printTemplate from '@/api/print-template.js';

	import {
		transformObjectToUrlParams
	} from '@/common/net/uri.js'

	import {
		DOWNLOAD_URL
	} from '@/common/config.js';

	import peopleIcon from '@/views/ai/images/zy0723_d1_img1.png';

	export default {
		data() {
			return {
				peopleIcon,
				DOWNLOAD_URL,
				iconSubmitted,
				resData: uni.getStorageSync('ai-finish-data'),
				recordList: [],
				picArr: []
			}
		},
		mounted() {
			console.log(this.resData);
			if(this.resData.YDZFXH) {
				this.getData();
			}
		},
		methods: {

			onPreviewAttach(url) {
				////console.log(url);
				uni.previewImage({
					urls: [url]
				})
			},

			previewRecord(item) {
				console.log(item);
				
				let recordData = uni.getStorageSync('record-data');

				let _self = this;
				uni.setStorageSync('router-KCSHT', item.mbbh);
				uni.showLoading({
					title: '加载表单中'
				});
				let type = true;
				let data = [];
				let list = {};
				list.record_id = item.recordId;
				list.template_id = item.mbbh;
				data.push(list);
				printTemplate
					.getPrintUrl(data, type)
					.then(result => {
						//console.log('goDetail');

						uni.setStorageSync('printURL', result);
						let params = {
							isOnlyRead: 1,
							type: MODE_MODIFY,
							id: item.mbbh,
							wrybh: recordData.WRYBH,
							recordId: item.recordId,
							title: item.mbmc
						};
						let urlParams = transformObjectToUrlParams(params);
						uni.hideLoading();
						// _self.$emit(EVENT_PREVIEW_RECORD);
						//console.log('go')
						uni.navigateTo({
							url: `/pages/record/record-form-previewer?${urlParams}`
						});
					})
					.catch(e => {
						if (e.error_msg === '没有找到模板') {
							let params = {
								type: MODE_MODIFY,
								id: item.mbbh,
								wrybh: _self.wrybh,
								recordId: item.recordId,
								backStep: 1
							};
							uni.navigateTo({
								url: `/pages/record/record-form-editor?${uriUtil.transformObjectToUrlParams(
			                        params
			                    )}`
							});
						}
					});
			},
			getData() {
				QUERY_BL_LIST({
					xh: this.resData.YDZFXH
				}).then(res => {
					// console.log(res)
					this.recordList = res.data_json;
				})

				if(this.resData.ZJSJXH) {
					QUERY_DYNAMICFORM_DATA({
						recordId: this.resData.ZJSJXH
					}).then(res => {
						// console.log(res)
						this.picArr = res.data_fjxx;
						// this.recordList = res.data_json;
					})
				}
			},
			backEvent() {
				uni.navigateBack();
			}
		}
	}
</script>

<style>
	@import './css/reset.css';
	@import './css/zy0303.css';
	@import './css/style.css';
	@import './css/zy0723.css';
</style>