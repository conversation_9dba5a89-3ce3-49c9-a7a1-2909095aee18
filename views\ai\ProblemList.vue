<template>
	<body class="yy-darkbg" style="height: 100vh; overflow: auto;">


		<!-- <AiFab v-show="!showAi" :title="'点击查看更多企业历史检查情况！'" :isDock="true" :existTabBar="true" :isOpenLayer="true"
			@openLayer="showAi = true;">
		</AiFab> -->

		<div class="bg1" style=" width: 100vw;">
			<div class="page-header">
				<i class="ic-back" @click="backEvent()"></i>
				<h1 class="c-title">{{recordData.RWBT || '-'}}</h1>
			</div>
			<div class="yy-pd35" style="height: calc(100vh - 140rpx); overflow: auto;">
				<div class="gap"></div>
				<div class="yy0723-gsmod">
					<div class="flx1 ac jb">
						<h1>问题记录</h1>
					</div>

					<div class="gap60"></div>
					<scroll-view style="height: calc(100vh - 280rpx);" class="record-task-list" scroll-y="true"
						@scrolltolower="onReachBottomEvent" v-if="problemList.length > 0">
						<ul class="yy0723-dstep">
							<li class="on" v-for="item in problemList" @click="goDetail(item)">
								<img src="images/yy0723-rtarr.png" alt="" class="yy0723-rtarr">
								<div class="flx1 ac yy0204-row">
									<p class="time1">{{item.JSSJ}} </p>
									<b class="zxbtn" v-show="item.RWLXMC">{{item.RWLXMC}}</b>
								</div>
								<p class="p1">{{item.BMMC}}</em> {{item.JCRMC}}</p>
								<div class="stepcont">
									<p class="p2" v-html="item.AISBMSG"></p>
									<!-- <p class="p2">排放口未纳入许可证或与许可证记录不一致</p> -->
									<!-- {{item.AISBMSG}}
								
									 -->
								</div>
							</li>
						</ul>
					</scroll-view>
					<NoData v-if="problemList.length == 0"></NoData>
				</div>
				<div class="gap60"></div>
			</div>
		</div>
	</body>
</template>

<script>


	import {
		QUERY_YDZFAI_ZFJCJL,
		QUERY_ZFZT_WTJL
	} from '@/api/ai.js';
import { option } from '../../uni_modules/qiun-data-charts/js_sdk/u-charts/config-ucharts';
	export default {
		mixins: [],
		components: {},
		data() {
			return {
				recordData: uni.getStorageSync('record-data'),
				pollutionInfo: {}, // 污染源基本信息
				checkData: {}, // 检查主题
				issueType: [], // 问题类型
				issueTypeTotal: 0, // 问题类型总数
				resourceData: [], // 来源
				problemList: [],
				showAi: false,
				pageNum: 1,
				pageSize: 10,
				WTDL: '',
				CITY: ''
			}
		},
		onLoad(options) {
			this.WTDL = options.WTDL;
			this.CITY = options.CITY;
			this.getData();
		},
		methods: {
			getData() {
				// 获取问题记录列表
				if(this.WTDL) {
					this.getProbleList();
				} else {
					this.getCityProbleList();
				}
			},

			getProbleList() {
				QUERY_YDZFAI_ZFJCJL({
					WRYBH: this.recordData.WRYBH,
					WTDL: this.WTDL,
					// pageNum: this.pageNum,
					// pageSize: this.pageSize
				}).then(res => {
					this.problemList = res.data_json;
					// if (this.pageNum > res.data_json.pages) {
					// 	uni.showToast({
					// 		icon: 'none',
					// 		title: '到底了！',
					// 		duration: 2000
					// 	})
					// } else {
					// 	this.problemList.push(...res.data_json.list);
					// }
				})
			},

			getCityProbleList() {
				QUERY_ZFZT_WTJL({
					WRYBH: this.recordData.WRYBH,
					JCZTJB: this.CITY,
					// pageNum: this.pageNum,
					// pageSize: this.pageSize
				}).then(res => {
					this.problemList = res.data_json.list;
					// if (this.pageNum > res.data_json.pages) {
					// 	uni.showToast({
					// 		icon: 'none',
					// 		title: '到底了！',
					// 		duration: 2000
					// 	})
					// } else {
					// 	this.problemList.push(...res.data_json.list);
					// }
				})
			},

			onReachBottomEvent() {
				// this.pageNum++;
				// this.getProbleList();
			},

			backEvent() {
				uni.navigateBack();
			},

			goDetail(item) {
				uni.setStorageSync('ai-finish-data', item);

				uni.navigateTo({
					url: '/views/ai/Finish'
				})
			}
		}
	}
</script>

<style>
@import "./css/reset.css";
@import "./css/zy0303.css";
@import "./css/style.css";

.zy0315-fujian-alert.yy0723-fjalert2 {
  height: calc(100vh - 80px);
}

.til1 {
  background: none;
}
</style>