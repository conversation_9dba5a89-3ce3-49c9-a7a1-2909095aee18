<template>
	<view v-if="show">
		<div class="mask" style="display: block"></div>
		<div class="zy0313-fankui" style="height: 560rpx;">
			<image src="./ai.png" style="width: 235rpx;height: 224rpx;" class="ai-box"></image>

			<p class="til">智能提醒</p>

			<!-- <view class="content-box">
				智能笔录助手检测到该笔录内容与结论不一致：发现问题情况为<span style="color: #F12F30;">“发现涉嫌环境违法问题”</span>，但检查小结中未描述问题情形。
			</view> -->
			<scroll-view 
				class="content-box"
				scroll-y="true">
				智能笔录助手检测到该笔录内容与结论不一致：<span v-html="highlightTextInHtml(content)"></span>
			</scroll-view>
			<div class="btns" style="justify-content: center; position: relative; top: -20rpx;">
				<button class="button-c2" style="width: 250rpx;height: 78rpx;" @click="show = false">知道了</button>
			</div>
		</div>
	</view>
</template>

<script>
	export default {
		props: {
			content:{
				type: String,
				default: ''
			}
		},
		data() {
			return {
				show: false
			}
		},
		created() {
			
		},
		mounted(){
			
		},
		methods: {
			showPop() {
				this.show = true;
			},
			highlightTextInHtml(text) {
				// 使用正则表达式匹配双引号中的内容
				const pattern = /"([^"]*)"/g;
				let match;
				let highlightedText = text;

				// 遍历所有匹配项，并替换为带有特定样式的HTML
				while ((match = pattern.exec(text)) !== null) {
					highlightedText = highlightedText.replace(new RegExp(match[0], 'g'), `<span style="color:#F12F30;">"${match[1]}"</span>`);
				}

				return highlightedText;
			}

		}
	}
</script>

<style>
	@import '../css/reset.css';
	@import '../css/zy0303.css';
	@import '../css/style.css';

	.content-box {
		width: 553rpx;
		height: 360rpx;
		font-family: Source Han Sans, Source Han Sans;
		font-weight: 400;
		font-size: 29rpx;
		color: #333333;
		line-height: 56rpx;
		text-align: left;
		font-style: normal;
		text-transform: none;
	}

	.ai-box {
		position: absolute;
		top: -120rpx;
		left: 0rpx;
	}
</style>