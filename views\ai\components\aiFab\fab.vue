<template>
	<view class="uni-cursor-point" :class="{
            'uni-fab__content--left': febPosition === 'left',
            'uni-fab__content--right': febPosition === 'right'
        }">
		<view v-if="
                popMenu &&
                (leftBottom || rightBottom || leftTop || rightTop) &&
                content.length  > 0  && isShow
            " class="uni-fab">
			<view :class="{
                    'uni-fab__content--other-platform': !isAndroidNvue
                }" :style="{
                    width: boxWidth,
                    height: boxHeight,
                    backgroundColor: styles.backgroundColor
                }" class="uni-fab__content" elevation="5">
				<view v-if="febPosition === 'left'" class="uni-fab__item uni-fab__item--first" />
				<view class="uni-fab__item" style="width: 90%" @click="goTask()">
					<!-- <image style="width: 100%; height: 100%;" src="./images/bg.png" class="start-btn"></image> -->
					<text class="company-box">{{title}}</text>
					<!-- <image src="./images/start.png" class="start-btn"></image>
					<image src="./images/point.png" class="point-btn"></image> -->
				</view>

				<view v-if="febPosition === 'right'" class="uni-fab__item uni-fab__item--first" />
			</view>
		</view>
		<view :class="{
                'uni-fab__circle--leftBottom': leftBottom,
                'uni-fab__circle--rightBottom': rightBottom,
                'uni-fab__content--other-platform': !isAndroidNvue
            }" class="uni-fab__circle uni-fab__plus" @click="_onClick"
			style="background: none;width: 148rpx; height: 140.35rpx; ">
			<image v-if="!isShow" style="width: 148rpx; height: 140.35rpx; " src="./images/ai_close.png"></image>

			<image v-if="isShow" style="width: 148rpx; height: 140.35rpx; " src="./images/ai_open.png"></image>
			<!-- <view class="uni-fab-helptips">

			</view> -->
			<!-- <view class="fab-circle-v"  :class="{'uni-fab__plus--active': isShow && content.length > 0}"></view>
			<view class="fab-circle-h" :class="{'uni-fab__plus--active': isShow  && content.length > 0}"></view> -->
		</view>
	</view>
</template>

<script>
	let platform = 'other';
	// #ifdef APP-NVUE
	platform = uni.getSystemInfoSync().platform;
	// #endif

	/**
	 * Fab 悬浮按钮
	 * @description 点击可展开一个图形按钮菜单
	 * @tutorial https://ext.dcloud.net.cn/plugin?id=144
	 * @property {Object} pattern 可选样式配置项
	 * @property {Object} horizontal = [left | right] 水平对齐方式
	 * 	@value left 左对齐
	 * 	@value right 右对齐
	 * @property {Object} vertical = [bottom | top] 垂直对齐方式
	 * 	@value bottom 下对齐
	 * 	@value top 上对齐
	 * @property {Object} direction = [horizontal | vertical] 展开菜单显示方式
	 * 	@value horizontal 水平显示
	 * 	@value vertical 垂直显示
	 * @property {Array} content 展开菜单内容配置项
	 * @property {Boolean} popMenu 是否使用弹出菜单
	 * @event {Function} trigger 展开菜单点击事件，返回点击信息
	 * @event {Function} fabClick 悬浮按钮点击事件
	 */
	export default {
		name: 'Fab',
		emits: ['fabClick', 'trigger'],
		props: {
			pattern: {
				type: Object,
				default () {
					return {};
				}
			},
			horizontal: {
				type: String,
				default: 'left'
			},
			vertical: {
				type: String,
				default: 'bottom'
			},
			direction: {
				type: String,
				default: 'horizontal'
			},
			content: {
				type: Array,
				default () {
					return [];
				}
			},
			show: {
				type: Boolean,
				default: false
			},
			popMenu: {
				type: Boolean,
				default: true
			},

			title: {
				type: String,
				default: ''
			},

			febPosition: {
				type: String,
				default: 'left'
			}
		},
		data() {
			return {
				fabShow: false,
				isShow: false,
				isAndroidNvue: platform === 'android',
				styles: {
					color: '#666',
					selectedColor: 'var(--color-primary)',
					backgroundColor: 'none',
					buttonColor: 'var(--color-primary)'
				}
			};
		},
		computed: {
			contentWidth(e) {
				return (this.content.length + 1) * 125 + 20 + 'rpx';
			},
			contentWidthMin() {
				return 120 + 'rpx';
			},
			// 动态计算宽度
			boxWidth() {
				return this.getPosition(3, 'horizontal');
			},
			// 动态计算高度
			boxHeight() {
				return this.getPosition(3, 'vertical');
			},
			// 计算左下位置
			leftBottom() {
				return this.getPosition(0, 'left', 'bottom');
			},
			// 计算右下位置
			rightBottom() {
				return this.getPosition(0, 'right', 'bottom');
			},
			// 计算左上位置
			leftTop() {
				return this.getPosition(0, 'left', 'top');
			},
			rightTop() {
				return this.getPosition(0, 'right', 'top');
			},
			flexDirectionStart() {
				return this.getPosition(1, 'vertical', 'top');
			},
			flexDirectionEnd() {
				return this.getPosition(1, 'vertical', 'bottom');
			},
			horizontalLeft() {
				return this.getPosition(2, 'horizontal', 'left');
			},
			horizontalRight() {
				return this.getPosition(2, 'horizontal', 'right');
			}
		},
		watch: {
			pattern(newValue, oldValue) {
				////console.log(JSON.stringify(newValue))
				this.styles = Object.assign({}, this.styles, newValue);
			},

			febPosition(val) {
				////console.log(val);
			}
		},
		created() {
			this.isShow = this.show;
			if (this.top === 0) {
				this.fabShow = true;
			}
			// 初始化样式
			this.styles = Object.assign({}, this.styles, this.pattern);

			setTimeout(() => {
				this.isShow = false;
			}, 6000)
		},
		methods: {
			goTask() {
				this.$emit('trigger');
			},

			getRecordData() {
				let data = uni.getStorageSync('record-data');
				if (data) {
					return data;
				} else {
					return {
						JCDXMC: ''
					}
				}
			},

			_onClick() {
				this.$emit('fabClick');
				if (!this.popMenu) {
					return;
				}
				this.isShow = !this.isShow;
			},
			open() {
				this.isShow = true;
			},
			close() {
				this.isShow = false;
			},
			/**
			 * 按钮点击事件
			 */
			_onItemClick(index, item) {
				this.$emit('trigger', {
					index,
					item
				});
			},
			/**
			 * 获取 位置信息
			 */
			getPosition(types, paramA, paramB) {
				if (types === 0) {
					return this.horizontal === paramA && this.vertical === paramB;
				} else if (types === 1) {
					return this.direction === paramA && this.vertical === paramB;
				} else if (types === 2) {
					return this.direction === paramA && this.horizontal === paramB;
				} else {
					return this.isShow && this.direction === paramA ?
						this.contentWidth :
						this.contentWidthMin;
				}
			}
		}
	};
</script>

<style lang="scss" scoped>
	.uni-fab {
		position: absolute;
		/* #ifndef APP-NVUE */
		display: flex;
		/* #endif */
		justify-content: center;
		align-items: center;
		z-index: 10;
		top: 19.5rpx;
	}

	.start-btn {
		width: 36rpx;
		height: 36rpx;
		position: relative;
		top: -23rpx;
	}

	.point-btn {
		width: 82.64rpx;
		height: 32.64rpx;

	}

	.drag-right .point-btn {
		position: absolute;
		top: -16rpx;
		left: 40rpx;
	}

	.drag-left .point-btn {
		position: absolute;
		top: -16rpx;
		right: 40rpx;
	}

	.drag-right .uni-fab__content {
		width: 701rpx !important;
		height: 100rpx !important;
		// background: url(./images/bg.png);
		// background-size: 100% 100%;
		// background: linear-gradient(90deg, #F6FEFF 0%, #A2DFFF 100%);
		// box-shadow: 0rpx 0rpx 14rpx 0rpx rgba(170, 211, 255, 0.8);
		// border-radius: 69rpx 69rpx 69rpx 69rpx;
		background: linear-gradient(180deg, #EAF2FF 0%, #FFFFFF 100%);
		border-radius: 69rpx 69rpx 69rpx 69rpx;
		// margin-right: 60rpx;
		text-align: left;
		position: relative;
		top: 30rpx;

	}

	.drag-left .uni-fab__content .uni-fab__item .company-box {
		text-align: left;
		line-height: 100rpx;
		display: inline-block;
		width: 500rpx;
		margin-left: 10rpx;
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
		font-weight: 500;
		font-weight: 400;
		font-size: 30rpx;
		color: #3580FF;
		text-align: center;
		line-height: 100rpx;
		padding-left: 10rpx;
	}

	.drag-right .uni-fab__content .uni-fab__item .company-box {
		text-align: left;
		line-height: 100rpx;
		display: inline-block;
		width: 500rpx;
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
		font-weight: 500;
		font-weight: 400;
		font-size: 30rpx;
		color: #3580FF;
		text-align: center;
		line-height: 100rpx;
		padding-left: 10rpx;
	}



	.drag-left .uni-fab__content {
		width: 701rpx !important;
		height: 100rpx !important;
		// background: url(./images/bg.png);
		// background-size: 100% 100%;
		background: linear-gradient(180deg, #EAF2FF 0%, #FFFFFF 100%);
		border-radius: 69rpx 69rpx 69rpx 69rpx;
		// background: linear-gradient(90deg, #A2DFFF 0%, #F6FEFF 100%);
		// box-shadow: 0rpx 0rpx 14rpx 0rpx rgba(170, 211, 255, 0.8);
		// border-radius: 69rpx 69rpx 69rpx 69rpx;
		// margin-left: 60rpx;
		text-align: left;
		position: relative;
		top: 30rpx;
	}

	.uni-cursor-point {
		/* #ifdef H5 */
		cursor: pointer;
		/* #endif */

		position: relative;
		width: 100%;
		height: 100%;
	}

	.uni-fab--active {
		opacity: 1;
	}

	.uni-fab__circle {
		position: absolute;
		/* #ifndef APP-NVUE */
		display: flex;
		/* #endif */
		justify-content: center;
		align-items: center;
		width: 120rpx;
		height: 120rpx;
		// background-color: #3c3e49;
		border-radius: 120rpx;
		z-index: 11;
	}

	.uni-fab__plus {
		font-weight: bold;
	}

	.fab-circle-v {
		position: absolute;
		width: 6rpx;
		height: 80rpx;
		left: 60rpx;
		top: 22rpx;
		background-color: white;
		transform: rotate(0deg);
		transition: transform 0.3s;
	}

	.fab-circle-h {
		position: absolute;
		width: 80rpx;
		height: 6rpx;
		left: 22rpx;
		top: 60rpx;
		background-color: white;
		transform: rotate(0deg);
		transition: transform 0.3s;
	}

	.uni-fab__plus--active {
		transform: rotate(135deg);
	}

	.uni-fab__content {
		/* #ifndef APP-NVUE */
		box-sizing: border-box;
		display: flex;
		/* #endif */
		flex-direction: row;
		border-radius: 120rpx;
		overflow: hidden;
		transition-property: width, height;
		transition-duration: 0.2s;
		width: 150rpx;
		height: 83rpx !important;
		border-color: #dddddd;
		border-width: 1rpx;
		border-style: solid;
		top: 80.5rpx;
	}

	.uni-fab__content--other-platform {
		border-width: 0px;
		// box-shadow: 0 10rpx 48rpx 0 rgba(7, 43, 130, 0.2);
	}

	.uni-fab__content--left {
		display: flex;
		justify-content: flex-start;
	}

	.uni-fab__content--right {
		display: flex;
		justify-content: flex-end;
	}

	.uni-fab__content--flexDirection {
		flex-direction: column;
		justify-content: flex-end;
	}

	.uni-fab__content--flexDirectionStart {
		flex-direction: column;
		justify-content: flex-start;
	}

	.uni-fab__content--flexDirectionEnd {
		flex-direction: column;
		justify-content: flex-end;
	}

	.uni-fab__item {
		/* #ifndef APP-NVUE */
		// display: flex;
		/* #endif */
		flex-direction: column;
		justify-content: center;
		align-items: center;
		width: 150rpx;
		height: 83rpx;
		// opacity: 0;
		transition: opacity 0.2s;
		color: black;

	}

	.uni-fab__item--active {
		opacity: 1;
	}

	.uni-fab__item-image {
		width: 25px;
		height: 25px;
		margin-bottom: 3px;
	}

	.uni-fab__item-text {
		color: #ffffff;
		font-size: 25rpx;
	}

	.uni-fab__item--first {
		width: 60rpx;
	}

	.uni-fab-helptips {
		font-size: 34rpx;
		width: 68rpx;
		height: 68rpx;
		display: flex;
		align-items: center;
	}
</style>