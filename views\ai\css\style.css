@charset "utf-8";

/* @font-face {
	font-family: "DIN-Bold";
	src: url("../fonts/DIN-Bold.woff2") format("woff2"), url("../fonts/DIN-Bold.woff") format("woff"), url("../fonts/DIN-Bold.ttf") format("truetype");
	font-weight: normal;
	font-style: normal;
}

@font-face {
	font-family: "DIN-Medium";
	src: url("../fonts/DIN-Medium.woff2") format("woff2"), url("../fonts/DIN-Medium.woff") format("woff"), url("../fonts/DIN-Bold.ttf") format("truetype");
	font-weight: normal;
	font-style: normal;
}

@font-face {
	font-family: "DIN-Regular";
	src: url("../fonts/DIN-Regular.woff2") format("woff2"), url("../fonts/DIN-Regular.woff") format("woff");
	font-weight: normal;
	font-style: normal;
} */

.yy-darkbg {
	background: #020316;
}

.yy-darkbg .bg1 {
	background: linear-gradient(#272B47 0%, #020316 100%);
}

.yy-darkbg::after {
	content: "";
	position: absolute;
	top: 0;
	right: 0;
	width: 100%;
	height: 596rpx;
	background: url(@/views/ai/images/yy0723-bg1.png) center center no-repeat;
	background-size: 100% 100%;
	z-index: -1;
}

.yy-darkbg .page-header .ic-back {
	background: url(@/views/ai/images/yy0723-back.png) no-repeat center;
	background-size: 100% auto;
}

.yy-darkbg .page-header .c-title {
	color: #fff;
}

.yy-pd35 {
	padding: 0 25rpx;
}

.yy0723-infobox1 {
	width: 100%;
	height: 292rpx;
	background: url(@/views/ai/images/yy0723-fkbg.png) no-repeat center;
	background-size: 100% 100%;
	margin-top: 70rpx;
	padding: 28rpx;
	box-sizing: border-box;
	position: relative;
}

.yy0723-infobox1::after {
	content: "";
	position: absolute;
	top: -9%;
	right: 5%;
	width: 202rpx;
	height: 209rpx;
	background: url(@/views/ai/images/yy0723-szbg1.png) center center no-repeat;
	background-size: 100% 100%;
}

.yy0723-infobox1 .p1 {
	font-weight: 500;
	font-size: 32rpx;
	color: #FFFFFF;
	font-family: "DIN-Regular";
}

.yy0723-infobox1 .p2 {
	font-weight: 400;
	font-size: 27rpx;
	color: #FFFFFF;
}

.yy0723-jcul1 li h1 {
	font-weight: 500;
	font-size: 32rpx;
	color: rgba(255, 255, 255, 0.8);
	font-family: "DIN-Regular";
	margin-bottom: 14rpx;
}

.yy0723-jcul1 li h2 {
	font-weight: 400;
	font-size: 27rpx;
	color: rgba(255, 255, 255, 0.8);
}

.yy0723-jcul1 li+li {
	margin-left: 119rpx;
}

.gap {
	height: 28rpx;
}

.gap60 {
	height: 42rpx;
}

.gap50 {
	height: 35rpx;
}

.yy0723-pos1 {
	width: 21rpx;
	height: auto;
	margin-right: 14rpx;
}

.yy0723-gsmod {
	background: #272B47;
	border-radius: 23rpx;
	padding: 28rpx;
	box-sizing: border-box;
}

.yy0723-gsmod h1 {
	font-weight: 700;
	font-size: 32rpx;
	color: #FFFFFF;
}

.yy0723-jcztul {
	padding-bottom: 14rpx;
}

.yy0723-jcztul li {
	text-align: center;
}

.yy0723-jcztul .p1 {
	font-weight: 500;
	font-size: 32rpx;
	color: #FFFFFF;
	font-family: "DIN-Regular";
	margin-bottom: 19rpx;
}

.yy0723-jcztul .p2 {
	font-weight: 500;
	font-size: 27rpx;
	color: #FFFFFF;
}

.yy0729-wttypeul {
	margin-top: 28rpx;
}

.yy0729-wttypeul li {
	display: flex;
	align-items: center;
	margin-bottom: 35rpx;
}

.yy0729-wttypeul li .yy0723-rank {
	width: 42rpx;
	height: auto;
	display: inline-block;
}

.yy0729-wttypeul li .flowbox {
	flex: 1;
	margin-left: 21rpx;
}

.yy0729-wttypeul li .flowbox .top .f1 {
	font-weight: 400;
	font-size: 27rpx;
	color: #FFFFFF;
}

.yy0729-wttypeul li .flowbox .top .f2 {
	font-weight: 500;
	font-size: 32rpx;
	color: #FFFFFF;
	font-family: "DIN-Regular";
}

.yy0729-wttypeul li .flowbox .bar {
	height: 24rpx;
	background: rgba(11, 14, 32, 0.5);
	border-radius: 14rpx;
	position: relative;
	margin-top: 18rpx;
}

.yy0729-wttypeul li .flowbox .bar b {
	position: absolute;
	left: 0;
	top: 0;
	bottom: 0;
	border-radius: 14rpx;
	background: linear-gradient(270deg, #AAFEFF 0%, #3FC161 100%);
}

.yy0729-wttypeul li .num {
	width: 35rpx;
	height: 35rpx;
	line-height: 35rpx;
	text-align: center;
	border-radius: 50%;
	background: #969AA7;
	font-weight: 500;
	font-size: 27rpx;
	color: #FFFFFF;
}

.yy0729-wttypeul li .num.n1 {
	width: 42rpx;
	height: 42rpx;
	background: url(@/views/ai/images/yy0723-top1.png) no-repeat center;
	background-size: 100% 100%;
	text-indent: -999px;
}

.yy0729-wttypeul li .num.n2 {
	width: 42rpx;
	height: 42rpx;
	background: url(@/views/ai/images/yy0723-top2.png) no-repeat center;
	background-size: 100% 100%;
	text-indent: -999px;
}

.yy0729-wttypeul li .num.n3 {
	width: 42rpx;
	height: 42rpx;
	background: url(@/views/ai/images/yy0723-top3.png) no-repeat center;
	background-size: 100% 100%;
	text-indent: -999px;
}

.yy-more {
	font-size: 27rpx;
	color: #FFFFFF;
}

.yy-more .yy0723-rtarr {
	width: 13rpx;
	height: auto;
	margin-left: 11rpx;
}


.yy0723-dstep {
	padding-left: 46rpx;
	padding-bottom: 14rpx;
}

.yy0723-dstep li {
	position: relative;
	padding-bottom: 60rpx;
}

.yy0723-dstep li .yy0723-rtarr {
	width: 13rpx;
	height: auto;
	position: absolute;
	right: 0;
	top: 30rpx;
}

.yy0723-dstep li .time1 {
	font-size: 32rpx;
	color: #FFFFFF;
	font-family: "DIN-Bold";
}

.yy0723-dstep li .zxbtn {
	height: 35rpx;
	line-height: 35rpx;
	background: rgba(250, 173, 20, 0.1);
	border-radius: 3rpx;
	display: inline-block;
	padding: 0 11rpx;
	font-size: 21rpx;
	color: #FAAD14;
	margin-left: 28rpx;
}

.yy0723-dstep li .p1 {
	font-weight: 400;
	font-size: 27rpx;
	color: #999999;
	margin-bottom: 28rpx;
}

.yy0723-dstep li .stepcont {
	background: rgba(5, 6, 26, 0.2);
	border-radius: 12rpx;
	padding: 14rpx 21rpx;
	position: relative;

}

.yy0723-dstep li .stepcont .p2 {
	font-size: 27rpx;
	color: #FFFFFF;
	line-height: 56rpx;
	padding-left: 30rpx;
	position: relative;
}

.yy0723-dstep li .stepcont .p2::before {
	content: '';
	position: absolute;
	left: 0;
	top: 50%;
	transform: translateY(-19%);
	width: 12rpx;
	height: 12rpx;
	background: #fff;
	border-radius: 50%;
}

.yy0723-dstep .yy0204-row {
	margin-bottom: 21rpx;
	position: relative;
}

.yy0723-dstep li .yy0204-row::before {
	content: '';
	position: absolute;
	left: -52rpx;
	top: 12%;
	width: 35rpx;
	height: 35rpx;
	border: 3rpx solid #DDDDDD;
	box-sizing: border-box;
	border-radius: 50%;
	z-index: 5;
}

.yy0723-dstep li.on .yy0204-row::before {
	width: 35rpx;
	height: 35rpx;
	border: 3rpx solid #3FC161;
	box-sizing: border-box;
	border-radius: 50%;
	z-index: 5;

}

.yy0723-dstep li.cur .yy0204-row::before {
	width: 35rpx;
	height: 35rpx;
	background: url(@/views/ai/images/yy-stepdot.png) no-repeat center;
	background-size: 100% 100%;
	border: none;
	top: 4%;

}

.yy0723-dstep li::after {
	content: '';
	position: absolute;
	left: -37rpx;
	top: 16%;
	bottom: 0;
	border-left: 1rpx solid #ddd;
}

.yy0723-dstep li.cur::after {
	top: 12%;
}

.yy0723-dstep li.on::after {
	top: 12%;
	border-left: 1rpx solid #3FC161;

}

.yy0723-dstep li:last-of-type {
	padding-bottom: 0;
}

.yy-pdbg40 {
	margin: 28rpx 28rpx 0;
	background-color: #fff;
	padding: 0 35rpx;
	border-radius: 21rpx;
}

.yy0723-footrobot {
	height: 101rpx;
	background: linear-gradient(180deg, #EAF2FF 0%, #FFFFFF 100%);
	border-radius: 70rpx;
	position: relative;
}

.yy0723-footrobot .p1 {
	font-weight: 400;
	font-size: 30rpx;
	color: #3580FF;
	text-align: center;
	line-height: 101rpx;
	padding-left: 10%;
}

.yy0723-footrobot .yy0723-robot1 {
	width: 96rpx;
	height: 132rpx;
	position: absolute;
	left: 42rpx;
	top: -50%;
}


.zy0315-fujian-alert.yy0723-fjalert2 {
	position: fixed;
	left: 0;
	right: 0;
	bottom: 0;
	height: 90%;
	background: linear-gradient(180deg, #E8EEFF 0%, #E8EEFF 60%, #FFFFFF 100%);
	padding-bottom: 121rpx;
	box-sizing: border-box;
}

.yy0723-msgcont {
	/*  height: 1193rpx; */
	height: calc(100% - 150rpx);
	overflow-y: auto;
}

.page-bottom {
	display: flex;
	align-items: center;
	padding: 0 25rpx;
}

.page-bottom input {
	flex: 1;
	font-size: 32rpx;
	color: #333333;
	line-height: 72rpx;
	height: 72rpx;
	background: #F4F6F8;
	border-radius: 37rpx;
	padding-left: 39rpx;
}

.yy0723-tools {
	width: 100%;
	height: 100%;
	display: flex;
	align-items: center;
}

.yy0723-write {
	width: 42rpx;
	height: auto;
	display: inline-block;
	margin-right: 21rpx;
}

.yy0723-add {
	width: 35rpx;
	height: auto;
	display: inline-block;
	margin-left: 21rpx;
}

.yypage-bottom2 input {
	flex: 1;
	color: #333333;
	line-height: 72rpx;
	height: 72rpx;
	background: #F4F6F8 url(@/views/ai/images/yy0723-voice.png) no-repeat 35% center;
	background-size: 21rpx auto;
	border-radius: 37rpx;
	padding-left: 39rpx;
	color: #999999;
	text-align: center;
	font-size: 32rpx;
}

.yy0723-data5 {
	margin: 28rpx 0;
	display: flex;
	justify-content: flex-end;
}

.yy0723-data5 .p1 {
	max-width: 85%;
	box-sizing: border-box;
	padding: 18rpx 46rpx;
	background: linear-gradient(270deg, #1F6BFF 0%, #6BA4FF 100%);
	border-radius: 40rpx 0rpx 40rpx 40rpx;
	font-size: 32rpx;
	color: #FFFFFF;
	line-height: 48rpx;
}

.yy0723-data2 {
	margin: 28rpx 0;
	padding: 28rpx 32rpx;
	background: #FFFFFF;
	border-radius: 0 23rpx 23rpx 23rpx;
}

.yy0723-data2 .p1 {
	font-size: 30rpx;
	color: #666666;
	line-height: 45rpx;
}

.yy0723-data2 .f2box {
	font-size: 30rpx;
	color: #666666;
}


.yy0723-data2 .f2box em {
	font-weight: 700;
	font-size: 30rpx;
	color: #333333;
}

.yy0723-data2 .f3box {
	font-size: 30rpx;
	color: #666666;
}


.yy0723-data2 .f3box em {
	font-weight: 700;
	font-size: 30rpx;
	color: #333333;
	display: inline-block;
	margin-bottom: 11rpx;
}

.yy0723-ask {
	font-size: 27rpx;
	color: #999999;
	margin-bottom: 18rpx;
}

.yy0723-qustion {
	height: 70rpx;
	line-height: 70rpx;
	background: #FFFFFF;
	border-radius: 70rpx;
	font-size: 27rpx;
	color: #333333;
	padding: 0 28rpx;
	box-sizing: border-box;
}

.yy0723-titbox {
	position: relative;
}

.yy0723-qh {
	width: 28rpx;
	height: auto;
	display: inline-block;
	position: absolute;
	top: 45rpx;
	right: 166rpx;
}


.page-header {
	height: 94rpx;
	position: relative;
}

.page-header .ic-back {
	position: absolute;
	left: 28rpx;
	top: 50%;
	transform: translateY(-50%);
	width: 18rpx;
	height: 31rpx;
	background: url(@/views/ai/images/ic_back.png) no-repeat center;
	background-size: 100%;
}

.page-header .header-menu {
	display: flex;
	justify-content: center;
	height: 94rpx;
	align-items: center;
}

.page-header .header-menu li {
	font-size: 32rpx;
	color: #999999;
	line-height: 78rpx;
	margin: 0 28rpx;
}

.page-header .header-menu li.on {
	font-weight: bold;
	color: #333333;
	position: relative;
}

.page-header .header-menu li.on::after {
	content: "";
	position: absolute;
	bottom: 0;
	left: 50%;
	transform: translateX(-50%);
	width: 50rpx;
	height: 19rpx;
	background: url(@/views/ai/images/zy0831_img6.png);
	background-size: 100%;
}

.page-header .c-title {
	text-align: center;
	font-size: 35rpx;
	color: #333;
	line-height: 94rpx;
	font-weight: bold;
	letter-spacing: 2rpx;
}