.zy0313-user {
	position: absolute;
	right: 25rpx;
	top: 50%;
	transform: translateY(-50%);
	display: flex;
}

.zy0313-user img {
	width: 34rpx;
	height: 34rpx;
}

.zy0313-data1 {
	padding: 25rpx;
}

.zy0313-data1 li {
	display: flex;
	align-items: center;
	height: 170rpx;
	background: #ffffff;
	border-radius: 23rpx;
	margin-bottom: 21rpx;
}

.zy0313-data1 li .ic {
	width: 80rpx;
	height: 80rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-left: 44rpx;
	margin-right: 23rpx;
}

.zy0313-data1 li .ic img {
	max-width: 100%;
}

.zy0313-data1 li .zi .p1 {
	font-weight: 700;
	font-size: 34rpx;
	color: #333333;
	line-height: 32rpx;
	margin-bottom: 26rpx;
	font-family: Source <PERSON>, Source <PERSON>;
}

.zy0313-data1 li .zi .p2 {
	font-size: 30rpx;
	color: #666666;
	line-height: 32rpx;
}

.zy0313-voice {
	flex: 1;
	height: 72rpx;
	background: #f4f6f8;
	border-radius: 37rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 32rpx;
	color: #999999;
	line-height: 32rpx;
}

.zy0313-voice img {
	width: 20rpx;
	margin-right: 13rpx;
}

.zy0313-add {
	display: flex;
	align-items: center;
	margin-left: 23rpx;
}

.zy0313-add img {
	width: 38rpx;
	height: 38rpx;
}

.zy0313-yuyin {
	position: absolute;
	z-index: 1111;
	bottom: 0;
	left: 0;
	right: 0;
	height: 444rpx;
	box-sizing: border-box;
	padding-top: 84rpx;
	background: #fff;
	border-top-left-radius: 30%;
	border-top-right-radius: 30%;
}

.zy0313-yuyin .ctx {
	padding: 0 28rpx;
	font-size: 35rpx;
	color: #333333;
	line-height: 56rpx;
	text-align: center;
}

.zy0313-talk {
	position: absolute;
	bottom: 37rpx;
	left: 0;
	right: 0;
	display: flex;
	align-items: center;
	justify-content: space-between;
	height: 98rpx;
	padding: 0 21rpx;
}

.zy0313-talk .bolang {
	display: flex;
	align-items: center;
	justify-content: space-between;
	flex: 1;
}

.zy0313-talk .bolang span {
	width: 5rpx;
	height: 9rpx;
	background: #4478ff;
	border-radius: 5rpx;
	animation-name: audio-wave;
	animation-duration: 2s;
	animation-timing-function: ease-in-out;
	animation-iteration-count: infinite;
}

.zy0313-talk .bolang span:nth-child(1) {
	animation-delay: 0.1s;
}

.zy0313-talk .bolang span:nth-child(2) {
	animation-delay: 0.2s;
}

.zy0313-talk .bolang span:nth-child(3) {
	animation-delay: 0.3s;
}

.zy0313-talk .bolang span:nth-child(4) {
	animation-delay: 0.4s;
}

.zy0313-talk .bolang span:nth-child(5) {
	animation-delay: 0.5s;
}

.zy0313-talk .anniu {
	position: relative;
	padding: 0 9rpx;
}

.zy0313-talk .anniu .p1 {
	position: absolute;
	top: -54rpx;
	left: 50%;
	transform: translateX(-50%);
	white-space: nowrap;
	font-size: 25rpx;
	color: #999999;
	line-height: 39rpx;
	text-align: center;
	margin-bottom: 16rpx;
}

.zy0313-talk .anniu .ic {
	display: flex;
}

.zy0313-talk .anniu .ic img {
	width: 98rpx;
}

.zy0313-fankui {
	position: absolute;
	right: 63rpx;
	left: 63rpx;
	top: 0;
	bottom: 0;
	height: 688rpx;
	margin: auto;
	z-index: 1111;
	background: linear-gradient(180deg, #dfe8ff 0%, #ffffff 100%);
	border-radius: 23rpx 23rpx 23rpx 23rpx;
	border: 3rpx solid #ffffff;
	box-sizing: border-box;
	padding: 28rpx 39rpx 39rpx 39rpx;
}

.zy0313-fankui .til {
	font-weight: 700;
	font-size: 38rpx;
	color: #333333;
	line-height: 39rpx;
	text-align: center;
	margin-bottom: 28rpx;
}

.zy0313-fankui .btns {
	display: flex;
	justify-content: space-between;
	margin-top: 35rpx;
}

.zy0313-fankui .btns .button-c1 {
	width: 250rpx;
	height: 79rpx;
	background: #e8eef7;
	border-radius: 12rpx 12rpx 12rpx 12rpx;
	font-size: 32rpx;
	color: #3d3d3d;
	line-height: 79rpx;
	text-align: center;
}

.zy0313-fankui .btns .button-c2 {
	width: 250rpx;
	height: 79rpx;
	background: #3580ff;
	border-radius: 12rpx 12rpx 12rpx 12rpx;
	font-size: 32rpx;
	color: #fff;
	line-height: 79rpx;
	text-align: center;
}

.zy0313-checkbox {
	font-size: 32rpx;
	color: #333333;
	line-height: 39rpx;
	display: flex;
	align-items: center;
}

.zy0313-checkbox input {
	width: 32rpx;
	height: 32rpx;
	background: url(@/views/ai/images/zy0313_checkno.png) 0 center no-repeat;
	background-size: 32rpx 32rpx;
	margin-right: 16rpx;
}

.zy0313-checkbox input:checked {
	background-image: url(@/views/ai/images/zy0313_checkyes.png);
}

.check-list {
	margin-bottom: 14rpx;
}

.check-list li {
	padding: 14rpx 0;
}

.zy0313-textarea {
	width: 100%;
	height: 219rpx;
	background: #ffffff;
	border-radius: 12rpx 12rpx 12rpx 12rpx;
	border: 1rpx solid #dddddd;
	box-sizing: border-box;
	padding: 21rpx 28rpx;
	font-size: 32rpx;
	color: #333333;
	line-height: 39rpx;
	font-family: "Microsoft YaHei";
	resize: none;
}

.page-header .c-title {
	text-align: center;
	font-size: 35rpx;
	color: #333;
	line-height: 94rpx;
	font-weight: bold;
	letter-spacing: 2rpx;
}

.zy0313-person {
	display: flex;
	align-items: center;
	padding-left: 35rpx;
	margin-top: 14rpx;
}

.zy0313-person .avatar {
	width: 120rpx;
	height: 120rpx;
	border-radius: 50%;
	overflow: hidden;
	display: flex;
}

.zy0313-person .avatar img {
	width: 100%;
	height: 100%;
}

.zy0313-person .name {
	margin-left: 30rpx;
	font-weight: 700;
	font-size: 41rpx;
	color: #333333;
	line-height: 41rpx;
}

.zy0313-handle {
	padding: 0 25rpx;
	margin-top: 42rpx;
}

.zy0313-handle li {
	height: 109rpx;
	background: #ffffff;
	border-radius: 14rpx;
	display: flex;
	align-items: center;
	padding: 0 28rpx;
	margin-bottom: 28rpx;
}

.zy0313-handle li .ic {
	margin-right: 21rpx;
	width: 35rpx;
	display: flex;
}

.zy0313-handle li .ic img {
	width: 100%;
}

.zy0313-handle li>p {
	font-weight: 500;
	font-size: 35rpx;
	color: #333333;
	line-height: 35rpx;
}

.zy0313-handle li .toggle {
	margin-left: auto;
	width: 52rpx;
	height: 32rpx;
	background: url(@/views/ai/images/zy0313_ic9.png);
	background-size: 100% 100%;
}

.zy0313-handle li .toggle.on {
	background-image: url(@/views/ai/images/zy0313_ic10.png);
}

.zy0313-signout {
	position: absolute;
	bottom: 60rpx;
	left: 25rpx;
	right: 25rpx;
	height: 109rpx;
	background: #ebf2ff;
	border-radius: 14rpx 14rpx 14rpx 14rpx;
	font-size: 35rpx;
	color: #3580ff;
	line-height: 109rpx;
	text-align: center;
}

.zy0313-data2 {
	margin: 21rpx 25rpx;
	padding: 35rpx 32rpx;
	padding-bottom: 18rpx;
	background: #ffffff;
	border-radius: 23rpx;
}

.zy0313-data2 .play {
	display: flex;
	align-items: center;
}

.zy0313-data2 .play .ic {
	width: 38rpx;
	height: 38rpx;
	background: url(@/views/ai/images/zy0314_ic1.png) no-repeat;
	background-size: 100% 100%;
}

.zy0313-data2 .play .ic.off {
	background-image: url(@/views/ai/images/zy0314_ic11.png);
}

.zy0313-data2 .play .bolang {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-left: 12rpx;
}

.zy0313-data2 .play .bolang span {
	width: 5rpx;
	height: 9rpx;
	background: #787878;
	border-radius: 5rpx;
	margin-right: 5rpx;
	animation-name: audio-wave2;
	animation-duration: 2s;
	animation-timing-function: ease-in-out;
	animation-iteration-count: infinite;
	animation-play-state: paused;
}

.zy0313-data2 .ctx {
	font-size: 32rpx;
	color: #333333;
	line-height: 46rpx;
	margin-top: 14rpx;
	margin-bottom: 21rpx;
}

.zy0313-data2 .cankao .til {
	font-size: 32rpx;
	color: #999999;
	line-height: 39rpx;
	margin-bottom: 21rpx;
}

.zy0313-data2 .cankao .list li {
	height: 80rpx;
	background: #f6f6f6 url(@/views/ai/images/zy0314_ic3.png) right 13rpx center no-repeat;
	background-size: 30rpx;
	border-radius: 12rpx;
	padding-left: 20rpx;
	padding-right: 13rpx;
	margin-bottom: 21rpx;
}

.zy0313-data2 .cankao .list li .resource {
	width: 80%;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	font-size: 30rpx;
	color: #666666;
	line-height: 80rpx;
	padding-left: 23rpx;
	background: url(@/views/ai/images/zy0314_ic2.png) 0 center no-repeat;
	background-size: 12rpx;
}

.zy0313-data2 .caozuo {
	border-top: 1rpx solid #dddddd;
	padding-top: 18rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.zy0313-data2 .caozuo .video {
	width: 238rpx;
	height: 63rpx;
	background: #ecf3ff url(@/views/ai/images/zy0314_ic4.png) 23rpx center no-repeat;
	background-size: 39rpx;
	box-sizing: border-box;
	padding-left: 70rpx;
	border-radius: 81rpx;
	font-size: 30rpx;
	color: #4478ff;
	line-height: 63rpx;
}

.zy0313-data2 .caozuo .tools {
	display: flex;
}

.zy0313-data2 .caozuo .tools li {
	display: flex;
	align-items: center;
}

.zy0313-data2 .caozuo .tools li img {
	width: 32rpx;
}

.zy0313-data2 .caozuo .tools li+li {
	margin-left: 70rpx;
}

.voice-click {
	display: flex;
	margin-right: 25rpx;
	margin-left: 10rpx;
}

.voice-click img {
	width: 27rpx;
	height: 38rpx;
}

.jianpan-click {
	display: flex;
	margin-right: 16rpx;
}

.jianpan-click img {
	width: 44rpx;
	height: 44rpx;
}

.zy0314-shengcheng {
	position: absolute;
	bottom: 139rpx;
	left: 50%;
	transform: translateX(-50%);
	height: 63rpx;
	background: #ffffff;
	border-radius: 32rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.zy0314-shengcheng .type1 {
	padding-left: 61rpx;
	padding-right: 19rpx;
	font-size: 27rpx;
	color: #666666;
	line-height: 63rpx;
	background: url(@/views/ai/images/zy0314_ic10.png) 24rpx center no-repeat;
	background-size: 31rpx;
}

.zy0314-shengcheng .type2 {
	padding-left: 61rpx;
	padding-right: 19rpx;
	font-size: 27rpx;
	color: #666666;
	line-height: 63rpx;
	background: url(@/views/ai/images/zy0314_ic9.png) 24rpx center no-repeat;
	background-size: 30rpx;
}

.zy0314-shengcheng img {
	width: 30rpx;
	height: 30rpx;
	margin-right: 7rpx;
}

@keyframes audio-wave {
	0% {
		height: 5rpx;
		transform: translateY(0rpx);
	}

	25% {
		height: 5rpx;
		transform: translateY(0rpx);
	}

	50% {
		height: 21rpx;
		transform: translateY(-3rpx) scaleY(1.5);
	}

	75% {
		height: 5rpx;
		transform: translateY(0rpx);
	}

	100% {
		height: 5rpx;
		transform: translateY(0rpx);
	}
}

@keyframes audio-wave2 {
	0% {
		height: 5rpx;
		transform: translateY(0rpx);
	}

	25% {
		height: 5rpx;
		transform: translateY(0rpx);
	}

	50% {
		height: 21rpx;
		transform: translateY(0rpx) scaleY(1.1);
	}

	75% {
		height: 5rpx;
		transform: translateY(0rpx);
	}

	100% {
		height: 5rpx;
		transform: translateY(0rpx);
	}
}

.zy0315-more-question {
	display: flex;
	align-items: center;
	margin: 21rpx 25rpx;
}

.zy0315-more-question .label {
	font-size: 27rpx;
	color: #999999;
	line-height: 30rpx;
}

.zy0315-more-question .items {
	display: flex;
	flex: 1;
	overflow-x: auto;
}

.zy0315-more-question .items .item {
	padding: 0 21rpx;
	height: 63rpx;
	background: #f6f8fa;
	border-radius: 139rpx;
	border: 2rpx solid #bbc3cb;
	box-sizing: border-box;
	white-space: nowrap;
	font-size: 27rpx;
	color: #9da2a7;
	line-height: 60rpx;
	margin-right: 16rpx;
}

.zy0315-fujian-alert {
	position: absolute;
	z-index: 1111;
	bottom: 0;
	left: 0;
	right: 0;
	background: linear-gradient(180deg, #e8eeff 0%, #ffffff 62%, #ffffff 100%);
	border-radius: 56rpx 56rpx 0rpx 0rpx;
	padding: 0 35rpx;
	box-sizing: border-box;
}

.zy0315-fujian-alert .close {
	position: absolute;
	top: 45rpx;
	right: 49rpx;
	width: 24rpx;
	height: 25rpx;
	background: url(@/views/ai/images/zy0315_close.png);
	background-size: 100%;
}

.zy0315-fujian-alert .til1 {
	font-weight: 700;
	font-size: 38rpx;
	color: #171717;
	line-height: 39rpx;
	text-align: center;
	padding-top: 35rpx;
	margin-bottom: 49rpx;
}

.zy0315-fujian-alert .til2 {
	font-weight: 700;
	font-size: 34rpx;
	color: #333333;
	line-height: 56rpx;
	text-align: center;
	margin-bottom: 25rpx;
}

.zy0315-fujian-alert .til3 {
	font-weight: 700;
	font-size: 34rpx;
	color: #333333;
	line-height: 39rpx;
	margin-bottom: 25rpx;
}

.zy0315-fujian-alert .til4 {
	font-size: 32rpx;
	color: #333333;
	line-height: 39rpx;
	margin-bottom: 19rpx;
	text-indent: 28rpx;
}

.zy0315-fujian-alert .yuedu-btn {
	width: 178rpx;
	height: 63rpx;
	background: #d1e2ff;
	border-radius: 82rpx 82rpx 82rpx 82rpx;
	font-size: 30rpx;
	color: #3580ff;
	line-height: 63rpx;
	text-align: center;
	margin-bottom: 26rpx;
}

.zy0315-fujian-alert .line {
	border-top: 2rpx solid #d5d8e1;
	margin-bottom: 25rpx;
}

.zy0315-fujian-alert .des {
	font-size: 32rpx;
	color: #3d3d3d;
	line-height: 56rpx;
	text-indent: 28rpx;
}

.zy0315-article {
	height: 825rpx;
	overflow-y: auto;
	overflow-x: hidden;
	padding-bottom: 28rpx;
}

.zy0315-article .item {
	margin-bottom: 28rpx;
}

.zy0315-article .item:last-child {
	margin-bottom: 0;
}