.gap {
	height: 28rpx;
}

.zy0723-page {
	background: linear-gradient(225deg, #272b47 0%, #020316 100%, #020316 100%);
	position: relative;
	min-height: 100%;
}

.zy0723-page::after {
	content: "";
	position: absolute;
	top: 0;
	right: 0;
	width: 100%;
	height: 596rpx;
	background: url(@/views/ai/images/page_bg.png) 0 0 no-repeat;
	background-size: 100% auto;
	z-index: -1;
}

.backbtn {
	position: absolute;
	left: 0;
	top: 0;
	width: 77rpx;
	height: 94rpx;
	background: url(@/views/ai/images/page_back.png) no-repeat center;
	background-size: 18rpx;
}

.zy0723-data1 {
	display: flex;
	align-items: flex-start;
	padding: 0 25rpx;
}

.zy0723-data1 .avatar {
	display: flex;
	align-items: center;
	margin-left: 5rpx;
	width: 133rpx;
	height: 133rpx;
}

.zy0723-data1 .avatar>.img1 {
	width: 100%;
	height: 100%;
}

.zy0723-data1 .info {
	padding-top: 11rpx;
	margin-left: 21rpx;
}

.zy0723-data1 .info .p1 {
	font-weight: 700;
	font-size: 39rpx;
	color: #ffffff;
	line-height: 42rpx;
	margin-bottom: 27rpx;
}

.zy0723-data1 .info .p2 {
	font-size: 32rpx;
	color: rgba(255, 255, 255, 0.5);
	line-height: 32rpx;
	margin-bottom: 24rpx;
}

.zy0723-data1 .info .p3 {
	font-size: 27rpx;
	color: rgba(255, 255, 255, 0.5);
	line-height: 32rpx;
	font-family: "DIN-Regular";
}

.zy0723-data1 .type {
	padding: 0 14rpx;
	height: 38rpx;
	background: #fc5256;
	border-radius: 6rpx 0rpx 6rpx 0rpx;
	font-size: 23rpx;
	color: #ffffff;
	line-height: 38rpx;
	margin-left: 28rpx;
	margin-top: 13rpx;
}

.zy0723-box1 {
	margin: 0 25rpx;
	padding: 35rpx 28rpx;
	background: #272b47;
	border-radius: 23rpx 23rpx 23rpx 23rpx;
}

.zy0723-til1 {
	font-weight: 700;
	font-size: 32rpx;
	color: #ffffff;
	line-height: 32rpx;
}

.zy0723-data2 .txt {
	font-size: 27rpx;
	color: rgba(255, 255, 255, 0.7);
	line-height: 45rpx;
	margin-bottom: 35rpx;
}

.zy0723-data2 .jieda .q1 {
	font-size: 30rpx;
	color: #faad14;
	line-height: 32rpx;
	padding-left: 45rpx;
	background: url(@/views/ai/images/zy0723_d2_img1.png) 0 center no-repeat;
	background-size: 32rpx auto;
	margin-bottom: 21rpx;
}

.zy0723-data2 .jieda .a1 {
	font-size: 27rpx;
	color: rgba(255, 255, 255, 0.7);
	line-height: 56rpx;
	padding-left: 24rpx;
	position: relative;
}

.zy0723-data2 .jieda .a1::after {
	content: "";
	position: absolute;
	left: 0;
	top: 50%;
	transform: translateY(-50%);
	width: 10rpx;
	height: 10rpx;
	background: rgba(255, 255, 255, 0.7);
	border-radius: 50%;
}

.zy0723-data2 .pingjia {
	display: flex;
	justify-content: space-evenly;
	margin-top: 49rpx;
	margin-bottom: 14rpx;
}

.zy0723-data2 .pingjia .item {
	padding-left: 64rpx;
	font-size: 32rpx;
	color: #ffffff;
	line-height: 45rpx;
}

.zy0723-data2 .pingjia .item.agree {
	background: url(@/views/ai/images/zy0723_d2_img2.png) 0 center no-repeat;
	background-size: 48rpx;
}

.zy0723-data2 .pingjia .item.agree.on {
	background-image: url(@/views/ai/images/zy0723_d2_img2_on.png);
}

.zy0723-data2 .pingjia .item.oppose {
	background: url(@/views/ai/images/zy0723_d2_img3.png) 0 center no-repeat;
	background-size: 48rpx;
}

.zy0723-data2 .pingjia .item.oppose.on {
	background-image: url(@/views/ai/images/zy0723_d2_img3_on.png);
}

.zy0723-data3 {
	display: flex;
	align-items: center;
}

.zy0723-data3 .item {
	position: relative;
	display: flex;
	align-items: center;
	margin-right: 21rpx;
}

.zy0723-data3 .item .file {
	width: 246rpx;
	height: 345rpx;
}

.zy0723-data3 .item .type {
	position: absolute;
	top: 0;
	right: 0;
	width: 92rpx;
	height: 90rpx;
}

.zy0723-data4 {
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.zy0723-data4 .img {
	height: 157rpx;
	width: auto;
}