Arguments: 
  D:\NodeJs\node.exe C:\Users\<USER>\AppData\Roaming\npm\node_modules\yarn\bin\yarn.js install

PATH: 
  C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\NVIDIA Corporation\NVIDIA NvDLISR;D:\Git\cmd;D:\TortoiseGit\bin;D:\NodeJs\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;;D:\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Roaming\npm

Yarn version: 
  1.22.19

Node version: 
  16.17.0

Platform: 
  win32 x64

Trace: 
  SyntaxError: D:\Bowo2023\山东\执法App\UNI_APP_ShanDong\package.json: Unexpected token } in JSON at position 886
      at JSON.parse (<anonymous>)
      at C:\Users\<USER>\AppData\Roaming\npm\node_modules\yarn\lib\cli.js:1629:59
      at Generator.next (<anonymous>)
      at step (C:\Users\<USER>\AppData\Roaming\npm\node_modules\yarn\lib\cli.js:310:30)
      at C:\Users\<USER>\AppData\Roaming\npm\node_modules\yarn\lib\cli.js:321:13

npm manifest: 
  {
      "name": "smart_form_app",
      "version": "1.0.0",
      "description": "混合App工程模板",
      "main": "main.js",
      "scripts": {
          "test": "test"
      },
      "uni-app": {
          "scripts": {
              "mp-dingtalk": {
                  "title": "钉钉小程序",
                  "env": {
                      "UNI_PLATFORM": "mp-alipay"
                  },
                  "define": {
                      "MP-DINGTALK": true
                  }
              }
          }
      },
      "dependencies": {
          "axios": "^0.19.2",
          "bowo-form": "^1.2.3",
          "bowo-sdk": "0.3.6",
          "crypto-js": "^4.0.0",
          "dayjs": "^1.8.35",
          "fabric": "^4.3.1",
          "vue-amap": "^0.5.10",
          "vue-esign": "^1.0.5",
          "vue-i18n": "^8.24.3",
          "sass": "^1.35.1",
          "sass-loader": "^10.1.1",
          "stylus": "^0.54.8",
          "stylus-loader": "3.0.2",
      },
      "repository": {
          "type": "git",
          "url": "http://gitlab.powerdata.com.cn/Android/SmartFormWeb.git"
      },
      "author": "",
      "license": "ISC"
  }

yarn manifest: 
  No manifest

Lockfile: 
  # THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
  # yarn lockfile v1
  
  
  "@caoke90/fraction@^1.0.0":
    version "1.0.0"
    resolved "http://nexus.szboanda.com:8081/repository/npm-all/@caoke90/fraction/-/fraction-1.0.0.tgz#c8dbc111c7b09702587964943848e5aa198d8a88"
    integrity sha512-AntHdewkWrWAoyD2dSGn3QngQ14hLizbRItvwgerRsXQ24BuYpCixwZQ/F+NINmEs1+IkAlz2l/Dkt206FRIOg==
  
  abab@^2.0.0:
    version "2.0.5"
    resolved "https://registry.npmjs.org/abab/-/abab-2.0.5.tgz"
    integrity sha512-9IK9EadsbHo6jLWIpxpR6pL0sazTXV6+SQv25ZB+F7Bj9mJNaOc4nCRabwd5M/JwmUa8idz6Eci6eKfJryPs6Q==
  
  abbrev@1:
    version "1.1.1"
    resolved "https://registry.npmjs.org/abbrev/-/abbrev-1.1.1.tgz"
    integrity sha512-nne9/IiQ/hzIhY6pdDnbBtz7DjPTKrY00P/zvPSm5pOFkl6xuGrGnXn/VtTNNfNtAfZ9/1RtehkszU9qcTii0Q==
  
  acorn-globals@^4.3.2:
    version "4.3.4"
    resolved "https://registry.npmjs.org/acorn-globals/-/acorn-globals-4.3.4.tgz"
    integrity sha512-clfQEh21R+D0leSbUdWf3OcfqyaCSAQ8Ryq00bofSekfr9W8u1jyYZo6ir0xu9Gtcf7BjcHJpnbZH7JOCpP60A==
    dependencies:
      acorn "^6.0.1"
      acorn-walk "^6.0.1"
  
  acorn-walk@^6.0.1:
    version "6.2.0"
    resolved "https://registry.npmjs.org/acorn-walk/-/acorn-walk-6.2.0.tgz"
    integrity sha512-7evsyfH1cLOCdAzZAd43Cic04yKydNx0cF+7tiA19p1XnLLPU4dpCQOqpjqwokFe//vS0QqfqqjCS2JkiIs0cA==
  
  acorn@^6.0.1:
    version "6.4.2"
    resolved "https://registry.npmjs.org/acorn/-/acorn-6.4.2.tgz"
    integrity sha512-XtGIhXwF8YM8bJhGxG5kXgjkEuNGLTkoYqVE+KMR+aspr4KGYmKYg7yUe3KghyQ9yheNwLnjmzh/7+gfDBmHCQ==
  
  acorn@^7.1.0:
    version "7.4.1"
    resolved "https://registry.npmjs.org/acorn/-/acorn-7.4.1.tgz"
    integrity sha512-nQyp0o1/mNdbTO1PO6kHkwSrmgZ0MT/jCCpNiwbUjGoRN4dlBhqJtoQuCnEOKzgTVwg0ZWiCoQy6SxMebQVh8A==
  
  ajv@^6.12.3:
    version "6.12.6"
    resolved "https://registry.npmjs.org/ajv/-/ajv-6.12.6.tgz"
    integrity sha512-j3fVLgvTo527anyYyJOGTYJbG+vnnQYvE0m5mmkc1TK+nxAppkCLMIL0aZ4dblVCNoGShhm+kzE4ZUykBoMg4g==
    dependencies:
      fast-deep-equal "^3.1.1"
      fast-json-stable-stringify "^2.0.0"
      json-schema-traverse "^0.4.1"
      uri-js "^4.2.2"
  
  ansi-regex@^2.0.0:
    version "2.1.1"
    resolved "https://registry.npmjs.org/ansi-regex/-/ansi-regex-2.1.1.tgz"
    integrity sha1-w7M6te42DYbg5ijwRorn7yfWVN8=
  
  aproba@^1.0.3:
    version "1.2.0"
    resolved "https://registry.npmjs.org/aproba/-/aproba-1.2.0.tgz"
    integrity sha512-Y9J6ZjXtoYh8RnXVCMOU/ttDmk1aBjunq9vO0ta5x85WDQiQfUF9sIPBITdbiiIVcBo03Hi3jMxigBtsddlXRw==
  
  are-we-there-yet@~1.1.2:
    version "1.1.5"
    resolved "https://registry.npmjs.org/are-we-there-yet/-/are-we-there-yet-1.1.5.tgz"
    integrity sha512-5hYdAkZlcG8tOLujVDTgCT+uPX0VnpAH28gWsLfzpXYm7wP6mp5Q/gYyR7YQ0cKVJcXJnl3j2kpBan13PtQf6w==
    dependencies:
      delegates "^1.0.0"
      readable-stream "^2.0.6"
  
  array-equal@^1.0.0:
    version "1.0.0"
    resolved "https://registry.npmjs.org/array-equal/-/array-equal-1.0.0.tgz"
    integrity sha1-jCpe8kcv2ep0KwTHenUJO6J1fJM=
  
  asn1@~0.2.3:
    version "0.2.4"
    resolved "https://registry.npmjs.org/asn1/-/asn1-0.2.4.tgz"
    integrity sha512-jxwzQpLQjSmWXgwaCZE9Nz+glAG01yF1QnWgbhGwHI5A6FRIEY6IVqtHhIepHqI7/kyEyQEagBC5mBEFlIYvdg==
    dependencies:
      safer-buffer "~2.1.0"
  
  assert-plus@1.0.0, assert-plus@^1.0.0:
    version "1.0.0"
    resolved "https://registry.npmjs.org/assert-plus/-/assert-plus-1.0.0.tgz"
    integrity sha1-8S4PPF13sLHN2RRpQuTpbB5N1SU=
  
  asynckit@^0.4.0:
    version "0.4.0"
    resolved "https://registry.npmjs.org/asynckit/-/asynckit-0.4.0.tgz"
    integrity sha1-x57Zf380y48robyXkLzDZkdLS3k=
  
  aws-sign2@~0.7.0:
    version "0.7.0"
    resolved "https://registry.npmjs.org/aws-sign2/-/aws-sign2-0.7.0.tgz"
    integrity sha1-tG6JCTSpWR8tL2+G1+ap8bP+dqg=
  
  aws4@^1.8.0:
    version "1.11.0"
    resolved "https://registry.npmjs.org/aws4/-/aws4-1.11.0.tgz"
    integrity sha512-xh1Rl34h6Fi1DC2WWKfxUTVqRsNnr6LsKz2+hfwDxQJWmrx8+c7ylaqBMcHfl1U1r2dsifOvKX3LQuLNZ+XSvA==
  
  axios@^0.19.2:
    version "0.19.2"
    resolved "https://registry.npmjs.org/axios/-/axios-0.19.2.tgz"
    integrity sha512-fjgm5MvRHLhx+osE2xoekY70AhARk3a6hkN+3Io1jc00jtquGvxYlKlsFUhmUET0V5te6CcZI7lcv2Ym61mjHA==
    dependencies:
      follow-redirects "1.5.10"
  
  balanced-match@^1.0.0:
    version "1.0.0"
    resolved "https://registry.npmjs.org/balanced-match/-/balanced-match-1.0.0.tgz"
    integrity sha1-ibTRmasr7kneFk6gK4nORi1xt2c=
  
  bcrypt-pbkdf@^1.0.0:
    version "1.0.2"
    resolved "https://registry.npmjs.org/bcrypt-pbkdf/-/bcrypt-pbkdf-1.0.2.tgz"
    integrity sha1-pDAdOJtqQ/m2f/PKEaP2Y342Dp4=
    dependencies:
      tweetnacl "^0.14.3"
  
  bowo-form@^1.2.3:
    version "1.2.6"
    resolved "http://nexus.szboanda.com:8081/repository/npm-all/bowo-form/-/bowo-form-1.2.6.tgz#37d7f67f378d784f1be032970b8685b2d8538ef4"
    integrity sha512-gaywPr8nLpWuogv0VFanyAFbE/0pgEYmnN5Oof/fAcDayvkmAB10rdvNOfYSmfkAXZu7yePKmBRpgOI7o2dBnw==
    dependencies:
      bowo-sdk "0.3.6"
      crypto-js "^4.1.1"
      dayjs "^1.10.7"
      exec-mathexpress "^1.0.15"
      lodash "^4.17.21"
      pdfh5 "^1.4.2"
  
  bowo-sdk@0.3.6:
    version "0.3.6"
    resolved "http://nexus.szboanda.com:8081/repository/npm-all/bowo-sdk/-/bowo-sdk-0.3.6.tgz#274d06a72026ae3eebec856267a1241f149cd6c0"
    integrity sha512-9+2RB1+MGRamKK9nmBS+Nalr2FW7RaMr4dwk/pTFjhDtC0YOYvFCVJofBGweqNfydrVIdKXUVzIa++ql5kuT5w==
    dependencies:
      crypto-js "^4.1.1"
  
  brace-expansion@^1.1.7:
    version "1.1.11"
    resolved "https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.1.11.tgz"
    integrity sha512-iCuPHDFgrHX7H2vEI/5xpz07zSHB00TpugqhmYtVmMO6518mCuRMoOYFldEBl0g187ufozdaHgWKcYFb61qGiA==
    dependencies:
      balanced-match "^1.0.0"
      concat-map "0.0.1"
  
  browser-process-hrtime@^1.0.0:
    version "1.0.0"
    resolved "https://registry.npmjs.org/browser-process-hrtime/-/browser-process-hrtime-1.0.0.tgz"
    integrity sha512-9o5UecI3GhkpM6DrXr69PblIuWxPKk9Y0jHBRhdocZ2y7YECBFCsHm79Pr3OyR2AvjhDkabFJaDJMYRazHgsow==
  
  camelcase@^1.2.1:
    version "1.2.1"
    resolved "https://registry.npmjs.org/camelcase/-/camelcase-1.2.1.tgz"
    integrity sha1-m7UwTS4LVmmLLHWLCKPqqdqlijk=
  
  canvas@^2.6.1:
    version "2.7.0"
    resolved "https://registry.npmjs.org/canvas/-/canvas-2.7.0.tgz"
    integrity sha512-pzCxtkHb+5su5MQjTtepMDlIOtaXo277x0C0u3nMOxtkhTyQ+h2yNKhlROAaDllWgRyePAUitC08sXw26Eb6aw==
    dependencies:
      nan "^2.14.0"
      node-pre-gyp "^0.15.0"
      simple-get "^3.0.3"
  
  caseless@~0.12.0:
    version "0.12.0"
    resolved "https://registry.npmjs.org/caseless/-/caseless-0.12.0.tgz"
    integrity sha1-G2gcIf+EAzyCZUMJBolCDRhxUdw=
  
  chownr@^1.1.1:
    version "1.1.4"
    resolved "https://registry.npmjs.org/chownr/-/chownr-1.1.4.tgz"
    integrity sha512-jJ0bqzaylmJtVnNgzTeSOs8DPavpbYgEr/b0YL8/2GO3xJEhInFmhKMUnEJQjZumK7KXGFhUy89PrsJWlakBVg==
  
  code-point-at@^1.0.0:
    version "1.1.0"
    resolved "https://registry.npmjs.org/code-point-at/-/code-point-at-1.1.0.tgz"
    integrity sha1-DQcLTQQ6W+ozovGkDi7bPZpMz3c=
  
  combined-stream@^1.0.6, combined-stream@~1.0.6:
    version "1.0.8"
    resolved "https://registry.npmjs.org/combined-stream/-/combined-stream-1.0.8.tgz"
    integrity sha512-FQN4MRfuJeHf7cBbBMJFXhKSDq+2kAArBlmRBvcvFE5BB1HZKXtSFASDhdlz9zOYwxh8lDdnvmMOe/+5cdoEdg==
    dependencies:
      delayed-stream "~1.0.0"
  
  concat-map@0.0.1:
    version "0.0.1"
    resolved "https://registry.npmjs.org/concat-map/-/concat-map-0.0.1.tgz"
    integrity sha1-2Klr13/Wjfd5OnMDajug1UBdR3s=
  
  console-control-strings@^1.0.0, console-control-strings@~1.1.0:
    version "1.1.0"
    resolved "https://registry.npmjs.org/console-control-strings/-/console-control-strings-1.1.0.tgz"
    integrity sha1-PXz0Rk22RG6mRL9LOVB/mFEAjo4=
  
  core-util-is@1.0.2, core-util-is@~1.0.0:
    version "1.0.2"
    resolved "https://registry.npmjs.org/core-util-is/-/core-util-is-1.0.2.tgz"
    integrity sha1-tf1UIgqivFq1eqtxQMlAdUUDwac=
  
  crypto-js@^4.0.0:
    version "4.0.0"
    resolved "https://registry.npmjs.org/crypto-js/-/crypto-js-4.0.0.tgz"
    integrity sha512-bzHZN8Pn+gS7DQA6n+iUmBfl0hO5DJq++QP3U6uTucDtk/0iGpXd/Gg7CGR0p8tJhofJyaKoWBuJI4eAO00BBg==
  
  crypto-js@^4.1.1:
    version "4.1.1"
    resolved "http://nexus.szboanda.com:8081/repository/npm-all/crypto-js/-/crypto-js-4.1.1.tgz#9e485bcf03521041bd85844786b83fb7619736cf"
    integrity sha512-o2JlM7ydqd3Qk9CA0L4NL6mTzU2sdx96a+oOfPu8Mkl/PK51vSyoi8/rQ8NknZtk44vq15lmhAj9CIAGwgeWKw==
  
  cssom@^0.4.1:
    version "0.4.4"
    resolved "https://registry.npmjs.org/cssom/-/cssom-0.4.4.tgz"
    integrity sha512-p3pvU7r1MyyqbTk+WbNJIgJjG2VmTIaB10rI93LzVPrmDJKkzKYMtxxyAvQXR/NS6otuzveI7+7BBq3SjBS2mw==
  
  cssom@~0.3.6:
    version "0.3.8"
    resolved "https://registry.npmjs.org/cssom/-/cssom-0.3.8.tgz"
    integrity sha512-b0tGHbfegbhPJpxpiBPU2sCkigAqtM9O121le6bbOlgyV+NyGyCmVfJ6QW9eRjz8CpNfWEOYBIMIGRYkLwsIYg==
  
  cssstyle@^2.0.0:
    version "2.3.0"
    resolved "https://registry.npmjs.org/cssstyle/-/cssstyle-2.3.0.tgz"
    integrity sha512-AZL67abkUzIuvcHqk7c09cezpGNcxUxU4Ioi/05xHk4DQeTkWmGYftIE6ctU6AEt+Gn4n1lDStOtj7FKycP71A==
    dependencies:
      cssom "~0.3.6"
  
  dashdash@^1.12.0:
    version "1.14.1"
    resolved "https://registry.npmjs.org/dashdash/-/dashdash-1.14.1.tgz"
    integrity sha1-hTz6D3y+L+1d4gMmuN1YEDX24vA=
    dependencies:
      assert-plus "^1.0.0"
  
  data-urls@^1.1.0:
    version "1.1.0"
    resolved "https://registry.npmjs.org/data-urls/-/data-urls-1.1.0.tgz"
    integrity sha512-YTWYI9se1P55u58gL5GkQHW4P6VJBJ5iBT+B5a7i2Tjadhv52paJG0qHX4A0OR6/t52odI64KP2YvFpkDOi3eQ==
    dependencies:
      abab "^2.0.0"
      whatwg-mimetype "^2.2.0"
      whatwg-url "^7.0.0"
  
  dayjs@^1.10.7:
    version "1.11.5"
    resolved "http://nexus.szboanda.com:8081/repository/npm-all/dayjs/-/dayjs-1.11.5.tgz#00e8cc627f231f9499c19b38af49f56dc0ac5e93"
    integrity sha512-CAdX5Q3YW3Gclyo5Vpqkgpj8fSdLQcRuzfX6mC6Phy0nfJ0eGYOeS7m4mt2plDWLAtA4TqTakvbboHvUxfe4iA==
  
  dayjs@^1.8.35:
    version "1.8.35"
    resolved "https://registry.npmjs.org/dayjs/-/dayjs-1.8.35.tgz"
    integrity sha512-isAbIEenO4ilm6f8cpqvgjZCsuerDAz2Kb7ri201AiNn58aqXuaLJEnCtfIMdCvERZHNGRY5lDMTr/jdAnKSWQ==
  
  debug@=3.1.0:
    version "3.1.0"
    resolved "https://registry.npmjs.org/debug/-/debug-3.1.0.tgz"
    integrity sha512-OX8XqP7/1a9cqkxYw2yXss15f26NKWBpDXQd0/uK/KPqdQhxbPa994hnzjcE2VqQpDslf55723cKPUOGSmMY3g==
    dependencies:
      ms "2.0.0"
  
  debug@^3.2.6:
    version "3.2.7"
    resolved "https://registry.npmjs.org/debug/-/debug-3.2.7.tgz"
    integrity sha512-CFjzYYAi4ThfiQvizrFQevTTXHtnCqWfe7x1AhgEscTz6ZbLbfoLRLPugTQyBth6f8ZERVUSyWHFD/7Wu4t1XQ==
    dependencies:
      ms "^2.1.1"
  
  decompress-response@^4.2.0:
    version "4.2.1"
    resolved "https://registry.npmjs.org/decompress-response/-/decompress-response-4.2.1.tgz"
    integrity sha512-jOSne2qbyE+/r8G1VU+G/82LBs2Fs4LAsTiLSHOCOMZQl2OKZ6i8i4IyHemTe+/yIXOtTcRQMzPcgyhoFlqPkw==
    dependencies:
      mimic-response "^2.0.0"
  
  deep-extend@^0.6.0:
    version "0.6.0"
    resolved "https://registry.npmjs.org/deep-extend/-/deep-extend-0.6.0.tgz"
    integrity sha512-LOHxIOaPYdHlJRtCQfDIVZtfw/ufM8+rVj649RIHzcm/vGwQRXFt6OPqIFWsm2XEMrNIEtWR64sY1LEKD2vAOA==
  
  deep-is@~0.1.3:
    version "0.1.3"
    resolved "https://registry.npmjs.org/deep-is/-/deep-is-0.1.3.tgz"
    integrity sha1-s2nW+128E+7PUk+RsHD+7cNXzzQ=
  
  delayed-stream@~1.0.0:
    version "1.0.0"
    resolved "https://registry.npmjs.org/delayed-stream/-/delayed-stream-1.0.0.tgz"
    integrity sha1-3zrhmayt+31ECqrgsp4icrJOxhk=
  
  delegates@^1.0.0:
    version "1.0.0"
    resolved "https://registry.npmjs.org/delegates/-/delegates-1.0.0.tgz"
    integrity sha1-hMbhWbgZBP3KWaDvRM2HDTElD5o=
  
  detect-libc@^1.0.2:
    version "1.0.3"
    resolved "https://registry.npmjs.org/detect-libc/-/detect-libc-1.0.3.tgz"
    integrity sha1-+hN8S9aY7fVc1c0CrFWfkaTEups=
  
  domexception@^1.0.1:
    version "1.0.1"
    resolved "https://registry.npmjs.org/domexception/-/domexception-1.0.1.tgz"
    integrity sha512-raigMkn7CJNNo6Ihro1fzG7wr3fHuYVytzquZKX5n0yizGsTcYgzdIUwj1X9pK0VvjeihV+XiclP+DjwbsSKug==
    dependencies:
      webidl-conversions "^4.0.2"
  
  ecc-jsbn@~0.1.1:
    version "0.1.2"
    resolved "https://registry.npmjs.org/ecc-jsbn/-/ecc-jsbn-0.1.2.tgz"
    integrity sha1-OoOpBOVDUyh4dMVkt1SThoSamMk=
    dependencies:
      jsbn "~0.1.0"
      safer-buffer "^2.1.0"
  
  escodegen@^1.11.1:
    version "1.14.3"
    resolved "https://registry.npmjs.org/escodegen/-/escodegen-1.14.3.tgz"
    integrity sha512-qFcX0XJkdg+PB3xjZZG/wKSuT1PnQWx57+TVSjIMmILd2yC/6ByYElPwJnslDsuWuSAp4AwJGumarAAmJch5Kw==
    dependencies:
      esprima "^4.0.1"
      estraverse "^4.2.0"
      esutils "^2.0.2"
      optionator "^0.8.1"
    optionalDependencies:
      source-map "~0.6.1"
  
  esprima@^4.0.1:
    version "4.0.1"
    resolved "https://registry.npmjs.org/esprima/-/esprima-4.0.1.tgz"
    integrity sha512-eGuFFw7Upda+g4p+QHvnW0RyTX/SVeJBDM/gCtMARO0cLuT2HcEKnTPvhjV6aGeqrCB/sbNop0Kszm0jsaWU4A==
  
  estraverse@^4.2.0:
    version "4.3.0"
    resolved "https://registry.npmjs.org/estraverse/-/estraverse-4.3.0.tgz"
    integrity sha512-39nnKffWz8xN1BU/2c79n9nB9HDzo0niYUqx6xyqUnyoAnQyyWpOTdZEeiCch8BBu515t4wp9ZmgVfVhn9EBpw==
  
  esutils@^2.0.2:
    version "2.0.3"
    resolved "https://registry.npmjs.org/esutils/-/esutils-2.0.3.tgz"
    integrity sha512-kVscqXk4OCp68SZ0dkgEKVi6/8ij300KBWTJq32P/dYeWTSwK41WyTxalN1eRmA5Z9UU/LX9D7FWSmV9SAYx6g==
  
  exec-mathexpress@^1.0.15:
    version "1.0.15"
    resolved "http://nexus.szboanda.com:8081/repository/npm-all/exec-mathexpress/-/exec-mathexpress-1.0.15.tgz#a1d2d24ee32fa1566efd4b28559ab86b54bfe520"
    integrity sha512-oqJr1Q08A9OdtIiPASyWV1KCtCg3ulQjmlQWxQmuQSEmVYpHDB3Nh71t5KhvPkE4romIT3RfRBdRDmo4YDtXeQ==
    dependencies:
      "@caoke90/fraction" "^1.0.0"
  
  extend@~3.0.2:
    version "3.0.2"
    resolved "https://registry.npmjs.org/extend/-/extend-3.0.2.tgz"
    integrity sha512-fjquC59cD7CyW6urNXK0FBufkZcoiGG80wTuPujX590cB5Ttln20E2UB4S/WARVqhXffZl2LNgS+gQdPIIim/g==
  
  extsprintf@1.3.0, extsprintf@^1.2.0:
    version "1.3.0"
    resolved "https://registry.npmjs.org/extsprintf/-/extsprintf-1.3.0.tgz"
    integrity sha1-lpGEQOMEGnpBT4xS48V06zw+HgU=
  
  fabric@^4.3.1:
    version "4.3.1"
    resolved "https://registry.npmjs.org/fabric/-/fabric-4.3.1.tgz"
    integrity sha512-KTaGHu7re3liNumzMWmgyD35RCKFdksxHr/kFWQ7u+Vo3tdNRGBiVDT8pWLl4U8W7UenBfziHWYjj1I7pOhsxA==
    optionalDependencies:
      canvas "^2.6.1"
      jsdom "^15.2.1"
  
  fast-deep-equal@^3.1.1:
    version "3.1.3"
    resolved "https://registry.npmjs.org/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz"
    integrity sha512-f3qQ9oQy9j2AhBe/H9VC91wLmKBCCU/gDOnKNAYG5hswO7BLKj09Hc5HYNz9cGI++xlpDCIgDaitVs03ATR84Q==
  
  fast-json-stable-stringify@^2.0.0:
    version "2.1.0"
    resolved "https://registry.npmjs.org/fast-json-stable-stringify/-/fast-json-stable-stringify-2.1.0.tgz"
    integrity sha512-lhd/wF+Lk98HZoTCtlVraHtfh5XYijIjalXck7saUtuanSDyLMxnHhSXEDJqHxD7msR8D0uCmqlkwjCV8xvwHw==
  
  fast-levenshtein@~2.0.6:
    version "2.0.6"
    resolved "https://registry.npmjs.org/fast-levenshtein/-/fast-levenshtein-2.0.6.tgz"
    integrity sha1-PYpcZog6FqMMqGQ+hR8Zuqd5eRc=
  
  follow-redirects@1.5.10:
    version "1.5.10"
    resolved "https://registry.npmjs.org/follow-redirects/-/follow-redirects-1.5.10.tgz"
    integrity sha512-0V5l4Cizzvqt5D44aTXbFZz+FtyXV1vrDN6qrelxtfYQKW0KO0W2T/hkE8xvGa/540LkZlkaUjO4ailYTFtHVQ==
    dependencies:
      debug "=3.1.0"
  
  forever-agent@~0.6.1:
    version "0.6.1"
    resolved "https://registry.npmjs.org/forever-agent/-/forever-agent-0.6.1.tgz"
    integrity sha1-+8cfDEGt6zf5bFd60e1C2P2sypE=
  
  form-data@~2.3.2:
    version "2.3.3"
    resolved "https://registry.npmjs.org/form-data/-/form-data-2.3.3.tgz"
    integrity sha512-1lLKB2Mu3aGP1Q/2eCOx0fNbRMe7XdwktwOruhfqqd0rIJWwN4Dh+E3hrPSlDCXnSR7UtZ1N38rVXm+6+MEhJQ==
    dependencies:
      asynckit "^0.4.0"
      combined-stream "^1.0.6"
      mime-types "^2.1.12"
  
  fs-minipass@^1.2.5:
    version "1.2.7"
    resolved "https://registry.npmjs.org/fs-minipass/-/fs-minipass-1.2.7.tgz"
    integrity sha512-GWSSJGFy4e9GUeCcbIkED+bgAoFyj7XF1mV8rma3QW4NIqX9Kyx79N/PF61H5udOV3aY1IaMLs6pGbH71nlCTA==
    dependencies:
      minipass "^2.6.0"
  
  fs.realpath@^1.0.0:
    version "1.0.0"
    resolved "https://registry.npmjs.org/fs.realpath/-/fs.realpath-1.0.0.tgz"
    integrity sha1-FQStJSMVjKpA20onh8sBQRmU6k8=
  
  gauge@~2.7.3:
    version "2.7.4"
    resolved "https://registry.npmjs.org/gauge/-/gauge-2.7.4.tgz"
    integrity sha1-LANAXHU4w51+s3sxcCLjJfsBi/c=
    dependencies:
      aproba "^1.0.3"
      console-control-strings "^1.0.0"
      has-unicode "^2.0.0"
      object-assign "^4.1.0"
      signal-exit "^3.0.0"
      string-width "^1.0.1"
      strip-ansi "^3.0.1"
      wide-align "^1.1.0"
  
  getpass@^0.1.1:
    version "0.1.7"
    resolved "https://registry.npmjs.org/getpass/-/getpass-0.1.7.tgz"
    integrity sha1-Xv+OPmhNVprkyysSgmBOi6YhSfo=
    dependencies:
      assert-plus "^1.0.0"
  
  glob@^7.1.3:
    version "7.1.6"
    resolved "https://registry.npmjs.org/glob/-/glob-7.1.6.tgz"
    integrity sha512-LwaxwyZ72Lk7vZINtNNrywX0ZuLyStrdDtabefZKAY5ZGJhVtgdznluResxNmPitE0SAO+O26sWTHeKSI2wMBA==
    dependencies:
      fs.realpath "^1.0.0"
      inflight "^1.0.4"
      inherits "2"
      minimatch "^3.0.4"
      once "^1.3.0"
      path-is-absolute "^1.0.0"
  
  har-schema@^2.0.0:
    version "2.0.0"
    resolved "https://registry.npmjs.org/har-schema/-/har-schema-2.0.0.tgz"
    integrity sha1-qUwiJOvKwEeCoNkDVSHyRzW37JI=
  
  har-validator@~5.1.3:
    version "5.1.5"
    resolved "https://registry.npmjs.org/har-validator/-/har-validator-5.1.5.tgz"
    integrity sha512-nmT2T0lljbxdQZfspsno9hgrG3Uir6Ks5afism62poxqBM6sDnMEuPmzTq8XN0OEwqKLLdh1jQI3qyE66Nzb3w==
    dependencies:
      ajv "^6.12.3"
      har-schema "^2.0.0"
  
  has-unicode@^2.0.0:
    version "2.0.1"
    resolved "https://registry.npmjs.org/has-unicode/-/has-unicode-2.0.1.tgz"
    integrity sha1-4Ob+aijPUROIVeCG0Wkedx3iqLk=
  
  html-encoding-sniffer@^1.0.2:
    version "1.0.2"
    resolved "https://registry.npmjs.org/html-encoding-sniffer/-/html-encoding-sniffer-1.0.2.tgz"
    integrity sha512-71lZziiDnsuabfdYiUeWdCVyKuqwWi23L8YeIgV9jSSZHCtb6wB1BKWooH7L3tn4/FuZJMVWyNaIDr4RGmaSYw==
    dependencies:
      whatwg-encoding "^1.0.1"
  
  http-signature@~1.2.0:
    version "1.2.0"
    resolved "https://registry.npmjs.org/http-signature/-/http-signature-1.2.0.tgz"
    integrity sha1-muzZJRFHcvPZW2WmCruPfBj7rOE=
    dependencies:
      assert-plus "^1.0.0"
      jsprim "^1.2.2"
      sshpk "^1.7.0"
  
  iconv-lite@0.4.24, iconv-lite@^0.4.4:
    version "0.4.24"
    resolved "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.4.24.tgz"
    integrity sha512-v3MXnZAcvnywkTUEZomIActle7RXXeedOR31wwl7VlyoXO4Qi9arvSenNQWne1TcRwhCL1HwLI21bEqdpj8/rA==
    dependencies:
      safer-buffer ">= 2.1.2 < 3"
  
  ignore-walk@^3.0.1:
    version "3.0.3"
    resolved "https://registry.npmjs.org/ignore-walk/-/ignore-walk-3.0.3.tgz"
    integrity sha512-m7o6xuOaT1aqheYHKf8W6J5pYH85ZI9w077erOzLje3JsB1gkafkAhHHY19dqjulgIZHFm32Cp5uNZgcQqdJKw==
    dependencies:
      minimatch "^3.0.4"
  
  inflight@^1.0.4:
    version "1.0.6"
    resolved "https://registry.npmjs.org/inflight/-/inflight-1.0.6.tgz"
    integrity sha1-Sb1jMdfQLQwJvJEKEHW6gWW1bfk=
    dependencies:
      once "^1.3.0"
      wrappy "1"
  
  inherits@2, inherits@~2.0.3:
    version "2.0.4"
    resolved "https://registry.npmjs.org/inherits/-/inherits-2.0.4.tgz"
    integrity sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ==
  
  ini@~1.3.0:
    version "1.3.8"
    resolved "https://registry.npmjs.org/ini/-/ini-1.3.8.tgz"
    integrity sha512-JV/yugV2uzW5iMRSiZAyDtQd+nxtUnjeLt0acNdw98kKLrvuRVyB80tsREOE7yvGVgalhZ6RNXCmEHkUKBKxew==
  
  ip-regex@^2.1.0:
    version "2.1.0"
    resolved "https://registry.npmjs.org/ip-regex/-/ip-regex-2.1.0.tgz"
    integrity sha1-+ni/XS5pE8kRzp+BnuUUa7bYROk=
  
  is-fullwidth-code-point@^1.0.0:
    version "1.0.0"
    resolved "https://registry.npmjs.org/is-fullwidth-code-point/-/is-fullwidth-code-point-1.0.0.tgz"
    integrity sha1-754xOG8DGn8NZDr4L95QxFfvAMs=
    dependencies:
      number-is-nan "^1.0.0"
  
  is-typedarray@~1.0.0:
    version "1.0.0"
    resolved "https://registry.npmjs.org/is-typedarray/-/is-typedarray-1.0.0.tgz"
    integrity sha1-5HnICFjfDBsR3dppQPlgEfzaSpo=
  
  isarray@~1.0.0:
    version "1.0.0"
    resolved "https://registry.npmjs.org/isarray/-/isarray-1.0.0.tgz"
    integrity sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE=
  
  isstream@~0.1.2:
    version "0.1.2"
    resolved "https://registry.npmjs.org/isstream/-/isstream-0.1.2.tgz"
    integrity sha1-R+Y/evVa+m+S4VAOaQ64uFKcCZo=
  
  jsbn@~0.1.0:
    version "0.1.1"
    resolved "https://registry.npmjs.org/jsbn/-/jsbn-0.1.1.tgz"
    integrity sha1-peZUwuWi3rXyAdls77yoDA7y9RM=
  
  jsdom@^15.2.1:
    version "15.2.1"
    resolved "https://registry.npmjs.org/jsdom/-/jsdom-15.2.1.tgz"
    integrity sha512-fAl1W0/7T2G5vURSyxBzrJ1LSdQn6Tr5UX/xD4PXDx/PDgwygedfW6El/KIj3xJ7FU61TTYnc/l/B7P49Eqt6g==
    dependencies:
      abab "^2.0.0"
      acorn "^7.1.0"
      acorn-globals "^4.3.2"
      array-equal "^1.0.0"
      cssom "^0.4.1"
      cssstyle "^2.0.0"
      data-urls "^1.1.0"
      domexception "^1.0.1"
      escodegen "^1.11.1"
      html-encoding-sniffer "^1.0.2"
      nwsapi "^2.2.0"
      parse5 "5.1.0"
      pn "^1.1.0"
      request "^2.88.0"
      request-promise-native "^1.0.7"
      saxes "^3.1.9"
      symbol-tree "^3.2.2"
      tough-cookie "^3.0.1"
      w3c-hr-time "^1.0.1"
      w3c-xmlserializer "^1.1.2"
      webidl-conversions "^4.0.2"
      whatwg-encoding "^1.0.5"
      whatwg-mimetype "^2.3.0"
      whatwg-url "^7.0.0"
      ws "^7.0.0"
      xml-name-validator "^3.0.0"
  
  json-schema-traverse@^0.4.1:
    version "0.4.1"
    resolved "https://registry.npmjs.org/json-schema-traverse/-/json-schema-traverse-0.4.1.tgz"
    integrity sha512-xbbCH5dCYU5T8LcEhhuh7HJ88HXuW3qsI3Y0zOZFKfZEHcpWiHU/Jxzk629Brsab/mMiHQti9wMP+845RPe3Vg==
  
  json-schema@0.2.3:
    version "0.2.3"
    resolved "https://registry.npmjs.org/json-schema/-/json-schema-0.2.3.tgz"
    integrity sha1-tIDIkuWaLwWVTOcnvT8qTogvnhM=
  
  json-stringify-safe@~5.0.1:
    version "5.0.1"
    resolved "https://registry.npmjs.org/json-stringify-safe/-/json-stringify-safe-5.0.1.tgz"
    integrity sha1-Epai1Y/UXxmg9s4B1lcB4sc1tus=
  
  jsprim@^1.2.2:
    version "1.4.1"
    resolved "https://registry.npmjs.org/jsprim/-/jsprim-1.4.1.tgz"
    integrity sha1-MT5mvB5cwG5Di8G3SZwuXFastqI=
    dependencies:
      assert-plus "1.0.0"
      extsprintf "1.3.0"
      json-schema "0.2.3"
      verror "1.10.0"
  
  levn@~0.3.0:
    version "0.3.0"
    resolved "https://registry.npmjs.org/levn/-/levn-0.3.0.tgz"
    integrity sha1-OwmSTt+fCDwEkP3UwLxEIeBHZO4=
    dependencies:
      prelude-ls "~1.1.2"
      type-check "~0.3.2"
  
  lodash.sortby@^4.7.0:
    version "4.7.0"
    resolved "https://registry.npmjs.org/lodash.sortby/-/lodash.sortby-4.7.0.tgz"
    integrity sha1-7dFMgk4sycHgsKG0K7UhBRakJDg=
  
  lodash@^4.17.19, lodash@^4.17.21:
    version "4.17.21"
    resolved "http://nexus.szboanda.com:8081/repository/npm-all/lodash/-/lodash-4.17.21.tgz#679591c564c3bffaae8454cf0b3df370c3d6911c"
    integrity sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg==
  
  mime-db@1.46.0:
    version "1.46.0"
    resolved "https://registry.npmjs.org/mime-db/-/mime-db-1.46.0.tgz"
    integrity sha512-svXaP8UQRZ5K7or+ZmfNhg2xX3yKDMUzqadsSqi4NCH/KomcH75MAMYAGVlvXn4+b/xOPhS3I2uHKRUzvjY7BQ==
  
  mime-types@^2.1.12, mime-types@~2.1.19:
    version "2.1.29"
    resolved "https://registry.npmjs.org/mime-types/-/mime-types-2.1.29.tgz"
    integrity sha512-Y/jMt/S5sR9OaqteJtslsFZKWOIIqMACsJSiHghlCAyhf7jfVYjKBmLiX8OgpWeW+fjJ2b+Az69aPFPkUOY6xQ==
    dependencies:
      mime-db "1.46.0"
  
  mimic-response@^2.0.0:
    version "2.1.0"
    resolved "https://registry.npmjs.org/mimic-response/-/mimic-response-2.1.0.tgz"
    integrity sha512-wXqjST+SLt7R009ySCglWBCFpjUygmCIfD790/kVbiGmUgfYGuB14PiTd5DwVxSV4NcYHjzMkoj5LjQZwTQLEA==
  
  minimatch@^3.0.4:
    version "3.0.4"
    resolved "https://registry.npmjs.org/minimatch/-/minimatch-3.0.4.tgz"
    integrity sha512-yJHVQEhyqPLUTgt9B83PXu6W3rx4MvvHvSUvToogpwoGDOUQ+yDrR0HRot+yOCdCO7u4hX3pWft6kWBBcqh0UA==
    dependencies:
      brace-expansion "^1.1.7"
  
  minimist@^1.2.0, minimist@^1.2.5:
    version "1.2.5"
    resolved "https://registry.npmjs.org/minimist/-/minimist-1.2.5.tgz"
    integrity sha512-FM9nNUYrRBAELZQT3xeZQ7fmMOBg6nWNmJKTcgsJeaLstP/UODVpGsr5OhXhhXg6f+qtJ8uiZ+PUxkDWcgIXLw==
  
  minipass@^2.6.0, minipass@^2.8.6, minipass@^2.9.0:
    version "2.9.0"
    resolved "https://registry.npmjs.org/minipass/-/minipass-2.9.0.tgz"
    integrity sha512-wxfUjg9WebH+CUDX/CdbRlh5SmfZiy/hpkxaRI16Y9W56Pa75sWgd/rvFilSgrauD9NyFymP/+JFV3KwzIsJeg==
    dependencies:
      safe-buffer "^5.1.2"
      yallist "^3.0.0"
  
  minizlib@^1.2.1:
    version "1.3.3"
    resolved "https://registry.npmjs.org/minizlib/-/minizlib-1.3.3.tgz"
    integrity sha512-6ZYMOEnmVsdCeTJVE0W9ZD+pVnE8h9Hma/iOwwRDsdQoePpoX56/8B6z3P9VNwppJuBKNRuFDRNRqRWexT9G9Q==
    dependencies:
      minipass "^2.9.0"
  
  mkdirp@^0.5.0, mkdirp@^0.5.3:
    version "0.5.5"
    resolved "https://registry.npmjs.org/mkdirp/-/mkdirp-0.5.5.tgz"
    integrity sha512-NKmAlESf6jMGym1++R0Ra7wvhV+wFW63FaSOFPwRahvea0gMUcGUhVeAg/0BC0wiv9ih5NYPB1Wn1UEI1/L+xQ==
    dependencies:
      minimist "^1.2.5"
  
  ms@2.0.0:
    version "2.0.0"
    resolved "https://registry.npmjs.org/ms/-/ms-2.0.0.tgz"
    integrity sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=
  
  ms@^2.1.1:
    version "2.1.3"
    resolved "https://registry.npmjs.org/ms/-/ms-2.1.3.tgz"
    integrity sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA==
  
  nan@^2.14.0:
    version "2.14.2"
    resolved "https://registry.npmjs.org/nan/-/nan-2.14.2.tgz"
    integrity sha512-M2ufzIiINKCuDfBSAUr1vWQ+vuVcA9kqx8JJUsbQi6yf1uGRyb7HfpdfUr5qLXf3B/t8dPvcjhKMmlfnP47EzQ==
  
  needle@^2.5.0:
    version "2.6.0"
    resolved "https://registry.npmjs.org/needle/-/needle-2.6.0.tgz"
    integrity sha512-KKYdza4heMsEfSWD7VPUIz3zX2XDwOyX2d+geb4vrERZMT5RMU6ujjaD+I5Yr54uZxQ2w6XRTAhHBbSCyovZBg==
    dependencies:
      debug "^3.2.6"
      iconv-lite "^0.4.4"
      sax "^1.2.4"
  
  node-pre-gyp@^0.15.0:
    version "0.15.0"
    resolved "https://registry.npmjs.org/node-pre-gyp/-/node-pre-gyp-0.15.0.tgz"
    integrity sha512-7QcZa8/fpaU/BKenjcaeFF9hLz2+7S9AqyXFhlH/rilsQ/hPZKK32RtR5EQHJElgu+q5RfbJ34KriI79UWaorA==
    dependencies:
      detect-libc "^1.0.2"
      mkdirp "^0.5.3"
      needle "^2.5.0"
      nopt "^4.0.1"
      npm-packlist "^1.1.6"
      npmlog "^4.0.2"
      rc "^1.2.7"
      rimraf "^2.6.1"
      semver "^5.3.0"
      tar "^4.4.2"
  
  nopt@^4.0.1:
    version "4.0.3"
    resolved "https://registry.npmjs.org/nopt/-/nopt-4.0.3.tgz"
    integrity sha512-CvaGwVMztSMJLOeXPrez7fyfObdZqNUK1cPAEzLHrTybIua9pMdmmPR5YwtfNftIOMv3DPUhFaxsZMNTQO20Kg==
    dependencies:
      abbrev "1"
      osenv "^0.1.4"
  
  npm-bundled@^1.0.1:
    version "1.1.1"
    resolved "https://registry.npmjs.org/npm-bundled/-/npm-bundled-1.1.1.tgz"
    integrity sha512-gqkfgGePhTpAEgUsGEgcq1rqPXA+tv/aVBlgEzfXwA1yiUJF7xtEt3CtVwOjNYQOVknDk0F20w58Fnm3EtG0fA==
    dependencies:
      npm-normalize-package-bin "^1.0.1"
  
  npm-normalize-package-bin@^1.0.1:
    version "1.0.1"
    resolved "https://registry.npmjs.org/npm-normalize-package-bin/-/npm-normalize-package-bin-1.0.1.tgz"
    integrity sha512-EPfafl6JL5/rU+ot6P3gRSCpPDW5VmIzX959Ob1+ySFUuuYHWHekXpwdUZcKP5C+DS4GEtdJluwBjnsNDl+fSA==
  
  npm-packlist@^1.1.6:
    version "1.4.8"
    resolved "https://registry.npmjs.org/npm-packlist/-/npm-packlist-1.4.8.tgz"
    integrity sha512-5+AZgwru5IevF5ZdnFglB5wNlHG1AOOuw28WhUq8/8emhBmLv6jX5by4WJCh7lW0uSYZYS6DXqIsyZVIXRZU9A==
    dependencies:
      ignore-walk "^3.0.1"
      npm-bundled "^1.0.1"
      npm-normalize-package-bin "^1.0.1"
  
  npmlog@^4.0.2:
    version "4.1.2"
    resolved "https://registry.npmjs.org/npmlog/-/npmlog-4.1.2.tgz"
    integrity sha512-2uUqazuKlTaSI/dC8AzicUck7+IrEaOnN/e0jd3Xtt1KcGpwx30v50mL7oPyr/h9bL3E4aZccVwpwP+5W9Vjkg==
    dependencies:
      are-we-there-yet "~1.1.2"
      console-control-strings "~1.1.0"
      gauge "~2.7.3"
      set-blocking "~2.0.0"
  
  number-is-nan@^1.0.0:
    version "1.0.1"
    resolved "https://registry.npmjs.org/number-is-nan/-/number-is-nan-1.0.1.tgz"
    integrity sha1-CXtgK1NCKlIsGvuHkDGDNpQaAR0=
  
  nwsapi@^2.2.0:
    version "2.2.0"
    resolved "https://registry.npmjs.org/nwsapi/-/nwsapi-2.2.0.tgz"
    integrity sha512-h2AatdwYH+JHiZpv7pt/gSX1XoRGb7L/qSIeuqA6GwYoF9w1vP1cw42TO0aI2pNyshRK5893hNSl+1//vHK7hQ==
  
  oauth-sign@~0.9.0:
    version "0.9.0"
    resolved "https://registry.npmjs.org/oauth-sign/-/oauth-sign-0.9.0.tgz"
    integrity sha512-fexhUFFPTGV8ybAtSIGbV6gOkSv8UtRbDBnAyLQw4QPKkgNlsH2ByPGtMUqdWkos6YCRmAqViwgZrJc/mRDzZQ==
  
  object-assign@^4.1.0:
    version "4.1.1"
    resolved "https://registry.npmjs.org/object-assign/-/object-assign-4.1.1.tgz"
    integrity sha1-IQmtx5ZYh8/AXLvUQsrIv7s2CGM=
  
  once@^1.3.0, once@^1.3.1:
    version "1.4.0"
    resolved "https://registry.npmjs.org/once/-/once-1.4.0.tgz"
    integrity sha1-WDsap3WWHUsROsF9nFC6753Xa9E=
    dependencies:
      wrappy "1"
  
  optionator@^0.8.1:
    version "0.8.3"
    resolved "https://registry.npmjs.org/optionator/-/optionator-0.8.3.tgz"
    integrity sha512-+IW9pACdk3XWmmTXG8m3upGUJst5XRGzxMRjXzAuJ1XnIFNvfhjjIuYkDvysnPQ7qzqVzLt78BCruntqRhWQbA==
    dependencies:
      deep-is "~0.1.3"
      fast-levenshtein "~2.0.6"
      levn "~0.3.0"
      prelude-ls "~1.1.2"
      type-check "~0.3.2"
      word-wrap "~1.2.3"
  
  os-homedir@^1.0.0:
    version "1.0.2"
    resolved "https://registry.npmjs.org/os-homedir/-/os-homedir-1.0.2.tgz"
    integrity sha1-/7xJiDNuDoM94MFox+8VISGqf7M=
  
  os-tmpdir@^1.0.0:
    version "1.0.2"
    resolved "https://registry.npmjs.org/os-tmpdir/-/os-tmpdir-1.0.2.tgz"
    integrity sha1-u+Z0BseaqFxc/sdm/lc0VV36EnQ=
  
  osenv@^0.1.4:
    version "0.1.5"
    resolved "https://registry.npmjs.org/osenv/-/osenv-0.1.5.tgz"
    integrity sha512-0CWcCECdMVc2Rw3U5w9ZjqX6ga6ubk1xDVKxtBQPK7wis/0F2r9T6k4ydGYhecl7YUBxBVxhL5oisPsNxAPe2g==
    dependencies:
      os-homedir "^1.0.0"
      os-tmpdir "^1.0.0"
  
  parse5@5.1.0:
    version "5.1.0"
    resolved "https://registry.npmjs.org/parse5/-/parse5-5.1.0.tgz"
    integrity sha512-fxNG2sQjHvlVAYmzBZS9YlDp6PTSSDwa98vkD4QgVDDCAo84z5X1t5XyJQ62ImdLXx5NdIIfihey6xpum9/gRQ==
  
  path-is-absolute@^1.0.0:
    version "1.0.1"
    resolved "https://registry.npmjs.org/path-is-absolute/-/path-is-absolute-1.0.1.tgz"
    integrity sha1-F0uSaHNVNP+8es5r9TpanhtcX18=
  
  pdfh5@^1.4.2:
    version "1.4.3"
    resolved "http://nexus.szboanda.com:8081/repository/npm-all/pdfh5/-/pdfh5-1.4.3.tgz#47690f07493ad1846d7110aae0fab6fd382b6545"
    integrity sha512-gpIV1caef4Eibi9MEtWWMZUFGHFs+1jhm7OFZ309zEO9fxP3yJqYsp8V3LsQ9yjBkRysk/aMqFaZx0wNSMPbsg==
  
  performance-now@^2.1.0:
    version "2.1.0"
    resolved "https://registry.npmjs.org/performance-now/-/performance-now-2.1.0.tgz"
    integrity sha1-Ywn04OX6kT7BxpMHrjZLSzd8nns=
  
  pn@^1.1.0:
    version "1.1.0"
    resolved "https://registry.npmjs.org/pn/-/pn-1.1.0.tgz"
    integrity sha512-2qHaIQr2VLRFoxe2nASzsV6ef4yOOH+Fi9FBOVH6cqeSgUnoyySPZkxzLuzd+RYOQTRpROA0ztTMqxROKSb/nA==
  
  prelude-ls@~1.1.2:
    version "1.1.2"
    resolved "https://registry.npmjs.org/prelude-ls/-/prelude-ls-1.1.2.tgz"
    integrity sha1-IZMqVJ9eUv/ZqCf1cOBL5iqX2lQ=
  
  process-nextick-args@~2.0.0:
    version "2.0.1"
    resolved "https://registry.npmjs.org/process-nextick-args/-/process-nextick-args-2.0.1.tgz"
    integrity sha512-3ouUOpQhtgrbOa17J7+uxOTpITYWaGP7/AhoR3+A+/1e9skrzelGi/dXzEYyvbxubEF6Wn2ypscTKiKJFFn1ag==
  
  psl@^1.1.28:
    version "1.8.0"
    resolved "https://registry.npmjs.org/psl/-/psl-1.8.0.tgz"
    integrity sha512-RIdOzyoavK+hA18OGGWDqUTsCLhtA7IcZ/6NCs4fFJaHBDab+pDDmDIByWFRQJq2Cd7r1OoQxBGKOaztq+hjIQ==
  
  punycode@^2.1.0, punycode@^2.1.1:
    version "2.1.1"
    resolved "https://registry.npmjs.org/punycode/-/punycode-2.1.1.tgz"
    integrity sha512-XRsRjdf+j5ml+y/6GKHPZbrF/8p2Yga0JPtdqTIY2Xe5ohJPD9saDJJLPvp9+NSBprVvevdXZybnj2cv8OEd0A==
  
  qs@~6.5.2:
    version "6.5.2"
    resolved "https://registry.npmjs.org/qs/-/qs-6.5.2.tgz"
    integrity sha512-N5ZAX4/LxJmF+7wN74pUD6qAh9/wnvdQcjq9TZjevvXzSUo7bfmw91saqMjzGS2xq91/odN2dW/WOl7qQHNDGA==
  
  rc@^1.2.7:
    version "1.2.8"
    resolved "https://registry.npmjs.org/rc/-/rc-1.2.8.tgz"
    integrity sha512-y3bGgqKj3QBdxLbLkomlohkvsA8gdAiUQlSBJnBhfn+BPxg4bc62d8TcBW15wavDfgexCgccckhcZvywyQYPOw==
    dependencies:
      deep-extend "^0.6.0"
      ini "~1.3.0"
      minimist "^1.2.0"
      strip-json-comments "~2.0.1"
  
  readable-stream@^2.0.6:
    version "2.3.7"
    resolved "https://registry.npmjs.org/readable-stream/-/readable-stream-2.3.7.tgz"
    integrity sha512-Ebho8K4jIbHAxnuxi7o42OrZgF/ZTNcsZj6nRKyUmkhLFq8CHItp/fy6hQZuZmP/n3yZ9VBUbp4zz/mX8hmYPw==
    dependencies:
      core-util-is "~1.0.0"
      inherits "~2.0.3"
      isarray "~1.0.0"
      process-nextick-args "~2.0.0"
      safe-buffer "~5.1.1"
      string_decoder "~1.1.1"
      util-deprecate "~1.0.1"
  
  request-promise-core@1.1.4:
    version "1.1.4"
    resolved "https://registry.npmjs.org/request-promise-core/-/request-promise-core-1.1.4.tgz"
    integrity sha512-TTbAfBBRdWD7aNNOoVOBH4pN/KigV6LyapYNNlAPA8JwbovRti1E88m3sYAwsLi5ryhPKsE9APwnjFTgdUjTpw==
    dependencies:
      lodash "^4.17.19"
  
  request-promise-native@^1.0.7:
    version "1.0.9"
    resolved "https://registry.npmjs.org/request-promise-native/-/request-promise-native-1.0.9.tgz"
    integrity sha512-wcW+sIUiWnKgNY0dqCpOZkUbF/I+YPi+f09JZIDa39Ec+q82CpSYniDp+ISgTTbKmnpJWASeJBPZmoxH84wt3g==
    dependencies:
      request-promise-core "1.1.4"
      stealthy-require "^1.1.1"
      tough-cookie "^2.3.3"
  
  request@^2.88.0:
    version "2.88.2"
    resolved "https://registry.npmjs.org/request/-/request-2.88.2.tgz"
    integrity sha512-MsvtOrfG9ZcrOwAW+Qi+F6HbD0CWXEh9ou77uOb7FM2WPhwT7smM833PzanhJLsgXjN89Ir6V2PczXNnMpwKhw==
    dependencies:
      aws-sign2 "~0.7.0"
      aws4 "^1.8.0"
      caseless "~0.12.0"
      combined-stream "~1.0.6"
      extend "~3.0.2"
      forever-agent "~0.6.1"
      form-data "~2.3.2"
      har-validator "~5.1.3"
      http-signature "~1.2.0"
      is-typedarray "~1.0.0"
      isstream "~0.1.2"
      json-stringify-safe "~5.0.1"
      mime-types "~2.1.19"
      oauth-sign "~0.9.0"
      performance-now "^2.1.0"
      qs "~6.5.2"
      safe-buffer "^5.1.2"
      tough-cookie "~2.5.0"
      tunnel-agent "^0.6.0"
      uuid "^3.3.2"
  
  rimraf@^2.6.1:
    version "2.7.1"
    resolved "https://registry.npmjs.org/rimraf/-/rimraf-2.7.1.tgz"
    integrity sha512-uWjbaKIK3T1OSVptzX7Nl6PvQ3qAGtKEtVRjRuazjfL3Bx5eI409VZSqgND+4UNnmzLVdPj9FqFJNPqBZFve4w==
    dependencies:
      glob "^7.1.3"
  
  safe-buffer@^5.0.1, safe-buffer@^5.1.2, safe-buffer@~5.1.0, safe-buffer@~5.1.1:
    version "5.1.2"
    resolved "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.1.2.tgz"
    integrity sha512-Gd2UZBJDkXlY7GbJxfsE8/nvKkUEU1G38c1siN6QP6a9PT9MmHB8GnpscSmMJSoF8LOIrt8ud/wPtojys4G6+g==
  
  "safer-buffer@>= 2.1.2 < 3", safer-buffer@^2.0.2, safer-buffer@^2.1.0, safer-buffer@~2.1.0:
    version "2.1.2"
    resolved "https://registry.npmjs.org/safer-buffer/-/safer-buffer-2.1.2.tgz"
    integrity sha512-YZo3K82SD7Riyi0E1EQPojLz7kpepnSQI9IyPbHHg1XXXevb5dJI7tpyN2ADxGcQbHG7vcyRHk0cbwqcQriUtg==
  
  sax@^1.2.4:
    version "1.2.4"
    resolved "https://registry.npmjs.org/sax/-/sax-1.2.4.tgz"
    integrity sha512-NqVDv9TpANUjFm0N8uM5GxL36UgKi9/atZw+x7YFnQ8ckwFGKrl4xX4yWtrey3UJm5nP1kUbnYgLopqWNSRhWw==
  
  saxes@^3.1.9:
    version "3.1.11"
    resolved "https://registry.npmjs.org/saxes/-/saxes-3.1.11.tgz"
    integrity sha512-Ydydq3zC+WYDJK1+gRxRapLIED9PWeSuuS41wqyoRmzvhhh9nc+QQrVMKJYzJFULazeGhzSV0QleN2wD3boh2g==
    dependencies:
      xmlchars "^2.1.1"
  
  semver@^5.3.0:
    version "5.7.1"
    resolved "https://registry.npmjs.org/semver/-/semver-5.7.1.tgz"
    integrity sha512-sauaDf/PZdVgrLTNYHRtpXa1iRiKcaebiKQ1BJdpQlWH2lCvexQdX55snPFyK7QzpudqbCI0qXFfOasHdyNDGQ==
  
  set-blocking@~2.0.0:
    version "2.0.0"
    resolved "https://registry.npmjs.org/set-blocking/-/set-blocking-2.0.0.tgz"
    integrity sha1-BF+XgtARrppoA93TgrJDkrPYkPc=
  
  signal-exit@^3.0.0:
    version "3.0.3"
    resolved "https://registry.npmjs.org/signal-exit/-/signal-exit-3.0.3.tgz"
    integrity sha512-VUJ49FC8U1OxwZLxIbTTrDvLnf/6TDgxZcK8wxR8zs13xpx7xbG60ndBlhNrFi2EMuFRoeDoJO7wthSLq42EjA==
  
  simple-concat@^1.0.0:
    version "1.0.1"
    resolved "https://registry.npmjs.org/simple-concat/-/simple-concat-1.0.1.tgz"
    integrity sha512-cSFtAPtRhljv69IK0hTVZQ+OfE9nePi/rtJmw5UjHeVyVroEqJXP1sFztKUy1qU+xvz3u/sfYJLa947b7nAN2Q==
  
  simple-get@^3.0.3:
    version "3.1.0"
    resolved "https://registry.npmjs.org/simple-get/-/simple-get-3.1.0.tgz"
    integrity sha512-bCR6cP+aTdScaQCnQKbPKtJOKDp/hj9EDLJo3Nw4y1QksqaovlW/bnptB6/c1e+qmNIDHRK+oXFDdEqBT8WzUA==
    dependencies:
      decompress-response "^4.2.0"
      once "^1.3.1"
      simple-concat "^1.0.0"
  
  source-map@~0.6.1:
    version "0.6.1"
    resolved "https://registry.npmjs.org/source-map/-/source-map-0.6.1.tgz"
    integrity sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g==
  
  sshpk@^1.7.0:
    version "1.16.1"
    resolved "https://registry.npmjs.org/sshpk/-/sshpk-1.16.1.tgz"
    integrity sha512-HXXqVUq7+pcKeLqqZj6mHFUMvXtOJt1uoUx09pFW6011inTMxqI8BA8PM95myrIyyKwdnzjdFjLiE6KBPVtJIg==
    dependencies:
      asn1 "~0.2.3"
      assert-plus "^1.0.0"
      bcrypt-pbkdf "^1.0.0"
      dashdash "^1.12.0"
      ecc-jsbn "~0.1.1"
      getpass "^0.1.1"
      jsbn "~0.1.0"
      safer-buffer "^2.0.2"
      tweetnacl "~0.14.0"
  
  stealthy-require@^1.1.1:
    version "1.1.1"
    resolved "https://registry.npmjs.org/stealthy-require/-/stealthy-require-1.1.1.tgz"
    integrity sha1-NbCYdbT/SfJqd35QmzCQoyJr8ks=
  
  string-width@^1.0.1, "string-width@^1.0.2 || 2":
    version "1.0.2"
    resolved "https://registry.npmjs.org/string-width/-/string-width-1.0.2.tgz"
    integrity sha1-EYvfW4zcUaKn5w0hHgfisLmxB9M=
    dependencies:
      code-point-at "^1.0.0"
      is-fullwidth-code-point "^1.0.0"
      strip-ansi "^3.0.0"
  
  string_decoder@~1.1.1:
    version "1.1.1"
    resolved "https://registry.npmjs.org/string_decoder/-/string_decoder-1.1.1.tgz"
    integrity sha512-n/ShnvDi6FHbbVfviro+WojiFzv+s8MPMHBczVePfUpDJLwoLT0ht1l4YwBCbi8pJAveEEdnkHyPyTP/mzRfwg==
    dependencies:
      safe-buffer "~5.1.0"
  
  strip-ansi@^3.0.0, strip-ansi@^3.0.1:
    version "3.0.1"
    resolved "https://registry.npmjs.org/strip-ansi/-/strip-ansi-3.0.1.tgz"
    integrity sha1-ajhfuIU9lS1f8F0Oiq+UJ43GPc8=
    dependencies:
      ansi-regex "^2.0.0"
  
  strip-json-comments@~2.0.1:
    version "2.0.1"
    resolved "https://registry.npmjs.org/strip-json-comments/-/strip-json-comments-2.0.1.tgz"
    integrity sha1-PFMZQukIwml8DsNEhYwobHygpgo=
  
  symbol-tree@^3.2.2:
    version "3.2.4"
    resolved "https://registry.npmjs.org/symbol-tree/-/symbol-tree-3.2.4.tgz"
    integrity sha512-9QNk5KwDF+Bvz+PyObkmSYjI5ksVUYtjW7AU22r2NKcfLJcXp96hkDWU3+XndOsUb+AQ9QhfzfCT2O+CNWT5Tw==
  
  tar@^4.4.2:
    version "4.4.13"
    resolved "https://registry.npmjs.org/tar/-/tar-4.4.13.tgz"
    integrity sha512-w2VwSrBoHa5BsSyH+KxEqeQBAllHhccyMFVHtGtdMpF4W7IRWfZjFiQceJPChOeTsSDVUpER2T8FA93pr0L+QA==
    dependencies:
      chownr "^1.1.1"
      fs-minipass "^1.2.5"
      minipass "^2.8.6"
      minizlib "^1.2.1"
      mkdirp "^0.5.0"
      safe-buffer "^5.1.2"
      yallist "^3.0.3"
  
  tough-cookie@^2.3.3, tough-cookie@~2.5.0:
    version "2.5.0"
    resolved "https://registry.npmjs.org/tough-cookie/-/tough-cookie-2.5.0.tgz"
    integrity sha512-nlLsUzgm1kfLXSXfRZMc1KLAugd4hqJHDTvc2hDIwS3mZAfMEuMbc03SujMF+GEcpaX/qboeycw6iO8JwVv2+g==
    dependencies:
      psl "^1.1.28"
      punycode "^2.1.1"
  
  tough-cookie@^3.0.1:
    version "3.0.1"
    resolved "https://registry.npmjs.org/tough-cookie/-/tough-cookie-3.0.1.tgz"
    integrity sha512-yQyJ0u4pZsv9D4clxO69OEjLWYw+jbgspjTue4lTQZLfV0c5l1VmK2y1JK8E9ahdpltPOaAThPcp5nKPUgSnsg==
    dependencies:
      ip-regex "^2.1.0"
      psl "^1.1.28"
      punycode "^2.1.1"
  
  tr46@^1.0.1:
    version "1.0.1"
    resolved "https://registry.npmjs.org/tr46/-/tr46-1.0.1.tgz"
    integrity sha1-qLE/1r/SSJUZZ0zN5VujaTtwbQk=
    dependencies:
      punycode "^2.1.0"
  
  tunnel-agent@^0.6.0:
    version "0.6.0"
    resolved "https://registry.npmjs.org/tunnel-agent/-/tunnel-agent-0.6.0.tgz"
    integrity sha1-J6XeoGs2sEoKmWZ3SykIaPD8QP0=
    dependencies:
      safe-buffer "^5.0.1"
  
  tweetnacl@^0.14.3, tweetnacl@~0.14.0:
    version "0.14.5"
    resolved "https://registry.npmjs.org/tweetnacl/-/tweetnacl-0.14.5.tgz"
    integrity sha1-WuaBd/GS1EViadEIr6k/+HQ/T2Q=
  
  type-check@~0.3.2:
    version "0.3.2"
    resolved "https://registry.npmjs.org/type-check/-/type-check-0.3.2.tgz"
    integrity sha1-WITKtRLPHTVeP7eE8wgEsrUg23I=
    dependencies:
      prelude-ls "~1.1.2"
  
  uppercamelcase@^1.1.0:
    version "1.1.0"
    resolved "https://registry.npmjs.org/uppercamelcase/-/uppercamelcase-1.1.0.tgz"
    integrity sha1-Mk2YprOvx+iolT4QZBUJsOTiP5c=
    dependencies:
      camelcase "^1.2.1"
  
  uri-js@^4.2.2:
    version "4.4.1"
    resolved "https://registry.npmjs.org/uri-js/-/uri-js-4.4.1.tgz"
    integrity sha512-7rKUyy33Q1yc98pQ1DAmLtwX109F7TIfWlW1Ydo8Wl1ii1SeHieeh0HHfPeL2fMXK6z0s8ecKs9frCuLJvndBg==
    dependencies:
      punycode "^2.1.0"
  
  util-deprecate@~1.0.1:
    version "1.0.2"
    resolved "https://registry.npmjs.org/util-deprecate/-/util-deprecate-1.0.2.tgz"
    integrity sha1-RQ1Nyfpw3nMnYvvS1KKJgUGaDM8=
  
  uuid@^3.3.2:
    version "3.4.0"
    resolved "https://registry.npmjs.org/uuid/-/uuid-3.4.0.tgz"
    integrity sha512-HjSDRw6gZE5JMggctHBcjVak08+KEVhSIiDzFnT9S9aegmp85S/bReBVTb4QTFaRNptJ9kuYaNhnbNEOkbKb/A==
  
  verror@1.10.0:
    version "1.10.0"
    resolved "https://registry.npmjs.org/verror/-/verror-1.10.0.tgz"
    integrity sha1-OhBcoXBTr1XW4nDB+CiGguGNpAA=
    dependencies:
      assert-plus "^1.0.0"
      core-util-is "1.0.2"
      extsprintf "^1.2.0"
  
  vue-amap@^0.5.10:
    version "0.5.10"
    resolved "https://registry.npmjs.org/vue-amap/-/vue-amap-0.5.10.tgz"
    integrity sha512-9ViNCev1vx32+zZ5RvF/TmUZNbwL9QrdA2/OnD2GlXMfQBkJy7D08Vb7379t6guqnopDPtWJ8K6gg72h9+4GUg==
    dependencies:
      uppercamelcase "^1.1.0"
  
  vue-esign@^1.0.5:
    version "1.0.5"
    resolved "https://registry.npmjs.org/vue-esign/-/vue-esign-1.0.5.tgz"
    integrity sha512-XwJWnV3YJfilT1qIU8CbJgPRdUltONuv6fjCaMAbvkt6Juidm2m9g73H+2FhPbn7nEliB2q7kZEnQXhbWP4mCw==
    dependencies:
      vue "^2.5.11"
  
  vue-i18n@^8.24.3:
    version "8.24.3"
    resolved "https://registry.npmjs.org/vue-i18n/-/vue-i18n-8.24.3.tgz"
    integrity sha512-uKAYzGbwGIJndY7JwhQwIGi1uyvErWkBfFwooOtjcNnIfMbAR49ad5dT/MiykrJ9pCcgvnocFjFsNLtTzyW+rg==
  
  vue@^2.5.11:
    version "2.6.12"
    resolved "https://registry.npmjs.org/vue/-/vue-2.6.12.tgz"
    integrity sha512-uhmLFETqPPNyuLLbsKz6ioJ4q7AZHzD8ZVFNATNyICSZouqP2Sz0rotWQC8UNBF6VGSCs5abnKJoStA6JbCbfg==
  
  w3c-hr-time@^1.0.1:
    version "1.0.2"
    resolved "https://registry.npmjs.org/w3c-hr-time/-/w3c-hr-time-1.0.2.tgz"
    integrity sha512-z8P5DvDNjKDoFIHK7q8r8lackT6l+jo/Ye3HOle7l9nICP9lf1Ci25fy9vHd0JOWewkIFzXIEig3TdKT7JQ5fQ==
    dependencies:
      browser-process-hrtime "^1.0.0"
  
  w3c-xmlserializer@^1.1.2:
    version "1.1.2"
    resolved "https://registry.npmjs.org/w3c-xmlserializer/-/w3c-xmlserializer-1.1.2.tgz"
    integrity sha512-p10l/ayESzrBMYWRID6xbuCKh2Fp77+sA0doRuGn4tTIMrrZVeqfpKjXHY+oDh3K4nLdPgNwMTVP6Vp4pvqbNg==
    dependencies:
      domexception "^1.0.1"
      webidl-conversions "^4.0.2"
      xml-name-validator "^3.0.0"
  
  webidl-conversions@^4.0.2:
    version "4.0.2"
    resolved "https://registry.npmjs.org/webidl-conversions/-/webidl-conversions-4.0.2.tgz"
    integrity sha512-YQ+BmxuTgd6UXZW3+ICGfyqRyHXVlD5GtQr5+qjiNW7bF0cqrzX500HVXPBOvgXb5YnzDd+h0zqyv61KUD7+Sg==
  
  whatwg-encoding@^1.0.1, whatwg-encoding@^1.0.5:
    version "1.0.5"
    resolved "https://registry.npmjs.org/whatwg-encoding/-/whatwg-encoding-1.0.5.tgz"
    integrity sha512-b5lim54JOPN9HtzvK9HFXvBma/rnfFeqsic0hSpjtDbVxR3dJKLc+KB4V6GgiGOvl7CY/KNh8rxSo9DKQrnUEw==
    dependencies:
      iconv-lite "0.4.24"
  
  whatwg-mimetype@^2.2.0, whatwg-mimetype@^2.3.0:
    version "2.3.0"
    resolved "https://registry.npmjs.org/whatwg-mimetype/-/whatwg-mimetype-2.3.0.tgz"
    integrity sha512-M4yMwr6mAnQz76TbJm914+gPpB/nCwvZbJU28cUD6dR004SAxDLOOSUaB1JDRqLtaOV/vi0IC5lEAGFgrjGv/g==
  
  whatwg-url@^7.0.0:
    version "7.1.0"
    resolved "https://registry.npmjs.org/whatwg-url/-/whatwg-url-7.1.0.tgz"
    integrity sha512-WUu7Rg1DroM7oQvGWfOiAK21n74Gg+T4elXEQYkOhtyLeWiJFoOGLXPKI/9gzIie9CtwVLm8wtw6YJdKyxSjeg==
    dependencies:
      lodash.sortby "^4.7.0"
      tr46 "^1.0.1"
      webidl-conversions "^4.0.2"
  
  wide-align@^1.1.0:
    version "1.1.3"
    resolved "https://registry.npmjs.org/wide-align/-/wide-align-1.1.3.tgz"
    integrity sha512-QGkOQc8XL6Bt5PwnsExKBPuMKBxnGxWWW3fU55Xt4feHozMUhdUMaBCk290qpm/wG5u/RSKzwdAC4i51YigihA==
    dependencies:
      string-width "^1.0.2 || 2"
  
  word-wrap@~1.2.3:
    version "1.2.3"
    resolved "https://registry.npmjs.org/word-wrap/-/word-wrap-1.2.3.tgz"
    integrity sha512-Hz/mrNwitNRh/HUAtM/VT/5VH+ygD6DV7mYKZAtHOrbs8U7lvPS6xf7EJKMF0uW1KJCl0H701g3ZGus+muE5vQ==
  
  wrappy@1:
    version "1.0.2"
    resolved "https://registry.npmjs.org/wrappy/-/wrappy-1.0.2.tgz"
    integrity sha1-tSQ9jz7BqjXxNkYFvA0QNuMKtp8=
  
  ws@^7.0.0:
    version "7.4.4"
    resolved "https://registry.npmjs.org/ws/-/ws-7.4.4.tgz"
    integrity sha512-Qm8k8ojNQIMx7S+Zp8u/uHOx7Qazv3Yv4q68MiWWWOJhiwG5W3x7iqmRtJo8xxrciZUY4vRxUTJCKuRnF28ZZw==
  
  xml-name-validator@^3.0.0:
    version "3.0.0"
    resolved "https://registry.npmjs.org/xml-name-validator/-/xml-name-validator-3.0.0.tgz"
    integrity sha512-A5CUptxDsvxKJEU3yO6DuWBSJz/qizqzJKOMIfUJHETbBw/sFaDxgd6fxm1ewUaM0jZ444Fc5vC5ROYurg/4Pw==
  
  xmlchars@^2.1.1:
    version "2.2.0"
    resolved "https://registry.npmjs.org/xmlchars/-/xmlchars-2.2.0.tgz"
    integrity sha512-JZnDKK8B0RCDw84FNdDAIpZK+JuJw+s7Lz8nksI7SIuU3UXJJslUthsi+uWBUYOwPFwW7W7PRLRfUKpxjtjFCw==
  
  yallist@^3.0.0, yallist@^3.0.3:
    version "3.1.1"
    resolved "https://registry.npmjs.org/yallist/-/yallist-3.1.1.tgz"
    integrity sha512-a4UGQaWPH59mOXUYnAG2ewncQS4i4F43Tv3JoAM+s2VDAmS9NsK8GpDMLrCHPksFT7h3K6TOoUNn2pb7RoXx4g==
